using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.PlansPaiement
{
    public partial class PlanPaiementListForm : Form
    {
        private readonly IPlanPaiementService _planPaiementService;
        private readonly IClientService _clientService;
        private readonly IUtilisateurService _utilisateurService;
        private readonly IAuthenticationService _authenticationService;
        private readonly IServiceProvider _serviceProvider;
        private readonly int _currentUserId;
        private List<PlanPaiement> _plansPaiement = new List<PlanPaiement>();
        private DataTable _dataTable = new DataTable();
        private Dictionary<int, string> _clientNames = new Dictionary<int, string>();
        private Dictionary<int, string> _utilisateurNames = new Dictionary<int, string>();

        public PlanPaiementListForm(
            IPlanPaiementService planPaiementService,
            IClientService clientService,
            IUtilisateurService utilisateurService,
            IAuthenticationService authenticationService,
            IServiceProvider serviceProvider)
        {
            _planPaiementService = planPaiementService ?? throw new ArgumentNullException(nameof(planPaiementService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _currentUserId = 1; // Valeur par défaut, à remplacer par la méthode appropriée

            InitializeComponent();
            InitializeDataTable();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form
            this.Text = "Liste des plans de paiement";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimizeBox = true;
            this.MaximizeBox = true;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            // Icône de l'application
            // this.Icon = Properties.Resources.AppIcon;

            // Main panel
            TableLayoutPanel mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 1,
                RowCount = 3,
                RowStyles = {
                    new RowStyle(SizeType.Absolute, 40),
                    new RowStyle(SizeType.Absolute, 50),
                    new RowStyle(SizeType.Percent, 100)
                }
            };
            this.Controls.Add(mainPanel);

            // Title label
            Label titleLabel = new Label
            {
                Text = "Plans de paiement et échéanciers",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft
            };
            mainPanel.Controls.Add(titleLabel, 0, 0);

            // Filters panel
            TableLayoutPanel filtersPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 7,
                RowCount = 1,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 20),
                    new ColumnStyle(SizeType.Percent, 20),
                    new ColumnStyle(SizeType.Percent, 20),
                    new ColumnStyle(SizeType.Percent, 20),
                    new ColumnStyle(SizeType.Percent, 20),
                    new ColumnStyle(SizeType.Absolute, 120),
                    new ColumnStyle(SizeType.Absolute, 120)
                }
            };
            mainPanel.Controls.Add(filtersPanel, 0, 1);

            // Client filter
            ComboBox clientFilterComboBox = new ComboBox
            {
                Name = "clientFilterComboBox",
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Margin = new Padding(3, 3, 10, 3)
            };
            clientFilterComboBox.SelectedIndexChanged += ClientFilterComboBox_SelectedIndexChanged;
            filtersPanel.Controls.Add(clientFilterComboBox, 0, 0);

            // Status filter
            ComboBox statusFilterComboBox = new ComboBox
            {
                Name = "statusFilterComboBox",
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Margin = new Padding(3, 3, 10, 3)
            };
            statusFilterComboBox.Items.Add("Tous les statuts");
            statusFilterComboBox.Items.Add("En cours");
            statusFilterComboBox.Items.Add("Terminé");
            statusFilterComboBox.Items.Add("En retard");
            statusFilterComboBox.Items.Add("Annulé");
            statusFilterComboBox.SelectedIndex = 0;
            statusFilterComboBox.SelectedIndexChanged += StatusFilterComboBox_SelectedIndexChanged;
            filtersPanel.Controls.Add(statusFilterComboBox, 1, 0);

            // Date range filter
            DateTimePicker dateDebutPicker = new DateTimePicker
            {
                Name = "dateDebutPicker",
                Dock = DockStyle.Fill,
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today.AddMonths(-1),
                Margin = new Padding(3, 3, 10, 3)
            };
            filtersPanel.Controls.Add(dateDebutPicker, 2, 0);

            DateTimePicker dateFinPicker = new DateTimePicker
            {
                Name = "dateFinPicker",
                Dock = DockStyle.Fill,
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today.AddMonths(1),
                Margin = new Padding(3, 3, 10, 3)
            };
            filtersPanel.Controls.Add(dateFinPicker, 3, 0);

            Button applyDateFilterButton = new Button
            {
                Name = "applyDateFilterButton",
                Text = "Appliquer",
                Dock = DockStyle.Fill,
                Margin = new Padding(3, 3, 10, 3)
            };
            applyDateFilterButton.Click += ApplyDateFilterButton_Click;
            filtersPanel.Controls.Add(applyDateFilterButton, 4, 0);

            // Add button
            Button addButton = new Button
            {
                Name = "addButton",
                Text = "Nouveau plan",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            addButton.Click += AddButton_Click;
            filtersPanel.Controls.Add(addButton, 5, 0);

            // Refresh button
            Button refreshButton = new Button
            {
                Name = "refreshButton",
                Text = "Actualiser",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            refreshButton.Click += RefreshButton_Click;
            filtersPanel.Controls.Add(refreshButton, 6, 0);

            // DataGridView
            DataGridView dataGridView = new DataGridView
            {
                Name = "dataGridView",
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                BackgroundColor = SystemColors.Window,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };
            dataGridView.CellDoubleClick += DataGridView_CellDoubleClick;
            dataGridView.DataSource = _dataTable;
            mainPanel.Controls.Add(dataGridView, 0, 2);

            this.ResumeLayout(false);
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Référence", typeof(string));
            _dataTable.Columns.Add("Client", typeof(string));
            _dataTable.Columns.Add("Date création", typeof(DateTime));
            _dataTable.Columns.Add("Date début", typeof(DateTime));
            _dataTable.Columns.Add("Date fin prévue", typeof(DateTime));
            _dataTable.Columns.Add("Montant total", typeof(decimal));
            _dataTable.Columns.Add("Montant payé", typeof(decimal));
            _dataTable.Columns.Add("Montant restant", typeof(decimal));
            _dataTable.Columns.Add("Statut", typeof(string));
            _dataTable.Columns.Add("Responsable", typeof(string));
            _dataTable.Columns.Add("Accord signé", typeof(bool));
        }

        private async void LoadData()
        {
            try
            {
                // Charger les plans de paiement
                _plansPaiement = (await _planPaiementService.GetAllPlansAsync()).ToList();

                // Charger les noms des clients
                var clients = await _clientService.GetAllAsync();
                _clientNames = clients.ToDictionary(c => c.Id, c => c.RaisonSociale);

                // Charger les noms des utilisateurs
                var utilisateurs = await _utilisateurService.GetAllAsync();
                _utilisateurNames = utilisateurs.ToDictionary(u => u.Id, u => u.NomComplet);

                // Remplir le filtre client
                var clientFilterComboBox = Controls.Find("clientFilterComboBox", true).FirstOrDefault() as ComboBox;
                if (clientFilterComboBox != null)
                {
                    clientFilterComboBox.Items.Clear();
                    clientFilterComboBox.Items.Add("Tous les clients");
                    foreach (var client in clients.OrderBy(c => c.RaisonSociale))
                    {
                        clientFilterComboBox.Items.Add(client.RaisonSociale);
                    }
                    clientFilterComboBox.SelectedIndex = 0;
                }

                // Mettre à jour la table de données
                UpdateDataTable(_plansPaiement);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des plans de paiement");
                MessageBox.Show($"Erreur lors du chargement des données : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateDataTable(List<PlanPaiement> plansPaiement)
        {
            _dataTable.Clear();

            foreach (var plan in plansPaiement)
            {
                string clientName = _clientNames.ContainsKey(plan.ClientId) ? _clientNames[plan.ClientId] : "Client inconnu";
                string responsableName = _utilisateurNames.ContainsKey(plan.ResponsableId) ? _utilisateurNames[plan.ResponsableId] : "Utilisateur inconnu";

                _dataTable.Rows.Add(
                    plan.Id,
                    plan.Reference,
                    clientName,
                    plan.DateCreationPlan,
                    plan.DateDebut,
                    plan.DateFinPrevue,
                    plan.MontantTotal,
                    plan.MontantPaye,
                    plan.MontantRestant,
                    plan.Statut,
                    responsableName,
                    plan.AccordSigne
                );
            }
        }

        private void ClientFilterComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void StatusFilterComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyDateFilterButton_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void RefreshButton_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new PlanPaiementEditForm(_planPaiementService, _clientService, _utilisateurService, _authenticationService, null))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout de plan de paiement");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                try
                {
                    var dataGridView = sender as DataGridView;
                    int planId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);

                    using (var form = new PlanPaiementDetailsForm(
                        _planPaiementService,
                        _clientService,
                        _utilisateurService,
                        _authenticationService,
                        _serviceProvider,
                        planId))
                    {
                        form.ShowDialog();
                        LoadData();
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Erreur lors de l'ouverture du formulaire de détails du plan de paiement");
                    MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void ApplyFilters()
        {
            try
            {
                var clientFilterComboBox = Controls.Find("clientFilterComboBox", true).FirstOrDefault() as ComboBox;
                var statusFilterComboBox = Controls.Find("statusFilterComboBox", true).FirstOrDefault() as ComboBox;
                var dateDebutPicker = Controls.Find("dateDebutPicker", true).FirstOrDefault() as DateTimePicker;
                var dateFinPicker = Controls.Find("dateFinPicker", true).FirstOrDefault() as DateTimePicker;

                if (clientFilterComboBox == null || statusFilterComboBox == null || dateDebutPicker == null || dateFinPicker == null)
                    return;

                var filteredPlans = _plansPaiement.ToList();

                // Filtre par client
                if (clientFilterComboBox.SelectedIndex > 0)
                {
                    string selectedClientName = clientFilterComboBox.SelectedItem.ToString();
                    int clientId = _clientNames.FirstOrDefault(c => c.Value == selectedClientName).Key;
                    filteredPlans = filteredPlans.Where(p => p.ClientId == clientId).ToList();
                }

                // Filtre par statut
                if (statusFilterComboBox.SelectedIndex > 0)
                {
                    string selectedStatus = statusFilterComboBox.SelectedItem.ToString();
                    filteredPlans = filteredPlans.Where(p => p.Statut == selectedStatus).ToList();
                }

                // Filtre par date
                DateTime dateDebut = dateDebutPicker.Value.Date;
                DateTime dateFin = dateFinPicker.Value.Date.AddDays(1).AddSeconds(-1);
                filteredPlans = filteredPlans.Where(p =>
                    (p.DateDebut >= dateDebut && p.DateDebut <= dateFin) ||
                    (p.DateFinPrevue >= dateDebut && p.DateFinPrevue <= dateFin) ||
                    (p.DateDebut <= dateDebut && p.DateFinPrevue >= dateFin)).ToList();

                // Mettre à jour la table de données
                UpdateDataTable(filteredPlans);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'application des filtres");
                MessageBox.Show($"Erreur lors de l'application des filtres : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
