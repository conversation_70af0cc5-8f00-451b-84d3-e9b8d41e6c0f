using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.WinForms.Helpers;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Alertes
{
    public partial class AlerteListForm : Form
    {
        private readonly IAlerteService _alerteService;
        private readonly IAuthenticationService _authenticationService;
        private readonly int _currentUserId;
        private List<Alerte> _alertes = new List<Alerte>();
        private DataTable _dataTable = new DataTable();

        public AlerteListForm(IAlerteService alerteService, IAuthenticationService authenticationService, int currentUserId)
        {
            _alerteService = alerteService ?? throw new ArgumentNullException(nameof(alerteService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }

        private async void AlerteListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Vérifier les permissions
                bool canView = await _authenticationService.HasPermissionAsync(_currentUserId, "ALERTES_VIEW");
                bool canAdd = await _authenticationService.HasPermissionAsync(_currentUserId, "ALERTES_ADD");
                bool canEdit = await _authenticationService.HasPermissionAsync(_currentUserId, "ALERTES_EDIT");
                bool canDelete = await _authenticationService.HasPermissionAsync(_currentUserId, "ALERTES_DELETE");

                if (!canView)
                {
                    MessageBox.Show("Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.",
                        "Accès refusé", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return;
                }

                // Configurer les boutons en fonction des permissions
                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                var addButton = toolStrip?.Items.Find("addButton", false).FirstOrDefault() as ToolStripButton;
                var editButton = toolStrip?.Items.Find("editButton", false).FirstOrDefault() as ToolStripButton;
                var deleteButton = toolStrip?.Items.Find("deleteButton", false).FirstOrDefault() as ToolStripButton;

                if (addButton != null) addButton.Enabled = canAdd;
                if (editButton != null) editButton.Enabled = canEdit;
                if (deleteButton != null) deleteButton.Enabled = canDelete;

                // Initialiser la table de données
                InitializeDataTable();

                // Charger les alertes
                await LoadAlertesAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire de liste des alertes");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Nom", typeof(string));
            _dataTable.Columns.Add("Type", typeof(string));
            _dataTable.Columns.Add("Condition", typeof(string));
            _dataTable.Columns.Add("Seuil", typeof(decimal));
            _dataTable.Columns.Add("Statut", typeof(string));
            _dataTable.Columns.Add("Email", typeof(bool));
            _dataTable.Columns.Add("Dernière Notification", typeof(DateTime));

            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (dataGridView.Columns["Seuil"] != null)
                    dataGridView.Columns["Seuil"].DefaultCellStyle.Format = "N2";
                if (dataGridView.Columns["Dernière Notification"] != null)
                    dataGridView.Columns["Dernière Notification"].DefaultCellStyle.Format = "dd/MM/yyyy HH:mm";
            }
        }

        private async Task LoadAlertesAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer les alertes
                _alertes = (await _alerteService.GetByUtilisateurIdAsync(_currentUserId)).ToList();

                // Mettre à jour la table de données
                UpdateDataTable(_alertes);

                // Mettre à jour le compteur
                var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
                var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
                if (countLabel != null)
                {
                    countLabel.Text = $"Nombre d'alertes : {_alertes.Count}";
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des alertes");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des alertes : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void UpdateDataTable(List<Alerte> alertes)
        {
            _dataTable.Clear();

            foreach (var alerte in alertes)
            {
                _dataTable.Rows.Add(
                    alerte.Id,
                    alerte.Nom,
                    alerte.Type,
                    alerte.Condition,
                    alerte.Seuil,
                    alerte.EstActive ? "Active" : "Inactive",
                    alerte.EnvoyerEmail,
                    alerte.DerniereNotification
                );
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            // Réinitialiser le champ de recherche
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var searchTextBox = toolStrip?.Items.Find("searchTextBox", false).FirstOrDefault() as ToolStripTextBox;
            if (searchTextBox != null)
            {
                searchTextBox.Text = string.Empty;
            }

            // Recharger les alertes
            await LoadAlertesAsync();
        }

        private void SearchButton_Click(object sender, EventArgs e)
        {
            Search();
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                Search();
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        private void Search()
        {
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var searchTextBox = toolStrip?.Items.Find("searchTextBox", false).FirstOrDefault() as ToolStripTextBox;
            if (searchTextBox != null)
            {
                string searchText = searchTextBox.Text.Trim().ToLower();
                if (string.IsNullOrEmpty(searchText))
                {
                    // Afficher toutes les alertes
                    UpdateDataTable(_alertes);
                }
                else
                {
                    // Filtrer les alertes
                    var filteredAlertes = _alertes.Where(a =>
                        a.Nom.ToLower().Contains(searchText) ||
                        a.Description.ToLower().Contains(searchText) ||
                        a.Type.ToLower().Contains(searchText)
                    ).ToList();

                    // Mettre à jour la table de données
                    UpdateDataTable(filteredAlertes);
                }
            }
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir le formulaire d'ajout d'alerte
                using (var form = new AlerteEditForm(_alerteService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les alertes
                        LoadAlertesAsync().Wait();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout d'alerte");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer l'alerte sélectionnée
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int alerteId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    EditAlerte(alerteId);
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une alerte à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de modification d'alerte");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int alerteId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    EditAlerte(alerteId);
                }
            }
        }

        private async void EditAlerte(int alerteId)
        {
            try
            {
                // Récupérer l'alerte
                var alerte = await _alerteService.GetByIdAsync(alerteId);
                if (alerte == null)
                {
                    MessageBox.Show("L'alerte demandée n'existe pas ou a été supprimée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Ouvrir le formulaire de modification d'alerte
                using (var form = new AlerteEditForm(_alerteService, _currentUserId, alerte))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les alertes
                        await LoadAlertesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de modification d'alerte");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer l'alerte sélectionnée
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int alerteId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string alerteName = dataGridView.SelectedRows[0].Cells["Nom"].Value.ToString();

                    // Demander confirmation
                    var result = MessageBox.Show($"Êtes-vous sûr de vouloir supprimer l'alerte '{alerteName}' ?\n\nCette action est irréversible.",
                        "Confirmation de suppression", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        // Supprimer l'alerte
                        bool success = await _alerteService.DeleteAsync(alerteId, _currentUserId);
                        if (success)
                        {
                            // Recharger les alertes
                            await LoadAlertesAsync();
                            MessageBox.Show("L'alerte a été supprimée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("Une erreur s'est produite lors de la suppression de l'alerte.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une alerte à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression d'une alerte");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void CheckAlertesButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Vérifier les alertes
                var alertesDeclenchees = await _alerteService.CheckAlertesAsync(_currentUserId);
                int count = alertesDeclenchees.Count();

                if (count > 0)
                {
                    // Afficher les alertes déclenchées
                    foreach (var alerte in alertesDeclenchees)
                    {
                        // Afficher une notification
                        NotificationHelper.ShowNotification(alerte, this);

                        // Enregistrer la notification
                        await _alerteService.RecordNotificationAsync(alerte.Id, _currentUserId);
                    }

                    MessageBox.Show($"{count} alerte(s) déclenchée(s).", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Aucune alerte déclenchée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Recharger les alertes pour mettre à jour les dates de dernière notification
                await LoadAlertesAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la vérification des alertes");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }
    }
}
