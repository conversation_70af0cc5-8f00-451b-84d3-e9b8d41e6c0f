using System;
using System.Collections.Generic;
using RecouvreX.Models.Enums;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un client dans le système
    /// </summary>
    public class Client : BaseEntity
    {
        /// <summary>
        /// Code unique du client
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Nom ou raison sociale du client
        /// </summary>
        public string RaisonSociale { get; set; } = string.Empty;

        /// <summary>
        /// Adresse du client
        /// </summary>
        public string Adresse { get; set; } = string.Empty;

        /// <summary>
        /// Ville du client
        /// </summary>
        public string Ville { get; set; } = string.Empty;

        /// <summary>
        /// Code postal du client
        /// </summary>
        public string CodePostal { get; set; } = string.Empty;

        /// <summary>
        /// Pays du client
        /// </summary>
        public string Pays { get; set; } = string.Empty;

        /// <summary>
        /// Numéro de téléphone du client
        /// </summary>
        public string Telephone { get; set; } = string.Empty;

        /// <summary>
        /// Adresse email du client
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Numéro d'identification fiscale du client
        /// </summary>
        public string NumeroFiscal { get; set; } = string.Empty;

        /// <summary>
        /// Type de client (catégorie)
        /// </summary>
        public string TypeClient { get; set; } = string.Empty;

        /// <summary>
        /// Identifiant du commercial responsable du client
        /// </summary>
        public int? CommercialId { get; set; }

        /// <summary>
        /// Commercial responsable du client (navigation property)
        /// </summary>
        public Utilisateur Commercial { get; set; } = null!;

        /// <summary>
        /// Limite de crédit accordée au client
        /// </summary>
        public decimal LimiteCredit { get; set; }

        /// <summary>
        /// Solde actuel du client
        /// </summary>
        public decimal SoldeActuel { get; set; }

        /// <summary>
        /// Segment du client (A, B, C)
        /// </summary>
        public SegmentClient Segment { get; set; } = SegmentClient.NonSegmente;

        /// <summary>
        /// Date de la dernière segmentation
        /// </summary>
        public DateTime? DateSegmentation { get; set; }

        /// <summary>
        /// Commentaire sur la segmentation
        /// </summary>
        public string CommentaireSegmentation { get; set; } = string.Empty;

        /// <summary>
        /// Indique si la segmentation a été faite manuellement
        /// </summary>
        public bool SegmentationManuelle { get; set; }

        /// <summary>
        /// Liste des factures du client (navigation property)
        /// </summary>
        public ICollection<Facture> Factures { get; set; } = new List<Facture>();

        /// <summary>
        /// Liste des contacts du client (navigation property)
        /// </summary>
        public ICollection<Contact> Contacts { get; set; } = new List<Contact>();
    }
}
