namespace RecouvreX.WinForms.Forms.Clients
{
    partial class ContactEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.SuspendLayout();
            //
            // ContactEditForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(500, 400);
            this.Name = "ContactEditForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Édition de contact";
            this.Load += new System.EventHandler(this.ContactEditForm_Load);
            this.ResumeLayout(false);

            // Panneau principal
            System.Windows.Forms.TableLayoutPanel mainPanel = new System.Windows.Forms.TableLayoutPanel();
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Padding = new System.Windows.Forms.Padding(10);
            mainPanel.ColumnCount = 2;
            mainPanel.RowCount = 8;
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 30F));
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 70F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.Controls.Add(mainPanel);

            // Nom
            System.Windows.Forms.Label nomLabel = new System.Windows.Forms.Label();
            nomLabel.Text = "Nom* :";
            nomLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(nomLabel, 0, 0);

            System.Windows.Forms.TextBox nomTextBox = new System.Windows.Forms.TextBox();
            nomTextBox.Name = "nomTextBox";
            nomTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(nomTextBox, 1, 0);

            // Prénom
            System.Windows.Forms.Label prenomLabel = new System.Windows.Forms.Label();
            prenomLabel.Text = "Prénom :";
            prenomLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(prenomLabel, 0, 1);

            System.Windows.Forms.TextBox prenomTextBox = new System.Windows.Forms.TextBox();
            prenomTextBox.Name = "prenomTextBox";
            prenomTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(prenomTextBox, 1, 1);

            // Fonction
            System.Windows.Forms.Label fonctionLabel = new System.Windows.Forms.Label();
            fonctionLabel.Text = "Fonction :";
            fonctionLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(fonctionLabel, 0, 2);

            System.Windows.Forms.TextBox fonctionTextBox = new System.Windows.Forms.TextBox();
            fonctionTextBox.Name = "fonctionTextBox";
            fonctionTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(fonctionTextBox, 1, 2);

            // Téléphone
            System.Windows.Forms.Label telephoneLabel = new System.Windows.Forms.Label();
            telephoneLabel.Text = "Téléphone :";
            telephoneLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(telephoneLabel, 0, 3);

            System.Windows.Forms.TextBox telephoneTextBox = new System.Windows.Forms.TextBox();
            telephoneTextBox.Name = "telephoneTextBox";
            telephoneTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(telephoneTextBox, 1, 3);

            // Email
            System.Windows.Forms.Label emailLabel = new System.Windows.Forms.Label();
            emailLabel.Text = "Email :";
            emailLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(emailLabel, 0, 4);

            System.Windows.Forms.TextBox emailTextBox = new System.Windows.Forms.TextBox();
            emailTextBox.Name = "emailTextBox";
            emailTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(emailTextBox, 1, 4);

            // Est principal
            System.Windows.Forms.Label estPrincipalLabel = new System.Windows.Forms.Label();
            estPrincipalLabel.Text = "Contact principal :";
            estPrincipalLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(estPrincipalLabel, 0, 5);

            System.Windows.Forms.CheckBox estPrincipalCheckBox = new System.Windows.Forms.CheckBox();
            estPrincipalCheckBox.Name = "estPrincipalCheckBox";
            estPrincipalCheckBox.Text = "";
            estPrincipalCheckBox.Anchor = System.Windows.Forms.AnchorStyles.Left;
            mainPanel.Controls.Add(estPrincipalCheckBox, 1, 5);

            // Notes
            System.Windows.Forms.Label notesLabel = new System.Windows.Forms.Label();
            notesLabel.Text = "Notes :";
            notesLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Top;
            mainPanel.Controls.Add(notesLabel, 0, 6);

            System.Windows.Forms.TextBox notesTextBox = new System.Windows.Forms.TextBox();
            notesTextBox.Name = "notesTextBox";
            notesTextBox.Multiline = true;
            notesTextBox.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            notesTextBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(notesTextBox, 1, 6);

            // Boutons
            System.Windows.Forms.FlowLayoutPanel buttonsPanel = new System.Windows.Forms.FlowLayoutPanel();
            buttonsPanel.FlowDirection = System.Windows.Forms.FlowDirection.RightToLeft;
            buttonsPanel.Dock = System.Windows.Forms.DockStyle.Bottom;
            buttonsPanel.Height = 40;
            buttonsPanel.Padding = new System.Windows.Forms.Padding(0, 5, 0, 0);
            this.Controls.Add(buttonsPanel);

            System.Windows.Forms.Button cancelButton = new System.Windows.Forms.Button();
            cancelButton.Text = "Annuler";
            cancelButton.Size = new System.Drawing.Size(100, 30);
            cancelButton.Click += new System.EventHandler(this.CancelButton_Click);
            buttonsPanel.Controls.Add(cancelButton);

            System.Windows.Forms.Button saveButton = new System.Windows.Forms.Button();
            saveButton.Text = "Enregistrer";
            saveButton.Size = new System.Drawing.Size(100, 30);
            saveButton.Click += new System.EventHandler(this.SaveButton_Click);
            buttonsPanel.Controls.Add(saveButton);
        }

        #endregion
    }
}
