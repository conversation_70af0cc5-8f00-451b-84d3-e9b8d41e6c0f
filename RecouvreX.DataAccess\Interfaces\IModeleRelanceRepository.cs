using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des modèles de relance
    /// </summary>
    public interface IModeleRelanceRepository : IRepository<ModeleRelance>
    {
        /// <summary>
        /// Récupère les modèles de relance par type
        /// </summary>
        /// <param name="type">Type de modèle de relance</param>
        /// <returns>Liste des modèles de relance du type spécifié</returns>
        Task<IEnumerable<ModeleRelance>> GetByTypeAsync(string type);

        /// <summary>
        /// Récupère les modèles de relance par niveau de fermeté
        /// </summary>
        /// <param name="niveauFermete">Niveau de fermeté</param>
        /// <returns>Liste des modèles de relance du niveau spécifié</returns>
        Task<IEnumerable<ModeleRelance>> GetByNiveauFermeteAsync(int niveauFermete);

        /// <summary>
        /// Récupère le modèle de relance par défaut pour un type et un niveau de fermeté
        /// </summary>
        /// <param name="type">Type de modèle de relance</param>
        /// <param name="niveauFermete">Niveau de fermeté</param>
        /// <returns>Modèle de relance par défaut ou null</returns>
        Task<ModeleRelance> GetDefaultAsync(string type, int niveauFermete);

        /// <summary>
        /// Définit un modèle de relance comme modèle par défaut pour son type et niveau de fermeté
        /// </summary>
        /// <param name="id">Identifiant du modèle de relance</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> SetAsDefaultAsync(int id, int modifiePar);
    }
}
