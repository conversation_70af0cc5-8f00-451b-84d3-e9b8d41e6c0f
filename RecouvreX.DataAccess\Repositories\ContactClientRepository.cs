using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour la gestion des contacts client
    /// </summary>
    public class ContactClientRepository : BaseRepository<ContactClient>, IContactClientRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public ContactClientRepository(DatabaseConnection dbConnection) : base(dbConnection, "ContactsClient")
        {
        }

        /// <summary>
        /// Récupère les contacts par client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des contacts pour le client spécifié</returns>
        public async Task<IEnumerable<ContactClient>> GetByClientIdAsync(int clientId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE ClientId = @ClientId AND EstActif = 1 ORDER BY EstPrincipal DESC, Nom, Prenom";
                return await connection.QueryAsync<ContactClient>(query, new { ClientId = clientId });
            }
        }

        /// <summary>
        /// Récupère le contact principal d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Contact principal du client ou null</returns>
        public async Task<ContactClient> GetPrincipalContactAsync(int clientId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE ClientId = @ClientId AND EstPrincipal = 1 AND EstActif = 1";
                var contacts = await connection.QueryAsync<ContactClient>(query, new { ClientId = clientId });
                return contacts.FirstOrDefault();
            }
        }

        /// <summary>
        /// Récupère le contact responsable des paiements d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Contact responsable des paiements du client ou null</returns>
        public async Task<ContactClient> GetPaymentContactAsync(int clientId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE ClientId = @ClientId AND EstResponsablePaiements = 1 AND EstActif = 1";
                var contacts = await connection.QueryAsync<ContactClient>(query, new { ClientId = clientId });
                return contacts.FirstOrDefault();
            }
        }

        /// <summary>
        /// Définit un contact comme contact principal
        /// </summary>
        /// <param name="contactId">Identifiant du contact</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> SetAsPrincipalAsync(int contactId, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Récupérer le contact
                var contact = await GetByIdAsync(contactId);
                if (contact == null)
                    return false;

                // Commencer une transaction
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Réinitialiser tous les contacts principaux pour ce client
                        var resetQuery = $@"
                            UPDATE {_tableName}
                            SET EstPrincipal = 0,
                                DateModification = @DateModification,
                                ModifiePar = @ModifiePar
                            WHERE ClientId = @ClientId AND EstActif = 1";

                        await connection.ExecuteAsync(resetQuery, new
                        {
                            ClientId = contact.ClientId,
                            DateModification = DateTime.Now,
                            ModifiePar = modifiePar
                        }, transaction);

                        // Définir le nouveau contact principal
                        var updateQuery = $@"
                            UPDATE {_tableName}
                            SET EstPrincipal = 1,
                                DateModification = @DateModification,
                                ModifiePar = @ModifiePar
                            WHERE Id = @Id AND EstActif = 1";

                        await connection.ExecuteAsync(updateQuery, new
                        {
                            Id = contactId,
                            DateModification = DateTime.Now,
                            ModifiePar = modifiePar
                        }, transaction);

                        // Valider la transaction
                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        // Annuler la transaction en cas d'erreur
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// Définit un contact comme responsable des paiements
        /// </summary>
        /// <param name="contactId">Identifiant du contact</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> SetAsPaymentResponsibleAsync(int contactId, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Récupérer le contact
                var contact = await GetByIdAsync(contactId);
                if (contact == null)
                    return false;

                // Commencer une transaction
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Réinitialiser tous les contacts responsables des paiements pour ce client
                        var resetQuery = $@"
                            UPDATE {_tableName}
                            SET EstResponsablePaiements = 0,
                                DateModification = @DateModification,
                                ModifiePar = @ModifiePar
                            WHERE ClientId = @ClientId AND EstActif = 1";

                        await connection.ExecuteAsync(resetQuery, new
                        {
                            ClientId = contact.ClientId,
                            DateModification = DateTime.Now,
                            ModifiePar = modifiePar
                        }, transaction);

                        // Définir le nouveau contact responsable des paiements
                        var updateQuery = $@"
                            UPDATE {_tableName}
                            SET EstResponsablePaiements = 1,
                                DateModification = @DateModification,
                                ModifiePar = @ModifiePar
                            WHERE Id = @Id AND EstActif = 1";

                        await connection.ExecuteAsync(updateQuery, new
                        {
                            Id = contactId,
                            DateModification = DateTime.Now,
                            ModifiePar = modifiePar
                        }, transaction);

                        // Valider la transaction
                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        // Annuler la transaction en cas d'erreur
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }
    }
}
