using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les historiques d'étapes de litiges
    /// </summary>
    public class HistoriqueEtapeLitigeRepository : IHistoriqueEtapeLitigeRepository
    {
        private readonly DatabaseConnection _dbConnection;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public HistoriqueEtapeLitigeRepository(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection ?? throw new ArgumentNullException(nameof(dbConnection));
        }

        /// <summary>
        /// Récupère tous les historiques d'étapes
        /// </summary>
        /// <returns>Liste des historiques d'étapes</returns>
        public async Task<IEnumerable<HistoriqueEtapeLitige>> GetAllAsync()
        {
            try
            {
                using (var connection = _dbConnection.CreateConnection())
                {
                    return await connection.QueryAsync<HistoriqueEtapeLitige>(
                        "SELECT * FROM HistoriqueEtapeLitiges WHERE EstActif = 1 ORDER BY DateChangement DESC");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération de tous les historiques d'étapes");
                throw;
            }
        }

        /// <summary>
        /// Récupère un historique d'étape par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'historique d'étape</param>
        /// <returns>Historique d'étape trouvé ou null</returns>
        public async Task<HistoriqueEtapeLitige> GetByIdAsync(int id)
        {
            try
            {
                using (var connection = _dbConnection.CreateConnection())
                {
                    return await connection.QueryFirstOrDefaultAsync<HistoriqueEtapeLitige>(
                        "SELECT * FROM HistoriqueEtapeLitiges WHERE Id = @Id AND EstActif = 1",
                        new { Id = id });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération de l'historique d'étape {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Récupère les historiques d'étapes pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des historiques d'étapes pour le litige</returns>
        public async Task<IEnumerable<HistoriqueEtapeLitige>> GetByLitigeIdAsync(int litigeId)
        {
            try
            {
                using (var connection = _dbConnection.CreateConnection())
                {
                    return await connection.QueryAsync<HistoriqueEtapeLitige>(
                        "SELECT * FROM HistoriqueEtapeLitiges WHERE LitigeId = @LitigeId AND EstActif = 1 ORDER BY DateChangement DESC",
                        new { LitigeId = litigeId });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des historiques d'étapes pour le litige {LitigeId}", litigeId);
                throw;
            }
        }

        /// <summary>
        /// Ajoute un historique d'étape
        /// </summary>
        /// <param name="historiqueEtape">Historique d'étape à ajouter</param>
        /// <returns>Historique d'étape ajouté</returns>
        public async Task<HistoriqueEtapeLitige> AddAsync(HistoriqueEtapeLitige historiqueEtape)
        {
            try
            {
                using (var connection = _dbConnection.CreateConnection())
                {
                    var id = await connection.QuerySingleAsync<int>(
                        @"INSERT INTO HistoriqueEtapeLitiges (LitigeId, EtapeLitigeId, DateChangement, Commentaire, UtilisateurId, EstActif, DateCreation, CreePar)
                          VALUES (@LitigeId, @EtapeLitigeId, @DateChangement, @Commentaire, @UtilisateurId, 1, GETDATE(), @UtilisateurId);
                          SELECT CAST(SCOPE_IDENTITY() as int)",
                        historiqueEtape);

                    historiqueEtape.Id = id;
                    return historiqueEtape;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ajout de l'historique d'étape");
                throw;
            }
        }

        /// <summary>
        /// Met à jour un historique d'étape
        /// </summary>
        /// <param name="historiqueEtape">Historique d'étape à mettre à jour</param>
        /// <returns>True si l'historique d'étape a été mis à jour</returns>
        public async Task<bool> UpdateAsync(HistoriqueEtapeLitige historiqueEtape)
        {
            try
            {
                using (var connection = _dbConnection.CreateConnection())
                {
                    var result = await connection.ExecuteAsync(
                        @"UPDATE HistoriqueEtapeLitiges
                          SET LitigeId = @LitigeId,
                              EtapeLitigeId = @EtapeLitigeId,
                              DateChangement = @DateChangement,
                              Commentaire = @Commentaire,
                              UtilisateurId = @UtilisateurId,
                              DateModification = GETDATE(),
                              ModifiePar = @UtilisateurId
                          WHERE Id = @Id",
                        historiqueEtape);

                    return result > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour de l'historique d'étape {Id}", historiqueEtape.Id);
                throw;
            }
        }
    }
}
