using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les documents de litiges
    /// </summary>
    public class DocumentLitigeRepository : IDocumentLitigeRepository
    {
        private readonly DatabaseConnection _dbConnection;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public DocumentLitigeRepository(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection ?? throw new ArgumentNullException(nameof(dbConnection));
        }

        /// <summary>
        /// Récupère tous les documents
        /// </summary>
        /// <returns>Liste des documents</returns>
        public async Task<IEnumerable<DocumentLitige>> GetAllAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT d.*, l.*, u.*
                    FROM DocumentsLitige d
                    LEFT JOIN Litiges l ON d.LitigeId = l.Id
                    LEFT JOIN Utilisateurs u ON d.UtilisateurId = u.Id
                    WHERE d.EstActif = 1
                    ORDER BY d.DateAjout DESC";

                var documentsDictionary = new Dictionary<int, DocumentLitige>();
                var result = await connection.QueryAsync<DocumentLitige, Litige, Utilisateur, DocumentLitige>(
                    query,
                    (document, litige, utilisateur) =>
                    {
                        if (!documentsDictionary.TryGetValue(document.Id, out var documentEntry))
                        {
                            documentEntry = document;
                            documentEntry.Litige = litige;
                            documentEntry.Utilisateur = utilisateur;
                            documentsDictionary.Add(document.Id, documentEntry);
                        }
                        return documentEntry;
                    },
                    splitOn: "Id,Id");

                return documentsDictionary.Values;
            }
        }

        /// <summary>
        /// Récupère un document par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du document</param>
        /// <returns>Document</returns>
        public async Task<DocumentLitige> GetByIdAsync(int id)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT d.*, l.*, u.*
                    FROM DocumentsLitige d
                    LEFT JOIN Litiges l ON d.LitigeId = l.Id
                    LEFT JOIN Utilisateurs u ON d.UtilisateurId = u.Id
                    WHERE d.Id = @Id AND d.EstActif = 1";

                var documentsDictionary = new Dictionary<int, DocumentLitige>();
                var result = await connection.QueryAsync<DocumentLitige, Litige, Utilisateur, DocumentLitige>(
                    query,
                    (document, litige, utilisateur) =>
                    {
                        if (!documentsDictionary.TryGetValue(document.Id, out var documentEntry))
                        {
                            documentEntry = document;
                            documentEntry.Litige = litige;
                            documentEntry.Utilisateur = utilisateur;
                            documentsDictionary.Add(document.Id, documentEntry);
                        }
                        return documentEntry;
                    },
                    new { Id = id },
                    splitOn: "Id,Id");

                return documentsDictionary.Values.FirstOrDefault();
            }
        }

        /// <summary>
        /// Récupère les documents pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des documents</returns>
        public async Task<IEnumerable<DocumentLitige>> GetByLitigeIdAsync(int litigeId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT d.*, l.*, u.*
                    FROM DocumentsLitige d
                    LEFT JOIN Litiges l ON d.LitigeId = l.Id
                    LEFT JOIN Utilisateurs u ON d.UtilisateurId = u.Id
                    WHERE d.LitigeId = @LitigeId AND d.EstActif = 1
                    ORDER BY d.DateAjout DESC";

                var documentsDictionary = new Dictionary<int, DocumentLitige>();
                var result = await connection.QueryAsync<DocumentLitige, Litige, Utilisateur, DocumentLitige>(
                    query,
                    (document, litige, utilisateur) =>
                    {
                        if (!documentsDictionary.TryGetValue(document.Id, out var documentEntry))
                        {
                            documentEntry = document;
                            documentEntry.Litige = litige;
                            documentEntry.Utilisateur = utilisateur;
                            documentsDictionary.Add(document.Id, documentEntry);
                        }
                        return documentEntry;
                    },
                    new { LitigeId = litigeId },
                    splitOn: "Id,Id");

                return documentsDictionary.Values;
            }
        }

        /// <summary>
        /// Ajoute un document
        /// </summary>
        /// <param name="document">Document à ajouter</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui crée le document</param>
        /// <returns>Document ajouté</returns>
        public async Task<DocumentLitige> AddAsync(DocumentLitige document, int creePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    INSERT INTO DocumentsLitige (
                        LitigeId, Nom, Description, CheminFichier, TypeFichier, TailleFichier,
                        DateAjout, UtilisateurId, DateCreation, CreePar, EstActif
                    ) VALUES (
                        @LitigeId, @Nom, @Description, @CheminFichier, @TypeFichier, @TailleFichier,
                        @DateAjout, @UtilisateurId, GETDATE(), @CreePar, 1
                    );
                    SELECT CAST(SCOPE_IDENTITY() as int)";

                var id = await connection.QuerySingleAsync<int>(query, new
                {
                    document.LitigeId,
                    document.Nom,
                    document.Description,
                    document.CheminFichier,
                    document.TypeFichier,
                    document.TailleFichier,
                    document.DateAjout,
                    document.UtilisateurId,
                    CreePar = creePar
                });

                document.Id = id;
                document.CreePar = creePar;
                document.DateCreation = DateTime.Now;
                document.EstActif = true;

                return document;
            }
        }

        /// <summary>
        /// Met à jour un document
        /// </summary>
        /// <param name="document">Document à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui modifie le document</param>
        /// <returns>True si le document a été mis à jour</returns>
        public async Task<bool> UpdateAsync(DocumentLitige document, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE DocumentsLitige
                    SET Nom = @Nom,
                        Description = @Description,
                        DateModification = GETDATE(),
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id";

                var result = await connection.ExecuteAsync(query, new
                {
                    document.Id,
                    document.Nom,
                    document.Description,
                    ModifiePar = modifiePar
                });

                return result > 0;
            }
        }

        /// <summary>
        /// Supprime un document
        /// </summary>
        /// <param name="id">Identifiant du document</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui supprime le document</param>
        /// <returns>True si le document a été supprimé</returns>
        public async Task<bool> DeleteAsync(int id, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE DocumentsLitige
                    SET EstActif = 0,
                        DateModification = GETDATE(),
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id";

                var result = await connection.ExecuteAsync(query, new { Id = id, ModifiePar = modifiePar });
                return result > 0;
            }
        }
    }
}
