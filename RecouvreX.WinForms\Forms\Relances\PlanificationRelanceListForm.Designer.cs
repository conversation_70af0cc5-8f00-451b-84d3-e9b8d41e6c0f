namespace RecouvreX.WinForms.Forms.Relances
{
    partial class PlanificationRelanceListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            toolStrip = new ToolStrip();
            validateButton = new ToolStripButton();
            sendButton = new ToolStripButton();
            cancelButton = new ToolStripButton();
            refreshButton = new ToolStripButton();
            filterPanel = new Panel();
            filterTable = new TableLayoutPanel();
            statutLabel = new Label();
            statutFilterComboBox = new ComboBox();
            niveauLabel = new Label();
            niveauFilterComboBox = new ComboBox();
            dateDebutLabel = new Label();
            dateDebutPicker = new DateTimePicker();
            dateFinLabel = new Label();
            dateFinPicker = new DateTimePicker();
            searchLabel = new Label();
            searchTextBox = new TextBox();
            applyFilterButton = new Button();
            dataGridView = new DataGridView();
            statusStrip = new StatusStrip();
            countLabel = new ToolStripStatusLabel();
            toolStrip.SuspendLayout();
            filterPanel.SuspendLayout();
            filterTable.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView).BeginInit();
            statusStrip.SuspendLayout();
            SuspendLayout();
            // 
            // toolStrip
            // 
            toolStrip.GripStyle = ToolStripGripStyle.Hidden;
            toolStrip.Items.AddRange(new ToolStripItem[] { validateButton, sendButton, cancelButton, refreshButton });
            toolStrip.Location = new Point(0, 80);
            toolStrip.Name = "toolStrip";
            toolStrip.Size = new Size(1000, 25);
            toolStrip.TabIndex = 0;
            // 
            // validateButton
            // 
            validateButton.Name = "validateButton";
            validateButton.Size = new Size(46, 22);
            validateButton.Text = "Valider";
            validateButton.Click += ValidateButton_Click;
            // 
            // sendButton
            // 
            sendButton.Name = "sendButton";
            sendButton.Size = new Size(53, 22);
            sendButton.Text = "Envoyer";
            sendButton.Click += SendButton_Click;
            // 
            // cancelButton
            // 
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(53, 22);
            cancelButton.Text = "Annuler";
            cancelButton.Click += CancelButton_Click;
            // 
            // refreshButton
            // 
            refreshButton.Name = "refreshButton";
            refreshButton.Size = new Size(61, 22);
            refreshButton.Text = "Rafraîchir";
            refreshButton.Click += RefreshButton_Click;
            // 
            // filterPanel
            // 
            filterPanel.Controls.Add(filterTable);
            filterPanel.Dock = DockStyle.Top;
            filterPanel.Location = new Point(0, 0);
            filterPanel.Name = "filterPanel";
            filterPanel.Padding = new Padding(10);
            filterPanel.Size = new Size(1000, 80);
            filterPanel.TabIndex = 1;
            // 
            // filterTable
            // 
            filterTable.ColumnCount = 6;
            filterTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.66F));
            filterTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.66F));
            filterTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.66F));
            filterTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.66F));
            filterTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.66F));
            filterTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.66F));
            filterTable.Controls.Add(statutLabel, 0, 0);
            filterTable.Controls.Add(statutFilterComboBox, 1, 0);
            filterTable.Controls.Add(niveauLabel, 2, 0);
            filterTable.Controls.Add(niveauFilterComboBox, 3, 0);
            filterTable.Controls.Add(dateDebutLabel, 0, 1);
            filterTable.Controls.Add(dateDebutPicker, 1, 1);
            filterTable.Controls.Add(dateFinLabel, 2, 1);
            filterTable.Controls.Add(dateFinPicker, 3, 1);
            filterTable.Controls.Add(searchLabel, 4, 0);
            filterTable.Controls.Add(searchTextBox, 5, 0);
            filterTable.Controls.Add(applyFilterButton, 5, 1);
            filterTable.Dock = DockStyle.Fill;
            filterTable.Location = new Point(10, 10);
            filterTable.Name = "filterTable";
            filterTable.RowCount = 2;
            filterTable.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            filterTable.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            filterTable.Size = new Size(980, 60);
            filterTable.TabIndex = 0;
            // 
            // statutLabel
            // 
            statutLabel.Dock = DockStyle.Fill;
            statutLabel.Location = new Point(3, 0);
            statutLabel.Name = "statutLabel";
            statutLabel.Size = new Size(157, 30);
            statutLabel.TabIndex = 0;
            statutLabel.Text = "Statut :";
            statutLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // statutFilterComboBox
            // 
            statutFilterComboBox.Dock = DockStyle.Fill;
            statutFilterComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            statutFilterComboBox.Location = new Point(166, 3);
            statutFilterComboBox.Name = "statutFilterComboBox";
            statutFilterComboBox.Size = new Size(157, 23);
            statutFilterComboBox.TabIndex = 1;
            // 
            // niveauLabel
            // 
            niveauLabel.Dock = DockStyle.Fill;
            niveauLabel.Location = new Point(329, 0);
            niveauLabel.Name = "niveauLabel";
            niveauLabel.Size = new Size(157, 30);
            niveauLabel.TabIndex = 2;
            niveauLabel.Text = "Niveau :";
            niveauLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // niveauFilterComboBox
            // 
            niveauFilterComboBox.Dock = DockStyle.Fill;
            niveauFilterComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            niveauFilterComboBox.Location = new Point(492, 3);
            niveauFilterComboBox.Name = "niveauFilterComboBox";
            niveauFilterComboBox.Size = new Size(157, 23);
            niveauFilterComboBox.TabIndex = 3;
            // 
            // dateDebutLabel
            // 
            dateDebutLabel.Dock = DockStyle.Fill;
            dateDebutLabel.Location = new Point(3, 30);
            dateDebutLabel.Name = "dateDebutLabel";
            dateDebutLabel.Size = new Size(157, 30);
            dateDebutLabel.TabIndex = 4;
            dateDebutLabel.Text = "Date début :";
            dateDebutLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // dateDebutPicker
            // 
            dateDebutPicker.Checked = false;
            dateDebutPicker.Dock = DockStyle.Fill;
            dateDebutPicker.Format = DateTimePickerFormat.Short;
            dateDebutPicker.Location = new Point(166, 33);
            dateDebutPicker.Name = "dateDebutPicker";
            dateDebutPicker.ShowCheckBox = true;
            dateDebutPicker.Size = new Size(157, 23);
            dateDebutPicker.TabIndex = 5;
            // 
            // dateFinLabel
            // 
            dateFinLabel.Dock = DockStyle.Fill;
            dateFinLabel.Location = new Point(329, 30);
            dateFinLabel.Name = "dateFinLabel";
            dateFinLabel.Size = new Size(157, 30);
            dateFinLabel.TabIndex = 6;
            dateFinLabel.Text = "Date fin :";
            dateFinLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // dateFinPicker
            // 
            dateFinPicker.Checked = false;
            dateFinPicker.Dock = DockStyle.Fill;
            dateFinPicker.Format = DateTimePickerFormat.Short;
            dateFinPicker.Location = new Point(492, 33);
            dateFinPicker.Name = "dateFinPicker";
            dateFinPicker.ShowCheckBox = true;
            dateFinPicker.Size = new Size(157, 23);
            dateFinPicker.TabIndex = 7;
            // 
            // searchLabel
            // 
            searchLabel.Dock = DockStyle.Fill;
            searchLabel.Location = new Point(655, 0);
            searchLabel.Name = "searchLabel";
            searchLabel.Size = new Size(157, 30);
            searchLabel.TabIndex = 8;
            searchLabel.Text = "Recherche :";
            searchLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // searchTextBox
            // 
            searchTextBox.Dock = DockStyle.Fill;
            searchTextBox.Location = new Point(818, 3);
            searchTextBox.Name = "searchTextBox";
            searchTextBox.Size = new Size(159, 23);
            searchTextBox.TabIndex = 9;
            searchTextBox.KeyDown += SearchTextBox_KeyDown;
            // 
            // applyFilterButton
            // 
            applyFilterButton.Dock = DockStyle.Fill;
            applyFilterButton.Location = new Point(818, 33);
            applyFilterButton.Name = "applyFilterButton";
            applyFilterButton.Size = new Size(159, 24);
            applyFilterButton.TabIndex = 10;
            applyFilterButton.Text = "Appliquer les filtres";
            applyFilterButton.Click += ApplyFilterButton_Click;
            // 
            // dataGridView
            // 
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView.Dock = DockStyle.Fill;
            dataGridView.Location = new Point(0, 0);
            dataGridView.MultiSelect = false;
            dataGridView.Name = "dataGridView";
            dataGridView.ReadOnly = true;
            dataGridView.RowHeadersVisible = false;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.Size = new Size(1000, 578);
            dataGridView.TabIndex = 2;
            dataGridView.CellDoubleClick += DataGridView_CellDoubleClick;
            // 
            // statusStrip
            // 
            statusStrip.Items.AddRange(new ToolStripItem[] { countLabel });
            statusStrip.Location = new Point(0, 578);
            statusStrip.Name = "statusStrip";
            statusStrip.Size = new Size(1000, 22);
            statusStrip.TabIndex = 3;
            // 
            // countLabel
            // 
            countLabel.Name = "countLabel";
            countLabel.Size = new Size(156, 17);
            countLabel.Text = "Nombre de planifications : 0";
            // 
            // PlanificationRelanceListForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1000, 600);
            Controls.Add(toolStrip);
            Controls.Add(filterPanel);
            Controls.Add(dataGridView);
            Controls.Add(statusStrip);
            Name = "PlanificationRelanceListForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Planification des relances";
            Load += PlanificationRelanceListForm_Load;
            toolStrip.ResumeLayout(false);
            toolStrip.PerformLayout();
            filterPanel.ResumeLayout(false);
            filterTable.ResumeLayout(false);
            filterTable.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView).EndInit();
            statusStrip.ResumeLayout(false);
            statusStrip.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private ToolStrip toolStrip;
        private ToolStripButton validateButton;
        private ToolStripButton sendButton;
        private ToolStripButton cancelButton;
        private ToolStripButton refreshButton;
        private Panel filterPanel;
        private TableLayoutPanel filterTable;
        private Label statutLabel;
        private ComboBox statutFilterComboBox;
        private Label niveauLabel;
        private ComboBox niveauFilterComboBox;
        private Label dateDebutLabel;
        private DateTimePicker dateDebutPicker;
        private Label dateFinLabel;
        private DateTimePicker dateFinPicker;
        private Label searchLabel;
        private TextBox searchTextBox;
        private Button applyFilterButton;
        private DataGridView dataGridView;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel countLabel;
    }
}
