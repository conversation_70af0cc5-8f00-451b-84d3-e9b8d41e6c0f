namespace RecouvreX.WinForms.Forms.Administration
{
    partial class RoleEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            mainPanel = new TableLayoutPanel();
            nomLabel = new Label();
            nomTextBox = new TextBox();
            descriptionLabel = new Label();
            descriptionTextBox = new TextBox();
            buttonsPanel = new FlowLayoutPanel();
            cancelButton = new Button();
            saveButton = new Button();
            noteLabel = new Label();
            mainPanel.SuspendLayout();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.ColumnCount = 2;
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            mainPanel.Controls.Add(nomLabel, 0, 0);
            mainPanel.Controls.Add(nomTextBox, 1, 0);
            mainPanel.Controls.Add(descriptionLabel, 0, 1);
            mainPanel.Controls.Add(descriptionTextBox, 1, 1);
            mainPanel.Controls.Add(buttonsPanel, 1, 2);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Location = new Point(0, 0);
            mainPanel.Name = "mainPanel";
            mainPanel.Padding = new Padding(10);
            mainPanel.RowCount = 3;
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 43F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 107F));
            mainPanel.Size = new Size(400, 169);
            mainPanel.TabIndex = 0;
            // 
            // nomLabel
            // 
            nomLabel.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            nomLabel.Location = new Point(13, 16);
            nomLabel.Name = "nomLabel";
            nomLabel.Size = new Size(108, 23);
            nomLabel.TabIndex = 0;
            nomLabel.Text = "Nom* :";
            // 
            // nomTextBox
            // 
            nomTextBox.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            nomTextBox.Location = new Point(127, 16);
            nomTextBox.Name = "nomTextBox";
            nomTextBox.Size = new Size(260, 23);
            nomTextBox.TabIndex = 1;
            nomTextBox.Validating += NomTextBox_Validating;
            // 
            // descriptionLabel
            // 
            descriptionLabel.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            descriptionLabel.Location = new Point(13, 55);
            descriptionLabel.Name = "descriptionLabel";
            descriptionLabel.Size = new Size(108, 23);
            descriptionLabel.TabIndex = 2;
            descriptionLabel.Text = "Description :";
            // 
            // descriptionTextBox
            // 
            descriptionTextBox.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            descriptionTextBox.Location = new Point(127, 55);
            descriptionTextBox.Multiline = true;
            descriptionTextBox.Name = "descriptionTextBox";
            descriptionTextBox.ScrollBars = ScrollBars.Vertical;
            descriptionTextBox.Size = new Size(260, 23);
            descriptionTextBox.TabIndex = 3;
            // 
            // buttonsPanel
            // 
            buttonsPanel.Anchor = AnchorStyles.Right;
            buttonsPanel.Controls.Add(cancelButton);
            buttonsPanel.Controls.Add(saveButton);
            buttonsPanel.FlowDirection = FlowDirection.RightToLeft;
            buttonsPanel.Location = new Point(127, 121);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.Size = new Size(260, 40);
            buttonsPanel.TabIndex = 4;
            // 
            // cancelButton
            // 
            cancelButton.Location = new Point(157, 3);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(100, 30);
            cancelButton.TabIndex = 0;
            cancelButton.Text = "Annuler";
            cancelButton.Click += CancelButton_Click;
            // 
            // saveButton
            // 
            saveButton.Location = new Point(51, 3);
            saveButton.Name = "saveButton";
            saveButton.Size = new Size(100, 30);
            saveButton.TabIndex = 1;
            saveButton.Text = "Enregistrer";
            saveButton.Click += SaveButton_Click;
            // 
            // noteLabel
            // 
            noteLabel.AutoSize = true;
            noteLabel.ForeColor = Color.Red;
            noteLabel.Location = new Point(10, 261);
            noteLabel.Name = "noteLabel";
            noteLabel.Size = new Size(124, 15);
            noteLabel.TabIndex = 1;
            noteLabel.Text = "* Champs obligatoires";
            // 
            // RoleEditForm
            // 
            ClientSize = new Size(400, 169);
            Controls.Add(mainPanel);
            Controls.Add(noteLabel);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "RoleEditForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Rôle";
            Load += RoleEditForm_Load;
            mainPanel.ResumeLayout(false);
            mainPanel.PerformLayout();
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private TableLayoutPanel mainPanel;
        private Label nomLabel;
        private TextBox nomTextBox;
        private Label descriptionLabel;
        private TextBox descriptionTextBox;
        private FlowLayoutPanel buttonsPanel;
        private Button cancelButton;
        private Button saveButton;
        private Label noteLabel;
    }
}
