using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une variable dynamique pour les modèles de relance
    /// </summary>
    public class VariableDynamique : BaseEntity
    {
        /// <summary>
        /// Nom de la variable (sans les accolades)
        /// </summary>
        public string Nom { get; set; } = string.Empty;

        /// <summary>
        /// Description de la variable
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Exemple de valeur
        /// </summary>
        public string Exemple { get; set; } = string.Empty;

        /// <summary>
        /// Catégorie de la variable (Client, Facture, Paiement, Système)
        /// </summary>
        public string Categorie { get; set; } = string.Empty;

        /// <summary>
        /// Expression à évaluer pour obtenir la valeur
        /// </summary>
        public string Expression { get; set; } = string.Empty;

        /// <summary>
        /// Format à appliquer à la valeur (ex: {0:C} pour monnaie)
        /// </summary>
        public string Format { get; set; } = string.Empty;
    }
}
