namespace RecouvreX.WinForms.Forms.Relances
{
    partial class ModeleRelanceEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            mainPanel = new TableLayoutPanel();
            nomLabel = new Label();
            nomTextBox = new TextBox();
            typeLabel = new Label();
            typeComboBox = new ComboBox();
            descriptionLabel = new Label();
            descriptionTextBox = new TextBox();
            niveauLabel = new Label();
            niveauComboBox = new ComboBox();
            objetLabel = new Label();
            objetTextBox = new TextBox();
            contenuLabel = new Label();
            contentPanel = new TableLayoutPanel();
            contenuTextBox = new TextBox();
            variablesPanel = new Panel();
            variablesLabel = new Label();
            variablesListBox = new ListBox();
            optionsLabel = new Label();
            optionsPanel = new Panel();
            estParDefautCheckBox = new CheckBox();
            estActifCheckBox = new CheckBox();
            buttonsPanel = new FlowLayoutPanel();
            cancelButton = new Button();
            saveButton = new Button();
            previewButton = new Button();
            mainPanel.SuspendLayout();
            contentPanel.SuspendLayout();
            variablesPanel.SuspendLayout();
            optionsPanel.SuspendLayout();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.ColumnCount = 2;
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            mainPanel.Controls.Add(nomLabel, 0, 0);
            mainPanel.Controls.Add(nomTextBox, 1, 0);
            mainPanel.Controls.Add(typeLabel, 0, 1);
            mainPanel.Controls.Add(typeComboBox, 1, 1);
            mainPanel.Controls.Add(descriptionLabel, 0, 2);
            mainPanel.Controls.Add(descriptionTextBox, 1, 2);
            mainPanel.Controls.Add(niveauLabel, 0, 3);
            mainPanel.Controls.Add(niveauComboBox, 1, 3);
            mainPanel.Controls.Add(objetLabel, 0, 4);
            mainPanel.Controls.Add(objetTextBox, 1, 4);
            mainPanel.Controls.Add(contenuLabel, 0, 5);
            mainPanel.Controls.Add(contentPanel, 1, 5);
            mainPanel.Controls.Add(optionsLabel, 0, 6);
            mainPanel.Controls.Add(optionsPanel, 1, 6);
            mainPanel.Controls.Add(buttonsPanel, 1, 7);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Location = new Point(0, 0);
            mainPanel.Name = "mainPanel";
            mainPanel.Padding = new Padding(10);
            mainPanel.RowCount = 9;
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            mainPanel.Size = new Size(800, 600);
            mainPanel.TabIndex = 0;
            // 
            // nomLabel
            // 
            nomLabel.Dock = DockStyle.Fill;
            nomLabel.Location = new Point(13, 10);
            nomLabel.Name = "nomLabel";
            nomLabel.Size = new Size(228, 30);
            nomLabel.TabIndex = 0;
            nomLabel.Text = "Nom :";
            nomLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // nomTextBox
            // 
            nomTextBox.Dock = DockStyle.Fill;
            nomTextBox.Location = new Point(247, 13);
            nomTextBox.Name = "nomTextBox";
            nomTextBox.Size = new Size(540, 23);
            nomTextBox.TabIndex = 1;
            // 
            // typeLabel
            // 
            typeLabel.Dock = DockStyle.Fill;
            typeLabel.Location = new Point(13, 40);
            typeLabel.Name = "typeLabel";
            typeLabel.Size = new Size(228, 30);
            typeLabel.TabIndex = 2;
            typeLabel.Text = "Type :";
            typeLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // typeComboBox
            // 
            typeComboBox.Dock = DockStyle.Fill;
            typeComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            typeComboBox.Location = new Point(247, 43);
            typeComboBox.Name = "typeComboBox";
            typeComboBox.Size = new Size(540, 23);
            typeComboBox.TabIndex = 3;
            typeComboBox.SelectedIndexChanged += TypeComboBox_SelectedIndexChanged;
            // 
            // descriptionLabel
            // 
            descriptionLabel.Dock = DockStyle.Fill;
            descriptionLabel.Location = new Point(13, 70);
            descriptionLabel.Name = "descriptionLabel";
            descriptionLabel.Size = new Size(228, 60);
            descriptionLabel.TabIndex = 4;
            descriptionLabel.Text = "Description :";
            descriptionLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // descriptionTextBox
            // 
            descriptionTextBox.Dock = DockStyle.Fill;
            descriptionTextBox.Location = new Point(247, 73);
            descriptionTextBox.Multiline = true;
            descriptionTextBox.Name = "descriptionTextBox";
            descriptionTextBox.ScrollBars = ScrollBars.Vertical;
            descriptionTextBox.Size = new Size(540, 54);
            descriptionTextBox.TabIndex = 5;
            // 
            // niveauLabel
            // 
            niveauLabel.Dock = DockStyle.Fill;
            niveauLabel.Location = new Point(13, 130);
            niveauLabel.Name = "niveauLabel";
            niveauLabel.Size = new Size(228, 30);
            niveauLabel.TabIndex = 6;
            niveauLabel.Text = "Niveau de fermeté :";
            niveauLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // niveauComboBox
            // 
            niveauComboBox.Dock = DockStyle.Fill;
            niveauComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            niveauComboBox.Location = new Point(247, 133);
            niveauComboBox.Name = "niveauComboBox";
            niveauComboBox.Size = new Size(540, 23);
            niveauComboBox.TabIndex = 7;
            // 
            // objetLabel
            // 
            objetLabel.Dock = DockStyle.Fill;
            objetLabel.Location = new Point(13, 160);
            objetLabel.Name = "objetLabel";
            objetLabel.Size = new Size(228, 30);
            objetLabel.TabIndex = 8;
            objetLabel.Text = "Objet :";
            objetLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // objetTextBox
            // 
            objetTextBox.Dock = DockStyle.Fill;
            objetTextBox.Location = new Point(247, 163);
            objetTextBox.Name = "objetTextBox";
            objetTextBox.Size = new Size(540, 23);
            objetTextBox.TabIndex = 9;
            // 
            // contenuLabel
            // 
            contenuLabel.Dock = DockStyle.Fill;
            contenuLabel.Location = new Point(13, 190);
            contenuLabel.Name = "contenuLabel";
            contenuLabel.Size = new Size(228, 310);
            contenuLabel.TabIndex = 10;
            contenuLabel.Text = "Contenu :";
            contenuLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // contentPanel
            // 
            contentPanel.ColumnCount = 2;
            contentPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            contentPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            contentPanel.Controls.Add(contenuTextBox, 0, 0);
            contentPanel.Controls.Add(variablesPanel, 1, 0);
            contentPanel.Dock = DockStyle.Fill;
            contentPanel.Location = new Point(247, 193);
            contentPanel.Name = "contentPanel";
            contentPanel.RowCount = 1;
            contentPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            contentPanel.Size = new Size(540, 304);
            contentPanel.TabIndex = 11;
            // 
            // contenuTextBox
            // 
            contenuTextBox.Dock = DockStyle.Fill;
            contenuTextBox.Location = new Point(3, 3);
            contenuTextBox.Multiline = true;
            contenuTextBox.Name = "contenuTextBox";
            contenuTextBox.ScrollBars = ScrollBars.Both;
            contenuTextBox.Size = new Size(372, 298);
            contenuTextBox.TabIndex = 0;
            // 
            // variablesPanel
            // 
            variablesPanel.Controls.Add(variablesLabel);
            variablesPanel.Controls.Add(variablesListBox);
            variablesPanel.Dock = DockStyle.Fill;
            variablesPanel.Location = new Point(381, 3);
            variablesPanel.Name = "variablesPanel";
            variablesPanel.Size = new Size(156, 298);
            variablesPanel.TabIndex = 1;
            // 
            // variablesLabel
            // 
            variablesLabel.Dock = DockStyle.Top;
            variablesLabel.Location = new Point(0, 0);
            variablesLabel.Name = "variablesLabel";
            variablesLabel.Size = new Size(156, 20);
            variablesLabel.TabIndex = 0;
            variablesLabel.Text = "Variables disponibles :";
            // 
            // variablesListBox
            // 
            variablesListBox.Dock = DockStyle.Fill;
            variablesListBox.ItemHeight = 15;
            variablesListBox.Location = new Point(0, 0);
            variablesListBox.Name = "variablesListBox";
            variablesListBox.Size = new Size(156, 298);
            variablesListBox.TabIndex = 1;
            variablesListBox.DoubleClick += VariablesListBox_DoubleClick;
            // 
            // optionsLabel
            // 
            optionsLabel.Dock = DockStyle.Fill;
            optionsLabel.Location = new Point(13, 500);
            optionsLabel.Name = "optionsLabel";
            optionsLabel.Size = new Size(228, 30);
            optionsLabel.TabIndex = 12;
            optionsLabel.Text = "Options :";
            optionsLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // optionsPanel
            // 
            optionsPanel.Controls.Add(estParDefautCheckBox);
            optionsPanel.Controls.Add(estActifCheckBox);
            optionsPanel.Dock = DockStyle.Fill;
            optionsPanel.Location = new Point(247, 503);
            optionsPanel.Name = "optionsPanel";
            optionsPanel.Size = new Size(540, 24);
            optionsPanel.TabIndex = 13;
            // 
            // estParDefautCheckBox
            // 
            estParDefautCheckBox.AutoSize = true;
            estParDefautCheckBox.Location = new Point(0, 5);
            estParDefautCheckBox.Name = "estParDefautCheckBox";
            estParDefautCheckBox.Size = new Size(205, 19);
            estParDefautCheckBox.TabIndex = 0;
            estParDefautCheckBox.Text = "Définir comme modèle par défaut";
            // 
            // estActifCheckBox
            // 
            estActifCheckBox.AutoSize = true;
            estActifCheckBox.Location = new Point(250, 5);
            estActifCheckBox.Name = "estActifCheckBox";
            estActifCheckBox.Size = new Size(51, 19);
            estActifCheckBox.TabIndex = 1;
            estActifCheckBox.Text = "Actif";
            // 
            // buttonsPanel
            // 
            buttonsPanel.Controls.Add(cancelButton);
            buttonsPanel.Controls.Add(saveButton);
            buttonsPanel.Controls.Add(previewButton);
            buttonsPanel.Dock = DockStyle.Fill;
            buttonsPanel.FlowDirection = FlowDirection.RightToLeft;
            buttonsPanel.Location = new Point(247, 533);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.Size = new Size(540, 34);
            buttonsPanel.TabIndex = 14;
            // 
            // cancelButton
            // 
            cancelButton.Location = new Point(437, 3);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(100, 30);
            cancelButton.TabIndex = 0;
            cancelButton.Text = "Annuler";
            cancelButton.Click += CancelButton_Click;
            // 
            // saveButton
            // 
            saveButton.Location = new Point(331, 3);
            saveButton.Name = "saveButton";
            saveButton.Size = new Size(100, 30);
            saveButton.TabIndex = 1;
            saveButton.Text = "Enregistrer";
            saveButton.Click += SaveButton_Click;
            // 
            // previewButton
            // 
            previewButton.Location = new Point(225, 3);
            previewButton.Name = "previewButton";
            previewButton.Size = new Size(100, 30);
            previewButton.TabIndex = 2;
            previewButton.Text = "Aperçu";
            previewButton.Click += PreviewButton_Click;
            // 
            // ModeleRelanceEditForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(800, 600);
            Controls.Add(mainPanel);
            Name = "ModeleRelanceEditForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Modèle de relance";
            Load += ModeleRelanceEditForm_Load;
            mainPanel.ResumeLayout(false);
            mainPanel.PerformLayout();
            contentPanel.ResumeLayout(false);
            contentPanel.PerformLayout();
            variablesPanel.ResumeLayout(false);
            optionsPanel.ResumeLayout(false);
            optionsPanel.PerformLayout();
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel mainPanel;
        private Label nomLabel;
        private TextBox nomTextBox;
        private Label typeLabel;
        private ComboBox typeComboBox;
        private Label descriptionLabel;
        private TextBox descriptionTextBox;
        private Label niveauLabel;
        private ComboBox niveauComboBox;
        private Label objetLabel;
        private TextBox objetTextBox;
        private Label contenuLabel;
        private TableLayoutPanel contentPanel;
        private TextBox contenuTextBox;
        private Panel variablesPanel;
        private Label variablesLabel;
        private ListBox variablesListBox;
        private Label optionsLabel;
        private Panel optionsPanel;
        private CheckBox estParDefautCheckBox;
        private CheckBox estActifCheckBox;
        private FlowLayoutPanel buttonsPanel;
        private Button cancelButton;
        private Button saveButton;
        private Button previewButton;
    }
}
