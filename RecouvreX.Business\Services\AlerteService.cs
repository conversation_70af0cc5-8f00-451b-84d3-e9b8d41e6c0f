using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des alertes
    /// </summary>
    public class AlerteService : IAlerteService
    {
        private readonly IAlerteRepository _alerteRepository;
        private readonly IFactureRepository _factureRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        public AlerteService(
            IAlerteRepository alerteRepository,
            IFactureRepository factureRepository,
            IClientRepository clientRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _alerteRepository = alerteRepository ?? throw new ArgumentNullException(nameof(alerteRepository));
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère toutes les alertes
        /// </summary>
        /// <returns>Liste des alertes</returns>
        public async Task<IEnumerable<Alerte>> GetAllAsync()
        {
            return await _alerteRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère une alerte par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'alerte</param>
        /// <returns>Alerte trouvée ou null</returns>
        public async Task<Alerte> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _alerteRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère les alertes d'un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des alertes de l'utilisateur</returns>
        public async Task<IEnumerable<Alerte>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            if (utilisateurId <= 0)
                return Enumerable.Empty<Alerte>();

            return await _alerteRepository.GetByUtilisateurIdAsync(utilisateurId);
        }

        /// <summary>
        /// Crée une nouvelle alerte
        /// </summary>
        /// <param name="alerte">Alerte à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Alerte créée avec son identifiant généré</returns>
        public async Task<Alerte> CreateAsync(Alerte alerte, int creePar)
        {
            if (alerte == null)
                throw new ArgumentNullException(nameof(alerte));

            if (creePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que l'utilisateur existe
            var utilisateur = await _utilisateurRepository.GetByIdAsync(alerte.UtilisateurId);
            if (utilisateur == null)
                throw new InvalidOperationException($"L'utilisateur avec l'ID {alerte.UtilisateurId} n'existe pas");

            // Créer l'alerte
            var createdAlerte = await _alerteRepository.AddAsync(alerte, creePar);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "Alerte",
                EntiteId = createdAlerte.Id,
                TypeAction = "Création",
                UtilisateurId = creePar,
                DateAction = DateTime.Now,
                Description = $"Création de l'alerte '{createdAlerte.Nom:s}'",
                DonneesAvant = null,
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdAlerte.Id,
                    createdAlerte.Nom,
                    createdAlerte.Description,
                    createdAlerte.Type,
                    createdAlerte.Condition,
                    createdAlerte.Seuil,
                    createdAlerte.EstActive,
                    createdAlerte.EnvoyerEmail,
                    createdAlerte.UtilisateurId,
                    UtilisateurNom = utilisateur.NomUtilisateur,
                    createdAlerte.FrequenceNotificationHeures
                })
            });

            return createdAlerte;
        }

        /// <summary>
        /// Met à jour une alerte existante
        /// </summary>
        /// <param name="alerte">Alerte à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Alerte mise à jour</returns>
        public async Task<Alerte> UpdateAsync(Alerte alerte, int modifiePar)
        {
            if (alerte == null)
                throw new ArgumentNullException(nameof(alerte));

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que l'alerte existe
            var existingAlerte = await _alerteRepository.GetByIdAsync(alerte.Id);
            if (existingAlerte == null)
                throw new InvalidOperationException($"L'alerte avec l'ID {alerte.Id} n'existe pas");

            // Vérifier que l'utilisateur existe
            var utilisateur = await _utilisateurRepository.GetByIdAsync(alerte.UtilisateurId);
            if (utilisateur == null)
                throw new InvalidOperationException($"L'utilisateur avec l'ID {alerte.UtilisateurId} n'existe pas");

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "Alerte",
                EntiteId = alerte.Id,
                TypeAction = "Modification",
                UtilisateurId = modifiePar,
                DateAction = DateTime.Now,
                Description = $"Modification de l'alerte '{alerte.Nom:s}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingAlerte.Id,
                    existingAlerte.Nom,
                    existingAlerte.Description,
                    existingAlerte.Type,
                    existingAlerte.Condition,
                    existingAlerte.Seuil,
                    existingAlerte.EstActive,
                    existingAlerte.EnvoyerEmail,
                    existingAlerte.UtilisateurId,
                    UtilisateurNom = utilisateur.NomUtilisateur,
                    existingAlerte.FrequenceNotificationHeures
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    alerte.Id,
                    alerte.Nom,
                    alerte.Description,
                    alerte.Type,
                    alerte.Condition,
                    alerte.Seuil,
                    alerte.EstActive,
                    alerte.EnvoyerEmail,
                    alerte.UtilisateurId,
                    UtilisateurNom = utilisateur.NomUtilisateur,
                    alerte.FrequenceNotificationHeures
                })
            });

            // Mettre à jour l'alerte
            return await _alerteRepository.UpdateAsync(alerte, modifiePar);
        }

        /// <summary>
        /// Supprime une alerte
        /// </summary>
        /// <param name="id">Identifiant de l'alerte à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant de l'alerte ne peut pas être négatif ou nul");

            if (supprimePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que l'alerte existe
            var alerte = await _alerteRepository.GetByIdAsync(id);
            if (alerte == null)
                return false;

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "Alerte",
                EntiteId = id,
                TypeAction = "Suppression",
                UtilisateurId = supprimePar,
                DateAction = DateTime.Now,
                Description = $"Suppression de l'alerte '{alerte.Nom:s}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    alerte.Id,
                    alerte.Nom,
                    alerte.Description,
                    alerte.Type,
                    alerte.Condition,
                    alerte.Seuil,
                    alerte.EstActive,
                    alerte.EnvoyerEmail,
                    alerte.UtilisateurId,
                    alerte.FrequenceNotificationHeures
                }),
                DonneesApres = null
            });

            // Supprimer l'alerte
            return await _alerteRepository.DeleteAsync(id, supprimePar);
        }

        /// <summary>
        /// Active ou désactive une alerte
        /// </summary>
        /// <param name="id">Identifiant de l'alerte</param>
        /// <param name="estActive">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> SetActiveStatusAsync(int id, bool estActive, int modifiePar)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant de l'alerte ne peut pas être négatif ou nul");

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que l'alerte existe
            var alerte = await _alerteRepository.GetByIdAsync(id);
            if (alerte == null)
                return false;

            // Mettre à jour l'état d'activation
            alerte.EstActive = estActive;

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "Alerte",
                EntiteId = id,
                TypeAction = estActive ? "Activation" : "Désactivation",
                UtilisateurId = modifiePar,
                DateAction = DateTime.Now,
                Description = $"{(estActive ? "Activation" : "Désactivation")} de l'alerte '{alerte.Nom:s}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new { EstActive = !estActive }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new { EstActive = estActive })
            });

            // Mettre à jour l'alerte
            await _alerteRepository.UpdateAsync(alerte, modifiePar);
            return true;
        }

        /// <summary>
        /// Vérifie les alertes et retourne celles qui sont déclenchées
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des alertes déclenchées</returns>
        public async Task<IEnumerable<Alerte>> CheckAlertesAsync(int utilisateurId)
        {
            if (utilisateurId <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Récupérer les alertes actives de l'utilisateur
            var alertes = await _alerteRepository.GetByUtilisateurIdAsync(utilisateurId);
            alertes = alertes.Where(a => a.EstActive);

            // Récupérer les données nécessaires pour vérifier les alertes
            var factures = await _factureRepository.GetAllAsync();
            var facturesEnRetard = factures.Where(f => f.Statut == StatutFacture.EnRetard);

            var alertesDeclenchees = new List<Alerte>();

            foreach (var alerte in alertes)
            {
                // Vérifier si l'alerte a déjà été notifiée récemment
                if (alerte.DerniereNotification.HasValue)
                {
                    var heuresDepuisDerniereNotification = (DateTime.Now - alerte.DerniereNotification.Value).TotalHours;
                    if (heuresDepuisDerniereNotification < alerte.FrequenceNotificationHeures)
                    {
                        // Ne pas déclencher l'alerte si la fréquence minimale n'est pas respectée
                        continue;
                    }
                }

                bool estDeclenchee = false;

                switch (alerte.Type)
                {
                    case TypeAlerte.MontantFacturesEnRetard:
                        decimal montantTotal = facturesEnRetard.Sum(f => f.MontantRestant);
                        estDeclenchee = VerifierCondition(montantTotal, alerte.Condition, alerte.Seuil);
                        break;

                    case TypeAlerte.NombreFacturesEnRetard:
                        int nombreFactures = facturesEnRetard.Count();
                        estDeclenchee = VerifierCondition(nombreFactures, alerte.Condition, alerte.Seuil);
                        break;

                    case TypeAlerte.AncienneteCreances:
                        double ageMoyen = facturesEnRetard.Any()
                            ? facturesEnRetard.Average(f => (DateTime.Now - f.DateEcheance).TotalDays)
                            : 0;
                        estDeclenchee = VerifierCondition((decimal)ageMoyen, alerte.Condition, alerte.Seuil);
                        break;

                    case TypeAlerte.TauxRecouvrement:
                        decimal montantTotalTTC = factures.Sum(f => f.MontantTTC);
                        decimal montantTotalPaye = factures.Sum(f => f.MontantPaye);
                        decimal tauxRecouvrement = montantTotalTTC > 0 ? (montantTotalPaye / montantTotalTTC) * 100 : 0;
                        estDeclenchee = VerifierCondition(tauxRecouvrement, alerte.Condition, alerte.Seuil);
                        break;

                    case TypeAlerte.MontantCreancesClient:
                        // Cette alerte nécessite un client spécifique, à implémenter plus tard
                        break;

                    case TypeAlerte.FactureEcheance:
                        // Vérifier les factures qui arrivent à échéance dans X jours
                        int joursAvantEcheance = (int)alerte.Seuil;
                        var facturesProchesEcheance = factures.Where(f =>
                            f.Statut != StatutFacture.Payee &&
                            f.Statut != StatutFacture.Annulee &&
                            (f.DateEcheance - DateTime.Now).TotalDays <= joursAvantEcheance &&
                            (f.DateEcheance - DateTime.Now).TotalDays > 0);
                        estDeclenchee = facturesProchesEcheance.Any();
                        break;
                }

                if (estDeclenchee)
                {
                    alertesDeclenchees.Add(alerte);
                }
            }

            return alertesDeclenchees;
        }

        /// <summary>
        /// Enregistre une notification pour une alerte
        /// </summary>
        /// <param name="alerteId">Identifiant de l'alerte</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>True si l'enregistrement a réussi, sinon False</returns>
        public async Task<bool> RecordNotificationAsync(int alerteId, int utilisateurId)
        {
            if (alerteId <= 0)
                throw new ArgumentException("L'identifiant de l'alerte ne peut pas être négatif ou nul");

            if (utilisateurId <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            return await _alerteRepository.UpdateDerniereNotificationAsync(alerteId, utilisateurId);
        }

        /// <summary>
        /// Vérifie si une valeur respecte une condition par rapport à un seuil
        /// </summary>
        /// <param name="valeur">Valeur à vérifier</param>
        /// <param name="condition">Condition à vérifier</param>
        /// <param name="seuil">Seuil de comparaison</param>
        /// <returns>True si la condition est respectée, sinon False</returns>
        private bool VerifierCondition(decimal valeur, string condition, decimal seuil)
        {
            switch (condition)
            {
                case ConditionAlerte.Superieur:
                    return valeur > seuil;
                case ConditionAlerte.Inferieur:
                    return valeur < seuil;
                case ConditionAlerte.Egal:
                    return valeur == seuil;
                case ConditionAlerte.SuperieurOuEgal:
                    return valeur >= seuil;
                case ConditionAlerte.InferieurOuEgal:
                    return valeur <= seuil;
                default:
                    return false;
            }
        }
    }
}
