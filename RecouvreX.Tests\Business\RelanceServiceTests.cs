using Moq;
using RecouvreX.Business.Interfaces;
using RecouvreX.Business.Services;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Xunit;

namespace RecouvreX.Tests.Business
{
    public class RelanceServiceTests
    {
        private readonly Mock<IRelanceRepository> _mockRelanceRepository;
        private readonly Mock<IFactureRepository> _mockFactureRepository;
        private readonly Mock<IAuditService> _mockAuditService;
        private readonly IRelanceService _relanceService;

        public RelanceServiceTests()
        {
            _mockRelanceRepository = new Mock<IRelanceRepository>();
            _mockFactureRepository = new Mock<IFactureRepository>();
            _mockAuditService = new Mock<IAuditService>();
            _relanceService = new RelanceService(
                _mockRelanceRepository.Object,
                _mockFactureRepository.Object,
                _mockAuditService.Object);
        }

        [Fact]
        public async Task GetAllAsync_ShouldReturnAllRelances()
        {
            // Arrange
            var expectedRelances = new List<Relance>
            {
                new Relance { Id = 1, FactureId = 1, Type = "Email", Niveau = 1 },
                new Relance { Id = 2, FactureId = 2, Type = "Téléphone", Niveau = 1 },
                new Relance { Id = 3, FactureId = 1, Type = "Courrier", Niveau = 2 }
            };

            _mockRelanceRepository.Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(expectedRelances);

            // Act
            var result = await _relanceService.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedRelances.Count, result.Count());
            Assert.Equal(expectedRelances, result);
            _mockRelanceRepository.Verify(repo => repo.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithValidId_ShouldReturnRelance()
        {
            // Arrange
            int relanceId = 1;
            var expectedRelance = new Relance { Id = relanceId, FactureId = 1, Type = "Email", Niveau = 1 };

            _mockRelanceRepository.Setup(repo => repo.GetByIdAsync(relanceId))
                .ReturnsAsync(expectedRelance);

            // Act
            var result = await _relanceService.GetByIdAsync(relanceId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedRelance, result);
            _mockRelanceRepository.Verify(repo => repo.GetByIdAsync(relanceId), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            int relanceId = 999;

            _mockRelanceRepository.Setup(repo => repo.GetByIdAsync(relanceId))
                .ReturnsAsync((Relance)null);

            // Act
            var result = await _relanceService.GetByIdAsync(relanceId);

            // Assert
            Assert.Null(result);
            _mockRelanceRepository.Verify(repo => repo.GetByIdAsync(relanceId), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_ShouldCreateRelanceAndReturnId()
        {
            // Arrange
            int userId = 1;
            int expectedRelanceId = 1;
            var relance = new Relance
            {
                FactureId = 1,
                Type = "Email",
                DateRelance = DateTime.Today,
                Niveau = 1,
                Statut = "Planifiée",
                Contenu = "Contenu de la relance",
                DateProchaineRelance = DateTime.Today.AddDays(7)
            };

            _mockRelanceRepository.Setup(repo => repo.CreateAsync(It.IsAny<Relance>()))
                .ReturnsAsync(expectedRelanceId);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _relanceService.CreateAsync(relance, userId);

            // Assert
            Assert.Equal(expectedRelanceId, result);
            Assert.Equal(expectedRelanceId, relance.Id);
            Assert.Equal(userId, relance.UtilisateurId);
            _mockRelanceRepository.Verify(repo => repo.CreateAsync(relance), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Relance",
                It.IsAny<string>(),
                expectedRelanceId,
                userId), Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_ShouldUpdateRelanceAndReturnTrue()
        {
            // Arrange
            int userId = 1;
            var relance = new Relance
            {
                Id = 1,
                FactureId = 1,
                Type = "Email",
                DateRelance = DateTime.Today,
                Niveau = 1,
                Statut = "Terminée",
                Contenu = "Contenu de la relance modifié",
                UtilisateurId = 1,
                DateProchaineRelance = DateTime.Today.AddDays(14)
            };

            _mockRelanceRepository.Setup(repo => repo.UpdateAsync(relance))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _relanceService.UpdateAsync(relance, userId);

            // Assert
            Assert.True(result);
            _mockRelanceRepository.Verify(repo => repo.UpdateAsync(relance), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Relance",
                It.IsAny<string>(),
                relance.Id,
                userId), Times.Once);
        }

        [Fact]
        public async Task DeleteAsync_ShouldDeleteRelanceAndReturnTrue()
        {
            // Arrange
            int relanceId = 1;
            int userId = 1;

            _mockRelanceRepository.Setup(repo => repo.DeleteAsync(relanceId))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _relanceService.DeleteAsync(relanceId, userId);

            // Assert
            Assert.True(result);
            _mockRelanceRepository.Verify(repo => repo.DeleteAsync(relanceId), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Relance",
                It.IsAny<string>(),
                relanceId,
                userId), Times.Once);
        }

        [Fact]
        public async Task GetByFactureIdAsync_ShouldReturnRelancesForFacture()
        {
            // Arrange
            int factureId = 1;
            var expectedRelances = new List<Relance>
            {
                new Relance { Id = 1, FactureId = factureId, Type = "Email", Niveau = 1 },
                new Relance { Id = 3, FactureId = factureId, Type = "Courrier", Niveau = 2 }
            };

            _mockRelanceRepository.Setup(repo => repo.GetByFactureIdAsync(factureId))
                .ReturnsAsync(expectedRelances);

            // Act
            var result = await _relanceService.GetByFactureIdAsync(factureId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedRelances.Count, result.Count());
            Assert.Equal(expectedRelances, result);
            _mockRelanceRepository.Verify(repo => repo.GetByFactureIdAsync(factureId), Times.Once);
        }

        [Fact]
        public async Task GetByStatutAsync_ShouldReturnRelancesWithSpecifiedStatut()
        {
            // Arrange
            string statut = "Planifiée";
            var expectedRelances = new List<Relance>
            {
                new Relance { Id = 1, FactureId = 1, Type = "Email", Niveau = 1, Statut = statut },
                new Relance { Id = 2, FactureId = 2, Type = "Téléphone", Niveau = 1, Statut = statut }
            };

            _mockRelanceRepository.Setup(repo => repo.GetByStatutAsync(statut))
                .ReturnsAsync(expectedRelances);

            // Act
            var result = await _relanceService.GetByStatutAsync(statut);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedRelances.Count, result.Count());
            Assert.Equal(expectedRelances, result);
            _mockRelanceRepository.Verify(repo => repo.GetByStatutAsync(statut), Times.Once);
        }

        [Fact]
        public async Task GetPlannedForDateAsync_ShouldReturnRelancesPlannedForDate()
        {
            // Arrange
            DateTime date = DateTime.Today;
            var expectedRelances = new List<Relance>
            {
                new Relance { Id = 1, FactureId = 1, Type = "Email", Niveau = 1, DateRelance = date },
                new Relance { Id = 2, FactureId = 2, Type = "Téléphone", Niveau = 1, DateRelance = date }
            };

            _mockRelanceRepository.Setup(repo => repo.GetPlannedForDateAsync(date))
                .ReturnsAsync(expectedRelances);

            // Act
            var result = await _relanceService.GetPlannedForDateAsync(date);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedRelances.Count, result.Count());
            Assert.Equal(expectedRelances, result);
            _mockRelanceRepository.Verify(repo => repo.GetPlannedForDateAsync(date), Times.Once);
        }

        [Fact]
        public async Task UpdateStatutAsync_ShouldUpdateRelanceStatutAndReturnTrue()
        {
            // Arrange
            int relanceId = 1;
            string nouveauStatut = "Terminée";
            int userId = 1;

            _mockRelanceRepository.Setup(repo => repo.UpdateStatutAsync(relanceId, nouveauStatut))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _relanceService.UpdateStatutAsync(relanceId, nouveauStatut, userId);

            // Assert
            Assert.True(result);
            _mockRelanceRepository.Verify(repo => repo.UpdateStatutAsync(relanceId, nouveauStatut), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Relance",
                It.IsAny<string>(),
                relanceId,
                userId), Times.Once);
        }

        [Fact]
        public async Task GenererRelancesAutomatiquesAsync_ShouldGenerateRelancesAndReturnCount()
        {
            // Arrange
            int userId = 1;
            var overdueInvoices = new List<Facture>
            {
                new Facture { Id = 1, Numero = "FAC001", ClientId = 1, MontantTTC = 1000, DateEcheance = DateTime.Today.AddDays(-10), Statut = "En retard" },
                new Facture { Id = 2, Numero = "FAC002", ClientId = 2, MontantTTC = 2000, DateEcheance = DateTime.Today.AddDays(-5), Statut = "En retard" }
            };

            _mockFactureRepository.Setup(repo => repo.GetOverdueInvoicesAsync())
                .ReturnsAsync(overdueInvoices);

            _mockRelanceRepository.Setup(repo => repo.GetByFactureIdAsync(It.IsAny<int>()))
                .ReturnsAsync(new List<Relance>());

            _mockRelanceRepository.Setup(repo => repo.CreateAsync(It.IsAny<Relance>()))
                .ReturnsAsync(1);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _relanceService.GenererRelancesAutomatiquesAsync(userId);

            // Assert
            Assert.Equal(overdueInvoices.Count, result);
            _mockFactureRepository.Verify(repo => repo.GetOverdueInvoicesAsync(), Times.Once);
            _mockRelanceRepository.Verify(repo => repo.GetByFactureIdAsync(It.IsAny<int>()), Times.Exactly(overdueInvoices.Count));
            _mockRelanceRepository.Verify(repo => repo.CreateAsync(It.IsAny<Relance>()), Times.Exactly(overdueInvoices.Count));
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Relance",
                It.IsAny<string>(),
                It.IsAny<int>(),
                userId), Times.Exactly(overdueInvoices.Count));
        }

        [Fact]
        public async Task GenererRelancesAutomatiquesAsync_WithExistingRelances_ShouldSkipAndReturnZero()
        {
            // Arrange
            int userId = 1;
            var overdueInvoices = new List<Facture>
            {
                new Facture { Id = 1, Numero = "FAC001", ClientId = 1, MontantTTC = 1000, DateEcheance = DateTime.Today.AddDays(-10), Statut = "En retard" }
            };

            var existingRelances = new List<Relance>
            {
                new Relance { Id = 1, FactureId = 1, Type = "Email", Niveau = 1, DateRelance = DateTime.Today.AddDays(-5) }
            };

            _mockFactureRepository.Setup(repo => repo.GetOverdueInvoicesAsync())
                .ReturnsAsync(overdueInvoices);

            _mockRelanceRepository.Setup(repo => repo.GetByFactureIdAsync(1))
                .ReturnsAsync(existingRelances);

            // Act
            var result = await _relanceService.GenererRelancesAutomatiquesAsync(userId);

            // Assert
            Assert.Equal(0, result);
            _mockFactureRepository.Verify(repo => repo.GetOverdueInvoicesAsync(), Times.Once);
            _mockRelanceRepository.Verify(repo => repo.GetByFactureIdAsync(1), Times.Once);
            _mockRelanceRepository.Verify(repo => repo.CreateAsync(It.IsAny<Relance>()), Times.Never);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()), Times.Never);
        }
    }
}
