using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une notification liée à un litige
    /// </summary>
    public class NotificationLitige : BaseEntity
    {
        /// <summary>
        /// Identifiant du litige associé
        /// </summary>
        public int LitigeId { get; set; }

        /// <summary>
        /// Litige associé (navigation property)
        /// </summary>
        public Litige Litige { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur destinataire
        /// </summary>
        public int UtilisateurId { get; set; }

        /// <summary>
        /// Utilisateur destinataire (navigation property)
        /// </summary>
        public Utilisateur Utilisateur { get; set; }

        /// <summary>
        /// Type de notification (Escalade, EtapeChangee, EcheanceProche, EcheanceDepassee)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Message de la notification
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Date de la notification
        /// </summary>
        public DateTime DateNotification { get; set; }

        /// <summary>
        /// Indique si la notification a été lue
        /// </summary>
        public bool EstLue { get; set; }

        /// <summary>
        /// Date de lecture de la notification
        /// </summary>
        public DateTime? DateLecture { get; set; }
    }
}
