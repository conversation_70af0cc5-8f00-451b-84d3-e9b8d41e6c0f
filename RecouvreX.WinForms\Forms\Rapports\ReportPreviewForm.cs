using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Rapports
{
    public partial class ReportPreviewForm : Form
    {
        private readonly string _reportData;
        private DataTable _dataTable = new DataTable();

        public ReportPreviewForm(string reportData)
        {
            _reportData = reportData ?? throw new ArgumentNullException(nameof(reportData));

            InitializeComponent();
            ParseReportData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form
            this.Text = "Aperçu du rapport";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimizeBox = true;
            this.MaximizeBox = true;
            this.FormBorderStyle = FormBorderStyle.Sizable;

            // Main panel
            TableLayoutPanel mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 1,
                RowCount = 3,
                RowStyles = {
                    new RowStyle(SizeType.Absolute, 40),
                    new RowStyle(SizeType.Percent, 100),
                    new RowStyle(SizeType.Absolute, 40)
                }
            };
            this.Controls.Add(mainPanel);

            // Title label
            Label titleLabel = new Label
            {
                Name = "titleLabel",
                Text = "Aperçu du rapport",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft
            };
            mainPanel.Controls.Add(titleLabel, 0, 0);

            // Tab control
            TabControl tabControl = new TabControl
            {
                Name = "tabControl",
                Dock = DockStyle.Fill
            };
            mainPanel.Controls.Add(tabControl, 0, 1);

            // Données tab
            TabPage donneesTab = new TabPage("Données");
            tabControl.TabPages.Add(donneesTab);

            DataGridView dataGridView = new DataGridView
            {
                Name = "dataGridView",
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                BackgroundColor = SystemColors.Window,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };
            dataGridView.DataSource = _dataTable;
            donneesTab.Controls.Add(dataGridView);

            // JSON tab
            TabPage jsonTab = new TabPage("JSON");
            tabControl.TabPages.Add(jsonTab);

            TextBox jsonTextBox = new TextBox
            {
                Name = "jsonTextBox",
                Dock = DockStyle.Fill,
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Both,
                Font = new Font("Consolas", 9)
            };
            jsonTab.Controls.Add(jsonTextBox);

            // Buttons panel
            TableLayoutPanel buttonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 100),
                    new ColumnStyle(SizeType.Absolute, 120),
                    new ColumnStyle(SizeType.Absolute, 120)
                }
            };
            mainPanel.Controls.Add(buttonsPanel, 0, 2);

            Button exportButton = new Button
            {
                Name = "exportButton",
                Text = "Exporter",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            exportButton.Click += ExportButton_Click;
            buttonsPanel.Controls.Add(exportButton, 1, 0);

            Button closeButton = new Button
            {
                Name = "closeButton",
                Text = "Fermer",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            closeButton.Click += CloseButton_Click;
            buttonsPanel.Controls.Add(closeButton, 2, 0);

            this.ResumeLayout(false);
        }

        private void ParseReportData()
        {
            try
            {
                // Afficher les données JSON brutes
                var jsonTextBox = Controls.Find("jsonTextBox", true).FirstOrDefault() as TextBox;
                if (jsonTextBox != null)
                {
                    // Formater le JSON pour une meilleure lisibilité
                    var parsedJson = JToken.Parse(_reportData);
                    jsonTextBox.Text = parsedJson.ToString(Formatting.Indented);
                }

                // Extraire les données pour le DataGridView
                var jsonObject = JsonConvert.DeserializeObject<dynamic>(_reportData);
                if (jsonObject != null)
                {
                    // Mettre à jour le titre
                    var titleLabel = Controls.Find("titleLabel", true).FirstOrDefault() as Label;
                    if (titleLabel != null && jsonObject.Title != null)
                    {
                        titleLabel.Text = jsonObject.Title.ToString();
                    }

                    // Créer une DataTable à partir des données
                    CreateDataTableFromJson(jsonObject);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'analyse des données du rapport");
                MessageBox.Show($"Erreur lors de l'analyse des données du rapport : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CreateDataTableFromJson(dynamic jsonObject)
        {
            try
            {
                _dataTable = new DataTable();

                // Rechercher les données principales (peut varier selon le type de rapport)
                dynamic dataArray = null;

                // Essayer différentes propriétés qui pourraient contenir les données
                if (jsonObject.Invoices != null)
                    dataArray = jsonObject.Invoices;
                else if (jsonObject.Payments != null)
                    dataArray = jsonObject.Payments;
                else if (jsonObject.Clients != null)
                    dataArray = jsonObject.Clients;
                else if (jsonObject.Reminders != null)
                    dataArray = jsonObject.Reminders;
                else if (jsonObject.Data != null)
                    dataArray = jsonObject.Data;
                else if (jsonObject.Items != null)
                    dataArray = jsonObject.Items;

                if (dataArray == null || !(dataArray is JArray) || !((JArray)dataArray).Any())
                {
                    MessageBox.Show("Aucune donnée n'a été trouvée dans le rapport.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Créer les colonnes à partir du premier élément
                var firstItem = ((JArray)dataArray)[0];
                foreach (JProperty property in firstItem)
                {
                    _dataTable.Columns.Add(property.Name);
                }

                // Ajouter les lignes
                foreach (var item in dataArray)
                {
                    var row = _dataTable.NewRow();
                    foreach (JProperty property in item)
                    {
                        row[property.Name] = property.Value.ToString();
                    }
                    _dataTable.Rows.Add(row);
                }

                // Mettre à jour le DataGridView
                var dataGridView = Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null)
                {
                    dataGridView.DataSource = _dataTable;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la création de la DataTable à partir du JSON");
                MessageBox.Show($"Erreur lors de la création de la table de données : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Demander le format d'export
                string format = "CSV";
                using (var form = new ExportFormatForm())
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        format = form.SelectedFormat;
                    }
                    else
                    {
                        return;
                    }
                }

                // Demander le chemin d'export
                string extension = format.ToLower();
                string filter = $"Fichiers {format} (*.{extension})|*.{extension}";

                using (SaveFileDialog saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Filter = filter;
                    saveFileDialog.DefaultExt = extension;
                    saveFileDialog.AddExtension = true;
                    saveFileDialog.FileName = $"Rapport_{DateTime.Now:yyyyMMdd}.{extension}";

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        // Exporter les données
                        switch (format)
                        {
                            case "CSV":
                                ExportToCsv(saveFileDialog.FileName);
                                break;
                            case "Excel":
                                ExportToExcel(saveFileDialog.FileName);
                                break;
                            case "PDF":
                                ExportToPdf(saveFileDialog.FileName);
                                break;
                            case "JSON":
                                File.WriteAllText(saveFileDialog.FileName, _reportData);
                                break;
                        }

                        MessageBox.Show($"Le rapport a été exporté avec succès vers {saveFileDialog.FileName}.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'exportation du rapport");
                MessageBox.Show($"Erreur lors de l'exportation du rapport : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToCsv(string filePath)
        {
            try
            {
                // Créer un StringBuilder pour construire le fichier CSV
                var csv = new StringBuilder();

                // Ajouter l'en-tête des colonnes
                var headers = _dataTable.Columns.Cast<DataColumn>().Select(column => column.ColumnName);
                csv.AppendLine(string.Join(",", headers));

                // Ajouter les lignes de données
                foreach (DataRow row in _dataTable.Rows)
                {
                    var fields = row.ItemArray.Select(field =>
                    {
                        // Échapper les champs qui contiennent des virgules ou des guillemets
                        string value = field.ToString();
                        if (value.Contains(',') || value.Contains('"') || value.Contains('\n'))
                        {
                            return $"\"{value.Replace("\"", "\"\"")}\"";
                        }
                        return value;
                    });
                    csv.AppendLine(string.Join(",", fields));
                }

                // Écrire le fichier CSV
                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'exportation CSV");
                MessageBox.Show($"Erreur lors de l'exportation CSV : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToExcel(string filePath)
        {
            try
            {
                // Créer un StringBuilder pour construire le fichier XML Excel
                var sb = new StringBuilder();

                // En-tête XML
                sb.AppendLine("<?xml version=\"1.0\"?>");
                sb.AppendLine("<?mso-application progid=\"Excel.Sheet\"?>");
                sb.AppendLine("<Workbook xmlns=\"urn:schemas-microsoft-com:office:spreadsheet\"");
                sb.AppendLine(" xmlns:o=\"urn:schemas-microsoft-com:office:office\"");
                sb.AppendLine(" xmlns:x=\"urn:schemas-microsoft-com:office:excel\"");
                sb.AppendLine(" xmlns:ss=\"urn:schemas-microsoft-com:office:spreadsheet\"");
                sb.AppendLine(" xmlns:html=\"http://www.w3.org/TR/REC-html40\">");

                // Styles
                sb.AppendLine(" <Styles>");
                sb.AppendLine("  <Style ss:ID=\"Default\" ss:Name=\"Normal\">");
                sb.AppendLine("   <Alignment ss:Vertical=\"Bottom\"/>");
                sb.AppendLine("   <Borders/>");
                sb.AppendLine("   <Font ss:FontName=\"Calibri\" x:Family=\"Swiss\" ss:Size=\"11\" ss:Color=\"#000000\"/>");
                sb.AppendLine("   <Interior/>");
                sb.AppendLine("   <NumberFormat/>");
                sb.AppendLine("   <Protection/>");
                sb.AppendLine("  </Style>");
                sb.AppendLine("  <Style ss:ID=\"s62\">");
                sb.AppendLine("   <Font ss:FontName=\"Calibri\" x:Family=\"Swiss\" ss:Size=\"11\" ss:Color=\"#000000\" ss:Bold=\"1\"/>");
                sb.AppendLine("   <Interior ss:Color=\"#D9D9D9\" ss:Pattern=\"Solid\"/>");
                sb.AppendLine("  </Style>");
                sb.AppendLine(" </Styles>");

                // Feuille de calcul
                sb.AppendLine(" <Worksheet ss:Name=\"Rapport\">");
                sb.AppendLine("  <Table ss:ExpandedColumnCount=\"" + _dataTable.Columns.Count + "\" ss:ExpandedRowCount=\"" + (_dataTable.Rows.Count + 1) + "\" x:FullColumns=\"1\" x:FullRows=\"1\">");

                // En-tête des colonnes
                sb.AppendLine("   <Row>");
                foreach (DataColumn column in _dataTable.Columns)
                {
                    sb.AppendLine($"    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">{column.ColumnName}</Data></Cell>");
                }
                sb.AppendLine("   </Row>");

                // Données
                foreach (DataRow row in _dataTable.Rows)
                {
                    sb.AppendLine("   <Row>");
                    foreach (var item in row.ItemArray)
                    {
                        string type = "String";
                        string value = item.ToString();

                        // Déterminer le type de données
                        if (item is int || item is long || item is short || item is byte)
                        {
                            type = "Number";
                        }
                        else if (item is decimal || item is double || item is float)
                        {
                            type = "Number";
                            // Formater les nombres décimaux avec un point comme séparateur décimal
                            value = ((decimal)Convert.ToDecimal(item)).ToString(CultureInfo.InvariantCulture);
                        }
                        else if (item is DateTime)
                        {
                            type = "DateTime";
                            value = ((DateTime)item).ToString("yyyy-MM-ddTHH:mm:ss.000");
                        }
                        else if (item is bool)
                        {
                            type = "Boolean";
                            value = ((bool)item) ? "1" : "0";
                        }

                        sb.AppendLine($"    <Cell><Data ss:Type=\"{type}\">{value}</Data></Cell>");
                    }
                    sb.AppendLine("   </Row>");
                }

                // Fermer la table et la feuille de calcul
                sb.AppendLine("  </Table>");
                sb.AppendLine(" </Worksheet>");
                sb.AppendLine("</Workbook>");

                // Écrire le fichier Excel
                File.WriteAllText(filePath, sb.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'exportation Excel");
                MessageBox.Show($"Erreur lors de l'exportation Excel : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToPdf(string filePath)
        {
            try
            {
                // Utiliser iText7 pour générer le PDF
                using (var writer = new iText.Kernel.Pdf.PdfWriter(filePath))
                {
                    using (var pdf = new iText.Kernel.Pdf.PdfDocument(writer))
                    {
                        var document = new iText.Layout.Document(pdf);

                        // Ajouter un en-tête
                        var headerText = new iText.Layout.Element.Paragraph("Rapport")
                            .SetFontSize(18)
                            .SetBold()
                            .SetTextAlignment(iText.Layout.Properties.TextAlignment.CENTER);
                        document.Add(headerText);

                        // Ajouter la date de génération
                        var dateText = new iText.Layout.Element.Paragraph($"Généré le: {DateTime.Now}")
                            .SetFontSize(10)
                            .SetTextAlignment(iText.Layout.Properties.TextAlignment.RIGHT);
                        document.Add(dateText);

                        document.Add(new iText.Layout.Element.Paragraph("\n"));

                        // Créer une table pour les données
                        var table = new iText.Layout.Element.Table(_dataTable.Columns.Count).UseAllAvailableWidth();

                        // En-tête de la table
                        foreach (DataColumn column in _dataTable.Columns)
                        {
                            table.AddHeaderCell(column.ColumnName);
                        }

                        // Données
                        foreach (DataRow row in _dataTable.Rows)
                        {
                            foreach (var item in row.ItemArray)
                            {
                                table.AddCell(item.ToString());
                            }
                        }

                        document.Add(table);

                        // Ajouter un pied de page
                        var footer = new iText.Layout.Element.Paragraph("RecouvreX - Rapport généré automatiquement")
                            .SetFontSize(8)
                            .SetTextAlignment(iText.Layout.Properties.TextAlignment.CENTER);
                        document.Add(footer);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'exportation PDF");
                MessageBox.Show($"Erreur lors de l'exportation PDF : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
