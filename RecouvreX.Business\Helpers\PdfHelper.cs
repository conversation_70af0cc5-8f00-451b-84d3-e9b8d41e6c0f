using RecouvreX.Models;
using System;
using System.IO;
using System.Collections.Generic;
using iText.Kernel.Pdf;
using iText.Layout;
using iText.Layout.Element;
using iText.Layout.Properties;
using iText.Kernel.Colors;
using iText.Kernel.Font;
using iText.IO.Font.Constants;

namespace RecouvreX.Business.Helpers
{
    /// <summary>
    /// Classe utilitaire pour la génération de documents PDF
    /// </summary>
    public static class PdfHelper
    {
        /// <summary>
        /// Génère un document PDF à partir d'un contenu HTML
        /// </summary>
        /// <param name="contenu">Contenu HTML</param>
        /// <param name="facture">Facture concernée</param>
        /// <param name="client">Client concerné</param>
        /// <param name="cheminFichier">Chemin du fichier PDF à générer</param>
        /// <returns>True si la génération a réussi, sinon False</returns>
        public static bool GeneratePdf(string contenu, Facture facture, Client client, string cheminFichier)
        {
            try
            {
                // Créer le répertoire si nécessaire
                var directory = Path.GetDirectoryName(cheminFichier);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Créer le document PDF
                using (var writer = new PdfWriter(cheminFichier))
                {
                    using (var pdf = new PdfDocument(writer))
                    {
                        using (var document = new iText.Layout.Document(pdf))
                        {
                            // Ajouter l'en-tête
                            document.Add(new Paragraph("RELANCE DE PAIEMENT")
                                .SetTextAlignment(TextAlignment.CENTER)
                                .SetFontSize(16)
                                .SetBold());

                            document.Add(new Paragraph($"Référence : {facture.Numero}")
                                .SetTextAlignment(TextAlignment.RIGHT)
                                .SetFontSize(10));

                            document.Add(new Paragraph($"Date : {DateTime.Now:dd/MM/yyyy}")
                                .SetTextAlignment(TextAlignment.RIGHT)
                                .SetFontSize(10));

                            // Ajouter les informations du client
                            document.Add(new Paragraph($"{client.RaisonSociale}")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(12)
                                .SetBold());

                            if (!string.IsNullOrEmpty(client.Adresse))
                            {
                                document.Add(new Paragraph($"{client.Adresse}")
                                    .SetTextAlignment(TextAlignment.LEFT)
                                    .SetFontSize(10));
                            }

                            if (!string.IsNullOrEmpty(client.CodePostal) || !string.IsNullOrEmpty(client.Ville))
                            {
                                document.Add(new Paragraph($"{client.CodePostal} {client.Ville}")
                                    .SetTextAlignment(TextAlignment.LEFT)
                                    .SetFontSize(10));
                            }

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter l'objet
                            document.Add(new Paragraph($"Objet : Relance de paiement - Facture {facture.Numero}")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(12)
                                .SetBold());

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter le contenu principal
                            document.Add(new Paragraph("Madame, Monsieur,")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter le contenu de la relance
                            document.Add(new Paragraph(contenu)
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter le tableau des factures
                            Table table = new Table(4);
                            table.SetWidth(UnitValue.CreatePercentValue(100));

                            // En-têtes du tableau
                            table.AddCell(new Cell().Add(new Paragraph("Facture").SetBold()));
                            table.AddCell(new Cell().Add(new Paragraph("Date").SetBold()));
                            table.AddCell(new Cell().Add(new Paragraph("Montant").SetBold()));
                            table.AddCell(new Cell().Add(new Paragraph("Échéance").SetBold()));

                            // Ligne de données
                            table.AddCell(new Cell().Add(new Paragraph(facture.Numero)));
                            table.AddCell(new Cell().Add(new Paragraph(facture.DateEmission.ToString("dd/MM/yyyy"))));
                            table.AddCell(new Cell().Add(new Paragraph($"{facture.MontantTotal:C2}")));
                            table.AddCell(new Cell().Add(new Paragraph(facture.DateEcheance.ToString("dd/MM/yyyy"))));

                            document.Add(table);

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter le pied de page
                            document.Add(new Paragraph("Nous vous remercions de votre attention.")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("Cordialement,")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("Service Recouvrement")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10)
                                .SetBold());
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de la génération du PDF pour la facture {FactureId}", facture.Id);
                return false;
            }
        }

        /// <summary>
        /// Génère une lettre de relance au format PDF
        /// </summary>
        /// <param name="facture">Facture concernée</param>
        /// <param name="client">Client concerné</param>
        /// <param name="niveau">Niveau de relance</param>
        /// <param name="cheminFichier">Chemin du fichier PDF à générer</param>
        /// <returns>True si la génération a réussi, sinon False</returns>
        public static bool GenererLettreRelance(Facture facture, Client client, int niveau, string cheminFichier)
        {
            try
            {
                // Créer le répertoire si nécessaire
                var directory = Path.GetDirectoryName(cheminFichier);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Générer le contenu en fonction du niveau de relance
                string contenu = string.Empty;
                string objet = string.Empty;

                switch (niveau)
                {
                    case 1:
                        objet = $"Rappel de paiement - Facture {facture.Numero}";
                        contenu = $"Nous constatons que votre facture n°{facture.Numero} d'un montant de {facture.MontantTotal:C2} émise le {facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {facture.DateEcheance:dd/MM/yyyy} n'a pas encore été réglée.\n\n" +
                                 $"Nous vous prions de bien vouloir procéder au règlement de cette facture dans les plus brefs délais.\n\n" +
                                 $"Si votre paiement est en cours, nous vous prions de ne pas tenir compte de ce rappel.";
                        break;

                    case 2:
                        objet = $"Deuxième rappel de paiement - Facture {facture.Numero}";
                        contenu = $"Malgré notre précédent rappel, nous constatons que votre facture n°{facture.Numero} d'un montant de {facture.MontantTotal:C2} émise le {facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {facture.DateEcheance:dd/MM/yyyy} n'a toujours pas été réglée.\n\n" +
                                 $"Nous vous prions de bien vouloir procéder au règlement de cette facture sous 8 jours.\n\n" +
                                 $"Si votre paiement est en cours, nous vous prions de ne pas tenir compte de ce rappel.";
                        break;

                    case 3:
                        objet = $"Mise en demeure - Facture {facture.Numero}";
                        contenu = $"Malgré nos précédents rappels, nous constatons que votre facture n°{facture.Numero} d'un montant de {facture.MontantTotal:C2} émise le {facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {facture.DateEcheance:dd/MM/yyyy} n'a toujours pas été réglée.\n\n" +
                                 $"Nous vous mettons en demeure de régler cette facture sous 48 heures. À défaut, nous nous verrons dans l'obligation d'engager une procédure de recouvrement judiciaire, ce qui entraînera des frais supplémentaires à votre charge.\n\n" +
                                 $"Si votre paiement est en cours, nous vous prions de nous en informer immédiatement.";
                        break;

                    default:
                        objet = $"Relance de paiement - Facture {facture.Numero}";
                        contenu = $"Nous constatons que votre facture n°{facture.Numero} d'un montant de {facture.MontantTotal:C2} émise le {facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {facture.DateEcheance:dd/MM/yyyy} n'a pas encore été réglée.\n\n" +
                                 $"Nous vous prions de bien vouloir procéder au règlement de cette facture dans les plus brefs délais.\n\n" +
                                 $"Si votre paiement est en cours, nous vous prions de ne pas tenir compte de ce rappel.";
                        break;
                }

                // Créer le document PDF
                using (var writer = new PdfWriter(cheminFichier))
                {
                    using (var pdf = new PdfDocument(writer))
                    {
                        using (var document = new iText.Layout.Document(pdf))
                        {
                            // Ajouter l'en-tête
                            document.Add(new Paragraph("RELANCE DE PAIEMENT")
                                .SetTextAlignment(TextAlignment.CENTER)
                                .SetFontSize(16)
                                .SetBold());

                            document.Add(new Paragraph($"Référence : {facture.Numero}")
                                .SetTextAlignment(TextAlignment.RIGHT)
                                .SetFontSize(10));

                            document.Add(new Paragraph($"Date : {DateTime.Now:dd/MM/yyyy}")
                                .SetTextAlignment(TextAlignment.RIGHT)
                                .SetFontSize(10));

                            // Ajouter les informations du client
                            document.Add(new Paragraph($"{client.RaisonSociale}")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(12)
                                .SetBold());

                            if (!string.IsNullOrEmpty(client.Adresse))
                            {
                                document.Add(new Paragraph($"{client.Adresse}")
                                    .SetTextAlignment(TextAlignment.LEFT)
                                    .SetFontSize(10));
                            }

                            if (!string.IsNullOrEmpty(client.CodePostal) || !string.IsNullOrEmpty(client.Ville))
                            {
                                document.Add(new Paragraph($"{client.CodePostal} {client.Ville}")
                                    .SetTextAlignment(TextAlignment.LEFT)
                                    .SetFontSize(10));
                            }

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter l'objet
                            document.Add(new Paragraph($"Objet : {objet}")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(12)
                                .SetBold());

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter le contenu principal
                            document.Add(new Paragraph("Madame, Monsieur,")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter le contenu de la relance
                            document.Add(new Paragraph(contenu)
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter le tableau des factures
                            Table table = new Table(4);
                            table.SetWidth(UnitValue.CreatePercentValue(100));

                            // En-têtes du tableau
                            table.AddCell(new Cell().Add(new Paragraph("Facture").SetBold()));
                            table.AddCell(new Cell().Add(new Paragraph("Date").SetBold()));
                            table.AddCell(new Cell().Add(new Paragraph("Montant").SetBold()));
                            table.AddCell(new Cell().Add(new Paragraph("Échéance").SetBold()));

                            // Ligne de données
                            table.AddCell(new Cell().Add(new Paragraph(facture.Numero)));
                            table.AddCell(new Cell().Add(new Paragraph(facture.DateEmission.ToString("dd/MM/yyyy"))));
                            table.AddCell(new Cell().Add(new Paragraph($"{facture.MontantTotal:C2}")));
                            table.AddCell(new Cell().Add(new Paragraph(facture.DateEcheance.ToString("dd/MM/yyyy"))));

                            document.Add(table);

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter le pied de page
                            document.Add(new Paragraph("Nous vous remercions de votre attention.")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("Cordialement,")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("Service Recouvrement")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10)
                                .SetBold());
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de la génération de la lettre de relance pour la facture {FactureId}", facture.Id);
                return false;
            }
        }
    }
}
