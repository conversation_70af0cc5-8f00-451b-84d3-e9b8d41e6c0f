using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Communications
{
    partial class NoteForm
    {
        /// <summary>
        /// Variable nécessaire au concepteur.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private TableLayoutPanel tableLayoutPanel;
        private Label suiviLabel;
        private CheckBox suiviNecessaireCheckBox;
        private Label contenuLabel;
        private TextBox contenuTextBox;
        private FlowLayoutPanel buttonsPanel;
        private Button cancelButton;
        private Button saveButton;
        private Label noteLabel;

        /// <summary>
        /// Nettoyage des ressources utilisées.
        /// </summary>
        /// <param name="disposing">true si les ressources managées doivent être supprimées ; sinon, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Code généré par le Concepteur Windows Form

        /// <summary>
        /// Méthode requise pour la prise en charge du concepteur - ne modifiez pas
        /// le contenu de cette méthode avec l'éditeur de code.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();

            // Panneau principal
            this.mainPanel = new Panel();
            this.mainPanel.Dock = DockStyle.Fill;
            this.mainPanel.Padding = new Padding(10);

            // Tableau de mise en page
            this.tableLayoutPanel = new TableLayoutPanel();
            this.tableLayoutPanel.Dock = DockStyle.Fill;
            this.tableLayoutPanel.ColumnCount = 2;
            this.tableLayoutPanel.RowCount = 3;
            this.tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            this.tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            this.tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            this.tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            this.tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));

            // Suivi nécessaire
            this.suiviLabel = new Label();
            this.suiviLabel.Text = "Suivi nécessaire:";
            this.suiviLabel.Anchor = AnchorStyles.Left | AnchorStyles.Right;

            this.suiviNecessaireCheckBox = new CheckBox();
            this.suiviNecessaireCheckBox.Name = "suiviNecessaireCheckBox";
            this.suiviNecessaireCheckBox.Text = "Nécessite un suivi";

            // Contenu
            this.contenuLabel = new Label();
            this.contenuLabel.Text = "Contenu *:";
            this.contenuLabel.Anchor = AnchorStyles.Left;

            this.contenuTextBox = new TextBox();
            this.contenuTextBox.Name = "contenuTextBox";
            this.contenuTextBox.Dock = DockStyle.Fill;
            this.contenuTextBox.Multiline = true;
            this.contenuTextBox.ScrollBars = ScrollBars.Vertical;

            // Boutons
            this.buttonsPanel = new FlowLayoutPanel();
            this.buttonsPanel.Dock = DockStyle.Fill;
            this.buttonsPanel.FlowDirection = FlowDirection.RightToLeft;
            this.buttonsPanel.WrapContents = false;

            this.cancelButton = new Button();
            this.cancelButton.Name = "cancelButton";
            this.cancelButton.Text = "Annuler";
            this.cancelButton.Size = new Size(100, 30);

            this.saveButton = new Button();
            this.saveButton.Name = "saveButton";
            this.saveButton.Text = "Enregistrer";
            this.saveButton.Size = new Size(100, 30);

            // Note sur les champs obligatoires
            this.noteLabel = new Label();
            this.noteLabel.Text = "* Champs obligatoires";
            this.noteLabel.AutoSize = true;
            this.noteLabel.ForeColor = Color.Red;
            this.noteLabel.Location = new Point(10, this.ClientSize.Height - 30);

            // Ajouter les contrôles au formulaire
            this.tableLayoutPanel.Controls.Add(this.suiviLabel, 0, 0);
            this.tableLayoutPanel.Controls.Add(this.suiviNecessaireCheckBox, 1, 0);
            this.tableLayoutPanel.Controls.Add(this.contenuLabel, 0, 1);
            this.tableLayoutPanel.Controls.Add(this.contenuTextBox, 1, 1);
            this.tableLayoutPanel.Controls.Add(this.buttonsPanel, 1, 2);

            this.buttonsPanel.Controls.Add(this.cancelButton);
            this.buttonsPanel.Controls.Add(this.saveButton);

            this.mainPanel.Controls.Add(this.tableLayoutPanel);

            this.Controls.Add(this.mainPanel);
            this.Controls.Add(this.noteLabel);

            // Configurer les événements
            this.suiviNecessaireCheckBox.CheckedChanged += new EventHandler(this.SuiviNecessaireCheckBox_CheckedChanged);
            this.contenuTextBox.Validating += new CancelEventHandler(this.ContenuTextBox_Validating);
            this.cancelButton.Click += new EventHandler(this.CancelButton_Click);
            this.saveButton.Click += new EventHandler(this.SaveButton_Click);

            // Configurer le formulaire
            this.ClientSize = new System.Drawing.Size(500, 400);
            this.Name = "NoteForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Ajouter une note";
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Load += new System.EventHandler(this.NoteForm_Load);

            this.ResumeLayout(false);
        }

        // La méthode CreateControls a été intégrée dans InitializeComponent

        #endregion
    }
}
