using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des documents
    /// </summary>
    public interface IDocumentRepository : IRepository<Document>
    {
        /// <summary>
        /// Récupère tous les documents associés à une entité
        /// </summary>
        /// <param name="typeEntite">Type d'entité (Facture, Paiement, Relance, Client)</param>
        /// <param name="entiteId">Identifiant de l'entité</param>
        /// <returns>Liste des documents associés à l'entité</returns>
        Task<IEnumerable<Document>> GetByEntityAsync(string typeEntite, int entiteId);

        /// <summary>
        /// Récupère les documents par type
        /// </summary>
        /// <param name="type">Type de document</param>
        /// <returns>Liste des documents du type spécifié</returns>
        Task<IEnumerable<Document>> GetByTypeAsync(string type);

        /// <summary>
        /// Récupère un document avec son contenu
        /// </summary>
        /// <param name="documentId">Identifiant du document</param>
        /// <returns>Document avec son contenu</returns>
        Task<Document?> GetWithContentAsync(int documentId);

        /// <summary>
        /// Recherche des documents par nom
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des documents correspondant au terme de recherche</returns>
        Task<IEnumerable<Document>> SearchByNameAsync(string searchTerm);
    }
}
