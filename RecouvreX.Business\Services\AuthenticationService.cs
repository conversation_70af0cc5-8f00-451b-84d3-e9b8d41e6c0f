using RecouvreX.Business.Interfaces;
using RecouvreX.Common.Security;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service d'authentification
    /// </summary>
    public class AuthenticationService : IAuthenticationService
    {
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IPermissionRepository _permissionRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="utilisateurRepository">Repository des utilisateurs</param>
        /// <param name="permissionRepository">Repository des permissions</param>
        /// <param name="journalAuditRepository">Repository du journal d'audit</param>
        public AuthenticationService(
            IUtilisateurRepository utilisateurRepository,
            IPermissionRepository permissionRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _permissionRepository = permissionRepository ?? throw new ArgumentNullException(nameof(permissionRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Authentifie un utilisateur avec son nom d'utilisateur et son mot de passe
        /// </summary>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="motDePasse">Mot de passe en clair</param>
        /// <returns>Utilisateur authentifié ou null si l'authentification échoue</returns>
        public async Task<Utilisateur> AuthenticateAsync(string nomUtilisateur, string motDePasse)
        {
            if (string.IsNullOrEmpty(nomUtilisateur) || string.IsNullOrEmpty(motDePasse))
                return null;

            var utilisateur = await _utilisateurRepository.AuthenticateAsync(nomUtilisateur, motDePasse);
            
            if (utilisateur != null)
            {
                // Mettre à jour la date de dernière connexion
                await _utilisateurRepository.UpdateLastLoginAsync(utilisateur.Id);
                
                // Journaliser la connexion
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    DateAction = DateTime.Now,
                    UtilisateurId = utilisateur.Id,
                    NomUtilisateur = utilisateur.NomUtilisateur,
                    TypeAction = TypeAudit.Connexion,
                    Description = $"Connexion de l'utilisateur {utilisateur.NomUtilisateur}"
                });
            }
            
            return utilisateur;
        }

        /// <summary>
        /// Vérifie si un utilisateur possède une permission spécifique
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="permissionCode">Code de la permission</param>
        /// <returns>True si l'utilisateur possède la permission, sinon False</returns>
        public async Task<bool> HasPermissionAsync(int utilisateurId, string permissionCode)
        {
            if (utilisateurId <= 0 || string.IsNullOrEmpty(permissionCode))
                return false;

            return await _permissionRepository.UserHasPermissionAsync(utilisateurId, permissionCode);
        }

        /// <summary>
        /// Déconnecte un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>True si la déconnexion a réussi, sinon False</returns>
        public async Task<bool> LogoutAsync(int utilisateurId)
        {
            if (utilisateurId <= 0)
                return false;

            var utilisateur = await _utilisateurRepository.GetByIdAsync(utilisateurId);
            if (utilisateur == null)
                return false;

            // Journaliser la déconnexion
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = utilisateur.Id,
                NomUtilisateur = utilisateur.NomUtilisateur,
                TypeAction = TypeAudit.Deconnexion,
                Description = $"Déconnexion de l'utilisateur {utilisateur.NomUtilisateur}"
            });

            return true;
        }

        /// <summary>
        /// Change le mot de passe d'un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="ancienMotDePasse">Ancien mot de passe en clair</param>
        /// <param name="nouveauMotDePasse">Nouveau mot de passe en clair</param>
        /// <returns>True si le changement a réussi, sinon False</returns>
        public async Task<bool> ChangePasswordAsync(int utilisateurId, string ancienMotDePasse, string nouveauMotDePasse)
        {
            if (utilisateurId <= 0 || string.IsNullOrEmpty(ancienMotDePasse) || string.IsNullOrEmpty(nouveauMotDePasse))
                return false;

            var utilisateur = await _utilisateurRepository.GetByIdAsync(utilisateurId);
            if (utilisateur == null)
                return false;

            // Vérifier l'ancien mot de passe
            if (!PasswordHasher.VerifyPassword(ancienMotDePasse, utilisateur.MotDePasse))
                return false;

            // Changer le mot de passe
            var result = await _utilisateurRepository.ChangePasswordAsync(utilisateurId, nouveauMotDePasse);
            
            if (result)
            {
                // Journaliser le changement de mot de passe
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    DateAction = DateTime.Now,
                    UtilisateurId = utilisateur.Id,
                    NomUtilisateur = utilisateur.NomUtilisateur,
                    TypeAction = TypeAudit.Modification,
                    TypeEntite = "Utilisateur",
                    EntiteId = utilisateur.Id,
                    Description = $"Changement de mot de passe pour l'utilisateur {utilisateur.NomUtilisateur}"
                });
            }
            
            return result;
        }

        /// <summary>
        /// Réinitialise le mot de passe d'un utilisateur (par un administrateur)
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="nouveauMotDePasse">Nouveau mot de passe en clair</param>
        /// <param name="administrateurId">Identifiant de l'administrateur qui effectue l'action</param>
        /// <returns>True si la réinitialisation a réussi, sinon False</returns>
        public async Task<bool> ResetPasswordAsync(int utilisateurId, string nouveauMotDePasse, int administrateurId)
        {
            if (utilisateurId <= 0 || string.IsNullOrEmpty(nouveauMotDePasse) || administrateurId <= 0)
                return false;

            var utilisateur = await _utilisateurRepository.GetByIdAsync(utilisateurId);
            if (utilisateur == null)
                return false;

            var administrateur = await _utilisateurRepository.GetByIdAsync(administrateurId);
            if (administrateur == null)
                return false;

            // Vérifier si l'administrateur a la permission de réinitialiser les mots de passe
            var hasPermission = await _permissionRepository.UserHasPermissionAsync(administrateurId, "USERS_EDIT");
            if (!hasPermission)
                return false;

            // Réinitialiser le mot de passe
            var result = await _utilisateurRepository.ChangePasswordAsync(utilisateurId, nouveauMotDePasse);
            
            if (result)
            {
                // Journaliser la réinitialisation du mot de passe
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    DateAction = DateTime.Now,
                    UtilisateurId = administrateur.Id,
                    NomUtilisateur = administrateur.NomUtilisateur,
                    TypeAction = TypeAudit.Modification,
                    TypeEntite = "Utilisateur",
                    EntiteId = utilisateur.Id,
                    Description = $"Réinitialisation du mot de passe pour l'utilisateur {utilisateur.NomUtilisateur} par l'administrateur {administrateur.NomUtilisateur}"
                });
            }
            
            return result;
        }

        /// <summary>
        /// Verrouille ou déverrouille un compte utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="estVerrouille">True pour verrouiller, False pour déverrouiller</param>
        /// <param name="administrateurId">Identifiant de l'administrateur qui effectue l'action</param>
        /// <returns>True si l'opération a réussi, sinon False</returns>
        public async Task<bool> SetAccountLockedStatusAsync(int utilisateurId, bool estVerrouille, int administrateurId)
        {
            if (utilisateurId <= 0 || administrateurId <= 0)
                return false;

            var utilisateur = await _utilisateurRepository.GetByIdAsync(utilisateurId);
            if (utilisateur == null)
                return false;

            var administrateur = await _utilisateurRepository.GetByIdAsync(administrateurId);
            if (administrateur == null)
                return false;

            // Vérifier si l'administrateur a la permission de gérer les utilisateurs
            var hasPermission = await _permissionRepository.UserHasPermissionAsync(administrateurId, "USERS_EDIT");
            if (!hasPermission)
                return false;

            // Verrouiller ou déverrouiller le compte
            var result = await _utilisateurRepository.SetAccountLockedStatusAsync(utilisateurId, estVerrouille);
            
            if (result)
            {
                // Journaliser le verrouillage ou déverrouillage du compte
                var action = estVerrouille ? "Verrouillage" : "Déverrouillage";
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    DateAction = DateTime.Now,
                    UtilisateurId = administrateur.Id,
                    NomUtilisateur = administrateur.NomUtilisateur,
                    TypeAction = TypeAudit.Modification,
                    TypeEntite = "Utilisateur",
                    EntiteId = utilisateur.Id,
                    Description = $"{action} du compte de l'utilisateur {utilisateur.NomUtilisateur} par l'administrateur {administrateur.NomUtilisateur}"
                });
            }
            
            return result;
        }
    }
}
