namespace RecouvreX.Models.Enums
{
    /// <summary>
    /// Types d'actions prioritaires
    /// </summary>
    public enum TypeActionPrioritaire
    {
        /// <summary>
        /// Relance téléphonique
        /// </summary>
        RelanceTelephonique = 1,

        /// <summary>
        /// Relance par email
        /// </summary>
        RelanceEmail = 2,

        /// <summary>
        /// Relance par courrier
        /// </summary>
        RelanceCourrier = 3,

        /// <summary>
        /// Négociation de plan de paiement
        /// </summary>
        NegociationPlanPaiement = 4,

        /// <summary>
        /// Escalade vers un niveau supérieur
        /// </summary>
        Escalade = 5,

        /// <summary>
        /// Procédure juridique
        /// </summary>
        ProcedureJuridique = 6,

        /// <summary>
        /// Autre action
        /// </summary>
        Autre = 99
    }
}
