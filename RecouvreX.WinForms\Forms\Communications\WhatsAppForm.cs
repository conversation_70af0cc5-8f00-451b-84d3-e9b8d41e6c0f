using RecouvreX.Business.Helpers;
using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Communications
{
    /// <summary>
    /// Formulaire pour l'envoi d'un message WhatsApp
    /// </summary>
    public partial class WhatsAppForm : Form
    {
        private readonly ICommunicationService _communicationService;
        private readonly IContactClientService _contactClientService;
        private readonly IFactureService _factureService;
        private readonly IClientService _clientService;
        private readonly int _currentUserId;
        private readonly int _factureId;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();
        private Facture _facture;
        private Client _client;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="communicationService">Service de gestion des communications</param>
        /// <param name="contactClientService">Service de gestion des contacts client</param>
        /// <param name="factureService">Service de gestion des factures</param>
        /// <param name="clientService">Service de gestion des clients</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        /// <param name="factureId">Identifiant de la facture</param>
        public WhatsAppForm(
            ICommunicationService communicationService,
            IContactClientService contactClientService,
            IFactureService factureService,
            IClientService clientService,
            int currentUserId,
            int factureId)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _contactClientService = contactClientService ?? throw new ArgumentNullException(nameof(contactClientService));
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _currentUserId = currentUserId;
            _factureId = factureId;

            InitializeComponent();

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;
        }

        // Les méthodes InitializeComponent() et CreateControls() ont été déplacées dans le fichier WhatsAppForm.Designer.cs

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void WhatsAppForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Charger les données de la facture
                _facture = await _factureService.GetByIdAsync(_factureId);
                if (_facture == null)
                {
                    MessageBox.Show("La facture demandée n'existe pas ou a été supprimée.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Charger les données du client
                _client = await _clientService.GetByIdAsync(_facture.ClientId);
                if (_client == null)
                {
                    MessageBox.Show("Le client associé à cette facture n'existe pas ou a été supprimé.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Afficher les informations de la facture
                var factureValueLabel = this.Controls.Find("factureValueLabel", true).FirstOrDefault() as Label;
                if (factureValueLabel != null)
                {
                    factureValueLabel.Text = $"{_facture.Numero} - {_facture.MontantTotal:C2} - Échéance : {_facture.DateEcheance:dd/MM/yyyy}";
                }

                // Afficher les informations du client
                var clientValueLabel = this.Controls.Find("clientValueLabel", true).FirstOrDefault() as Label;
                if (clientValueLabel != null)
                {
                    clientValueLabel.Text = $"{_client.RaisonSociale}";
                }

                // Charger les contacts du client
                await LoadContactsAsync();

                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'envoi de message WhatsApp");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Cursor.Current = Cursors.Default;
            }
        }

        /// <summary>
        /// Chargement des contacts du client
        /// </summary>
        private async Task LoadContactsAsync()
        {
            try
            {
                // Récupérer les contacts du client
                var contacts = await _contactClientService.GetByClientIdAsync(_client.Id);

                // Remplir la liste déroulante des destinataires
                var destinataireComboBox = this.Controls.Find("destinataireComboBox", true).FirstOrDefault() as ComboBox;
                if (destinataireComboBox != null)
                {
                    destinataireComboBox.Items.Clear();
                    destinataireComboBox.DisplayMember = "NomComplet";
                    destinataireComboBox.ValueMember = "Id";

                    foreach (var contact in contacts)
                    {
                        if (!string.IsNullOrEmpty(contact.Telephone))
                        {
                            destinataireComboBox.Items.Add(contact);
                        }
                    }

                    // Sélectionner le contact principal s'il existe
                    var contactPrincipal = contacts.FirstOrDefault(c => c.EstPrincipal);
                    if (contactPrincipal != null && !string.IsNullOrEmpty(contactPrincipal.Telephone))
                    {
                        destinataireComboBox.SelectedItem = contactPrincipal;
                    }
                    else if (destinataireComboBox.Items.Count > 0)
                    {
                        destinataireComboBox.SelectedIndex = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des contacts du client");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des contacts : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Validation du destinataire
        /// </summary>
        private void DestinataireComboBox_Validating(object sender, CancelEventArgs e)
        {
            var destinataireComboBox = sender as ComboBox;
            if (destinataireComboBox != null)
            {
                if (destinataireComboBox.SelectedItem == null)
                {
                    _errorProvider.SetError(destinataireComboBox, "Veuillez sélectionner un destinataire");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(destinataireComboBox, string.Empty);
                }
            }
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Aperçu"
        /// </summary>
        private void PreviewButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer le niveau de relance
                var niveauComboBox = this.Controls.Find("niveauComboBox", true).FirstOrDefault() as ComboBox;
                int niveau = niveauComboBox?.SelectedIndex + 1 ?? 1;

                // Générer le message en fonction du niveau de relance
                string message = string.Empty;

                switch (niveau)
                {
                    case 1:
                        message = $"Bonjour, nous constatons que votre facture n°{_facture.Numero} d'un montant de {_facture.MontantTotal:C2} émise le {_facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {_facture.DateEcheance:dd/MM/yyyy} n'a pas encore été réglée. Nous vous prions de bien vouloir procéder au règlement de cette facture dans les plus brefs délais. Si votre paiement est en cours, nous vous prions de ne pas tenir compte de ce rappel. Cordialement, Service Recouvrement";
                        break;

                    case 2:
                        message = $"Bonjour, malgré notre précédent rappel, nous constatons que votre facture n°{_facture.Numero} d'un montant de {_facture.MontantTotal:C2} émise le {_facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {_facture.DateEcheance:dd/MM/yyyy} n'a toujours pas été réglée. Nous vous prions de bien vouloir procéder au règlement de cette facture sous 8 jours. Si votre paiement est en cours, nous vous prions de ne pas tenir compte de ce rappel. Cordialement, Service Recouvrement";
                        break;

                    case 3:
                        message = $"Bonjour, malgré nos précédents rappels, nous constatons que votre facture n°{_facture.Numero} d'un montant de {_facture.MontantTotal:C2} émise le {_facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {_facture.DateEcheance:dd/MM/yyyy} n'a toujours pas été réglée. Nous vous mettons en demeure de régler cette facture sous 48 heures. À défaut, nous nous verrons dans l'obligation d'engager une procédure de recouvrement judiciaire. Si votre paiement est en cours, nous vous prions de nous en informer immédiatement. Cordialement, Service Recouvrement";
                        break;
                }

                // Afficher l'aperçu
                MessageBox.Show(message, "Aperçu du message WhatsApp", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'affichage de l'aperçu du message WhatsApp");
                MessageBox.Show($"Une erreur s'est produite lors de l'affichage de l'aperçu : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Annuler"
        /// </summary>
        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Envoyer"
        /// </summary>
        private async void SendButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant de continuer.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer les valeurs du formulaire
                var destinataireComboBox = this.Controls.Find("destinataireComboBox", true).FirstOrDefault() as ComboBox;
                var niveauComboBox = this.Controls.Find("niveauComboBox", true).FirstOrDefault() as ComboBox;
                var suiviNecessaireCheckBox = this.Controls.Find("suiviNecessaireCheckBox", true).FirstOrDefault() as CheckBox;

                var contact = destinataireComboBox?.SelectedItem as ContactClient;
                int niveau = niveauComboBox?.SelectedIndex + 1 ?? 1;
                bool suiviNecessaire = suiviNecessaireCheckBox?.Checked ?? false;

                if (contact == null)
                {
                    MessageBox.Show("Veuillez sélectionner un destinataire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Envoyer le message WhatsApp
                bool messageEnvoye = WhatsAppHelper.EnvoyerRelanceWhatsApp(_facture, _client, niveau, contact.Telephone);

                // Enregistrer la communication
                string contenu = string.Empty;
                switch (niveau)
                {
                    case 1:
                        contenu = $"Rappel de paiement pour la facture {_facture.Numero}";
                        break;
                    case 2:
                        contenu = $"Deuxième rappel de paiement pour la facture {_facture.Numero}";
                        break;
                    case 3:
                        contenu = $"Mise en demeure pour la facture {_facture.Numero}";
                        break;
                    default:
                        contenu = $"Relance de paiement pour la facture {_facture.Numero}";
                        break;
                }

                var communication = await _communicationService.EnregistrerSMSAsync(
                    _factureId,
                    contact.Id,
                    DirectionCommunication.Sortant,
                    contenu,
                    messageEnvoye ? "Envoyé" : "Échec de l'envoi",
                    suiviNecessaire,
                    suiviNecessaire ? DateTime.Now.AddDays(7) : (DateTime?)null,
                    _currentUserId);

                // Restaurer le curseur
                this.Cursor = Cursors.Default;

                // Afficher un message de confirmation
                if (messageEnvoye)
                {
                    MessageBox.Show("Le message WhatsApp a été envoyé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Une erreur s'est produite lors de l'envoi du message WhatsApp. La communication a été enregistrée avec un statut d'échec.", "Avertissement", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // Fermer le formulaire
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'envoi du message WhatsApp");
                MessageBox.Show($"Une erreur s'est produite lors de l'envoi du message WhatsApp : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }
    }
}
