namespace RecouvreX.WinForms.Forms.Common
{
    partial class SelectionFactureForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            mainPanel = new TableLayoutPanel();
            infoLabel = new Label();
            dataGridView = new DataGridView();
            bottomPanel = new TableLayoutPanel();
            montantDisponibleLabel = new Label();
            montantLabel = new Label();
            montantNumericUpDown = new NumericUpDown();
            buttonsPanel = new FlowLayoutPanel();
            cancelButton = new Button();
            selectButton = new Button();
            mainPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView).BeginInit();
            bottomPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)montantNumericUpDown).BeginInit();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.ColumnCount = 1;
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            mainPanel.Controls.Add(infoLabel, 0, 0);
            mainPanel.Controls.Add(dataGridView, 0, 1);
            mainPanel.Controls.Add(bottomPanel, 0, 2);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Location = new Point(0, 0);
            mainPanel.Name = "mainPanel";
            mainPanel.Padding = new Padding(10);
            mainPanel.RowCount = 3;
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F));
            mainPanel.Size = new Size(800, 450);
            mainPanel.TabIndex = 0;
            // 
            // infoLabel
            // 
            infoLabel.Dock = DockStyle.Fill;
            infoLabel.Location = new Point(13, 10);
            infoLabel.Name = "infoLabel";
            infoLabel.Size = new Size(774, 30);
            infoLabel.TabIndex = 0;
            infoLabel.Text = "Sélectionnez une facture et indiquez le montant à affecter :";
            infoLabel.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // dataGridView
            // 
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView.Dock = DockStyle.Fill;
            dataGridView.Location = new Point(13, 43);
            dataGridView.MultiSelect = false;
            dataGridView.Name = "dataGridView";
            dataGridView.ReadOnly = true;
            dataGridView.RowHeadersVisible = false;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.Size = new Size(774, 334);
            dataGridView.TabIndex = 1;
            dataGridView.SelectionChanged += DataGridView_SelectionChanged;
            // 
            // bottomPanel
            // 
            bottomPanel.ColumnCount = 3;
            bottomPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            bottomPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            bottomPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            bottomPanel.Controls.Add(montantDisponibleLabel, 0, 0);
            bottomPanel.Controls.Add(montantLabel, 1, 0);
            bottomPanel.Controls.Add(montantNumericUpDown, 2, 0);
            bottomPanel.Controls.Add(buttonsPanel, 0, 1);
            bottomPanel.Dock = DockStyle.Fill;
            bottomPanel.Location = new Point(13, 383);
            bottomPanel.Name = "bottomPanel";
            bottomPanel.RowCount = 2;
            bottomPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            bottomPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            bottomPanel.Size = new Size(774, 54);
            bottomPanel.TabIndex = 2;
            // 
            // montantDisponibleLabel
            // 
            montantDisponibleLabel.Dock = DockStyle.Fill;
            montantDisponibleLabel.Location = new Point(3, 0);
            montantDisponibleLabel.Name = "montantDisponibleLabel";
            montantDisponibleLabel.Size = new Size(252, 27);
            montantDisponibleLabel.TabIndex = 0;
            montantDisponibleLabel.Text = "Montant disponible : 0,00 €";
            montantDisponibleLabel.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // montantLabel
            // 
            montantLabel.Dock = DockStyle.Fill;
            montantLabel.Location = new Point(261, 0);
            montantLabel.Name = "montantLabel";
            montantLabel.Size = new Size(252, 27);
            montantLabel.TabIndex = 1;
            montantLabel.Text = "Montant à affecter :";
            montantLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // montantNumericUpDown
            // 
            montantNumericUpDown.DecimalPlaces = 2;
            montantNumericUpDown.Dock = DockStyle.Fill;
            montantNumericUpDown.Increment = new decimal(new int[] { 1, 0, 0, 131072 });
            montantNumericUpDown.Location = new Point(519, 3);
            montantNumericUpDown.Maximum = new decimal(new int[] { 1000000, 0, 0, 0 });
            montantNumericUpDown.Name = "montantNumericUpDown";
            montantNumericUpDown.Size = new Size(252, 23);
            montantNumericUpDown.TabIndex = 2;
            montantNumericUpDown.ThousandsSeparator = true;
            // 
            // buttonsPanel
            // 
            bottomPanel.SetColumnSpan(buttonsPanel, 3);
            buttonsPanel.Controls.Add(cancelButton);
            buttonsPanel.Controls.Add(selectButton);
            buttonsPanel.Dock = DockStyle.Fill;
            buttonsPanel.FlowDirection = FlowDirection.RightToLeft;
            buttonsPanel.Location = new Point(3, 30);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.Size = new Size(768, 21);
            buttonsPanel.TabIndex = 3;
            // 
            // cancelButton
            // 
            cancelButton.Location = new Point(665, 3);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(100, 30);
            cancelButton.TabIndex = 0;
            cancelButton.Text = "Annuler";
            cancelButton.Click += CancelButton_Click;
            // 
            // selectButton
            // 
            selectButton.Location = new Point(559, 3);
            selectButton.Name = "selectButton";
            selectButton.Size = new Size(100, 30);
            selectButton.TabIndex = 1;
            selectButton.Text = "Sélectionner";
            selectButton.Click += SelectButton_Click;
            // 
            // SelectionFactureForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(800, 450);
            Controls.Add(mainPanel);
            Name = "SelectionFactureForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Sélectionner une facture";
            Load += SelectionFactureForm_Load;
            mainPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dataGridView).EndInit();
            bottomPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)montantNumericUpDown).EndInit();
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel mainPanel;
        private Label infoLabel;
        private DataGridView dataGridView;
        private TableLayoutPanel bottomPanel;
        private Label montantDisponibleLabel;
        private Label montantLabel;
        private NumericUpDown montantNumericUpDown;
        private FlowLayoutPanel buttonsPanel;
        private Button cancelButton;
        private Button selectButton;
    }
}
