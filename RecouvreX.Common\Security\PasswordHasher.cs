using System;

namespace RecouvreX.Common.Security
{
    /// <summary>
    /// Classe utilitaire pour le hachage et la vérification des mots de passe
    /// </summary>
    public static class PasswordHasher
    {
        /// <summary>
        /// Hache un mot de passe en utilisant BCrypt
        /// </summary>
        /// <param name="password">Mot de passe en clair</param>
        /// <returns>Mot de passe haché</returns>
        public static string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                throw new ArgumentNullException(nameof(password));

            // Utiliser BCrypt pour hacher le mot de passe
            return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
        }

        /// <summary>
        /// Vérifie si un mot de passe en clair correspond à un mot de passe haché
        /// </summary>
        /// <param name="password">Mot de passe en clair</param>
        /// <param name="hashedPassword">Mot de passe haché</param>
        /// <returns>True si le mot de passe correspond, sinon False</returns>
        public static bool VerifyPassword(string password, string hashedPassword)
        {
            if (string.IsNullOrEmpty(password))
                throw new ArgumentNullException(nameof(password));

            if (string.IsNullOrEmpty(hashedPassword))
                throw new ArgumentNullException(nameof(hashedPassword));

            // Vérifier le mot de passe avec BCrypt
            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }

        /// <summary>
        /// Génère un mot de passe aléatoire
        /// </summary>
        /// <param name="length">Longueur du mot de passe (par défaut 12)</param>
        /// <returns>Mot de passe aléatoire</returns>
        public static string GenerateRandomPassword(int length = 12)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_-+=";
            var random = new Random();
            var password = new char[length];

            for (int i = 0; i < length; i++)
            {
                password[i] = chars[random.Next(chars.Length)];
            }

            return new string(password);
        }
    }
}
