using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des modèles de relance
    /// </summary>
    public interface IModeleRelanceService
    {
        /// <summary>
        /// Récupère tous les modèles de relance
        /// </summary>
        /// <returns>Liste des modèles de relance</returns>
        Task<IEnumerable<ModeleRelance>> GetAllAsync();

        /// <summary>
        /// Récupère un modèle de relance par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du modèle de relance</param>
        /// <returns>Modèle de relance trouvé ou null</returns>
        Task<ModeleRelance> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les modèles de relance par type
        /// </summary>
        /// <param name="type">Type de modèle de relance</param>
        /// <returns>Liste des modèles de relance du type spécifié</returns>
        Task<IEnumerable<ModeleRelance>> GetByTypeAsync(string type);

        /// <summary>
        /// Récupère les modèles de relance par niveau de fermeté
        /// </summary>
        /// <param name="niveauFermete">Niveau de fermeté</param>
        /// <returns>Liste des modèles de relance du niveau spécifié</returns>
        Task<IEnumerable<ModeleRelance>> GetByNiveauFermeteAsync(int niveauFermete);

        /// <summary>
        /// Récupère le modèle de relance par défaut pour un type et un niveau de fermeté
        /// </summary>
        /// <param name="type">Type de modèle de relance</param>
        /// <param name="niveauFermete">Niveau de fermeté</param>
        /// <returns>Modèle de relance par défaut ou null</returns>
        Task<ModeleRelance> GetDefaultAsync(string type, int niveauFermete);

        /// <summary>
        /// Crée un nouveau modèle de relance
        /// </summary>
        /// <param name="modeleRelance">Modèle de relance à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Modèle de relance créé avec son identifiant généré</returns>
        Task<ModeleRelance> CreateAsync(ModeleRelance modeleRelance, int creePar);

        /// <summary>
        /// Met à jour un modèle de relance existant
        /// </summary>
        /// <param name="modeleRelance">Modèle de relance à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Modèle de relance mis à jour</returns>
        Task<ModeleRelance> UpdateAsync(ModeleRelance modeleRelance, int modifiePar);

        /// <summary>
        /// Supprime un modèle de relance
        /// </summary>
        /// <param name="id">Identifiant du modèle de relance à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Définit un modèle de relance comme modèle par défaut pour son type et niveau de fermeté
        /// </summary>
        /// <param name="id">Identifiant du modèle de relance</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> SetAsDefaultAsync(int id, int modifiePar);

        /// <summary>
        /// Active ou désactive un modèle de relance
        /// </summary>
        /// <param name="id">Identifiant du modèle de relance</param>
        /// <param name="estActif">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> SetActiveStatusAsync(int id, bool estActif, int modifiePar);

        /// <summary>
        /// Applique les variables dynamiques à un modèle de relance
        /// </summary>
        /// <param name="modeleRelance">Modèle de relance</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Contenu du modèle avec les variables remplacées</returns>
        Task<string> ApplyVariablesAsync(ModeleRelance modeleRelance, int factureId);

        /// <summary>
        /// Récupère toutes les variables dynamiques disponibles
        /// </summary>
        /// <returns>Liste des variables dynamiques</returns>
        Task<IEnumerable<VariableDynamique>> GetAllVariablesAsync();
    }
}
