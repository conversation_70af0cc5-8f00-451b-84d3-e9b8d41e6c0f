using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des paiements
    /// </summary>
    public interface IPaiementRepository : IRepository<Paiement>
    {
        /// <summary>
        /// Récupère un paiement par sa référence
        /// </summary>
        /// <param name="reference">Référence du paiement</param>
        /// <returns>Paiement trouvé ou null</returns>
        Task<Paiement> GetByReferenceAsync(string reference);

        /// <summary>
        /// Récupère un paiement avec ses factures associées
        /// </summary>
        /// <param name="paiementId">Identifiant du paiement</param>
        /// <returns>Paiement avec ses factures</returns>
        Task<Paiement> GetWithFacturesAsync(int paiementId);

        /// <summary>
        /// Récupère tous les paiements d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des paiements du client</returns>
        Task<IEnumerable<Paiement>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère tous les paiements associés à une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des paiements associés à la facture</returns>
        Task<IEnumerable<Paiement>> GetByFactureIdAsync(int factureId);

        /// <summary>
        /// Récupère les paiements par mode de paiement
        /// </summary>
        /// <param name="modePaiement">Mode de paiement</param>
        /// <returns>Liste des paiements effectués avec le mode spécifié</returns>
        Task<IEnumerable<Paiement>> GetByModePaiementAsync(string modePaiement);

        /// <summary>
        /// Récupère les paiements par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des paiements effectués dans la période spécifiée</returns>
        Task<IEnumerable<Paiement>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Associe un paiement à une facture
        /// </summary>
        /// <param name="paiementId">Identifiant du paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="montantAffecte">Montant affecté à la facture</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'association a réussi, sinon False</returns>
        Task<bool> AssociateToFactureAsync(int paiementId, int factureId, decimal montantAffecte, int userId);

        /// <summary>
        /// Dissocie un paiement d'une facture
        /// </summary>
        /// <param name="paiementId">Identifiant du paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>True si la dissociation a réussi, sinon False</returns>
        Task<bool> DissociateFromFactureAsync(int paiementId, int factureId);
    }
}
