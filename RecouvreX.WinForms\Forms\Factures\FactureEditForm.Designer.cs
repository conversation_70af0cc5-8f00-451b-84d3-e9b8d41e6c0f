namespace RecouvreX.WinForms.Forms.Factures
{
    partial class FactureEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // FactureEditForm
            // 
            this.ClientSize = new System.Drawing.Size(600, 500);
            this.Name = "FactureEditForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Facture"; // Titre par défaut, sera mis à jour dans le constructeur
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Load += new System.EventHandler(this.FactureEditForm_Load);
            this.ResumeLayout(false);

            // Panneau principal
            System.Windows.Forms.TableLayoutPanel mainPanel = new System.Windows.Forms.TableLayoutPanel();
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Padding = new System.Windows.Forms.Padding(10);
            mainPanel.ColumnCount = 2;
            mainPanel.RowCount = 12;
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 30F));
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 70F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            this.Controls.Add(mainPanel);

            // Numéro de facture
            System.Windows.Forms.Label numeroLabel = new System.Windows.Forms.Label();
            numeroLabel.Text = "Numéro :";
            numeroLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(numeroLabel, 0, 0);

            this.numeroTextBox = new System.Windows.Forms.TextBox();
            this.numeroTextBox.Name = "numeroTextBox";
            this.numeroTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            this.numeroTextBox.Enabled = false; // Sera mis à jour dans le constructeur
            mainPanel.Controls.Add(this.numeroTextBox, 1, 0);

            // Client
            System.Windows.Forms.Label clientLabel = new System.Windows.Forms.Label();
            clientLabel.Text = "Client* :";
            clientLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(clientLabel, 0, 1);

            System.Windows.Forms.ComboBox clientComboBox = new System.Windows.Forms.ComboBox();
            clientComboBox.Name = "clientComboBox";
            clientComboBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            clientComboBox.Validating += new System.ComponentModel.CancelEventHandler(this.ClientComboBox_Validating);
            mainPanel.Controls.Add(clientComboBox, 1, 1);

            // Date d'émission
            System.Windows.Forms.Label dateEmissionLabel = new System.Windows.Forms.Label();
            dateEmissionLabel.Text = "Date d'émission* :";
            dateEmissionLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(dateEmissionLabel, 0, 2);

            System.Windows.Forms.DateTimePicker dateEmissionPicker = new System.Windows.Forms.DateTimePicker();
            dateEmissionPicker.Name = "dateEmissionPicker";
            dateEmissionPicker.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            dateEmissionPicker.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            dateEmissionPicker.Value = System.DateTime.Today;
            mainPanel.Controls.Add(dateEmissionPicker, 1, 2);

            // Date d'échéance
            System.Windows.Forms.Label dateEcheanceLabel = new System.Windows.Forms.Label();
            dateEcheanceLabel.Text = "Date d'échéance* :";
            dateEcheanceLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(dateEcheanceLabel, 0, 3);

            System.Windows.Forms.DateTimePicker dateEcheancePicker = new System.Windows.Forms.DateTimePicker();
            dateEcheancePicker.Name = "dateEcheancePicker";
            dateEcheancePicker.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            dateEcheancePicker.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            dateEcheancePicker.Value = System.DateTime.Today.AddDays(30);
            mainPanel.Controls.Add(dateEcheancePicker, 1, 3);

            // Montant HT
            System.Windows.Forms.Label montantHTLabel = new System.Windows.Forms.Label();
            montantHTLabel.Text = "Montant HT* :";
            montantHTLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(montantHTLabel, 0, 4);

            System.Windows.Forms.NumericUpDown montantHTNumericUpDown = new System.Windows.Forms.NumericUpDown();
            montantHTNumericUpDown.Name = "montantHTNumericUpDown";
            montantHTNumericUpDown.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            montantHTNumericUpDown.DecimalPlaces = 2;
            montantHTNumericUpDown.Maximum = 1000000;
            montantHTNumericUpDown.ThousandsSeparator = true;
            montantHTNumericUpDown.ValueChanged += new System.EventHandler(this.MontantHTNumericUpDown_ValueChanged);
            mainPanel.Controls.Add(montantHTNumericUpDown, 1, 4);

            // Taux TVA
            System.Windows.Forms.Label tauxTVALabel = new System.Windows.Forms.Label();
            tauxTVALabel.Text = "Taux TVA (%) :";
            tauxTVALabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(tauxTVALabel, 0, 5);

            System.Windows.Forms.NumericUpDown tauxTVANumericUpDown = new System.Windows.Forms.NumericUpDown();
            tauxTVANumericUpDown.Name = "tauxTVANumericUpDown";
            tauxTVANumericUpDown.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            tauxTVANumericUpDown.DecimalPlaces = 2;
            tauxTVANumericUpDown.Maximum = 100;
            tauxTVANumericUpDown.Value = 20; // Taux par défaut
            tauxTVANumericUpDown.ValueChanged += new System.EventHandler(this.TauxTVANumericUpDown_ValueChanged);
            mainPanel.Controls.Add(tauxTVANumericUpDown, 1, 5);

            // Montant TVA
            System.Windows.Forms.Label montantTVALabel = new System.Windows.Forms.Label();
            montantTVALabel.Text = "Montant TVA :";
            montantTVALabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(montantTVALabel, 0, 6);

            System.Windows.Forms.NumericUpDown montantTVANumericUpDown = new System.Windows.Forms.NumericUpDown();
            montantTVANumericUpDown.Name = "montantTVANumericUpDown";
            montantTVANumericUpDown.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            montantTVANumericUpDown.DecimalPlaces = 2;
            montantTVANumericUpDown.Maximum = 1000000;
            montantTVANumericUpDown.ThousandsSeparator = true;
            montantTVANumericUpDown.ReadOnly = true;
            montantTVANumericUpDown.Enabled = false;
            mainPanel.Controls.Add(montantTVANumericUpDown, 1, 6);

            // Montant TTC
            System.Windows.Forms.Label montantTTCLabel = new System.Windows.Forms.Label();
            montantTTCLabel.Text = "Montant TTC :";
            montantTTCLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(montantTTCLabel, 0, 7);

            System.Windows.Forms.NumericUpDown montantTTCNumericUpDown = new System.Windows.Forms.NumericUpDown();
            montantTTCNumericUpDown.Name = "montantTTCNumericUpDown";
            montantTTCNumericUpDown.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            montantTTCNumericUpDown.DecimalPlaces = 2;
            montantTTCNumericUpDown.Maximum = 1000000;
            montantTTCNumericUpDown.ThousandsSeparator = true;
            montantTTCNumericUpDown.ReadOnly = true;
            montantTTCNumericUpDown.Enabled = false;
            mainPanel.Controls.Add(montantTTCNumericUpDown, 1, 7);

            // Montant payé
            System.Windows.Forms.Label montantPayeLabel = new System.Windows.Forms.Label();
            montantPayeLabel.Text = "Montant payé :";
            montantPayeLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(montantPayeLabel, 0, 8);

            System.Windows.Forms.NumericUpDown montantPayeNumericUpDown = new System.Windows.Forms.NumericUpDown();
            montantPayeNumericUpDown.Name = "montantPayeNumericUpDown";
            montantPayeNumericUpDown.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            montantPayeNumericUpDown.DecimalPlaces = 2;
            montantPayeNumericUpDown.Maximum = 1000000;
            montantPayeNumericUpDown.ThousandsSeparator = true;
            montantPayeNumericUpDown.ValueChanged += new System.EventHandler(this.MontantPayeNumericUpDown_ValueChanged);
            mainPanel.Controls.Add(montantPayeNumericUpDown, 1, 8);

            // Montant restant
            System.Windows.Forms.Label montantRestantLabel = new System.Windows.Forms.Label();
            montantRestantLabel.Text = "Montant restant :";
            montantRestantLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(montantRestantLabel, 0, 9);

            System.Windows.Forms.NumericUpDown montantRestantNumericUpDown = new System.Windows.Forms.NumericUpDown();
            montantRestantNumericUpDown.Name = "montantRestantNumericUpDown";
            montantRestantNumericUpDown.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            montantRestantNumericUpDown.DecimalPlaces = 2;
            montantRestantNumericUpDown.Maximum = 1000000;
            montantRestantNumericUpDown.ThousandsSeparator = true;
            montantRestantNumericUpDown.ReadOnly = true;
            montantRestantNumericUpDown.Enabled = false;
            mainPanel.Controls.Add(montantRestantNumericUpDown, 1, 9);

            // Statut
            System.Windows.Forms.Label statutLabel = new System.Windows.Forms.Label();
            statutLabel.Text = "Statut :";
            statutLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(statutLabel, 0, 10);

            this.statutComboBox = new System.Windows.Forms.ComboBox();
            this.statutComboBox.Name = "statutComboBox";
            this.statutComboBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            this.statutComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.statutComboBox.Items.Add("En attente");
            this.statutComboBox.Items.Add("Payée partiellement");
            this.statutComboBox.Items.Add("Payée");
            this.statutComboBox.Items.Add("En retard");
            this.statutComboBox.Items.Add("Annulée");
            this.statutComboBox.Items.Add("Litige");
            this.statutComboBox.SelectedIndex = 0;
            this.statutComboBox.Enabled = false; // Sera mis à jour dans le constructeur
            mainPanel.Controls.Add(this.statutComboBox, 1, 10);

            // Boutons
            System.Windows.Forms.FlowLayoutPanel buttonsPanel = new System.Windows.Forms.FlowLayoutPanel();
            buttonsPanel.FlowDirection = System.Windows.Forms.FlowDirection.RightToLeft;
            buttonsPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            buttonsPanel.Anchor = System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(buttonsPanel, 1, 11);
            mainPanel.SetColumnSpan(buttonsPanel, 2);

            System.Windows.Forms.Button cancelButton = new System.Windows.Forms.Button();
            cancelButton.Name = "cancelButton";
            cancelButton.Text = "Annuler";
            cancelButton.Size = new System.Drawing.Size(100, 30);
            cancelButton.Click += new System.EventHandler(this.CancelButton_Click);
            buttonsPanel.Controls.Add(cancelButton);

            System.Windows.Forms.Button saveButton = new System.Windows.Forms.Button();
            saveButton.Name = "saveButton";
            saveButton.Text = "Enregistrer";
            saveButton.Size = new System.Drawing.Size(100, 30);
            saveButton.Click += new System.EventHandler(this.SaveButton_Click);
            buttonsPanel.Controls.Add(saveButton);

            // Note sur les champs obligatoires
            System.Windows.Forms.Label noteLabel = new System.Windows.Forms.Label();
            noteLabel.Text = "* Champs obligatoires";
            noteLabel.AutoSize = true;
            noteLabel.ForeColor = System.Drawing.Color.Red;
            noteLabel.Location = new System.Drawing.Point(10, this.ClientSize.Height - 30);
            this.Controls.Add(noteLabel);
        }

        #endregion
    }
}
