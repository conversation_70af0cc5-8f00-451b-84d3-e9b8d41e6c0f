using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;

namespace RecouvreX.WinForms.Forms.Clients
{
    /// <summary>
    /// Formulaire de liste des scores de risque client
    /// </summary>
    public partial class ScoreRisqueClientListForm : Form
    {
        private readonly IScoreRisqueClientService _scoreRisqueClientService;
        private readonly IClientService _clientService;
        private readonly int _currentUserId;
        private List<ScoreRisqueClient> _scores = new List<ScoreRisqueClient>();
        private DataTable _scoresDataTable = new DataTable();
        private Dictionary<string, int> _distribution = new Dictionary<string, int>();

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="scoreRisqueClientService">Service de gestion des scores de risque client</param>
        /// <param name="clientService">Service de gestion des clients</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        public ScoreRisqueClientListForm(
            IScoreRisqueClientService scoreRisqueClientService,
            IClientService clientService,
            int currentUserId)
        {
            _scoreRisqueClientService = scoreRisqueClientService ?? throw new ArgumentNullException(nameof(scoreRisqueClientService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }



        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void ScoreRisqueClientListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Initialiser la table de données
                InitializeDataTable();

                // Charger les scores de risque
                await LoadScoresAsync();

                // Charger la distribution des catégories
                await LoadDistributionAsync();

                // Mettre à jour les graphiques
                UpdateCharts();

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des scores de risque client");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des scores de risque client : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Initialisation de la table de données
        /// </summary>
        private void InitializeDataTable()
        {
            _scoresDataTable = new DataTable();
            _scoresDataTable.Columns.Add("Id", typeof(int));
            _scoresDataTable.Columns.Add("ClientId", typeof(int));
            _scoresDataTable.Columns.Add("Client", typeof(string));
            _scoresDataTable.Columns.Add("Score", typeof(int));
            _scoresDataTable.Columns.Add("Categorie", typeof(string));
            _scoresDataTable.Columns.Add("CategorieDescription", typeof(string));
            _scoresDataTable.Columns.Add("DelaiMoyenPaiement", typeof(double));
            _scoresDataTable.Columns.Add("PourcentageFacturesRetard", typeof(double));
            _scoresDataTable.Columns.Add("MontantTotalRetard", typeof(decimal));
            _scoresDataTable.Columns.Add("NombreMoyenRelances", typeof(double));
            _scoresDataTable.Columns.Add("DateMiseAJour", typeof(DateTime));

            this.scoresDataGridView.DataSource = _scoresDataTable;

            // Configurer les colonnes
            this.scoresDataGridView.Columns["Id"].Visible = false;
            this.scoresDataGridView.Columns["ClientId"].Visible = false;
            this.scoresDataGridView.Columns["Client"].HeaderText = "Client";
            this.scoresDataGridView.Columns["Score"].HeaderText = "Score";
            this.scoresDataGridView.Columns["Categorie"].HeaderText = "Catégorie";
            this.scoresDataGridView.Columns["CategorieDescription"].HeaderText = "Description";
            this.scoresDataGridView.Columns["DelaiMoyenPaiement"].HeaderText = "Délai moyen (jours)";
            this.scoresDataGridView.Columns["PourcentageFacturesRetard"].HeaderText = "% Factures en retard";
            this.scoresDataGridView.Columns["MontantTotalRetard"].HeaderText = "Montant en retard";
            this.scoresDataGridView.Columns["NombreMoyenRelances"].HeaderText = "Nb moyen relances";
            this.scoresDataGridView.Columns["DateMiseAJour"].HeaderText = "Mise à jour";

            // Formater les colonnes
            this.scoresDataGridView.Columns["DelaiMoyenPaiement"].DefaultCellStyle.Format = "N1";
            this.scoresDataGridView.Columns["PourcentageFacturesRetard"].DefaultCellStyle.Format = "P1";
            this.scoresDataGridView.Columns["MontantTotalRetard"].DefaultCellStyle.Format = "C2";
            this.scoresDataGridView.Columns["NombreMoyenRelances"].DefaultCellStyle.Format = "N1";
            this.scoresDataGridView.Columns["DateMiseAJour"].DefaultCellStyle.Format = "dd/MM/yyyy HH:mm";
        }

        /// <summary>
        /// Chargement des scores de risque
        /// </summary>
        private async Task LoadScoresAsync()
        {
            try
            {
                // Récupérer les scores de risque
                _scores = (await _scoreRisqueClientService.GetAllAsync()).ToList();

                // Filtrer les scores si nécessaire
                ApplyFilters();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des scores de risque client");
                throw;
            }
        }

        /// <summary>
        /// Chargement de la distribution des catégories
        /// </summary>
        private async Task LoadDistributionAsync()
        {
            try
            {
                // Récupérer la distribution des catégories
                _distribution = await _scoreRisqueClientService.GetClientDistributionByCategorieAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement de la distribution des catégories");
                throw;
            }
        }

        /// <summary>
        /// Mise à jour des graphiques
        /// </summary>
        private void UpdateCharts()
        {
            try
            {
                // Mettre à jour le graphique de distribution des catégories
                this.distributionChart.Series["Distribution"].Points.Clear();

                int total = _distribution.Values.Sum();
                foreach (var kvp in _distribution.OrderBy(d => d.Key))
                {
                    var point = this.distributionChart.Series["Distribution"].Points.Add(kvp.Value);
                    point.LegendText = $"Catégorie {kvp.Key} - {ScoreRisqueClient.GetDescriptionCategorie(kvp.Key)}";
                    point.Label = $"{kvp.Value} ({(double)kvp.Value / total:P0})";
                    point.Color = ColorTranslator.FromHtml(ScoreRisqueClient.GetCouleurCategorie(kvp.Key));
                }

                // Mettre à jour le graphique de répartition des scores
                this.scoresChart.Series["Scores"].Points.Clear();

                // Regrouper les scores par tranches de 10
                var scoreGroups = _scores
                    .GroupBy(s => s.Score / 10)
                    .OrderBy(g => g.Key)
                    .Select(g => new { Tranche = g.Key, Count = g.Count() })
                    .ToList();

                foreach (var group in scoreGroups)
                {
                    int minScore = group.Tranche * 10;
                    int maxScore = minScore + 9;
                    var point = this.scoresChart.Series["Scores"].Points.Add(group.Count);
                    point.AxisLabel = $"{minScore}-{maxScore}";
                    point.Label = group.Count.ToString();

                    // Définir la couleur en fonction de la tranche
                    string categorie = "A";
                    if (minScore <= 20) categorie = "A";
                    else if (minScore <= 40) categorie = "B";
                    else if (minScore <= 60) categorie = "C";
                    else if (minScore <= 80) categorie = "D";
                    else categorie = "E";

                    point.Color = ColorTranslator.FromHtml(ScoreRisqueClient.GetCouleurCategorie(categorie));
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour des graphiques");
                throw;
            }
        }

        /// <summary>
        /// Application des filtres
        /// </summary>
        private void ApplyFilters()
        {
            try
            {
                // Récupérer les valeurs des filtres
                string categorieFilter = this.categorieComboBox.SelectedItem?.ToString() ?? "Toutes";
                string searchFilter = this.searchTextBox.Text?.Trim().ToLower() ?? string.Empty;

                // Filtrer les scores
                var filteredScores = _scores;

                if (categorieFilter != "Toutes")
                {
                    filteredScores = filteredScores.Where(s => s.Categorie == categorieFilter).ToList();
                }

                if (!string.IsNullOrEmpty(searchFilter))
                {
                    filteredScores = filteredScores.Where(s =>
                        s.Client?.RaisonSociale?.ToLower().Contains(searchFilter) == true ||
                        s.Client?.Code?.ToLower().Contains(searchFilter) == true
                    ).ToList();
                }

                // Mettre à jour la table de données
                _scoresDataTable.Clear();

                foreach (var score in filteredScores)
                {
                    _scoresDataTable.Rows.Add(
                        score.Id,
                        score.ClientId,
                        score.Client?.RaisonSociale,
                        score.Score,
                        score.Categorie,
                        ScoreRisqueClient.GetDescriptionCategorie(score.Categorie),
                        score.DelaiMoyenPaiement,
                        score.PourcentageFacturesRetard / 100, // Convertir en pourcentage pour l'affichage
                        score.MontantTotalRetard,
                        score.NombreMoyenRelances,
                        score.DateMiseAJour
                    );
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'application des filtres");
                throw;
            }
        }

        /// <summary>
        /// Gestion du changement de catégorie
        /// </summary>
        private void CategorieComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du changement de catégorie");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gestion de la touche Entrée dans la zone de recherche
        /// </summary>
        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                ApplyFilters();
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        /// <summary>
        /// Gestion du clic sur le bouton de recherche
        /// </summary>
        private void SearchButton_Click(object sender, EventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la recherche");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gestion du clic sur le bouton de mise à jour des scores
        /// </summary>
        private async void UpdateButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Demander confirmation
                var result = MessageBox.Show(
                    "Voulez-vous mettre à jour les scores de risque de tous les clients ? Cette opération peut prendre du temps.",
                    "Confirmation",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.No)
                    return;

                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Mettre à jour les scores
                int count = await _scoreRisqueClientService.CalculateAndUpdateAllScoresAsync(_currentUserId);

                // Recharger les données
                await LoadScoresAsync();
                await LoadDistributionAsync();
                UpdateCharts();

                // Restaurer le curseur
                this.Cursor = Cursors.Default;

                // Afficher un message de confirmation
                MessageBox.Show($"Les scores de risque de {count} clients ont été mis à jour avec succès.",
                    "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour des scores de risque");
                MessageBox.Show($"Une erreur s'est produite lors de la mise à jour des scores de risque : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Formatage des cellules du tableau
        /// </summary>
        private void ScoresDataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            var dataGridView = sender as DataGridView;
            if (dataGridView == null || e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            // Colorer la cellule de la catégorie en fonction de sa valeur
            if (dataGridView.Columns[e.ColumnIndex].Name == "Categorie" && e.Value != null)
            {
                string categorie = e.Value.ToString();
                e.CellStyle.BackColor = ColorTranslator.FromHtml(ScoreRisqueClient.GetCouleurCategorie(categorie));
                e.CellStyle.ForeColor = Color.White;
                e.CellStyle.Font = new Font(dataGridView.Font, FontStyle.Bold);
            }

            // Colorer la cellule du score en fonction de sa valeur
            if (dataGridView.Columns[e.ColumnIndex].Name == "Score" && e.Value != null)
            {
                int score = Convert.ToInt32(e.Value);
                string categorie = ScoreRisqueClient.GetCategorie(score);
                e.CellStyle.BackColor = ColorTranslator.FromHtml(ScoreRisqueClient.GetCouleurCategorie(categorie));
                e.CellStyle.ForeColor = Color.White;
                e.CellStyle.Font = new Font(dataGridView.Font, FontStyle.Bold);
            }
        }
    }
}
