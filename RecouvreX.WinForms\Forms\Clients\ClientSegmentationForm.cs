using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;

namespace RecouvreX.WinForms.Forms.Clients
{
    /// <summary>
    /// Formulaire de segmentation des clients
    /// </summary>
    public partial class ClientSegmentationForm : Form
    {
        private readonly IClientService _clientService;
        private readonly int _currentUserId;
        private List<Client> _clients = new List<Client>();
        private DataTable _clientsDataTable = new DataTable();
        private Dictionary<SegmentClient, int> _distribution = new Dictionary<SegmentClient, int>();

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="clientService">Service de gestion des clients</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        public ClientSegmentationForm(
            IClientService clientService,
            int currentUserId)
        {
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }



        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void ClientSegmentationForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Initialiser la table de données
                InitializeDataTable();

                // Charger les clients
                await LoadClientsAsync();

                // Charger la distribution des segments
                await LoadDistributionAsync();

                // Mettre à jour le graphique
                UpdateChart();

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des clients pour la segmentation");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des clients : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Initialisation de la table de données
        /// </summary>
        private void InitializeDataTable()
        {
            _clientsDataTable = new DataTable();
            _clientsDataTable.Columns.Add("Id", typeof(int));
            _clientsDataTable.Columns.Add("Code", typeof(string));
            _clientsDataTable.Columns.Add("RaisonSociale", typeof(string));
            _clientsDataTable.Columns.Add("Segment", typeof(string));
            _clientsDataTable.Columns.Add("SegmentEnum", typeof(SegmentClient));
            _clientsDataTable.Columns.Add("DateSegmentation", typeof(string));
            _clientsDataTable.Columns.Add("SegmentationManuelle", typeof(bool));
            _clientsDataTable.Columns.Add("CommentaireSegmentation", typeof(string));
            _clientsDataTable.Columns.Add("SoldeActuel", typeof(decimal));

            var dataGridView = this.Controls.Find("clientsDataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _clientsDataTable;

                // Configurer les colonnes
                dataGridView.Columns["Id"].Visible = false;
                dataGridView.Columns["SegmentEnum"].Visible = false;
                dataGridView.Columns["Code"].HeaderText = "Code";
                dataGridView.Columns["RaisonSociale"].HeaderText = "Raison sociale";
                dataGridView.Columns["Segment"].HeaderText = "Segment";
                dataGridView.Columns["DateSegmentation"].HeaderText = "Date de segmentation";
                dataGridView.Columns["SegmentationManuelle"].HeaderText = "Segmentation manuelle";
                dataGridView.Columns["CommentaireSegmentation"].HeaderText = "Commentaire";
                dataGridView.Columns["SoldeActuel"].HeaderText = "Solde actuel";
                dataGridView.Columns["SoldeActuel"].DefaultCellStyle.Format = "C2";
            }
        }

        /// <summary>
        /// Chargement des clients
        /// </summary>
        private async Task LoadClientsAsync()
        {
            // Récupérer tous les clients
            _clients = (await _clientService.GetAllAsync()).ToList();

            // Filtrer les clients selon le segment sélectionné
            var segmentComboBox = this.Controls.Find("segmentComboBox", true).FirstOrDefault() as ComboBox;
            var searchTextBox = this.Controls.Find("searchTextBox", true).FirstOrDefault() as TextBox;

            FilterClients(segmentComboBox?.SelectedIndex ?? 0, searchTextBox?.Text ?? "");
        }

        /// <summary>
        /// Filtrage des clients
        /// </summary>
        private void FilterClients(int segmentIndex, string searchTerm)
        {
            // Filtrer par segment
            IEnumerable<Client> filteredClients = _clients;

            switch (segmentIndex)
            {
                case 1: // A - Stratégique
                    filteredClients = filteredClients.Where(c => c.Segment == SegmentClient.A);
                    break;
                case 2: // B - Important
                    filteredClients = filteredClients.Where(c => c.Segment == SegmentClient.B);
                    break;
                case 3: // C - Standard
                    filteredClients = filteredClients.Where(c => c.Segment == SegmentClient.C);
                    break;
                case 4: // Non segmenté
                    filteredClients = filteredClients.Where(c => c.Segment == SegmentClient.NonSegmente);
                    break;
                default: // Tous
                    break;
            }

            // Filtrer par terme de recherche
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                searchTerm = searchTerm.ToLower();
                filteredClients = filteredClients.Where(c =>
                    c.RaisonSociale.ToLower().Contains(searchTerm) ||
                    c.Code.ToLower().Contains(searchTerm));
            }

            // Mettre à jour la table de données
            _clientsDataTable.Clear();
            foreach (var client in filteredClients)
            {
                string segmentText = GetSegmentText(client.Segment);
                string dateSegmentation = client.DateSegmentation.HasValue
                    ? client.DateSegmentation.Value.ToString("dd/MM/yyyy HH:mm")
                    : "-";

                _clientsDataTable.Rows.Add(
                    client.Id,
                    client.Code,
                    client.RaisonSociale,
                    segmentText,
                    client.Segment,
                    dateSegmentation,
                    client.SegmentationManuelle,
                    client.CommentaireSegmentation ?? "-",
                    client.SoldeActuel);
            }
        }

        /// <summary>
        /// Chargement de la distribution des segments
        /// </summary>
        private async Task LoadDistributionAsync()
        {
            _distribution = await _clientService.GetClientDistributionBySegmentAsync();

            // S'assurer que tous les segments sont présents
            foreach (SegmentClient segment in Enum.GetValues(typeof(SegmentClient)))
            {
                if (!_distribution.ContainsKey(segment))
                {
                    _distribution[segment] = 0;
                }
            }
        }

        /// <summary>
        /// Mise à jour du graphique
        /// </summary>
        private void UpdateChart()
        {
            var chart = this.Controls.Find("distributionChart", true).FirstOrDefault() as Chart;
            if (chart != null)
            {
                chart.Series["Distribution"].Points.Clear();

                int total = _distribution.Values.Sum();
                if (total > 0)
                {
                    // Ajouter les points pour chaque segment
                    foreach (var kvp in _distribution.OrderBy(d => d.Key))
                    {
                        string segmentText = GetSegmentText(kvp.Key);
                        double percentage = (double)kvp.Value / total;

                        var point = chart.Series["Distribution"].Points.Add(kvp.Value);
                        point.LegendText = segmentText;
                        point.Label = $"{kvp.Value} ({percentage:P0})";

                        // Définir la couleur en fonction du segment
                        switch (kvp.Key)
                        {
                            case SegmentClient.A:
                                point.Color = Color.Green;
                                break;
                            case SegmentClient.B:
                                point.Color = Color.Blue;
                                break;
                            case SegmentClient.C:
                                point.Color = Color.Orange;
                                break;
                            case SegmentClient.NonSegmente:
                                point.Color = Color.Gray;
                                break;
                        }
                    }
                }
                else
                {
                    // Aucun client
                    var point = chart.Series["Distribution"].Points.Add(1);
                    point.LegendText = "Aucun client";
                    point.Label = "Aucun client";
                    point.Color = Color.Gray;
                }
            }
        }

        /// <summary>
        /// Obtient le texte correspondant à un segment
        /// </summary>
        private string GetSegmentText(SegmentClient segment)
        {
            switch (segment)
            {
                case SegmentClient.A:
                    return "A - Stratégique";
                case SegmentClient.B:
                    return "B - Important";
                case SegmentClient.C:
                    return "C - Standard";
                case SegmentClient.NonSegmente:
                    return "Non segmenté";
                default:
                    return segment.ToString();
            }
        }

        /// <summary>
        /// Événement de changement de sélection dans la liste déroulante des segments
        /// </summary>
        private void SegmentComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            var segmentComboBox = sender as ComboBox;
            var searchTextBox = this.Controls.Find("searchTextBox", true).FirstOrDefault() as TextBox;

            if (segmentComboBox != null)
            {
                FilterClients(segmentComboBox.SelectedIndex, searchTextBox?.Text ?? "");
            }
        }

        /// <summary>
        /// Événement de touche enfoncée dans la zone de recherche
        /// </summary>
        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                e.SuppressKeyPress = true;

                var segmentComboBox = this.Controls.Find("segmentComboBox", true).FirstOrDefault() as ComboBox;
                var searchTextBox = sender as TextBox;

                if (searchTextBox != null)
                {
                    FilterClients(segmentComboBox?.SelectedIndex ?? 0, searchTextBox.Text);
                }
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton de recherche
        /// </summary>
        private void SearchButton_Click(object sender, EventArgs e)
        {
            var segmentComboBox = this.Controls.Find("segmentComboBox", true).FirstOrDefault() as ComboBox;
            var searchTextBox = this.Controls.Find("searchTextBox", true).FirstOrDefault() as TextBox;

            FilterClients(segmentComboBox?.SelectedIndex ?? 0, searchTextBox?.Text ?? "");
        }

        /// <summary>
        /// Événement de clic sur le bouton de segmentation automatique
        /// </summary>
        private async void SegmentButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Demander confirmation
                var result = MessageBox.Show(
                    "Voulez-vous lancer la segmentation automatique de tous les clients ?\n\n" +
                    "Note : Les clients déjà segmentés manuellement ne seront pas modifiés.",
                    "Confirmation",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Afficher un indicateur de chargement
                    this.Cursor = Cursors.WaitCursor;

                    // Lancer la segmentation automatique
                    int count = await _clientService.SegmenterClientsAutomatiquementAsync(_currentUserId);

                    // Recharger les données
                    await LoadClientsAsync();
                    await LoadDistributionAsync();
                    UpdateChart();

                    // Restaurer le curseur
                    this.Cursor = Cursors.Default;

                    // Afficher un message de confirmation
                    MessageBox.Show(
                        $"{count} clients ont été segmentés automatiquement.",
                        "Segmentation terminée",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la segmentation automatique des clients");
                MessageBox.Show(
                    $"Une erreur s'est produite lors de la segmentation automatique : {ex.Message}",
                    "Erreur",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Événement de formatage des cellules de la grille
        /// </summary>
        private void ClientsDataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            var dataGridView = sender as DataGridView;
            if (dataGridView != null && e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                // Colorer la cellule du segment en fonction de sa valeur
                if (dataGridView.Columns[e.ColumnIndex].Name == "Segment")
                {
                    var segmentEnum = (SegmentClient)dataGridView.Rows[e.RowIndex].Cells["SegmentEnum"].Value;

                    switch (segmentEnum)
                    {
                        case SegmentClient.A:
                            e.CellStyle.BackColor = Color.LightGreen;
                            e.CellStyle.ForeColor = Color.DarkGreen;
                            e.CellStyle.Font = new Font(dataGridView.Font, FontStyle.Bold);
                            break;
                        case SegmentClient.B:
                            e.CellStyle.BackColor = Color.LightBlue;
                            e.CellStyle.ForeColor = Color.DarkBlue;
                            break;
                        case SegmentClient.C:
                            e.CellStyle.BackColor = Color.LightSalmon;
                            e.CellStyle.ForeColor = Color.DarkRed;
                            break;
                        case SegmentClient.NonSegmente:
                            e.CellStyle.BackColor = Color.LightGray;
                            e.CellStyle.ForeColor = Color.DimGray;
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// Événement de double-clic sur une cellule de la grille
        /// </summary>
        private async void ClientsDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            var dataGridView = sender as DataGridView;
            if (dataGridView != null && e.RowIndex >= 0)
            {
                // Récupérer l'ID du client
                int clientId = (int)dataGridView.Rows[e.RowIndex].Cells["Id"].Value;
                string raisonSociale = (string)dataGridView.Rows[e.RowIndex].Cells["RaisonSociale"].Value;
                SegmentClient currentSegment = (SegmentClient)dataGridView.Rows[e.RowIndex].Cells["SegmentEnum"].Value;

                // Afficher la boîte de dialogue de modification du segment
                using (var form = new ClientSegmentEditForm(clientId, raisonSociale, currentSegment))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        try
                        {
                            // Mettre à jour le segment du client
                            await _clientService.UpdateSegmentAsync(
                                clientId,
                                form.SelectedSegment,
                                form.Commentaire,
                                _currentUserId);

                            // Recharger les données
                            await LoadClientsAsync();
                            await LoadDistributionAsync();
                            UpdateChart();

                            MessageBox.Show(
                                "Le segment du client a été mis à jour avec succès.",
                                "Mise à jour réussie",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information);
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, "Erreur lors de la mise à jour du segment du client {ClientId}", clientId);
                            MessageBox.Show(
                                $"Une erreur s'est produite lors de la mise à jour du segment : {ex.Message}",
                                "Erreur",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Error);
                        }
                    }
                }
            }
        }
    }
}
