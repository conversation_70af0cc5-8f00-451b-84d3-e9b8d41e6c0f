using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les paiements
    /// </summary>
    public class PaiementRepository : BaseRepository<Paiement>, IPaiementRepository
    {
        private readonly IFactureRepository _factureRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        /// <param name="factureRepository">Repository des factures</param>
        public PaiementRepository(DatabaseConnection dbConnection, IFactureRepository factureRepository)
            : base(dbConnection, "Paiements")
        {
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
        }

        /// <summary>
        /// Récupère un paiement par sa référence
        /// </summary>
        /// <param name="reference">Référence du paiement</param>
        /// <returns>Paiement trouvé ou null</returns>
        public async Task<Paiement> GetByReferenceAsync(string reference)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Paiements WHERE Reference = @Reference AND EstActif = 1";
                return await connection.QueryFirstOrDefaultAsync<Paiement>(query, new { Reference = reference });
            }
        }

        /// <summary>
        /// Récupère un paiement avec ses factures associées
        /// </summary>
        /// <param name="paiementId">Identifiant du paiement</param>
        /// <returns>Paiement avec ses factures</returns>
        public async Task<Paiement> GetWithFacturesAsync(int paiementId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT p.*, fp.*, f.*
                    FROM Paiements p
                    LEFT JOIN FacturePaiements fp ON p.Id = fp.PaiementId
                    LEFT JOIN Factures f ON fp.FactureId = f.Id
                    WHERE p.Id = @PaiementId AND p.EstActif = 1";

                Paiement paiement = null;
                var facturePaiements = new Dictionary<int, FacturePaiement>();

                var results = await connection.QueryAsync<Paiement, FacturePaiement, Facture, Paiement>(
                    query,
                    (p, fp, f) =>
                    {
                        if (paiement == null)
                        {
                            paiement = p;
                            paiement.FacturePaiements = new List<FacturePaiement>();
                        }

                        if (fp != null && f != null)
                        {
                            fp.Paiement = paiement;
                            fp.Facture = f;
                            paiement.FacturePaiements.Add(fp);
                        }

                        return paiement;
                    },
                    new { PaiementId = paiementId },
                    splitOn: "Id,Id");

                return results.FirstOrDefault();
            }
        }

        /// <summary>
        /// Récupère tous les paiements d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des paiements du client</returns>
        public async Task<IEnumerable<Paiement>> GetByClientIdAsync(int clientId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Paiements WHERE ClientId = @ClientId AND EstActif = 1";
                return await connection.QueryAsync<Paiement>(query, new { ClientId = clientId });
            }
        }

        /// <summary>
        /// Récupère tous les paiements associés à une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des paiements associés à la facture</returns>
        public async Task<IEnumerable<Paiement>> GetByFactureIdAsync(int factureId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT p.*
                    FROM Paiements p
                    INNER JOIN FacturePaiements fp ON p.Id = fp.PaiementId
                    WHERE fp.FactureId = @FactureId AND p.EstActif = 1";

                return await connection.QueryAsync<Paiement>(query, new { FactureId = factureId });
            }
        }

        /// <summary>
        /// Récupère les paiements par mode de paiement
        /// </summary>
        /// <param name="modePaiement">Mode de paiement</param>
        /// <returns>Liste des paiements effectués avec le mode spécifié</returns>
        public async Task<IEnumerable<Paiement>> GetByModePaiementAsync(string modePaiement)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Paiements WHERE ModePaiement = @ModePaiement AND EstActif = 1";
                return await connection.QueryAsync<Paiement>(query, new { ModePaiement = modePaiement });
            }
        }

        /// <summary>
        /// Récupère les paiements par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des paiements effectués dans la période spécifiée</returns>
        public async Task<IEnumerable<Paiement>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT *
                    FROM Paiements
                    WHERE DatePaiement BETWEEN @DateDebut AND @DateFin
                        AND EstActif = 1";

                return await connection.QueryAsync<Paiement>(query, new { DateDebut = dateDebut, DateFin = dateFin });
            }
        }

        /// <summary>
        /// Associe un paiement à une facture
        /// </summary>
        /// <param name="paiementId">Identifiant du paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="montantAffecte">Montant affecté à la facture</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'association a réussi, sinon False</returns>
        public async Task<bool> AssociateToFactureAsync(int paiementId, int factureId, decimal montantAffecte, int userId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Vérifier si l'association existe déjà
                var checkQuery = @"
                    SELECT COUNT(*)
                    FROM FacturePaiements
                    WHERE PaiementId = @PaiementId AND FactureId = @FactureId";

                var exists = await connection.ExecuteScalarAsync<int>(checkQuery, new { PaiementId = paiementId, FactureId = factureId }) > 0;

                if (exists)
                {
                    // Mettre à jour l'association existante
                    var updateQuery = @"
                        UPDATE FacturePaiements
                        SET MontantAffecte = @MontantAffecte,
                            DateModification = @DateModification,
                            ModifiePar = @ModifiePar
                        WHERE PaiementId = @PaiementId AND FactureId = @FactureId";

                    var parameters = new
                    {
                        PaiementId = paiementId,
                        FactureId = factureId,
                        MontantAffecte = montantAffecte,
                        DateModification = DateTime.Now,
                        ModifiePar = userId
                    };

                    await connection.ExecuteAsync(updateQuery, parameters);
                }
                else
                {
                    // Créer une nouvelle association
                    var insertQuery = @"
                        INSERT INTO FacturePaiements (FactureId, PaiementId, MontantAffecte, DateCreation, CreePar, EstActif)
                        VALUES (@FactureId, @PaiementId, @MontantAffecte, @DateCreation, @CreePar, 1)";

                    var parameters = new
                    {
                        FactureId = factureId,
                        PaiementId = paiementId,
                        MontantAffecte = montantAffecte,
                        DateCreation = DateTime.Now,
                        CreePar = userId
                    };

                    await connection.ExecuteAsync(insertQuery, parameters);
                }

                // Mettre à jour le montant payé de la facture
                var totalPaye = await GetTotalPaidForInvoiceAsync(factureId);
                await _factureRepository.UpdateMontantPayeAsync(factureId, totalPaye, userId);

                return true;
            }
        }

        /// <summary>
        /// Dissocie un paiement d'une facture
        /// </summary>
        /// <param name="paiementId">Identifiant du paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>True si la dissociation a réussi, sinon False</returns>
        public async Task<bool> DissociateFromFactureAsync(int paiementId, int factureId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    DELETE FROM FacturePaiements
                    WHERE PaiementId = @PaiementId AND FactureId = @FactureId";

                var parameters = new
                {
                    PaiementId = paiementId,
                    FactureId = factureId
                };

                var result = await connection.ExecuteAsync(query, parameters);
                
                // Mettre à jour le montant payé de la facture
                var totalPaye = await GetTotalPaidForInvoiceAsync(factureId);
                await _factureRepository.UpdateMontantPayeAsync(factureId, totalPaye, 1); // Utilisateur système

                return result > 0;
            }
        }

        /// <summary>
        /// Récupère le montant total payé pour une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Montant total payé</returns>
        private async Task<decimal> GetTotalPaidForInvoiceAsync(int factureId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT COALESCE(SUM(MontantAffecte), 0)
                    FROM FacturePaiements
                    WHERE FactureId = @FactureId";

                return await connection.ExecuteScalarAsync<decimal>(query, new { FactureId = factureId });
            }
        }
    }
}
