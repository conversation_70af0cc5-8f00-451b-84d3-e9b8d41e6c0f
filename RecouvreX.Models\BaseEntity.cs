using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Classe de base pour toutes les entités du système
    /// </summary>
    public abstract class BaseEntity
    {
        /// <summary>
        /// Identifiant unique de l'entité
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Date de création de l'entité
        /// </summary>
        public DateTime DateCreation { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a créé l'entité
        /// </summary>
        public int CreePar { get; set; }

        /// <summary>
        /// Date de dernière modification de l'entité
        /// </summary>
        public DateTime? DateModification { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a modifié l'entité
        /// </summary>
        public int? ModifiePar { get; set; }

        /// <summary>
        /// Indique si l'entité est active ou supprimée logiquement
        /// </summary>
        public bool EstActif { get; set; } = true;
    }
}
