using System;
using System.Collections.Generic;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un paiement dans le système
    /// </summary>
    public class Paiement : BaseEntity
    {
        /// <summary>
        /// Numéro de référence du paiement
        /// </summary>
        public string Reference { get; set; } = string.Empty;

        /// <summary>
        /// Identifiant du client qui a effectué le paiement
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// Client associé (navigation property)
        /// </summary>
        public Client Client { get; set; } = null!;

        /// <summary>
        /// Date du paiement
        /// </summary>
        public DateTime DatePaiement { get; set; }

        /// <summary>
        /// Montant du paiement
        /// </summary>
        public decimal Montant { get; set; }

        /// <summary>
        /// Mode de paiement (Chèque, Virement, Espèces, etc.)
        /// </summary>
        public string ModePaiement { get; set; } = string.Empty;

        /// <summary>
        /// Numéro de référence bancaire (numéro de chèque, référence de virement, etc.)
        /// </summary>
        public string ReferenceBancaire { get; set; } = string.Empty;

        /// <summary>
        /// Notes ou commentaires sur le paiement
        /// </summary>
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// Liste des associations facture-paiement (navigation property)
        /// </summary>
        public ICollection<FacturePaiement> FacturePaiements { get; set; } = new List<FacturePaiement>();

        /// <summary>
        /// Liste des documents associés à ce paiement (navigation property)
        /// </summary>
        public ICollection<Document> Documents { get; set; } = new List<Document>();

        /// <summary>
        /// Montant déjà utilisé du paiement (calculé à partir des associations facture-paiement)
        /// </summary>
        public decimal MontantUtilise
        {
            get
            {
                if (FacturePaiements == null || !FacturePaiements.Any())
                    return 0;

                return FacturePaiements.Sum(fp => fp.MontantAffecte);
            }
        }

        /// <summary>
        /// Montant disponible du paiement (calculé à partir du montant total et du montant utilisé)
        /// </summary>
        public decimal MontantDisponible
        {
            get
            {
                return Montant - MontantUtilise;
            }
        }
    }
}
