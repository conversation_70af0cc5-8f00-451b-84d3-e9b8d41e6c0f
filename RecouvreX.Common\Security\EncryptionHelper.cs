using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace RecouvreX.Common.Security
{
    /// <summary>
    /// Classe d'aide pour le chiffrement et le déchiffrement des données
    /// </summary>
    public static class EncryptionHelper
    {
        // Clé de chiffrement (à remplacer par une clé plus sécurisée en production)
        private static readonly byte[] Key = Encoding.UTF8.GetBytes("RecouvreXSecretKey12345678901234");
        
        // Vecteur d'initialisation (IV)
        private static readonly byte[] IV = Encoding.UTF8.GetBytes("RecouvreXInitVect");

        /// <summary>
        /// Chiffre une chaîne de caractères
        /// </summary>
        /// <param name="plainText">Texte à chiffrer</param>
        /// <returns>Texte chiffré en Base64</returns>
        public static string Encrypt(string plainText)
        {
            if (string.IsNullOrEmpty(plainText))
                return plainText;

            try
            {
                using (var aes = Aes.Create())
                {
                    aes.Key = Key;
                    aes.IV = IV;

                    var encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                    using (var memoryStream = new MemoryStream())
                    {
                        using (var cryptoStream = new CryptoStream(memoryStream, encryptor, CryptoStreamMode.Write))
                        {
                            using (var streamWriter = new StreamWriter(cryptoStream))
                            {
                                streamWriter.Write(plainText);
                            }
                            return Convert.ToBase64String(memoryStream.ToArray());
                        }
                    }
                }
            }
            catch (Exception)
            {
                // En cas d'erreur, retourner le texte non chiffré
                return plainText;
            }
        }

        /// <summary>
        /// Déchiffre une chaîne de caractères
        /// </summary>
        /// <param name="cipherText">Texte chiffré en Base64</param>
        /// <returns>Texte déchiffré</returns>
        public static string Decrypt(string cipherText)
        {
            if (string.IsNullOrEmpty(cipherText))
                return cipherText;

            try
            {
                // Vérifier si le texte est au format ENC:xxx
                if (cipherText.StartsWith("ENC:"))
                {
                    cipherText = cipherText.Substring(4);
                }

                byte[] buffer = Convert.FromBase64String(cipherText);

                using (var aes = Aes.Create())
                {
                    aes.Key = Key;
                    aes.IV = IV;

                    var decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                    using (var memoryStream = new MemoryStream(buffer))
                    {
                        using (var cryptoStream = new CryptoStream(memoryStream, decryptor, CryptoStreamMode.Read))
                        {
                            using (var streamReader = new StreamReader(cryptoStream))
                            {
                                return streamReader.ReadToEnd();
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // En cas d'erreur, retourner le texte chiffré
                return cipherText;
            }
        }

        /// <summary>
        /// Déchiffre une chaîne de connexion
        /// </summary>
        /// <param name="connectionString">Chaîne de connexion potentiellement chiffrée</param>
        /// <returns>Chaîne de connexion déchiffrée</returns>
        public static string DecryptConnectionString(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return connectionString;

            try
            {
                // Vérifier si la chaîne de connexion contient un mot de passe chiffré
                var builder = new Microsoft.Data.SqlClient.SqlConnectionStringBuilder(connectionString);
                
                if (!string.IsNullOrEmpty(builder.Password) && builder.Password.StartsWith("ENC:"))
                {
                    // Déchiffrer le mot de passe
                    builder.Password = Decrypt(builder.Password.Substring(4));
                    return builder.ConnectionString;
                }

                return connectionString;
            }
            catch (Exception)
            {
                // En cas d'erreur, retourner la chaîne de connexion d'origine
                return connectionString;
            }
        }
    }
}
