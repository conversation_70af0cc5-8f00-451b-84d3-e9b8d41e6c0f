using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des catégories de litiges
    /// </summary>
    public interface ICategorieLitigeRepository : IRepository<CategorieLitige>
    {
        /// <summary>
        /// Récupère une catégorie de litige par son nom
        /// </summary>
        /// <param name="nom">Nom de la catégorie</param>
        /// <returns>Catégorie de litige trouvée ou null</returns>
        Task<CategorieLitige> GetByNomAsync(string nom);

        /// <summary>
        /// Récupère toutes les catégories de litiges avec le nombre de litiges associés
        /// </summary>
        /// <returns>Liste des catégories de litiges avec le nombre de litiges</returns>
        Task<IEnumerable<CategorieLitige>> GetAllWithCountAsync();
    }
}
