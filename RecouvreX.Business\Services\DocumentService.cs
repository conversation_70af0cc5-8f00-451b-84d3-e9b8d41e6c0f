using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des documents
    /// </summary>
    public class DocumentService : IDocumentService
    {
        private readonly IDocumentRepository _documentRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="documentRepository">Repository des documents</param>
        /// <param name="utilisateurRepository">Repository des utilisateurs</param>
        /// <param name="journalAuditRepository">Repository du journal d'audit</param>
        public DocumentService(
            IDocumentRepository documentRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _documentRepository = documentRepository ?? throw new ArgumentNullException(nameof(documentRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère tous les documents
        /// </summary>
        /// <returns>Liste des documents</returns>
        public async Task<IEnumerable<Document>> GetAllAsync()
        {
            return await _documentRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère un document par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du document</param>
        /// <returns>Document trouvé ou null</returns>
        public async Task<Document> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _documentRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère tous les documents associés à une entité
        /// </summary>
        /// <param name="typeEntite">Type d'entité (Facture, Paiement, Relance, Client)</param>
        /// <param name="entiteId">Identifiant de l'entité</param>
        /// <returns>Liste des documents associés à l'entité</returns>
        public async Task<IEnumerable<Document>> GetByEntityAsync(string typeEntite, int entiteId)
        {
            if (string.IsNullOrEmpty(typeEntite) || entiteId <= 0)
                return new List<Document>();

            return await _documentRepository.GetByEntityAsync(typeEntite, entiteId);
        }

        /// <summary>
        /// Récupère les documents par type
        /// </summary>
        /// <param name="type">Type de document</param>
        /// <returns>Liste des documents du type spécifié</returns>
        public async Task<IEnumerable<Document>> GetByTypeAsync(string type)
        {
            if (string.IsNullOrEmpty(type))
                return new List<Document>();

            return await _documentRepository.GetByTypeAsync(type);
        }

        /// <summary>
        /// Récupère un document avec son contenu
        /// </summary>
        /// <param name="documentId">Identifiant du document</param>
        /// <returns>Document avec son contenu</returns>
        public async Task<Document?> GetWithContentAsync(int documentId)
        {
            if (documentId <= 0)
                return null;

            return await _documentRepository.GetWithContentAsync(documentId);
        }

        /// <summary>
        /// Recherche des documents par nom
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des documents correspondant au terme de recherche</returns>
        public async Task<IEnumerable<Document>> SearchByNameAsync(string searchTerm)
        {
            if (string.IsNullOrEmpty(searchTerm))
                return new List<Document>();

            return await _documentRepository.SearchByNameAsync(searchTerm);
        }

        /// <summary>
        /// Crée un nouveau document à partir d'un fichier
        /// </summary>
        /// <param name="filePath">Chemin du fichier</param>
        /// <param name="nom">Nom du document</param>
        /// <param name="type">Type du document</param>
        /// <param name="description">Description du document</param>
        /// <param name="typeEntite">Type d'entité associée</param>
        /// <param name="entiteId">Identifiant de l'entité associée</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Document créé avec son identifiant généré</returns>
        public async Task<Document> CreateFromFileAsync(string filePath, string nom, string type, string description, string typeEntite, int entiteId, int creePar)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                throw new ArgumentException($"Le fichier '{filePath}' n'existe pas");

            if (string.IsNullOrEmpty(nom))
                nom = Path.GetFileName(filePath);

            if (string.IsNullOrEmpty(type))
                type = TypeDocument.Autre;

            if (string.IsNullOrEmpty(typeEntite) || entiteId <= 0 || creePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la création d'un document");

            // Lire le contenu du fichier
            byte[] contenu = await File.ReadAllBytesAsync(filePath);

            // Déterminer le type MIME
            string mimeType = GetMimeType(filePath);

            // Créer le document
            var document = new Document
            {
                Nom = nom,
                Type = type,
                MimeType = mimeType,
                Taille = contenu.Length,
                Contenu = contenu,
                Description = description,
                TypeEntite = typeEntite,
                EntiteId = entiteId
            };

            // Ajouter le document
            var createdDocument = await _documentRepository.AddAsync(document, creePar);

            // Journaliser la création
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = creePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(creePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Creation,
                TypeEntite = "Document",
                EntiteId = createdDocument.Id,
                Description = $"Création du document '{createdDocument.Nom}' de type '{createdDocument.Type}' pour {typeEntite} #{entiteId}",
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdDocument.Id,
                    createdDocument.Nom,
                    createdDocument.Type,
                    createdDocument.MimeType,
                    createdDocument.Taille,
                    createdDocument.Description,
                    createdDocument.TypeEntite,
                    createdDocument.EntiteId
                })
            });

            return createdDocument;
        }

        /// <summary>
        /// Crée un nouveau document à partir d'un flux de données
        /// </summary>
        /// <param name="stream">Flux de données</param>
        /// <param name="nom">Nom du document</param>
        /// <param name="type">Type du document</param>
        /// <param name="mimeType">Type MIME du document</param>
        /// <param name="description">Description du document</param>
        /// <param name="typeEntite">Type d'entité associée</param>
        /// <param name="entiteId">Identifiant de l'entité associée</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Document créé avec son identifiant généré</returns>
        public async Task<Document> CreateFromStreamAsync(Stream stream, string nom, string type, string mimeType, string description, string typeEntite, int entiteId, int creePar)
        {
            if (stream == null)
                throw new ArgumentNullException(nameof(stream));

            if (string.IsNullOrEmpty(nom))
                throw new ArgumentException("Le nom du document ne peut pas être vide");

            if (string.IsNullOrEmpty(type))
                type = TypeDocument.Autre;

            if (string.IsNullOrEmpty(mimeType))
                mimeType = "application/octet-stream";

            if (string.IsNullOrEmpty(typeEntite) || entiteId <= 0 || creePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la création d'un document");

            // Lire le contenu du flux
            using (var memoryStream = new MemoryStream())
            {
                await stream.CopyToAsync(memoryStream);
                byte[] contenu = memoryStream.ToArray();

                // Créer le document
                var document = new Document
                {
                    Nom = nom,
                    Type = type,
                    MimeType = mimeType,
                    Taille = contenu.Length,
                    Contenu = contenu,
                    Description = description,
                    TypeEntite = typeEntite,
                    EntiteId = entiteId
                };

                // Ajouter le document
                var createdDocument = await _documentRepository.AddAsync(document, creePar);

                // Journaliser la création
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    DateAction = DateTime.Now,
                    UtilisateurId = creePar,
                    NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(creePar))?.NomUtilisateur ?? "Système",
                    TypeAction = TypeAudit.Creation,
                    TypeEntite = "Document",
                    EntiteId = createdDocument.Id,
                    Description = $"Création du document '{createdDocument.Nom}' de type '{createdDocument.Type}' pour {typeEntite} #{entiteId}",
                    DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                    {
                        createdDocument.Id,
                        createdDocument.Nom,
                        createdDocument.Type,
                        createdDocument.MimeType,
                        createdDocument.Taille,
                        createdDocument.Description,
                        createdDocument.TypeEntite,
                        createdDocument.EntiteId
                    })
                });

                return createdDocument;
            }
        }

        /// <summary>
        /// Met à jour un document existant
        /// </summary>
        /// <param name="document">Document à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Document mis à jour</returns>
        public async Task<Document> UpdateAsync(Document document, int modifiePar)
        {
            if (document == null || document.Id <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la mise à jour d'un document");

            // Récupérer le document existant
            var existingDocument = await _documentRepository.GetByIdAsync(document.Id);
            if (existingDocument == null)
                throw new InvalidOperationException($"Le document avec l'ID {document.Id} n'existe pas");

            // Conserver le contenu existant si non fourni
            if (document.Contenu == null || document.Contenu.Length == 0)
            {
                document.Contenu = existingDocument.Contenu;
                document.Taille = existingDocument.Taille;
                document.MimeType = existingDocument.MimeType;
            }

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Document",
                EntiteId = document.Id,
                Description = $"Modification du document '{document.Nom}' de type '{document.Type}' pour {document.TypeEntite} #{document.EntiteId}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingDocument.Id,
                    existingDocument.Nom,
                    existingDocument.Type,
                    existingDocument.MimeType,
                    existingDocument.Taille,
                    existingDocument.Description,
                    existingDocument.TypeEntite,
                    existingDocument.EntiteId
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    document.Id,
                    document.Nom,
                    document.Type,
                    document.MimeType,
                    document.Taille,
                    document.Description,
                    document.TypeEntite,
                    document.EntiteId
                })
            });

            // Mettre à jour le document
            return await _documentRepository.UpdateAsync(document, modifiePar);
        }

        /// <summary>
        /// Supprime un document
        /// </summary>
        /// <param name="id">Identifiant du document à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0 || supprimePar <= 0)
                return false;

            // Récupérer le document à supprimer
            var document = await _documentRepository.GetByIdAsync(id);
            if (document == null)
                return false;

            // Journaliser la suppression
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = supprimePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(supprimePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Suppression,
                TypeEntite = "Document",
                EntiteId = id,
                Description = $"Suppression du document '{document.Nom}' de type '{document.Type}' pour {document.TypeEntite} #{document.EntiteId}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    document.Id,
                    document.Nom,
                    document.Type,
                    document.MimeType,
                    document.Taille,
                    document.Description,
                    document.TypeEntite,
                    document.EntiteId
                })
            });

            // Supprimer le document
            return await _documentRepository.DeleteAsync(id, supprimePar);
        }

        /// <summary>
        /// Enregistre le contenu d'un document dans un fichier
        /// </summary>
        /// <param name="documentId">Identifiant du document</param>
        /// <param name="filePath">Chemin du fichier de destination</param>
        /// <returns>True si l'enregistrement a réussi, sinon False</returns>
        public async Task<bool> SaveToFileAsync(int documentId, string filePath)
        {
            if (documentId <= 0 || string.IsNullOrEmpty(filePath))
                return false;

            // Récupérer le document avec son contenu
            var document = await _documentRepository.GetWithContentAsync(documentId);
            if (document == null || document.Contenu == null || document.Contenu.Length == 0)
                return false;

            try
            {
                // Créer le répertoire de destination si nécessaire
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    Directory.CreateDirectory(directory);

                // Enregistrer le contenu dans le fichier
                await File.WriteAllBytesAsync(filePath, document.Contenu);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Récupère le contenu d'un document sous forme de flux de données
        /// </summary>
        /// <param name="documentId">Identifiant du document</param>
        /// <returns>Flux de données contenant le document</returns>
        public async Task<Stream> GetContentAsStreamAsync(int documentId)
        {
            if (documentId <= 0)
                return null;

            // Récupérer le document avec son contenu
            var document = await _documentRepository.GetWithContentAsync(documentId);
            if (document == null || document.Contenu == null || document.Contenu.Length == 0)
                return null;

            // Créer un flux de données à partir du contenu
            return new MemoryStream(document.Contenu);
        }

        /// <summary>
        /// Détermine le type MIME d'un fichier en fonction de son extension
        /// </summary>
        /// <param name="filePath">Chemin du fichier</param>
        /// <returns>Type MIME du fichier</returns>
        private string GetMimeType(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            
            return extension switch
            {
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".ppt" => "application/vnd.ms-powerpoint",
                ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                ".txt" => "text/plain",
                ".csv" => "text/csv",
                ".xml" => "application/xml",
                ".json" => "application/json",
                ".jpg" => "image/jpeg",
                ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".zip" => "application/zip",
                ".rar" => "application/x-rar-compressed",
                ".7z" => "application/x-7z-compressed",
                _ => "application/octet-stream"
            };
        }
    }
}
