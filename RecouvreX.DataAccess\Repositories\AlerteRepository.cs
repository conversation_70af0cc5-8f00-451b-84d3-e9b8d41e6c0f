using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour la gestion des alertes
    /// </summary>
    public class AlerteRepository : BaseRepository<Alerte>, IAlerteRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public AlerteRepository(DatabaseConnection dbConnection) : base(dbConnection, "Alertes")
        {
        }

        /// <summary>
        /// Récupère les alertes d'un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des alertes de l'utilisateur</returns>
        public async Task<IEnumerable<Alerte>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE UtilisateurId = @UtilisateurId AND EstActif = 1";
                return await connection.QueryAsync<Alerte>(query, new { UtilisateurId = utilisateurId });
            }
        }

        /// <summary>
        /// Met à jour la date de dernière notification d'une alerte
        /// </summary>
        /// <param name="alerteId">Identifiant de l'alerte</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateDerniereNotificationAsync(int alerteId, int utilisateurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $@"
                    UPDATE {_tableName}
                    SET DerniereNotification = @DerniereNotification,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id AND UtilisateurId = @UtilisateurId AND EstActif = 1";

                var parameters = new
                {
                    Id = alerteId,
                    UtilisateurId = utilisateurId,
                    DerniereNotification = DateTime.Now,
                    DateModification = DateTime.Now,
                    ModifiePar = utilisateurId
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }
    }
}
