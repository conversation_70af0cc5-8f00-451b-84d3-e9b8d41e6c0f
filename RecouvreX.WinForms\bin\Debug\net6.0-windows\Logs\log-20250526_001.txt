2025-05-26 00:05:53.375 +01:00 [INF] Démarrage de l'application RecouvreX
2025-05-26 00:05:53.973 +01:00 [ERR] Erreur lors du test de connexion à la base de données
System.TypeInitializationException: The type initializer for 'Microsoft.Data.SqlClient.SqlConnection' threw an exception.
 ---> System.TypeInitializationException: The type initializer for 'Microsoft.Data.SqlClient.SqlConnectionFactory' threw an exception.
 ---> System.TypeInitializationException: The type initializer for 'Microsoft.Data.SqlClient.SqlPerformanceCounters' threw an exception.
 ---> System.TypeInitializationException: The type initializer for 'Microsoft.Data.Common.ADP' threw an exception.
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'System.Data.SqlClient, Version=0.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'. Le fichier spécifié est introuvable.
File name: 'System.Data.SqlClient, Version=0.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
   at Microsoft.Data.Common.ADP..cctor()
   --- End of inner exception stack trace ---
   at Microsoft.Data.ProviderBase.DbConnectionPoolCounters..ctor(String categoryName, String categoryHelp)
   at Microsoft.Data.SqlClient.SqlPerformanceCounters..cctor()
   --- End of inner exception stack trace ---
   at Microsoft.Data.SqlClient.SqlConnectionFactory..ctor()
   at Microsoft.Data.SqlClient.SqlConnectionFactory..cctor()
   --- End of inner exception stack trace ---
   at Microsoft.Data.SqlClient.SqlConnection..cctor()
   --- End of inner exception stack trace ---
   at Microsoft.Data.SqlClient.SqlConnection..ctor()
   at Microsoft.Data.SqlClient.SqlConnection..ctor(String connectionString, SqlCredential credential)
   at RecouvreX.Business.Services.DatabaseAdminService.TestConnectionAsync(String server, String database, String userId, String password, Boolean integratedSecurity) in E:\AG APP\RecouvreX  App\RecouvreX.Business\Services\DatabaseAdminService.cs:line 63
2025-05-26 00:05:54.037 +01:00 [WRN] La connexion à la base de données a échoué. Affichage du formulaire de configuration de la connexion.
2025-05-26 00:05:56.502 +01:00 [ERR] Erreur lors du test de connexion à la base de données
System.TypeInitializationException: The type initializer for 'Microsoft.Data.SqlClient.SqlConnection' threw an exception.
 ---> System.TypeInitializationException: The type initializer for 'Microsoft.Data.SqlClient.SqlConnectionFactory' threw an exception.
 ---> System.TypeInitializationException: The type initializer for 'Microsoft.Data.SqlClient.SqlPerformanceCounters' threw an exception.
 ---> System.TypeInitializationException: The type initializer for 'Microsoft.Data.Common.ADP' threw an exception.
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'System.Data.SqlClient, Version=0.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'. Le fichier spécifié est introuvable.
File name: 'System.Data.SqlClient, Version=0.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
   at Microsoft.Data.Common.ADP..cctor()
   --- End of inner exception stack trace ---
   at Microsoft.Data.ProviderBase.DbConnectionPoolCounters..ctor(String categoryName, String categoryHelp)
   at Microsoft.Data.SqlClient.SqlPerformanceCounters..cctor()
   --- End of inner exception stack trace ---
   at Microsoft.Data.SqlClient.SqlConnectionFactory..ctor()
   at Microsoft.Data.SqlClient.SqlConnectionFactory..cctor()
   --- End of inner exception stack trace ---
   at Microsoft.Data.SqlClient.SqlConnection..cctor()
   --- End of inner exception stack trace ---
   at Microsoft.Data.SqlClient.SqlConnection..ctor()
   at Microsoft.Data.SqlClient.SqlConnection..ctor(String connectionString, SqlCredential credential)
   at RecouvreX.Business.Services.DatabaseAdminService.TestConnectionAsync(String server, String database, String userId, String password, Boolean integratedSecurity) in E:\AG APP\RecouvreX  App\RecouvreX.Business\Services\DatabaseAdminService.cs:line 63
2025-05-26 00:06:00.031 +01:00 [INF] Arrêt de l'application RecouvreX
