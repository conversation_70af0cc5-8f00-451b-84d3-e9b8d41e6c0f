using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Administration
{
    public partial class RoleListForm : Form
    {
        private readonly IRoleService _roleService;
        private readonly IAuthenticationService _authenticationService;
        private readonly int _currentUserId;
        private List<RoleWithStats> _roles = new List<RoleWithStats>();
        private DataTable _dataTable = new DataTable();

        public RoleListForm(IRoleService roleService, IAuthenticationService authenticationService, int currentUserId)
        {
            _roleService = roleService ?? throw new ArgumentNullException(nameof(roleService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }



        private async void RoleListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Vérifier les permissions
                bool canManageRoles = await _authenticationService.HasPermissionAsync(_currentUserId, "ROLES_MANAGE");
                if (!canManageRoles)
                {
                    MessageBox.Show("Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.",
                        "Accès refusé", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return;
                }

                // Initialiser la table de données
                InitializeDataTable();

                // Charger les rôles
                await LoadRolesAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement de la liste des rôles");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des rôles : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Nom", typeof(string));
            _dataTable.Columns.Add("Description", typeof(string));
            _dataTable.Columns.Add("Nombre d'utilisateurs", typeof(int));
            _dataTable.Columns.Add("Nombre de permissions", typeof(int));

            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;
            }
        }

        private async Task LoadRolesAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer les rôles avec leurs statistiques
                _roles = (await _roleService.GetAllWithStatsAsync()).ToList();

                // Mettre à jour la table de données
                UpdateDataTable(_roles);

                // Mettre à jour le compteur
                UpdateCounter(_roles);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des rôles");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des rôles : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void UpdateDataTable(List<RoleWithStats> roles)
        {
            _dataTable.Clear();

            foreach (var role in roles)
            {
                _dataTable.Rows.Add(
                    role.Id,
                    role.Nom,
                    role.Description,
                    role.NombreUtilisateurs,
                    role.NombrePermissions
                );
            }
        }

        private void UpdateCounter(List<RoleWithStats> roles)
        {
            var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
            var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
            if (countLabel != null)
            {
                countLabel.Text = $"Nombre de rôles : {roles.Count}";
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            // Recharger les rôles
            await LoadRolesAsync();
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir le formulaire d'ajout de rôle
                using (var form = new RoleEditForm(_roleService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les rôles
                        LoadRolesAsync().Wait();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout de rôle");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
            {
                int roleId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                EditRole(roleId);
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un rôle à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int roleId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    EditRole(roleId);
                }
            }
        }

        private void EditRole(int roleId)
        {
            try
            {
                // Récupérer le rôle
                var role = _roles.FirstOrDefault(r => r.Id == roleId);
                if (role != null)
                {
                    // Vérifier si c'est le rôle administrateur
                    if (role.Nom.Equals("Administrateur", StringComparison.OrdinalIgnoreCase))
                    {
                        MessageBox.Show("Le rôle Administrateur ne peut pas être modifié.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // Ouvrir le formulaire de modification de rôle
                    using (var form = new RoleEditForm(_roleService, _currentUserId, role))
                    {
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            // Recharger les rôles
                            LoadRolesAsync().Wait();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de modification de rôle");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer le rôle sélectionné
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int roleId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string roleName = dataGridView.SelectedRows[0].Cells["Nom"].Value.ToString();
                    int nombreUtilisateurs = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Nombre d'utilisateurs"].Value);

                    // Vérifier si c'est le rôle administrateur
                    if (roleName.Equals("Administrateur", StringComparison.OrdinalIgnoreCase))
                    {
                        MessageBox.Show("Le rôle Administrateur ne peut pas être supprimé.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // Vérifier si le rôle est utilisé
                    if (nombreUtilisateurs > 0)
                    {
                        MessageBox.Show($"Ce rôle est attribué à {nombreUtilisateurs} utilisateur(s). Veuillez d'abord modifier ces utilisateurs.",
                            "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // Demander confirmation
                    var result = MessageBox.Show($"Êtes-vous sûr de vouloir supprimer le rôle '{roleName}' ?\n\nCette action est irréversible.",
                        "Confirmation de suppression", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        // Supprimer le rôle
                        bool success = await _roleService.DeleteAsync(roleId, _currentUserId);
                        if (success)
                        {
                            MessageBox.Show("Le rôle a été supprimé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            // Recharger les rôles
                            await LoadRolesAsync();
                        }
                        else
                        {
                            MessageBox.Show("Impossible de supprimer le rôle.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un rôle à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression du rôle");
                MessageBox.Show($"Une erreur s'est produite lors de la suppression du rôle : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PermissionsButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer le rôle sélectionné
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int roleId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string roleName = dataGridView.SelectedRows[0].Cells["Nom"].Value.ToString();

                    // Ouvrir le formulaire de gestion des permissions
                    using (var form = new RolePermissionsForm(_roleService, _currentUserId, roleId, roleName))
                    {
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            // Recharger les rôles
                            LoadRolesAsync().Wait();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un rôle pour gérer ses permissions.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de gestion des permissions");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
