using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour la gestion des scores de risque client
    /// </summary>
    public class ScoreRisqueClientRepository : BaseRepository<ScoreRisqueClient>, IScoreRisqueClientRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public ScoreRisqueClientRepository(DatabaseConnection dbConnection) : base(dbConnection, "app.ScoresRisqueClient")
        {
        }

        /// <summary>
        /// Récupère tous les scores de risque client
        /// </summary>
        /// <returns>Liste des scores de risque client</returns>
        public async Task<IEnumerable<ScoreRisqueClient>> GetAllAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT s.*, c.RaisonSociale, c.Code
                    FROM app.ScoresRisqueClient s
                    INNER JOIN app.Clients c ON s.ClientId = c.Id
                    WHERE s.EstActif = 1
                    ORDER BY s.Score DESC";

                var scores = await connection.QueryAsync<ScoreRisqueClient, Client, ScoreRisqueClient>(
                    query,
                    (score, client) =>
                    {
                        score.Client = client;
                        return score;
                    },
                    splitOn: "RaisonSociale");

                return scores;
            }
        }

        /// <summary>
        /// Récupère un score de risque client par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du score de risque client</param>
        /// <returns>Score de risque client trouvé ou null</returns>
        public async Task<ScoreRisqueClient> GetByIdAsync(int id)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT s.*, c.RaisonSociale, c.Code
                    FROM app.ScoresRisqueClient s
                    INNER JOIN app.Clients c ON s.ClientId = c.Id
                    WHERE s.Id = @Id AND s.EstActif = 1";

                var scores = await connection.QueryAsync<ScoreRisqueClient, Client, ScoreRisqueClient>(
                    query,
                    (score, client) =>
                    {
                        score.Client = client;
                        return score;
                    },
                    new { Id = id },
                    splitOn: "RaisonSociale");

                return scores.FirstOrDefault();
            }
        }

        /// <summary>
        /// Récupère le score de risque d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Score de risque du client ou null si non trouvé</returns>
        public async Task<ScoreRisqueClient> GetByClientIdAsync(int clientId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT s.*, c.RaisonSociale, c.Code
                    FROM app.ScoresRisqueClient s
                    INNER JOIN app.Clients c ON s.ClientId = c.Id
                    WHERE s.ClientId = @ClientId AND s.EstActif = 1";

                var scores = await connection.QueryAsync<ScoreRisqueClient, Client, ScoreRisqueClient>(
                    query,
                    (score, client) =>
                    {
                        score.Client = client;
                        return score;
                    },
                    new { ClientId = clientId },
                    splitOn: "RaisonSociale");

                return scores.FirstOrDefault();
            }
        }

        /// <summary>
        /// Ajoute un nouveau score de risque client
        /// </summary>
        /// <param name="scoreRisqueClient">Score de risque client à ajouter</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Score de risque client ajouté avec son identifiant généré</returns>
        public async Task<ScoreRisqueClient> AddAsync(ScoreRisqueClient scoreRisqueClient, int creePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    INSERT INTO app.ScoresRisqueClient (
                        ClientId, Score, Categorie, DateMiseAJour, FacteursRisque,
                        DelaiMoyenPaiement, PourcentageFacturesRetard, MontantTotalRetard, NombreMoyenRelances,
                        DateCreation, CreePar, EstActif
                    )
                    VALUES (
                        @ClientId, @Score, @Categorie, @DateMiseAJour, @FacteursRisque,
                        @DelaiMoyenPaiement, @PourcentageFacturesRetard, @MontantTotalRetard, @NombreMoyenRelances,
                        GETDATE(), @CreePar, 1
                    );
                    SELECT CAST(SCOPE_IDENTITY() as int)";

                var parameters = new DynamicParameters(scoreRisqueClient);
                parameters.Add("CreePar", creePar);

                var id = await connection.QuerySingleAsync<int>(query, parameters);
                scoreRisqueClient.Id = id;

                return scoreRisqueClient;
            }
        }

        /// <summary>
        /// Met à jour un score de risque client existant
        /// </summary>
        /// <param name="scoreRisqueClient">Score de risque client à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Score de risque client mis à jour</returns>
        public async Task<ScoreRisqueClient> UpdateAsync(ScoreRisqueClient scoreRisqueClient, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE app.ScoresRisqueClient
                    SET ClientId = @ClientId,
                        Score = @Score,
                        Categorie = @Categorie,
                        DateMiseAJour = @DateMiseAJour,
                        FacteursRisque = @FacteursRisque,
                        DelaiMoyenPaiement = @DelaiMoyenPaiement,
                        PourcentageFacturesRetard = @PourcentageFacturesRetard,
                        MontantTotalRetard = @MontantTotalRetard,
                        NombreMoyenRelances = @NombreMoyenRelances,
                        DateModification = GETDATE(),
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id AND EstActif = 1";

                var parameters = new DynamicParameters(scoreRisqueClient);
                parameters.Add("ModifiePar", modifiePar);

                await connection.ExecuteAsync(query, parameters);

                return scoreRisqueClient;
            }
        }

        /// <summary>
        /// Supprime un score de risque client
        /// </summary>
        /// <param name="id">Identifiant du score de risque client à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE app.ScoresRisqueClient
                    SET EstActif = 0,
                        DateModification = GETDATE(),
                        ModifiePar = @SupprimePar
                    WHERE Id = @Id AND EstActif = 1";

                var parameters = new { Id = id, SupprimePar = supprimePar };
                var rowsAffected = await connection.ExecuteAsync(query, parameters);

                return rowsAffected > 0;
            }
        }

        /// <summary>
        /// Récupère les clients par catégorie de risque
        /// </summary>
        /// <param name="categorie">Catégorie de risque (A, B, C, D, E)</param>
        /// <returns>Liste des clients dans la catégorie spécifiée</returns>
        public async Task<IEnumerable<Client>> GetClientsByCategorieAsync(string categorie)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT c.*
                    FROM app.Clients c
                    INNER JOIN app.ScoresRisqueClient s ON c.Id = s.ClientId
                    WHERE s.Categorie = @Categorie AND c.EstActif = 1 AND s.EstActif = 1
                    ORDER BY s.Score DESC";

                return await connection.QueryAsync<Client>(query, new { Categorie = categorie });
            }
        }

        /// <summary>
        /// Récupère les statistiques de répartition des clients par catégorie de risque
        /// </summary>
        /// <returns>Dictionnaire avec la catégorie comme clé et le nombre de clients comme valeur</returns>
        public async Task<Dictionary<string, int>> GetClientDistributionByCategorieAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT s.Categorie, COUNT(s.Id) AS Count
                    FROM app.ScoresRisqueClient s
                    WHERE s.EstActif = 1
                    GROUP BY s.Categorie
                    ORDER BY s.Categorie";

                var results = await connection.QueryAsync<(string Categorie, int Count)>(query);
                return results.ToDictionary(r => r.Categorie, r => r.Count);
            }
        }
    }
}
