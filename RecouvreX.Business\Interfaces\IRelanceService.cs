using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des relances
    /// </summary>
    public interface IRelanceService
    {
        /// <summary>
        /// Récupère toutes les relances
        /// </summary>
        /// <returns>Liste des relances</returns>
        Task<IEnumerable<Relance>> GetAllAsync();

        /// <summary>
        /// Récupère une relance par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la relance</param>
        /// <returns>Relance trouvée ou null</returns>
        Task<Relance> GetByIdAsync(int id);

        /// <summary>
        /// Récupère toutes les relances d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des relances de la facture</returns>
        Task<IEnumerable<Relance>> GetByFactureIdAsync(int factureId);

        /// <summary>
        /// Récupère toutes les relances effectuées par un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des relances effectuées par l'utilisateur</returns>
        Task<IEnumerable<Relance>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les relances par type
        /// </summary>
        /// <param name="type">Type de relance</param>
        /// <returns>Liste des relances du type spécifié</returns>
        Task<IEnumerable<Relance>> GetByTypeAsync(string type);

        /// <summary>
        /// Récupère les relances par statut
        /// </summary>
        /// <param name="statut">Statut de relance</param>
        /// <returns>Liste des relances ayant le statut spécifié</returns>
        Task<IEnumerable<Relance>> GetByStatutAsync(string statut);

        /// <summary>
        /// Récupère les relances par niveau
        /// </summary>
        /// <param name="niveau">Niveau de relance</param>
        /// <returns>Liste des relances du niveau spécifié</returns>
        Task<IEnumerable<Relance>> GetByNiveauAsync(int niveau);

        /// <summary>
        /// Récupère les relances par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des relances effectuées dans la période spécifiée</returns>
        Task<IEnumerable<Relance>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère les relances planifiées pour une date donnée
        /// </summary>
        /// <param name="date">Date des relances planifiées</param>
        /// <returns>Liste des relances planifiées pour la date spécifiée</returns>
        Task<IEnumerable<Relance>> GetPlannedForDateAsync(DateTime date);

        /// <summary>
        /// Crée une nouvelle relance
        /// </summary>
        /// <param name="relance">Relance à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Relance créée avec son identifiant généré</returns>
        Task<Relance> CreateAsync(Relance relance, int creePar);

        /// <summary>
        /// Met à jour une relance existante
        /// </summary>
        /// <param name="relance">Relance à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Relance mise à jour</returns>
        Task<Relance> UpdateAsync(Relance relance, int modifiePar);

        /// <summary>
        /// Supprime une relance
        /// </summary>
        /// <param name="id">Identifiant de la relance à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Met à jour le statut d'une relance
        /// </summary>
        /// <param name="relanceId">Identifiant de la relance</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateStatutAsync(int relanceId, string statut, int modifiePar);

        /// <summary>
        /// Planifie une relance pour une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="type">Type de relance</param>
        /// <param name="datePrevue">Date prévue pour la relance</param>
        /// <param name="niveau">Niveau de la relance</param>
        /// <param name="contenu">Contenu de la relance</param>
        /// <param name="planifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Relance planifiée avec son identifiant généré</returns>
        Task<Relance> PlanifierRelanceAsync(int factureId, string type, DateTime datePrevue, int niveau, string contenu, int planifiePar);

        /// <summary>
        /// Génère des relances automatiques pour les factures en retard
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre de relances générées</returns>
        Task<int> GenererRelancesAutomatiquesAsync(int utilisateurId);
    }
}
