using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.Clients
{
    public partial class ClientListForm : Form
    {
        private readonly IClientService _clientService;
        private readonly IAuthenticationService _authenticationService;
        private readonly IFactureService _factureService;
        private readonly IContactService _contactService;
        private readonly int _currentUserId;
        private readonly bool _selectionMode;
        private List<Client> _clients = new List<Client>();
        private DataTable _dataTable = new DataTable();

        public int? SelectedClientId { get; private set; }

        public ClientListForm(IClientService clientService, IAuthenticationService authenticationService, int currentUserId, bool selectionMode = false)
        {
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _currentUserId = currentUserId;
            _selectionMode = selectionMode;

            InitializeComponent();

            // Si en mode sélection, ajuster le formulaire
            if (_selectionMode)
            {
                this.Text = "Sélectionner un client";

                // Ajouter un bouton de sélection
                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                if (toolStrip != null)
                {
                    // Ajouter un séparateur
                    toolStrip.Items.Add(new ToolStripSeparator());

                    // Ajouter le bouton de sélection
                    var selectButton = new ToolStripButton();
                    selectButton.Name = "selectButton";
                    selectButton.Text = "Sélectionner";
                    selectButton.Image = null; // Ajouter une icône
                    selectButton.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
                    selectButton.Click += new EventHandler(this.SelectButton_Click);
                    toolStrip.Items.Add(selectButton);
                }
            }
        }



        private async void ClientListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Vérifier les permissions
                bool canView = await _authenticationService.HasPermissionAsync(_currentUserId, "CLIENTS_VIEW");
                bool canAdd = await _authenticationService.HasPermissionAsync(_currentUserId, "CLIENTS_ADD");
                bool canEdit = await _authenticationService.HasPermissionAsync(_currentUserId, "CLIENTS_EDIT");
                bool canDelete = await _authenticationService.HasPermissionAsync(_currentUserId, "CLIENTS_DELETE");

                if (!canView)
                {
                    MessageBox.Show("Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.",
                        "Accès refusé", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return;
                }

                // Configurer les boutons en fonction des permissions
                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                var addButton = toolStrip?.Items.Find("addButton", false).FirstOrDefault() as ToolStripButton;
                var editButton = toolStrip?.Items.Find("editButton", false).FirstOrDefault() as ToolStripButton;
                var deleteButton = toolStrip?.Items.Find("deleteButton", false).FirstOrDefault() as ToolStripButton;

                if (addButton != null) addButton.Enabled = canAdd;
                if (editButton != null) editButton.Enabled = canEdit;
                if (deleteButton != null) deleteButton.Enabled = canDelete;

                // Initialiser la table de données
                InitializeDataTable();

                // Charger les clients
                await LoadClientsAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement de la liste des clients");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des clients : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Code", typeof(string));
            _dataTable.Columns.Add("Raison Sociale", typeof(string));
            _dataTable.Columns.Add("Ville", typeof(string));
            _dataTable.Columns.Add("Téléphone", typeof(string));
            _dataTable.Columns.Add("Email", typeof(string));
            _dataTable.Columns.Add("Solde Actuel", typeof(decimal));

            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (dataGridView.Columns["Solde Actuel"] != null)
                    dataGridView.Columns["Solde Actuel"].DefaultCellStyle.Format = "C2";
            }
        }

        private async Task LoadClientsAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer les clients
                _clients = (await _clientService.GetAllAsync()).ToList();

                // Mettre à jour la table de données
                UpdateDataTable(_clients);

                // Mettre à jour le compteur
                var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
                var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
                if (countLabel != null)
                {
                    countLabel.Text = $"Nombre de clients : {_clients.Count}";
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des clients");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des clients : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void UpdateDataTable(List<Client> clients)
        {
            _dataTable.Clear();

            foreach (var client in clients)
            {
                _dataTable.Rows.Add(
                    client.Id,
                    client.Code,
                    client.RaisonSociale,
                    client.Ville,
                    client.Telephone,
                    client.Email,
                    client.SoldeActuel
                );
            }
        }

        private async void SearchButton_Click(object sender, EventArgs e)
        {
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var searchTextBox = toolStrip?.Items.Find("searchTextBox", false).FirstOrDefault() as ToolStripTextBox;
            if (searchTextBox != null)
            {
                string searchTerm = searchTextBox.Text.Trim();
                await SearchClientsAsync(searchTerm);
            }
        }

        private async void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                e.SuppressKeyPress = true;

                var searchTextBox = sender as ToolStripTextBox;
                if (searchTextBox != null)
                {
                    string searchTerm = searchTextBox.Text.Trim();
                    await SearchClientsAsync(searchTerm);
                }
            }
        }

        private async Task SearchClientsAsync(string searchTerm)
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                if (string.IsNullOrEmpty(searchTerm))
                {
                    // Si la recherche est vide, charger tous les clients
                    await LoadClientsAsync();
                }
                else
                {
                    // Rechercher les clients
                    var clients = await _clientService.SearchByNameAsync(searchTerm);
                    _clients = clients.ToList();

                    // Mettre à jour la table de données
                    UpdateDataTable(_clients);

                    // Mettre à jour le compteur
                    var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
                    var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
                    if (countLabel != null)
                    {
                        countLabel.Text = $"Résultats : {_clients.Count} client(s)";
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la recherche des clients");
                MessageBox.Show($"Une erreur s'est produite lors de la recherche des clients : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            // Réinitialiser le champ de recherche
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var searchTextBox = toolStrip?.Items.Find("searchTextBox", false).FirstOrDefault() as ToolStripTextBox;
            if (searchTextBox != null)
            {
                searchTextBox.Text = string.Empty;
            }

            // Recharger les clients
            await LoadClientsAsync();
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir le formulaire d'ajout de client
                using (var form = new ClientEditForm(_clientService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les clients
                        LoadClientsAsync().Wait();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout de client");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer le client sélectionné
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int clientId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    EditClient(clientId);
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un client à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de modification de client");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int clientId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);

                    if (_selectionMode)
                    {
                        // En mode sélection, retourner l'ID du client
                        SelectedClientId = clientId;
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        // En mode normal, ouvrir le formulaire de détails
                        EditClient(clientId);
                    }
                }
            }
        }

        private void SelectButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer le client sélectionné
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int clientId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    SelectedClientId = clientId;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un client.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la sélection d'un client");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditClient(int clientId)
        {
            try
            {
                // Récupérer le client
                var client = _clients.FirstOrDefault(c => c.Id == clientId);
                if (client != null)
                {
                    // Ouvrir le formulaire de modification de client
                    using (var form = new ClientEditForm(_clientService, _currentUserId, client))
                    {
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            // Recharger les clients
                            LoadClientsAsync().Wait();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de modification de client");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer le client sélectionné
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int clientId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string clientName = dataGridView.SelectedRows[0].Cells["Raison Sociale"].Value.ToString();

                    // Demander confirmation
                    var result = MessageBox.Show($"Êtes-vous sûr de vouloir supprimer le client '{clientName}' ?\n\nCette action est irréversible.",
                        "Confirmation de suppression", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        // Supprimer le client
                        bool success = await _clientService.DeleteAsync(clientId, _currentUserId);
                        if (success)
                        {
                            MessageBox.Show("Le client a été supprimé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            // Recharger les clients
                            await LoadClientsAsync();
                        }
                        else
                        {
                            MessageBox.Show("Impossible de supprimer le client. Il est peut-être référencé par d'autres éléments.",
                                "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un client à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression du client");
                MessageBox.Show($"Une erreur s'est produite lors de la suppression du client : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
