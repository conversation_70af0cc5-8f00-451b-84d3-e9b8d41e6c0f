using RecouvreX.Models;

namespace RecouvreX.Data.Interfaces
{
    /// <summary>
    /// Interface pour le repository des documents de litiges
    /// </summary>
    public interface IDocumentLitigeRepository
    {
        /// <summary>
        /// Récupère tous les documents
        /// </summary>
        /// <returns>Liste de tous les documents</returns>
        Task<IEnumerable<DocumentLitige>> GetAllAsync();

        /// <summary>
        /// Récupère un document par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du document</param>
        /// <returns>Document trouvé ou null</returns>
        Task<DocumentLitige> GetByIdAsync(int id);

        /// <summary>
        /// Récupère tous les documents pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des documents du litige</returns>
        Task<IEnumerable<DocumentLitige>> GetByLitigeIdAsync(int litigeId);

        /// <summary>
        /// Ajoute un document
        /// </summary>
        /// <param name="document">Document à ajouter</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui ajoute le document</param>
        /// <returns>Document ajouté</returns>
        Task<DocumentLitige> AddAsync(DocumentLitige document, int creePar);

        /// <summary>
        /// Met à jour un document
        /// </summary>
        /// <param name="document">Document à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui modifie le document</param>
        /// <returns>True si le document a été mis à jour</returns>
        Task<bool> UpdateAsync(DocumentLitige document, int modifiePar);

        /// <summary>
        /// Supprime un document
        /// </summary>
        /// <param name="id">Identifiant du document</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui supprime le document</param>
        /// <returns>True si le document a été supprimé</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);
    }
}
