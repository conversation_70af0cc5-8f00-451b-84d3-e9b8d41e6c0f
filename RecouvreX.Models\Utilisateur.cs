using System;
using System.Collections.Generic;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un utilisateur du système
    /// </summary>
    public class Utilisateur : BaseEntity
    {
        /// <summary>
        /// Nom d'utilisateur pour la connexion
        /// </summary>
        public string NomUtilisateur { get; set; } = string.Empty;

        /// <summary>
        /// Mot de passe haché
        /// </summary>
        public string MotDePasse { get; set; } = string.Empty;

        /// <summary>
        /// Nom complet de l'utilisateur
        /// </summary>
        public string NomComplet { get; set; } = string.Empty;

        /// <summary>
        /// Adresse email de l'utilisateur
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Numéro de téléphone de l'utilisateur
        /// </summary>
        public string Telephone { get; set; } = string.Empty;

        /// <summary>
        /// Identifiant du rôle de l'utilisateur
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// Rôle de l'utilisateur (navigation property)
        /// </summary>
        public Role Role { get; set; } = null!;

        /// <summary>
        /// Date de dernière connexion
        /// </summary>
        public DateTime? DerniereConnexion { get; set; }

        /// <summary>
        /// Indique si l'utilisateur est verrouillé
        /// </summary>
        public bool EstVerrouille { get; set; } = false;

        /// <summary>
        /// Nombre de tentatives de connexion échouées
        /// </summary>
        public int TentativesConnexionEchouees { get; set; } = 0;
    }
}
