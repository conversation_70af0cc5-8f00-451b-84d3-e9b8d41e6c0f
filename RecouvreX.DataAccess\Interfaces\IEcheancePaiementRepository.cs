using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des échéances de paiement
    /// </summary>
    public interface IEcheancePaiementRepository : IRepository<EcheancePaiement>
    {
        /// <summary>
        /// Récupère toutes les échéances d'un plan de paiement
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Liste des échéances du plan de paiement</returns>
        Task<IEnumerable<EcheancePaiement>> GetByPlanPaiementIdAsync(int planPaiementId);

        /// <summary>
        /// Récupère les échéances à venir pour une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des échéances à venir dans la période spécifiée</returns>
        Task<IEnumerable<EcheancePaiement>> GetUpcomingAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère les échéances en retard
        /// </summary>
        /// <returns>Liste des échéances en retard</returns>
        Task<IEnumerable<EcheancePaiement>> GetLateAsync();

        /// <summary>
        /// Récupère les échéances en retard pour un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des échéances en retard pour le client</returns>
        Task<IEnumerable<EcheancePaiement>> GetLateByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère les échéances payées pour une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des échéances payées dans la période spécifiée</returns>
        Task<IEnumerable<EcheancePaiement>> GetPaidAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Marque une échéance comme payée
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="paiementId">Identifiant du paiement associé</param>
        /// <param name="montantPaye">Montant effectivement payé</param>
        /// <param name="datePaiement">Date du paiement</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> MarkAsPaidAsync(int echeanceId, int paiementId, decimal montantPaye, DateTime datePaiement, int userId);

        /// <summary>
        /// Marque une échéance comme non payée (annulation d'un paiement)
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> MarkAsUnpaidAsync(int echeanceId, int userId);

        /// <summary>
        /// Marque une échéance comme ayant reçu une notification
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> MarkNotificationSentAsync(int echeanceId, int userId);

        /// <summary>
        /// Récupère les statistiques de respect des échéances pour un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Statistiques de respect des échéances</returns>
        Task<(int Total, int Payees, int EnRetard, decimal TauxRespect)> GetClientStatisticsAsync(int clientId);
    }
}
