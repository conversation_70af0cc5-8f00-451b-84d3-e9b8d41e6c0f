using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.ComponentModel;

namespace RecouvreX.WinForms.Forms.Clients
{
    public partial class ClientEditForm : Form
    {
        private readonly IClientService _clientService;
        private readonly int _currentUserId;
        private readonly Client? _client;
        private readonly bool _isEditMode;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();
        private TextBox codeTextBox;

        public ClientEditForm(IClientService clientService, int currentUserId, Client? client = null)
        {
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _currentUserId = currentUserId;
            _client = client;
            _isEditMode = client != null;

            InitializeComponent();

            // Mettre à jour le titre du formulaire après l'initialisation des composants
            this.Text = _isEditMode ? "Modifier un client" : "Ajouter un client";

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;

            // Activer/désactiver le champ de code en fonction du mode
            if (codeTextBox != null)
            {
                codeTextBox.Enabled = _isEditMode;
            }
        }

        private async void ClientEditForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Charger les commerciaux
                await LoadCommercialsAsync();

                // Si en mode édition, remplir les champs avec les données du client
                if (_isEditMode && _client != null)
                {
                    FillFormWithClientData(_client);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'édition de client");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadCommercialsAsync()
        {
            try
            {
                // TODO: Charger les commerciaux depuis le service
                // Pour l'instant, on ajoute juste un élément vide
                var commercialComboBox = this.Controls.Find("commercialComboBox", true).FirstOrDefault() as ComboBox;
                if (commercialComboBox != null)
                {
                    commercialComboBox.Items.Add(new ComboBoxItem { Id = 0, Text = "-- Aucun --" });
                    commercialComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des commerciaux");
                throw;
            }
        }

        private void FillFormWithClientData(Client client)
        {
            var codeTextBox = this.Controls.Find("codeTextBox", true).FirstOrDefault() as TextBox;
            var raisonSocialeTextBox = this.Controls.Find("raisonSocialeTextBox", true).FirstOrDefault() as TextBox;
            var adresseTextBox = this.Controls.Find("adresseTextBox", true).FirstOrDefault() as TextBox;
            var codePostalTextBox = this.Controls.Find("codePostalTextBox", true).FirstOrDefault() as TextBox;
            var villeTextBox = this.Controls.Find("villeTextBox", true).FirstOrDefault() as TextBox;
            var paysTextBox = this.Controls.Find("paysTextBox", true).FirstOrDefault() as TextBox;
            var telephoneTextBox = this.Controls.Find("telephoneTextBox", true).FirstOrDefault() as TextBox;
            var emailTextBox = this.Controls.Find("emailTextBox", true).FirstOrDefault() as TextBox;
            var commercialComboBox = this.Controls.Find("commercialComboBox", true).FirstOrDefault() as ComboBox;
            var limiteCreditNumericUpDown = this.Controls.Find("limiteCreditNumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var soldeActuelNumericUpDown = this.Controls.Find("soldeActuelNumericUpDown", true).FirstOrDefault() as NumericUpDown;

            if (codeTextBox != null) codeTextBox.Text = client.Code;
            if (raisonSocialeTextBox != null) raisonSocialeTextBox.Text = client.RaisonSociale;
            if (adresseTextBox != null) adresseTextBox.Text = client.Adresse;
            if (codePostalTextBox != null) codePostalTextBox.Text = client.CodePostal;
            if (villeTextBox != null) villeTextBox.Text = client.Ville;
            if (paysTextBox != null) paysTextBox.Text = client.Pays;
            if (telephoneTextBox != null) telephoneTextBox.Text = client.Telephone;
            if (emailTextBox != null) emailTextBox.Text = client.Email;

            if (commercialComboBox != null && client.CommercialId.HasValue)
            {
                // Sélectionner le commercial
                foreach (ComboBoxItem item in commercialComboBox.Items)
                {
                    if (item.Id == client.CommercialId.Value)
                    {
                        commercialComboBox.SelectedItem = item;
                        break;
                    }
                }
            }

            if (limiteCreditNumericUpDown != null) limiteCreditNumericUpDown.Value = client.LimiteCredit;
            if (soldeActuelNumericUpDown != null) soldeActuelNumericUpDown.Value = client.SoldeActuel;
        }

        private void RaisonSocialeTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "La raison sociale est obligatoire.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private void EmailTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null && !string.IsNullOrWhiteSpace(textBox.Text))
            {
                if (!IsValidEmail(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "L'adresse email n'est pas valide.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant d'enregistrer.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer les valeurs du formulaire
                var raisonSocialeTextBox = this.Controls.Find("raisonSocialeTextBox", true).FirstOrDefault() as TextBox;
                var adresseTextBox = this.Controls.Find("adresseTextBox", true).FirstOrDefault() as TextBox;
                var codePostalTextBox = this.Controls.Find("codePostalTextBox", true).FirstOrDefault() as TextBox;
                var villeTextBox = this.Controls.Find("villeTextBox", true).FirstOrDefault() as TextBox;
                var paysTextBox = this.Controls.Find("paysTextBox", true).FirstOrDefault() as TextBox;
                var telephoneTextBox = this.Controls.Find("telephoneTextBox", true).FirstOrDefault() as TextBox;
                var emailTextBox = this.Controls.Find("emailTextBox", true).FirstOrDefault() as TextBox;
                var commercialComboBox = this.Controls.Find("commercialComboBox", true).FirstOrDefault() as ComboBox;
                var limiteCreditNumericUpDown = this.Controls.Find("limiteCreditNumericUpDown", true).FirstOrDefault() as NumericUpDown;

                if (raisonSocialeTextBox == null)
                    throw new InvalidOperationException("Le champ raison sociale est introuvable.");

                // Créer ou mettre à jour le client
                Client client;
                if (_isEditMode && _client != null)
                {
                    // Mode édition
                    client = _client;
                }
                else
                {
                    // Mode ajout
                    client = new Client();
                }

                // Remplir les propriétés du client
                client.RaisonSociale = raisonSocialeTextBox.Text.Trim();
                client.Adresse = adresseTextBox?.Text.Trim() ?? string.Empty;
                client.CodePostal = codePostalTextBox?.Text.Trim() ?? string.Empty;
                client.Ville = villeTextBox?.Text.Trim() ?? string.Empty;
                client.Pays = paysTextBox?.Text.Trim() ?? "France";
                client.Telephone = telephoneTextBox?.Text.Trim() ?? string.Empty;
                client.Email = emailTextBox?.Text.Trim() ?? string.Empty;

                if (commercialComboBox != null && commercialComboBox.SelectedItem is ComboBoxItem selectedCommercial && selectedCommercial.Id > 0)
                {
                    client.CommercialId = selectedCommercial.Id;
                }
                else
                {
                    client.CommercialId = null;
                }

                client.LimiteCredit = limiteCreditNumericUpDown?.Value ?? 0;

                // Enregistrer le client
                if (_isEditMode)
                {
                    // Mettre à jour le client existant
                    await _clientService.UpdateAsync(client, _currentUserId);
                    MessageBox.Show("Le client a été mis à jour avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Créer un nouveau client
                    await _clientService.CreateAsync(client, _currentUserId);
                    MessageBox.Show("Le client a été créé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement du client");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement du client : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    // Classe pour les éléments de la ComboBox
    public class ComboBoxItem
    {
        public int Id { get; set; }
        public string Text { get; set; } = string.Empty;

        public override string ToString()
        {
            return Text;
        }
    }
}
