using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des rôles
    /// </summary>
    public class RoleService : IRoleService
    {
        private readonly IRoleRepository _roleRepository;
        private readonly IPermissionRepository _permissionRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="roleRepository">Repository des rôles</param>
        /// <param name="permissionRepository">Repository des permissions</param>
        /// <param name="journalAuditRepository">Repository du journal d'audit</param>
        public RoleService(
            IRoleRepository roleRepository,
            IPermissionRepository permissionRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _roleRepository = roleRepository ?? throw new ArgumentNullException(nameof(roleRepository));
            _permissionRepository = permissionRepository ?? throw new ArgumentNullException(nameof(permissionRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère tous les rôles
        /// </summary>
        /// <returns>Liste des rôles</returns>
        public async Task<IEnumerable<Role>> GetAllAsync()
        {
            return await _roleRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère un rôle par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du rôle</param>
        /// <returns>Rôle trouvé ou null</returns>
        public async Task<Role> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _roleRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère un rôle par son nom
        /// </summary>
        /// <param name="nom">Nom du rôle</param>
        /// <returns>Rôle trouvé ou null</returns>
        public async Task<Role> GetByNomAsync(string nom)
        {
            if (string.IsNullOrEmpty(nom))
                return null;

            return await _roleRepository.GetByNomAsync(nom);
        }

        /// <summary>
        /// Récupère un rôle avec ses permissions
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Rôle avec ses permissions</returns>
        public async Task<Role> GetWithPermissionsAsync(int roleId)
        {
            if (roleId <= 0)
                return null;

            return await _roleRepository.GetWithPermissionsAsync(roleId);
        }

        /// <summary>
        /// Récupère tous les rôles avec leurs permissions
        /// </summary>
        /// <returns>Liste des rôles avec leurs permissions</returns>
        public async Task<IEnumerable<Role>> GetAllWithPermissionsAsync()
        {
            return await _roleRepository.GetAllWithPermissionsAsync();
        }

        /// <summary>
        /// Crée un nouveau rôle
        /// </summary>
        /// <param name="role">Rôle à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Rôle créé avec son identifiant généré</returns>
        public async Task<Role> CreateAsync(Role role, int creePar)
        {
            if (role == null || creePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la création d'un rôle");

            // Vérifier si le nom du rôle existe déjà
            var existingRole = await _roleRepository.GetByNomAsync(role.Nom);
            if (existingRole != null)
                throw new InvalidOperationException($"Le rôle '{role.Nom}' existe déjà");

            // Créer le rôle
            var createdRole = await _roleRepository.AddAsync(role, creePar);

            // Journaliser la création
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = creePar,
                NomUtilisateur = "Système", // À remplacer par le nom d'utilisateur réel si disponible
                TypeAction = TypeAudit.Creation,
                TypeEntite = "Role",
                EntiteId = createdRole.Id,
                Description = $"Création du rôle {createdRole.Nom}",
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdRole.Id,
                    createdRole.Nom,
                    createdRole.Description
                })
            });

            return createdRole;
        }

        /// <summary>
        /// Met à jour un rôle existant
        /// </summary>
        /// <param name="role">Rôle à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Rôle mis à jour</returns>
        public async Task<Role> UpdateAsync(Role role, int modifiePar)
        {
            if (role == null || role.Id <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la mise à jour d'un rôle");

            // Récupérer le rôle existant
            var existingRole = await _roleRepository.GetByIdAsync(role.Id);
            if (existingRole == null)
                throw new InvalidOperationException($"Le rôle avec l'ID {role.Id} n'existe pas");

            // Vérifier si le nom du rôle existe déjà pour un autre rôle
            if (role.Nom != existingRole.Nom)
            {
                var roleWithSameName = await _roleRepository.GetByNomAsync(role.Nom);
                if (roleWithSameName != null && roleWithSameName.Id != role.Id)
                    throw new InvalidOperationException($"Le rôle '{role.Nom}' existe déjà");
            }

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = "Système", // À remplacer par le nom d'utilisateur réel si disponible
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Role",
                EntiteId = role.Id,
                Description = $"Modification du rôle {role.Nom}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingRole.Id,
                    existingRole.Nom,
                    existingRole.Description
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    role.Id,
                    role.Nom,
                    role.Description
                })
            });

            // Mettre à jour le rôle
            return await _roleRepository.UpdateAsync(role, modifiePar);
        }

        /// <summary>
        /// Supprime un rôle
        /// </summary>
        /// <param name="id">Identifiant du rôle à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0 || supprimePar <= 0)
                return false;

            // Récupérer le rôle à supprimer
            var role = await _roleRepository.GetByIdAsync(id);
            if (role == null)
                return false;

            // Journaliser la suppression
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = supprimePar,
                NomUtilisateur = "Système", // À remplacer par le nom d'utilisateur réel si disponible
                TypeAction = TypeAudit.Suppression,
                TypeEntite = "Role",
                EntiteId = id,
                Description = $"Suppression du rôle {role.Nom}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    role.Id,
                    role.Nom,
                    role.Description
                })
            });

            // Supprimer le rôle
            return await _roleRepository.DeleteAsync(id, supprimePar);
        }

        /// <summary>
        /// Ajoute une permission à un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <param name="permissionId">Identifiant de la permission</param>
        /// <param name="ajoutePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'ajout a réussi, sinon False</returns>
        public async Task<bool> AddPermissionAsync(int roleId, int permissionId, int ajoutePar)
        {
            if (roleId <= 0 || permissionId <= 0 || ajoutePar <= 0)
                return false;

            // Vérifier si le rôle existe
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null)
                return false;

            // Vérifier si la permission existe
            var permission = await _permissionRepository.GetByIdAsync(permissionId);
            if (permission == null)
                return false;

            // Ajouter la permission au rôle
            var result = await _roleRepository.AddPermissionAsync(roleId, permissionId, ajoutePar);

            if (result)
            {
                // Journaliser l'ajout de la permission
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    DateAction = DateTime.Now,
                    UtilisateurId = ajoutePar,
                    NomUtilisateur = "Système", // À remplacer par le nom d'utilisateur réel si disponible
                    TypeAction = TypeAudit.Modification,
                    TypeEntite = "RolePermission",
                    EntiteId = roleId,
                    Description = $"Ajout de la permission '{permission.Nom}' au rôle '{role.Nom}'",
                    DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                    {
                        RoleId = roleId,
                        RoleNom = role.Nom,
                        PermissionId = permissionId,
                        PermissionNom = permission.Nom,
                        PermissionCode = permission.Code
                    })
                });
            }

            return result;
        }

        /// <summary>
        /// Supprime une permission d'un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <param name="permissionId">Identifiant de la permission</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> RemovePermissionAsync(int roleId, int permissionId, int supprimePar)
        {
            if (roleId <= 0 || permissionId <= 0 || supprimePar <= 0)
                return false;

            // Vérifier si le rôle existe
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null)
                return false;

            // Vérifier si la permission existe
            var permission = await _permissionRepository.GetByIdAsync(permissionId);
            if (permission == null)
                return false;

            // Journaliser la suppression de la permission avant de la supprimer
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = supprimePar,
                NomUtilisateur = "Système", // À remplacer par le nom d'utilisateur réel si disponible
                TypeAction = TypeAudit.Suppression,
                TypeEntite = "RolePermission",
                EntiteId = roleId,
                Description = $"Suppression de la permission '{permission.Nom}' du rôle '{role.Nom}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    RoleId = roleId,
                    RoleNom = role.Nom,
                    PermissionId = permissionId,
                    PermissionNom = permission.Nom,
                    PermissionCode = permission.Code
                })
            });

            // Supprimer la permission du rôle
            return await _roleRepository.RemovePermissionAsync(roleId, permissionId);
        }

        /// <summary>
        /// Récupère tous les rôles avec des statistiques (nombre d'utilisateurs et de permissions)
        /// </summary>
        /// <returns>Liste des rôles avec leurs statistiques</returns>
        public async Task<IEnumerable<RoleWithStats>> GetAllWithStatsAsync()
        {
            // Récupérer tous les rôles
            var roles = await _roleRepository.GetAllAsync();
            var result = new List<RoleWithStats>();

            foreach (var role in roles)
            {
                // Récupérer le nombre d'utilisateurs pour ce rôle
                var utilisateurs = await _roleRepository.GetUserCountByRoleIdAsync(role.Id);

                // Récupérer le nombre de permissions pour ce rôle
                var permissions = await _roleRepository.GetPermissionCountByRoleIdAsync(role.Id);

                // Créer l'objet RoleWithStats
                var roleWithStats = new RoleWithStats
                {
                    Id = role.Id,
                    Nom = role.Nom,
                    Description = role.Description,
                    NombreUtilisateurs = utilisateurs,
                    NombrePermissions = permissions,
                    DateCreation = role.DateCreation,
                    CreePar = role.CreePar,
                    DateModification = role.DateModification,
                    ModifiePar = role.ModifiePar
                };

                result.Add(roleWithStats);
            }

            return result;
        }

        /// <summary>
        /// Récupère toutes les permissions disponibles
        /// </summary>
        /// <returns>Liste de toutes les permissions</returns>
        public async Task<IEnumerable<Permission>> GetAllPermissionsAsync()
        {
            return await _permissionRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère les permissions associées à un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Liste des permissions du rôle</returns>
        public async Task<IEnumerable<Permission>> GetPermissionsByRoleIdAsync(int roleId)
        {
            if (roleId <= 0)
                return new List<Permission>();

            return await _permissionRepository.GetByRoleIdAsync(roleId);
        }

        /// <summary>
        /// Met à jour les permissions d'un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <param name="permissionIds">Liste des identifiants des permissions à associer au rôle</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdatePermissionsAsync(int roleId, List<int> permissionIds, int modifiePar)
        {
            if (roleId <= 0 || permissionIds == null || modifiePar <= 0)
                return false;

            // Vérifier si le rôle existe
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null)
                return false;

            // Récupérer les permissions actuelles du rôle
            var currentPermissions = await GetPermissionsByRoleIdAsync(roleId);
            var currentPermissionIds = new List<int>();
            foreach (var permission in currentPermissions)
            {
                currentPermissionIds.Add(permission.Id);
            }

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = "Système", // À remplacer par le nom d'utilisateur réel si disponible
                TypeAction = TypeAudit.Modification,
                TypeEntite = "RolePermissions",
                EntiteId = roleId,
                Description = $"Mise à jour des permissions du rôle '{role.Nom}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    RoleId = roleId,
                    RoleNom = role.Nom,
                    PermissionIds = currentPermissionIds
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    RoleId = roleId,
                    RoleNom = role.Nom,
                    PermissionIds = permissionIds
                })
            });

            // Mettre à jour les permissions du rôle
            return await _roleRepository.UpdatePermissionsAsync(roleId, permissionIds, modifiePar);
        }
    }
}
