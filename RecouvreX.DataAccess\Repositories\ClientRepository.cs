using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les clients
    /// </summary>
    public class ClientRepository : BaseRepository<Client>, IClientRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public ClientRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "Clients")
        {
        }

        /// <summary>
        /// Récupère un client par son code
        /// </summary>
        /// <param name="code">Code du client</param>
        /// <returns>Client trouvé ou null</returns>
        public async Task<Client> GetByCodeAsync(string code)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Clients WHERE Code = @Code AND EstActif = 1";
                return await connection.QueryFirstOrDefaultAsync<Client>(query, new { Code = code });
            }
        }

        /// <summary>
        /// Récupère un client avec ses contacts
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Client avec ses contacts</returns>
        public async Task<Client> GetWithContactsAsync(int clientId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT c.*, co.*
                    FROM Clients c
                    LEFT JOIN Contacts co ON c.Id = co.ClientId
                    WHERE c.Id = @ClientId AND c.EstActif = 1";

                Client client = null;
                var contacts = new List<Contact>();

                var results = await connection.QueryAsync<Client, Contact, Client>(
                    query,
                    (c, co) =>
                    {
                        if (client == null)
                        {
                            client = c;
                            client.Contacts = new List<Contact>();
                        }

                        if (co != null && co.Id != 0)
                        {
                            client.Contacts.Add(co);
                        }

                        return client;
                    },
                    new { ClientId = clientId },
                    splitOn: "Id");

                return results.FirstOrDefault();
            }
        }

        /// <summary>
        /// Récupère un client avec ses factures
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Client avec ses factures</returns>
        public async Task<Client> GetWithFacturesAsync(int clientId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT c.*, f.*
                    FROM Clients c
                    LEFT JOIN Factures f ON c.Id = f.ClientId
                    WHERE c.Id = @ClientId AND c.EstActif = 1";

                Client client = null;
                var factures = new List<Facture>();

                var results = await connection.QueryAsync<Client, Facture, Client>(
                    query,
                    (c, f) =>
                    {
                        if (client == null)
                        {
                            client = c;
                            client.Factures = new List<Facture>();
                        }

                        if (f != null && f.Id != 0)
                        {
                            client.Factures.Add(f);
                        }

                        return client;
                    },
                    new { ClientId = clientId },
                    splitOn: "Id");

                return results.FirstOrDefault();
            }
        }

        /// <summary>
        /// Récupère tous les clients d'un commercial
        /// </summary>
        /// <param name="commercialId">Identifiant du commercial</param>
        /// <returns>Liste des clients du commercial</returns>
        public async Task<IEnumerable<Client>> GetByCommercialIdAsync(int commercialId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Clients WHERE CommercialId = @CommercialId AND EstActif = 1";
                return await connection.QueryAsync<Client>(query, new { CommercialId = commercialId });
            }
        }

        /// <summary>
        /// Recherche des clients par nom ou raison sociale
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des clients correspondant au terme de recherche</returns>
        public async Task<IEnumerable<Client>> SearchByNameAsync(string searchTerm)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT *
                    FROM Clients
                    WHERE (RaisonSociale LIKE @SearchTerm OR Code LIKE @SearchTerm)
                        AND EstActif = 1";

                return await connection.QueryAsync<Client>(query, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        /// <summary>
        /// Met à jour le solde d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="montant">Montant à ajouter (positif) ou soustraire (négatif) au solde</param>
        /// <returns>Nouveau solde du client</returns>
        public async Task<decimal> UpdateSoldeAsync(int clientId, decimal montant)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Récupérer le solde actuel
                var queryGet = "SELECT SoldeActuel FROM Clients WHERE Id = @ClientId";
                var soldeActuel = await connection.ExecuteScalarAsync<decimal>(queryGet, new { ClientId = clientId });

                // Calculer le nouveau solde
                var nouveauSolde = soldeActuel + montant;

                // Mettre à jour le solde
                var queryUpdate = @"
                    UPDATE Clients
                    SET SoldeActuel = @NouveauSolde,
                        DateModification = @DateModification
                    WHERE Id = @ClientId";

                var parameters = new
                {
                    ClientId = clientId,
                    NouveauSolde = nouveauSolde,
                    DateModification = DateTime.Now
                };

                await connection.ExecuteAsync(queryUpdate, parameters);

                return nouveauSolde;
            }
        }

        /// <summary>
        /// Récupère les clients avec des factures en retard
        /// </summary>
        /// <returns>Liste des clients avec des factures en retard</returns>
        public async Task<IEnumerable<Client>> GetWithOverdueInvoicesAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT DISTINCT c.*
                    FROM Clients c
                    INNER JOIN Factures f ON c.Id = f.ClientId
                    WHERE f.Statut = @Statut
                        AND c.EstActif = 1
                        AND f.EstActif = 1";

                return await connection.QueryAsync<Client>(query, new { Statut = StatutFacture.EnRetard });
            }
        }

        /// <summary>
        /// Récupère les clients par segment
        /// </summary>
        /// <param name="segment">Segment de client</param>
        /// <returns>Liste des clients du segment spécifié</returns>
        public async Task<IEnumerable<Client>> GetBySegmentAsync(SegmentClient segment)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT *
                    FROM Clients
                    WHERE Segment = @Segment
                        AND EstActif = 1
                    ORDER BY RaisonSociale";

                return await connection.QueryAsync<Client>(query, new { Segment = (int)segment });
            }
        }

        /// <summary>
        /// Met à jour le segment d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="segment">Nouveau segment</param>
        /// <param name="commentaire">Commentaire sur la segmentation</param>
        /// <param name="segmentationManuelle">Indique si la segmentation a été faite manuellement</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Client mis à jour</returns>
        public async Task<Client> UpdateSegmentAsync(int clientId, SegmentClient segment, string commentaire, bool segmentationManuelle, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE Clients
                    SET Segment = @Segment,
                        CommentaireSegmentation = @Commentaire,
                        DateSegmentation = @DateSegmentation,
                        SegmentationManuelle = @SegmentationManuelle,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @ClientId AND EstActif = 1;

                    SELECT *
                    FROM Clients
                    WHERE Id = @ClientId AND EstActif = 1";

                var parameters = new
                {
                    ClientId = clientId,
                    Segment = (int)segment,
                    Commentaire = commentaire,
                    DateSegmentation = DateTime.Now,
                    SegmentationManuelle = segmentationManuelle,
                    DateModification = DateTime.Now,
                    ModifiePar = modifiePar
                };

                return await connection.QueryFirstOrDefaultAsync<Client>(query, parameters);
            }
        }

        /// <summary>
        /// Récupère la répartition des clients par segment
        /// </summary>
        /// <returns>Dictionnaire avec le segment comme clé et le nombre de clients comme valeur</returns>
        public async Task<Dictionary<SegmentClient, int>> GetClientDistributionBySegmentAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT Segment, COUNT(*) AS Count
                    FROM Clients
                    WHERE EstActif = 1
                    GROUP BY Segment
                    ORDER BY Segment";

                var results = await connection.QueryAsync<(int Segment, int Count)>(query);

                return results.ToDictionary(
                    r => (SegmentClient)r.Segment,
                    r => r.Count);
            }
        }
    }
}
