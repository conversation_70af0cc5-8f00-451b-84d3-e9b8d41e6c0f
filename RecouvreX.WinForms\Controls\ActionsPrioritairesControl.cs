using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Controls
{
    /// <summary>
    /// Contrôle utilisateur pour afficher les actions prioritaires
    /// </summary>
    public partial class ActionsPrioritairesControl : UserControl
    {
        private readonly IActionPrioritaireService _actionPrioritaireService;
        private readonly IUtilisateurService _utilisateurService;
        private readonly int _currentUserId;
        private DataTable _actionsDataTable;
        private List<ActionPrioritaire> _actions;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="actionPrioritaireService">Service de gestion des actions prioritaires</param>
        /// <param name="utilisateurService">Service de gestion des utilisateurs</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        public ActionsPrioritairesControl(
            IActionPrioritaireService actionPrioritaireService,
            IUtilisateurService utilisateurService,
            int currentUserId)
        {
            _actionPrioritaireService = actionPrioritaireService ?? throw new ArgumentNullException(nameof(actionPrioritaireService));
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _currentUserId = currentUserId;

            InitializeComponent();
            InitializeDataTable();

            // Charger les données au chargement du contrôle
            this.Load += async (s, e) => await LoadDataAsync();
        }

        /// <summary>
        /// Initialise la table de données
        /// </summary>
        private void InitializeDataTable()
        {
            _actionsDataTable = new DataTable();
            _actionsDataTable.Columns.Add("Id", typeof(int));
            _actionsDataTable.Columns.Add("Client", typeof(string));
            _actionsDataTable.Columns.Add("Description", typeof(string));
            _actionsDataTable.Columns.Add("TypeAction", typeof(string));
            _actionsDataTable.Columns.Add("NiveauPriorite", typeof(string));
            _actionsDataTable.Columns.Add("DateEcheance", typeof(DateTime));
            _actionsDataTable.Columns.Add("Statut", typeof(string));
            _actionsDataTable.Columns.Add("ScorePriorite", typeof(int));

            // Configurer la source de données du DataGridView
            actionsDataGridView.DataSource = _actionsDataTable;
        }

        /// <summary>
        /// Charge les données des actions prioritaires
        /// </summary>
        public async Task LoadDataAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Récupérer les actions prioritaires
                _actions = (await _actionPrioritaireService.GetAllAsync())
                    .Where(a => !a.EstCompletee && a.DateEcheance >= DateTime.Now)
                    .OrderByDescending(a => a.ScorePriorite)
                    .Take(10)
                    .ToList();

                // Mettre à jour la table de données
                UpdateDataTable(_actions);

                // Mettre à jour le compteur
                countLabel.Text = $"{_actions.Count} action(s) prioritaire(s)";

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des actions prioritaires : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Met à jour la table de données avec les actions
        /// </summary>
        private void UpdateDataTable(List<ActionPrioritaire> actions)
        {
            // Effacer les données existantes
            _actionsDataTable.Rows.Clear();

            // Ajouter les nouvelles données
            foreach (var action in actions)
            {
                string statut = action.EstCompletee ? "Complétée" : (action.DateEcheance < DateTime.Now ? "En retard" : "À faire");

                _actionsDataTable.Rows.Add(
                    action.Id,
                    action.Client?.RaisonSociale,
                    action.Description,
                    action.TypeAction.ToString(),
                    action.NiveauPriorite.ToString(),
                    action.DateEcheance,
                    statut,
                    action.ScorePriorite
                );
            }

            // Configurer les colonnes
            if (actionsDataGridView.Columns.Count > 0)
            {
                actionsDataGridView.Columns["Id"].Visible = false;
                actionsDataGridView.Columns["ScorePriorite"].Visible = false;
                actionsDataGridView.Columns["Client"].HeaderText = "Client";
                actionsDataGridView.Columns["Description"].HeaderText = "Description";
                actionsDataGridView.Columns["TypeAction"].HeaderText = "Type d'action";
                actionsDataGridView.Columns["NiveauPriorite"].HeaderText = "Priorité";
                actionsDataGridView.Columns["DateEcheance"].HeaderText = "Échéance";
                actionsDataGridView.Columns["Statut"].HeaderText = "Statut";

                // Ajuster la largeur des colonnes
                actionsDataGridView.Columns["Client"].Width = 150;
                actionsDataGridView.Columns["Description"].Width = 300;
                actionsDataGridView.Columns["TypeAction"].Width = 120;
                actionsDataGridView.Columns["NiveauPriorite"].Width = 80;
                actionsDataGridView.Columns["DateEcheance"].Width = 100;
                actionsDataGridView.Columns["Statut"].Width = 80;

                // Formater les dates
                actionsDataGridView.Columns["DateEcheance"].DefaultCellStyle.Format = "dd/MM/yyyy";
            }
        }

        /// <summary>
        /// Gère le formatage des cellules du DataGridView
        /// </summary>
        private void ActionsDataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            // Colorer les cellules selon le niveau de priorité
            if (actionsDataGridView.Columns[e.ColumnIndex].Name == "NiveauPriorite" && e.Value != null)
            {
                string priorite = e.Value.ToString();
                switch (priorite)
                {
                    case "Critique":
                        e.CellStyle.BackColor = Color.Red;
                        e.CellStyle.ForeColor = Color.White;
                        break;
                    case "Haute":
                        e.CellStyle.BackColor = Color.Orange;
                        e.CellStyle.ForeColor = Color.Black;
                        break;
                    case "Moyenne":
                        e.CellStyle.BackColor = Color.Yellow;
                        e.CellStyle.ForeColor = Color.Black;
                        break;
                    case "Basse":
                        e.CellStyle.BackColor = Color.LightGreen;
                        e.CellStyle.ForeColor = Color.Black;
                        break;
                }
            }

            // Colorer les cellules selon le statut
            if (actionsDataGridView.Columns[e.ColumnIndex].Name == "Statut" && e.Value != null)
            {
                string statut = e.Value.ToString();
                switch (statut)
                {
                    case "En retard":
                        e.CellStyle.BackColor = Color.Red;
                        e.CellStyle.ForeColor = Color.White;
                        break;
                    case "À faire":
                        e.CellStyle.BackColor = Color.LightBlue;
                        e.CellStyle.ForeColor = Color.Black;
                        break;
                    case "Complétée":
                        e.CellStyle.BackColor = Color.LightGreen;
                        e.CellStyle.ForeColor = Color.Black;
                        break;
                }
            }
        }

        /// <summary>
        /// Gère le clic sur le bouton "Voir toutes les actions"
        /// </summary>
        private void ViewAllButton_Click(object sender, EventArgs e)
        {
            // Ouvrir le formulaire de liste des actions prioritaires
            var actionPrioritaireListForm = new Forms.Actions.ActionPrioritaireListForm(
                _actionPrioritaireService,
                null, // ClientService n'est pas disponible dans ce contexte, mais nous devons le passer
                _utilisateurService,
                _currentUserId);

            actionPrioritaireListForm.ShowDialog();

            // Recharger les données après la fermeture du formulaire
            _ = LoadDataAsync();
        }

        /// <summary>
        /// Gère le clic sur le bouton "Générer des actions"
        /// </summary>
        private async void GenerateButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Demander confirmation
                var result = MessageBox.Show(
                    "Voulez-vous générer automatiquement des actions prioritaires pour tous les clients ?",
                    "Confirmation",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Afficher un indicateur de chargement
                    this.Cursor = Cursors.WaitCursor;

                    // Générer les actions prioritaires
                    int count = await _actionPrioritaireService.GenerateActionsAsync(_currentUserId);

                    // Recharger les actions
                    await LoadDataAsync();

                    // Afficher un message de confirmation
                    MessageBox.Show($"{count} actions prioritaires ont été générées avec succès.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la génération des actions prioritaires : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }
    }
}
