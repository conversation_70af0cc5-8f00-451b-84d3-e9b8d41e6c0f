using Microsoft.Extensions.Configuration;
using RecouvreX.Business.Interfaces;
using Serilog;
using System;
using System.Data;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Admin
{
    /// <summary>
    /// Formulaire d'administration de la base de données
    /// </summary>
    public partial class DatabaseAdminForm : Form
    {
        private readonly IDatabaseAdminService _databaseAdminService;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="databaseAdminService">Service d'administration de la base de données</param>
        /// <param name="configuration">Configuration de l'application</param>
        public DatabaseAdminForm(IDatabaseAdminService databaseAdminService, IConfiguration configuration)
        {
            _databaseAdminService = databaseAdminService ?? throw new ArgumentNullException(nameof(databaseAdminService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            
            InitializeComponent();
        }

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void DatabaseAdminForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Charger l'icône du formulaire
                try
                {
                    this.Icon = new Icon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "database.ico"));
                }
                catch
                {
                    // Ignorer les erreurs d'icône
                }

                // Charger les informations sur la base de données
                await LoadDatabaseInfoAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'administration de la base de données");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Charge les informations sur la base de données
        /// </summary>
        private async Task LoadDatabaseInfoAsync()
        {
            try
            {
                // Désactiver les contrôles pendant le chargement
                EnableControls(false);
                
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;
                
                // Récupérer les informations sur la base de données
                var dbInfo = await _databaseAdminService.GetDatabaseInfoAsync();
                
                // Afficher les informations
                databaseNameLabel.Text = dbInfo["DatabaseName"].ToString();
                createDateLabel.Text = dbInfo["CreateDate"].ToString();
                sizeLabel.Text = $"{dbInfo["SizeMB"]} Mo";
                tableCountLabel.Text = dbInfo["TableCount"].ToString();
                rowCountLabel.Text = dbInfo["TotalRows"].ToString();
                
                // Charger la liste des tables
                var tables = await _databaseAdminService.GetTablesAsync();
                tablesListBox.Items.Clear();
                foreach (var table in tables)
                {
                    tablesListBox.Items.Add(table);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des informations sur la base de données");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Réactiver les contrôles
                EnableControls(true);
                
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Sauvegarder
        /// </summary>
        private async void BackupButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Afficher une boîte de dialogue pour sélectionner le fichier de sauvegarde
                using (var saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Filter = "Fichiers de sauvegarde SQL Server (*.bak)|*.bak";
                    saveFileDialog.Title = "Sauvegarder la base de données";
                    saveFileDialog.FileName = $"RecouvreX_{DateTime.Now:yyyyMMdd_HHmmss}.bak";
                    
                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        // Désactiver les contrôles pendant la sauvegarde
                        EnableControls(false);
                        
                        // Afficher un indicateur de chargement
                        Cursor.Current = Cursors.WaitCursor;
                        
                        // Effectuer la sauvegarde
                        bool success = await _databaseAdminService.BackupDatabaseAsync(
                            saveFileDialog.FileName, 
                            "FULL", 
                            $"Sauvegarde complète de la base de données RecouvreX - {DateTime.Now}");
                        
                        // Afficher le résultat
                        if (success)
                        {
                            MessageBox.Show($"La base de données a été sauvegardée avec succès dans le fichier {saveFileDialog.FileName}.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("Impossible de sauvegarder la base de données.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la sauvegarde de la base de données");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Réactiver les contrôles
                EnableControls(true);
                
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Restaurer
        /// </summary>
        private async void RestoreButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Afficher une boîte de dialogue pour sélectionner le fichier de sauvegarde
                using (var openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "Fichiers de sauvegarde SQL Server (*.bak)|*.bak";
                    openFileDialog.Title = "Restaurer la base de données";
                    
                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        // Demander confirmation
                        DialogResult result = MessageBox.Show(
                            "Êtes-vous sûr de vouloir restaurer la base de données ? Cette opération remplacera toutes les données existantes.",
                            "Confirmation",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Warning);
                        
                        if (result == DialogResult.Yes)
                        {
                            // Désactiver les contrôles pendant la restauration
                            EnableControls(false);
                            
                            // Afficher un indicateur de chargement
                            Cursor.Current = Cursors.WaitCursor;
                            
                            // Effectuer la restauration
                            bool success = await _databaseAdminService.RestoreDatabaseAsync(
                                openFileDialog.FileName, 
                                null, 
                                true);
                            
                            // Afficher le résultat
                            if (success)
                            {
                                MessageBox.Show("La base de données a été restaurée avec succès. L'application doit être redémarrée pour prendre en compte les modifications.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                Application.Restart();
                            }
                            else
                            {
                                MessageBox.Show("Impossible de restaurer la base de données.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la restauration de la base de données");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Réactiver les contrôles
                EnableControls(true);
                
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Vérifier l'intégrité
        /// </summary>
        private async void CheckIntegrityButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Désactiver les contrôles pendant la vérification
                EnableControls(false);
                
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;
                
                // Effectuer la vérification
                string result = await _databaseAdminService.CheckDatabaseIntegrityAsync("NONE");
                
                // Afficher le résultat
                resultTextBox.Text = result;
                tabControl.SelectedTab = sqlTabPage;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la vérification de l'intégrité de la base de données");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Réactiver les contrôles
                EnableControls(true);
                
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Exécuter la requête
        /// </summary>
        private async void ExecuteQueryButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer la requête SQL
                string sqlQuery = sqlTextBox.Text.Trim();
                
                if (string.IsNullOrEmpty(sqlQuery))
                {
                    MessageBox.Show("Veuillez saisir une requête SQL.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    sqlTextBox.Focus();
                    return;
                }
                
                // Désactiver les contrôles pendant l'exécution
                EnableControls(false);
                
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;
                
                // Exécuter la requête
                if (sqlQuery.Trim().ToUpper().StartsWith("SELECT"))
                {
                    // Requête de sélection
                    DataTable result = await _databaseAdminService.ExecuteCustomQueryAsync(sqlQuery);
                    
                    // Afficher le résultat
                    resultDataGridView.DataSource = result;
                }
                else
                {
                    // Commande SQL
                    int rowsAffected = await _databaseAdminService.ExecuteCustomCommandAsync(sqlQuery);
                    
                    // Afficher le résultat
                    resultTextBox.Text = $"{rowsAffected} ligne(s) affectée(s).";
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'exécution de la requête SQL");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Réactiver les contrôles
                EnableControls(true);
                
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Planifier une sauvegarde
        /// </summary>
        private async void ScheduleBackupButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Afficher une boîte de dialogue pour sélectionner le fichier de sauvegarde
                using (var saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Filter = "Fichiers de sauvegarde SQL Server (*.bak)|*.bak";
                    saveFileDialog.Title = "Planifier une sauvegarde de la base de données";
                    saveFileDialog.FileName = $"RecouvreX_Backup.bak";
                    
                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        // Désactiver les contrôles pendant la planification
                        EnableControls(false);
                        
                        // Afficher un indicateur de chargement
                        Cursor.Current = Cursors.WaitCursor;
                        
                        // Récupérer les paramètres de planification
                        string schedule = scheduleComboBox.SelectedItem.ToString();
                        TimeSpan time = backupTimeTimePicker.Value.TimeOfDay;
                        
                        // Planifier la sauvegarde
                        bool success = await _databaseAdminService.ScheduleBackupAsync(
                            saveFileDialog.FileName, 
                            schedule, 
                            time);
                        
                        // Afficher le résultat
                        if (success)
                        {
                            MessageBox.Show($"La sauvegarde automatique a été planifiée avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("Impossible de planifier la sauvegarde automatique.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la planification d'une sauvegarde automatique");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Réactiver les contrôles
                EnableControls(true);
                
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Fermer
        /// </summary>
        private void CloseButton_Click(object sender, EventArgs e)
        {
            Close();
        }

        /// <summary>
        /// Active ou désactive les contrôles du formulaire
        /// </summary>
        /// <param name="enabled">True pour activer les contrôles, sinon False</param>
        private void EnableControls(bool enabled)
        {
            backupButton.Enabled = enabled;
            restoreButton.Enabled = enabled;
            checkIntegrityButton.Enabled = enabled;
            executeQueryButton.Enabled = enabled;
            scheduleBackupButton.Enabled = enabled;
            closeButton.Enabled = enabled;
        }
    }
}
