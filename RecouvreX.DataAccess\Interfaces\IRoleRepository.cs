using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des rôles
    /// </summary>
    public interface IRoleRepository : IRepository<Role>
    {
        /// <summary>
        /// Récupère un rôle par son nom
        /// </summary>
        /// <param name="nom">Nom du rôle</param>
        /// <returns>Rôle trouvé ou null</returns>
        Task<Role> GetByNomAsync(string nom);

        /// <summary>
        /// Récupère un rôle avec ses permissions
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Rôle avec ses permissions</returns>
        Task<Role> GetWithPermissionsAsync(int roleId);

        /// <summary>
        /// Récupère tous les rôles avec leurs permissions
        /// </summary>
        /// <returns>Liste des rôles avec leurs permissions</returns>
        Task<IEnumerable<Role>> GetAllWithPermissionsAsync();

        /// <summary>
        /// Ajoute une permission à un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <param name="permissionId">Identifiant de la permission</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'ajout a réussi, sinon False</returns>
        Task<bool> AddPermissionAsync(int roleId, int permissionId, int userId);

        /// <summary>
        /// Supprime une permission d'un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <param name="permissionId">Identifiant de la permission</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> RemovePermissionAsync(int roleId, int permissionId);

        /// <summary>
        /// Récupère le nombre d'utilisateurs associés à un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Nombre d'utilisateurs</returns>
        Task<int> GetUserCountByRoleIdAsync(int roleId);

        /// <summary>
        /// Récupère le nombre de permissions associées à un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Nombre de permissions</returns>
        Task<int> GetPermissionCountByRoleIdAsync(int roleId);

        /// <summary>
        /// Met à jour les permissions d'un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <param name="permissionIds">Liste des identifiants des permissions à associer au rôle</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdatePermissionsAsync(int roleId, List<int> permissionIds, int modifiePar);
    }
}
