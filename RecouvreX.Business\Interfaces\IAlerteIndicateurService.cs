using RecouvreX.Models.Reporting;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des alertes sur indicateurs
    /// </summary>
    public interface IAlerteIndicateurService
    {
        /// <summary>
        /// Récupère toutes les alertes
        /// </summary>
        /// <returns>Liste des alertes</returns>
        Task<IEnumerable<AlerteIndicateur>> GetAllAsync();

        /// <summary>
        /// Récupère une alerte par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'alerte</param>
        /// <returns>Alerte trouvée ou null</returns>
        Task<AlerteIndicateur> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les alertes par indicateur
        /// </summary>
        /// <param name="indicateur">Nom de l'indicateur</param>
        /// <returns>Liste des alertes pour l'indicateur spécifié</returns>
        Task<IEnumerable<AlerteIndicateur>> GetByIndicateurAsync(string indicateur);

        /// <summary>
        /// Récupère les alertes par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des alertes pour l'utilisateur spécifié</returns>
        Task<IEnumerable<AlerteIndicateur>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les alertes actives
        /// </summary>
        /// <returns>Liste des alertes actives</returns>
        Task<IEnumerable<AlerteIndicateur>> GetActiveAsync();

        /// <summary>
        /// Récupère les alertes par sévérité
        /// </summary>
        /// <param name="severite">Niveau de sévérité</param>
        /// <returns>Liste des alertes avec le niveau de sévérité spécifié</returns>
        Task<IEnumerable<AlerteIndicateur>> GetBySeveriteAsync(string severite);

        /// <summary>
        /// Crée une nouvelle alerte
        /// </summary>
        /// <param name="alerte">Alerte à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Alerte créée avec son identifiant généré</returns>
        Task<AlerteIndicateur> CreateAsync(AlerteIndicateur alerte, int creePar);

        /// <summary>
        /// Met à jour une alerte existante
        /// </summary>
        /// <param name="alerte">Alerte à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Alerte mise à jour</returns>
        Task<AlerteIndicateur> UpdateAsync(AlerteIndicateur alerte, int modifiePar);

        /// <summary>
        /// Supprime une alerte
        /// </summary>
        /// <param name="id">Identifiant de l'alerte à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Active ou désactive une alerte
        /// </summary>
        /// <param name="id">Identifiant de l'alerte</param>
        /// <param name="estActive">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> SetActiveStatusAsync(int id, bool estActive, int modifiePar);

        /// <summary>
        /// Vérifie les alertes et déclenche celles qui atteignent leur seuil
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue la vérification</param>
        /// <returns>Liste des alertes déclenchées</returns>
        Task<IEnumerable<AlerteIndicateur>> CheckAlertsAsync(int utilisateurId);

        /// <summary>
        /// Envoie une notification pour une alerte déclenchée
        /// </summary>
        /// <param name="alerte">Alerte déclenchée</param>
        /// <param name="valeurActuelle">Valeur actuelle de l'indicateur</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui envoie la notification</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        Task<bool> SendAlertNotificationAsync(AlerteIndicateur alerte, decimal valeurActuelle, int utilisateurId);
    }
}
