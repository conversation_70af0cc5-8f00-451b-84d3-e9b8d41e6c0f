using RecouvreX.Models;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des utilisateurs
    /// </summary>
    public interface IUtilisateurRepository : IRepository<Utilisateur>
    {
        /// <summary>
        /// Récupère un utilisateur par son nom d'utilisateur
        /// </summary>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <returns>Utilisateur trouvé ou null</returns>
        Task<Utilisateur> GetByNomUtilisateurAsync(string nomUtilisateur);

        /// <summary>
        /// Vérifie si les informations de connexion sont valides
        /// </summary>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="motDePasse">Mot de passe en clair</param>
        /// <returns>Utilisateur authentifié ou null</returns>
        Task<Utilisateur> AuthenticateAsync(string nomUtilisateur, string motDePasse);

        /// <summary>
        /// Met à jour la date de dernière connexion d'un utilisateur
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateLastLoginAsync(int userId);

        /// <summary>
        /// Incrémente le compteur de tentatives de connexion échouées
        /// </summary>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <returns>Nombre de tentatives échouées</returns>
        Task<int> IncrementFailedLoginAttemptsAsync(string nomUtilisateur);

        /// <summary>
        /// Réinitialise le compteur de tentatives de connexion échouées
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <returns>True si la réinitialisation a réussi, sinon False</returns>
        Task<bool> ResetFailedLoginAttemptsAsync(int userId);

        /// <summary>
        /// Verrouille ou déverrouille un compte utilisateur
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <param name="estVerrouille">True pour verrouiller, False pour déverrouiller</param>
        /// <returns>True si l'opération a réussi, sinon False</returns>
        Task<bool> SetAccountLockedStatusAsync(int userId, bool estVerrouille);

        /// <summary>
        /// Change le mot de passe d'un utilisateur
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <param name="nouveauMotDePasse">Nouveau mot de passe en clair</param>
        /// <returns>True si le changement a réussi, sinon False</returns>
        Task<bool> ChangePasswordAsync(int userId, string nouveauMotDePasse);

        /// <summary>
        /// Met à jour le mot de passe d'un utilisateur (déjà haché)
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <param name="motDePasseHache">Mot de passe haché</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdatePasswordAsync(int userId, string motDePasseHache, int modifiePar);

        /// <summary>
        /// Active ou désactive un utilisateur
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <param name="estActif">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateActiveStatusAsync(int userId, bool estActif, int modifiePar);
    }
}
