namespace RecouvreX.WinForms.Forms.Administration
{
    partial class UtilisateurEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // UtilisateurEditForm
            //
            this.ClientSize = new System.Drawing.Size(500, 450);
            this.Name = "UtilisateurEditForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Utilisateur"; // Titre par défaut, sera mis à jour dans le constructeur
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Load += new System.EventHandler(this.UtilisateurEditForm_Load);
            this.ResumeLayout(false);

            // Panneau principal
            System.Windows.Forms.TableLayoutPanel mainPanel = new System.Windows.Forms.TableLayoutPanel();
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Padding = new System.Windows.Forms.Padding(10);
            mainPanel.ColumnCount = 2;
            mainPanel.RowCount = 10;
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 30F));
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 70F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            this.Controls.Add(mainPanel);

            // Nom d'utilisateur
            System.Windows.Forms.Label nomUtilisateurLabel = new System.Windows.Forms.Label();
            nomUtilisateurLabel.Text = "Nom d'utilisateur* :";
            nomUtilisateurLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(nomUtilisateurLabel, 0, 0);

            System.Windows.Forms.TextBox nomUtilisateurTextBox = new System.Windows.Forms.TextBox();
            nomUtilisateurTextBox.Name = "nomUtilisateurTextBox";
            nomUtilisateurTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            nomUtilisateurTextBox.Validating += new System.ComponentModel.CancelEventHandler(this.NomUtilisateurTextBox_Validating);
            mainPanel.Controls.Add(nomUtilisateurTextBox, 1, 0);

            // Mot de passe (uniquement en mode ajout)
            this.motDePasseLabel = new System.Windows.Forms.Label();
            this.motDePasseLabel.Text = "Mot de passe* :";
            this.motDePasseLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            this.motDePasseLabel.Visible = true; // Sera mis à jour dans le constructeur
            mainPanel.Controls.Add(this.motDePasseLabel, 0, 1);

            this.motDePasseTextBox = new System.Windows.Forms.TextBox();
            this.motDePasseTextBox.Name = "motDePasseTextBox";
            this.motDePasseTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            this.motDePasseTextBox.PasswordChar = '•';
            this.motDePasseTextBox.Visible = true; // Sera mis à jour dans le constructeur
            this.motDePasseTextBox.Validating += new System.ComponentModel.CancelEventHandler(this.MotDePasseTextBox_Validating);
            mainPanel.Controls.Add(this.motDePasseTextBox, 1, 1);

            // Confirmation du mot de passe (uniquement en mode ajout)
            this.confirmationMotDePasseLabel = new System.Windows.Forms.Label();
            this.confirmationMotDePasseLabel.Text = "Confirmation* :";
            this.confirmationMotDePasseLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            this.confirmationMotDePasseLabel.Visible = true; // Sera mis à jour dans le constructeur
            mainPanel.Controls.Add(this.confirmationMotDePasseLabel, 0, 2);

            this.confirmationMotDePasseTextBox = new System.Windows.Forms.TextBox();
            this.confirmationMotDePasseTextBox.Name = "confirmationMotDePasseTextBox";
            this.confirmationMotDePasseTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            this.confirmationMotDePasseTextBox.PasswordChar = '•';
            this.confirmationMotDePasseTextBox.Visible = true; // Sera mis à jour dans le constructeur
            this.confirmationMotDePasseTextBox.Validating += new System.ComponentModel.CancelEventHandler(this.ConfirmationMotDePasseTextBox_Validating);
            mainPanel.Controls.Add(this.confirmationMotDePasseTextBox, 1, 2);

            // Nom
            System.Windows.Forms.Label nomLabel = new System.Windows.Forms.Label();
            nomLabel.Text = "Nom* :";
            nomLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(nomLabel, 0, 3);

            System.Windows.Forms.TextBox nomTextBox = new System.Windows.Forms.TextBox();
            nomTextBox.Name = "nomTextBox";
            nomTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            nomTextBox.Validating += new System.ComponentModel.CancelEventHandler(this.NomTextBox_Validating);
            mainPanel.Controls.Add(nomTextBox, 1, 3);

            // Prénom
            System.Windows.Forms.Label prenomLabel = new System.Windows.Forms.Label();
            prenomLabel.Text = "Prénom* :";
            prenomLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(prenomLabel, 0, 4);

            System.Windows.Forms.TextBox prenomTextBox = new System.Windows.Forms.TextBox();
            prenomTextBox.Name = "prenomTextBox";
            prenomTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            prenomTextBox.Validating += new System.ComponentModel.CancelEventHandler(this.PrenomTextBox_Validating);
            mainPanel.Controls.Add(prenomTextBox, 1, 4);

            // Email
            System.Windows.Forms.Label emailLabel = new System.Windows.Forms.Label();
            emailLabel.Text = "Email* :";
            emailLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(emailLabel, 0, 5);

            System.Windows.Forms.TextBox emailTextBox = new System.Windows.Forms.TextBox();
            emailTextBox.Name = "emailTextBox";
            emailTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            emailTextBox.Validating += new System.ComponentModel.CancelEventHandler(this.EmailTextBox_Validating);
            mainPanel.Controls.Add(emailTextBox, 1, 5);

            // Téléphone
            System.Windows.Forms.Label telephoneLabel = new System.Windows.Forms.Label();
            telephoneLabel.Text = "Téléphone :";
            telephoneLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(telephoneLabel, 0, 6);

            System.Windows.Forms.TextBox telephoneTextBox = new System.Windows.Forms.TextBox();
            telephoneTextBox.Name = "telephoneTextBox";
            telephoneTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(telephoneTextBox, 1, 6);

            // Rôle
            System.Windows.Forms.Label roleLabel = new System.Windows.Forms.Label();
            roleLabel.Text = "Rôle* :";
            roleLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(roleLabel, 0, 7);

            System.Windows.Forms.ComboBox roleComboBox = new System.Windows.Forms.ComboBox();
            roleComboBox.Name = "roleComboBox";
            roleComboBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            roleComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            roleComboBox.Validating += new System.ComponentModel.CancelEventHandler(this.RoleComboBox_Validating);
            mainPanel.Controls.Add(roleComboBox, 1, 7);

            // Actif
            System.Windows.Forms.Label actifLabel = new System.Windows.Forms.Label();
            actifLabel.Text = "Actif :";
            actifLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(actifLabel, 0, 8);

            System.Windows.Forms.CheckBox actifCheckBox = new System.Windows.Forms.CheckBox();
            actifCheckBox.Name = "actifCheckBox";
            actifCheckBox.Anchor = System.Windows.Forms.AnchorStyles.Left;
            actifCheckBox.Checked = true;
            mainPanel.Controls.Add(actifCheckBox, 1, 8);

            // Boutons
            System.Windows.Forms.FlowLayoutPanel buttonsPanel = new System.Windows.Forms.FlowLayoutPanel();
            buttonsPanel.FlowDirection = System.Windows.Forms.FlowDirection.RightToLeft;
            buttonsPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            buttonsPanel.Anchor = System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(buttonsPanel, 1, 9);
            mainPanel.SetColumnSpan(buttonsPanel, 2);

            System.Windows.Forms.Button cancelButton = new System.Windows.Forms.Button();
            cancelButton.Name = "cancelButton";
            cancelButton.Text = "Annuler";
            cancelButton.Size = new System.Drawing.Size(100, 30);
            cancelButton.Click += new System.EventHandler(this.CancelButton_Click);
            buttonsPanel.Controls.Add(cancelButton);

            System.Windows.Forms.Button saveButton = new System.Windows.Forms.Button();
            saveButton.Name = "saveButton";
            saveButton.Text = "Enregistrer";
            saveButton.Size = new System.Drawing.Size(100, 30);
            saveButton.Click += new System.EventHandler(this.SaveButton_Click);
            buttonsPanel.Controls.Add(saveButton);

            // Note sur les champs obligatoires
            System.Windows.Forms.Label noteLabel = new System.Windows.Forms.Label();
            noteLabel.Text = "* Champs obligatoires";
            noteLabel.AutoSize = true;
            noteLabel.ForeColor = System.Drawing.Color.Red;
            noteLabel.Location = new System.Drawing.Point(10, this.ClientSize.Height - 30);
            this.Controls.Add(noteLabel);
        }

        #endregion
    }
}
