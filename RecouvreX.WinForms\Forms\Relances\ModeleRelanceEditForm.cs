using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using RecouvreX.WinForms.Helpers;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Relances
{
    public partial class ModeleRelanceEditForm : Form
    {
        private readonly IModeleRelanceService _modeleRelanceService;
        private readonly IFactureService _factureService;
        private readonly IClientService _clientService;
        private readonly int _currentUserId;
        private readonly ModeleRelance _modele;
        private readonly bool _isEditMode;
        private List<VariableDynamique> _variables;

        public ModeleRelanceEditForm(IModeleRelanceService modeleRelanceService, int currentUserId, ModeleRelance modele = null)
        {
            _modeleRelanceService = modeleRelanceService ?? throw new ArgumentNullException(nameof(modeleRelanceService));
            _currentUserId = currentUserId;
            _modele = modele ?? new ModeleRelance { UtilisateurId = currentUserId, EstActif = true, NiveauFermete = 1 };
            _isEditMode = modele != null;

            InitializeComponent();
        }

        private async void ModeleRelanceEditForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Configurer le titre du formulaire
                this.Text = _isEditMode ? "Modifier un modèle de relance" : "Ajouter un modèle de relance";

                // Remplir les combobox
                var typeComboBox = this.Controls.Find("typeComboBox", true)[0] as ComboBox;
                var niveauComboBox = this.Controls.Find("niveauComboBox", true)[0] as ComboBox;

                if (typeComboBox != null)
                {
                    typeComboBox.Items.Clear();
                    typeComboBox.Items.Add(TypeModeleRelance.Email);
                    typeComboBox.Items.Add(TypeModeleRelance.Lettre);
                    typeComboBox.Items.Add(TypeModeleRelance.SMS);
                }

                if (niveauComboBox != null)
                {
                    niveauComboBox.Items.Clear();
                    niveauComboBox.Items.Add("Niveau 1 (Courtois)");
                    niveauComboBox.Items.Add("Niveau 2 (Ferme)");
                    niveauComboBox.Items.Add("Niveau 3 (Mise en demeure)");
                }

                // Charger les variables dynamiques
                await LoadVariablesAsync();

                // Remplir les champs avec les valeurs du modèle
                if (_isEditMode)
                {
                    var nomTextBox = this.Controls.Find("nomTextBox", true)[0] as TextBox;
                    var descriptionTextBox = this.Controls.Find("descriptionTextBox", true)[0] as TextBox;
                    var objetTextBox = this.Controls.Find("objetTextBox", true)[0] as TextBox;
                    var contenuTextBox = this.Controls.Find("contenuTextBox", true)[0] as TextBox;
                    var estParDefautCheckBox = this.Controls.Find("estParDefautCheckBox", true)[0] as CheckBox;
                    var estActifCheckBox = this.Controls.Find("estActifCheckBox", true)[0] as CheckBox;

                    if (nomTextBox != null) nomTextBox.Text = _modele.Nom;
                    if (descriptionTextBox != null) descriptionTextBox.Text = _modele.Description;
                    if (typeComboBox != null) typeComboBox.SelectedItem = _modele.Type;
                    if (niveauComboBox != null) niveauComboBox.SelectedIndex = _modele.NiveauFermete - 1;
                    if (objetTextBox != null) objetTextBox.Text = _modele.Objet;
                    if (contenuTextBox != null) contenuTextBox.Text = _modele.Contenu;
                    if (estParDefautCheckBox != null) estParDefautCheckBox.Checked = _modele.EstParDefaut;
                    if (estActifCheckBox != null) estActifCheckBox.Checked = _modele.EstActif;
                }
                else
                {
                    // Valeurs par défaut pour un nouveau modèle
                    var typeComboBoxValue = this.Controls.Find("typeComboBox", true)[0] as ComboBox;
                    var niveauComboBoxValue = this.Controls.Find("niveauComboBox", true)[0] as ComboBox;
                    var estActifCheckBox = this.Controls.Find("estActifCheckBox", true)[0] as CheckBox;

                    if (typeComboBoxValue != null) typeComboBoxValue.SelectedIndex = 0;
                    if (niveauComboBoxValue != null) niveauComboBoxValue.SelectedIndex = 0;
                    if (estActifCheckBox != null) estActifCheckBox.Checked = true;
                }

                // Mettre à jour la visibilité du champ Objet en fonction du type sélectionné
                UpdateObjetVisibility();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'édition de modèle de relance");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadVariablesAsync()
        {
            try
            {
                // Récupérer les variables dynamiques
                _variables = (await _modeleRelanceService.GetAllVariablesAsync()) as List<VariableDynamique>;

                // Remplir la liste des variables
                var variablesListBox = this.Controls.Find("variablesListBox", true)[0] as ListBox;
                if (variablesListBox != null && _variables != null)
                {
                    variablesListBox.Items.Clear();
                    foreach (var variable in _variables)
                    {
                        variablesListBox.Items.Add($"{{{variable.Nom}}} - {variable.Description}");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des variables dynamiques");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des variables dynamiques : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateObjetVisibility()
        {
            var typeComboBox = this.Controls.Find("typeComboBox", true)[0] as ComboBox;
            var objetLabel = this.Controls.Find("objetLabel", true)[0] as Label;
            var objetTextBox = this.Controls.Find("objetTextBox", true)[0] as TextBox;

            if (typeComboBox != null && objetLabel != null && objetTextBox != null)
            {
                bool isEmail = typeComboBox.SelectedItem?.ToString() == TypeModeleRelance.Email;
                objetLabel.Visible = isEmail;
                objetTextBox.Visible = isEmail;
            }
        }

        private void TypeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateObjetVisibility();
        }

        private void VariablesListBox_DoubleClick(object sender, EventArgs e)
        {
            var variablesListBox = sender as ListBox;
            var contenuTextBox = this.Controls.Find("contenuTextBox", true)[0] as TextBox;

            if (variablesListBox != null && contenuTextBox != null && variablesListBox.SelectedIndex >= 0)
            {
                string selectedItem = variablesListBox.SelectedItem.ToString();
                string variableName = selectedItem.Substring(0, selectedItem.IndexOf('}') + 1);

                // Insérer la variable à la position du curseur
                contenuTextBox.SelectedText = variableName;
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer les valeurs des champs
                var nomTextBox = this.Controls.Find("nomTextBox", true)[0] as TextBox;
                var descriptionTextBox = this.Controls.Find("descriptionTextBox", true)[0] as TextBox;
                var typeComboBox = this.Controls.Find("typeComboBox", true)[0] as ComboBox;
                var niveauComboBox = this.Controls.Find("niveauComboBox", true)[0] as ComboBox;
                var objetTextBox = this.Controls.Find("objetTextBox", true)[0] as TextBox;
                var contenuTextBox = this.Controls.Find("contenuTextBox", true)[0] as TextBox;
                var estParDefautCheckBox = this.Controls.Find("estParDefautCheckBox", true)[0] as CheckBox;
                var estActifCheckBox = this.Controls.Find("estActifCheckBox", true)[0] as CheckBox;

                // Valider les champs obligatoires
                if (string.IsNullOrWhiteSpace(nomTextBox?.Text))
                {
                    MessageBox.Show("Le nom du modèle est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    nomTextBox?.Focus();
                    return;
                }

                if (typeComboBox?.SelectedItem == null)
                {
                    MessageBox.Show("Le type de modèle est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    typeComboBox?.Focus();
                    return;
                }

                if (niveauComboBox?.SelectedIndex < 0)
                {
                    MessageBox.Show("Le niveau de fermeté est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    niveauComboBox?.Focus();
                    return;
                }

                if (typeComboBox?.SelectedItem.ToString() == TypeModeleRelance.Email && string.IsNullOrWhiteSpace(objetTextBox?.Text))
                {
                    MessageBox.Show("L'objet de l'email est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    objetTextBox?.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(contenuTextBox?.Text))
                {
                    MessageBox.Show("Le contenu du modèle est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    contenuTextBox?.Focus();
                    return;
                }

                // Mettre à jour l'objet modèle
                _modele.Nom = nomTextBox?.Text;
                _modele.Description = descriptionTextBox?.Text;
                _modele.Type = typeComboBox?.SelectedItem.ToString();
                _modele.NiveauFermete = niveauComboBox?.SelectedIndex + 1 ?? 1;
                _modele.Objet = objetTextBox?.Text;
                _modele.Contenu = contenuTextBox?.Text;
                _modele.EstParDefaut = estParDefautCheckBox?.Checked ?? false;
                _modele.EstActif = estActifCheckBox?.Checked ?? true;

                // Enregistrer le modèle
                if (_isEditMode)
                {
                    await _modeleRelanceService.UpdateAsync(_modele, _currentUserId);
                    MessageBox.Show("Le modèle de relance a été modifié avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _modeleRelanceService.CreateAsync(_modele, _currentUserId);
                    MessageBox.Show("Le modèle de relance a été créé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Fermer le formulaire
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement du modèle de relance");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement du modèle de relance : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void PreviewButton_Click(object sender, EventArgs e)
        {
            try
            {
                var contenuTextBox = this.Controls.Find("contenuTextBox", true)[0] as TextBox;
                var nomTextBox = this.Controls.Find("nomTextBox", true)[0] as TextBox;

                if (contenuTextBox != null && !string.IsNullOrEmpty(contenuTextBox.Text))
                {
                    string titre = $"Aperçu du modèle : {nomTextBox?.Text ?? "Nouveau modèle"}";
                    ModeleRelanceHelper.ShowPreview(contenuTextBox.Text, titre);
                }
                else
                {
                    MessageBox.Show("Le contenu du modèle est vide.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'affichage de l'aperçu du modèle de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
