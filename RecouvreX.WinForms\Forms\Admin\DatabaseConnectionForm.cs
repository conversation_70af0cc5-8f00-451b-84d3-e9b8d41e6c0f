using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RecouvreX.Business.Interfaces;
using RecouvreX.Common.Security;
using RecouvreX.DataAccess;
using Serilog;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Admin
{
    /// <summary>
    /// Formulaire de configuration de la connexion à la base de données
    /// </summary>
    public partial class DatabaseConnectionForm : Form
    {
        private readonly IDatabaseAdminService _databaseAdminService;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="databaseAdminService">Service d'administration de la base de données</param>
        /// <param name="configuration">Configuration de l'application</param>
        public DatabaseConnectionForm(IDatabaseAdminService databaseAdminService, IConfiguration configuration)
        {
            _databaseAdminService = databaseAdminService ?? throw new ArgumentNullException(nameof(databaseAdminService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            
            InitializeComponent();
        }

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private void DatabaseConnectionForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Charger l'icône du formulaire
                try
                {
                    this.Icon = new Icon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "database.ico"));
                }
                catch
                {
                    // Ignorer les erreurs d'icône
                }

                // Charger les paramètres de connexion actuels
                LoadCurrentConnectionSettings();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire de configuration de la connexion");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Charge les paramètres de connexion actuels
        /// </summary>
        private void LoadCurrentConnectionSettings()
        {
            try
            {
                // Récupérer la chaîne de connexion actuelle
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                
                if (!string.IsNullOrEmpty(connectionString))
                {
                    // Déchiffrer la chaîne de connexion si nécessaire
                    connectionString = EncryptionHelper.DecryptConnectionString(connectionString);
                    
                    // Extraire les paramètres de la chaîne de connexion
                    var builder = new SqlConnectionStringBuilder(connectionString);
                    
                    // Remplir les champs du formulaire
                    serverTextBox.Text = builder.DataSource;
                    databaseTextBox.Text = builder.InitialCatalog;
                    
                    if (builder.IntegratedSecurity)
                    {
                        windowsAuthRadioButton.Checked = true;
                    }
                    else
                    {
                        sqlAuthRadioButton.Checked = true;
                        userIdTextBox.Text = builder.UserID;
                        passwordTextBox.Text = builder.Password;
                    }
                    
                    // Options avancées
                    timeoutNumericUpDown.Value = builder.ConnectTimeout;
                    trustServerCertificateCheckBox.Checked = builder.TrustServerCertificate;
                    encryptCheckBox.Checked = builder.Encrypt;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des paramètres de connexion actuels");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de changement de sélection du type d'authentification
        /// </summary>
        private void AuthTypeRadioButton_CheckedChanged(object sender, EventArgs e)
        {
            // Activer/désactiver les champs d'authentification SQL Server
            userIdTextBox.Enabled = sqlAuthRadioButton.Checked;
            passwordTextBox.Enabled = sqlAuthRadioButton.Checked;
            userIdLabel.Enabled = sqlAuthRadioButton.Checked;
            passwordLabel.Enabled = sqlAuthRadioButton.Checked;
        }

        /// <summary>
        /// Événement de clic sur le bouton Tester la connexion
        /// </summary>
        private async void TestConnectionButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Désactiver les contrôles pendant le test
                EnableControls(false);
                
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;
                
                // Récupérer les paramètres de connexion
                string server = serverTextBox.Text.Trim();
                string database = databaseTextBox.Text.Trim();
                bool integratedSecurity = windowsAuthRadioButton.Checked;
                string userId = userIdTextBox.Text.Trim();
                string password = passwordTextBox.Text;
                
                // Valider les paramètres
                if (string.IsNullOrEmpty(server))
                {
                    MessageBox.Show("Veuillez saisir le nom du serveur.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    serverTextBox.Focus();
                    return;
                }
                
                if (string.IsNullOrEmpty(database))
                {
                    MessageBox.Show("Veuillez saisir le nom de la base de données.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    databaseTextBox.Focus();
                    return;
                }
                
                if (!integratedSecurity)
                {
                    if (string.IsNullOrEmpty(userId))
                    {
                        MessageBox.Show("Veuillez saisir l'identifiant utilisateur.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        userIdTextBox.Focus();
                        return;
                    }
                }
                
                // Tester la connexion
                bool success = await _databaseAdminService.TestConnectionAsync(
                    server, 
                    database, 
                    integratedSecurity ? null : userId, 
                    integratedSecurity ? null : password, 
                    integratedSecurity);
                
                // Afficher le résultat
                if (success)
                {
                    MessageBox.Show("La connexion à la base de données a été établie avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Impossible de se connecter à la base de données. Veuillez vérifier les paramètres de connexion.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du test de connexion à la base de données");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Réactiver les contrôles
                EnableControls(true);
                
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Enregistrer
        /// </summary>
        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Désactiver les contrôles pendant l'enregistrement
                EnableControls(false);
                
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;
                
                // Récupérer les paramètres de connexion
                string server = serverTextBox.Text.Trim();
                string database = databaseTextBox.Text.Trim();
                bool integratedSecurity = windowsAuthRadioButton.Checked;
                string userId = userIdTextBox.Text.Trim();
                string password = passwordTextBox.Text;
                int timeout = (int)timeoutNumericUpDown.Value;
                bool trustServerCertificate = trustServerCertificateCheckBox.Checked;
                bool encrypt = encryptCheckBox.Checked;
                
                // Valider les paramètres
                if (string.IsNullOrEmpty(server))
                {
                    MessageBox.Show("Veuillez saisir le nom du serveur.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    serverTextBox.Focus();
                    return;
                }
                
                if (string.IsNullOrEmpty(database))
                {
                    MessageBox.Show("Veuillez saisir le nom de la base de données.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    databaseTextBox.Focus();
                    return;
                }
                
                if (!integratedSecurity)
                {
                    if (string.IsNullOrEmpty(userId))
                    {
                        MessageBox.Show(@"Veuillez saisir l'identifiant utilisateur.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        userIdTextBox.Focus();
                        return;
                    }
                }
                
                // Construire la chaîne de connexion
                var builder = new SqlConnectionStringBuilder
                {
                    DataSource = server,
                    InitialCatalog = database,
                    IntegratedSecurity = integratedSecurity,
                    ConnectTimeout = timeout,
                    TrustServerCertificate = trustServerCertificate,
                    Encrypt = encrypt
                };
                
                if (!integratedSecurity)
                {
                    builder.UserID = userId;
                    builder.Password = password;
                }
                
                // Enregistrer la chaîne de connexion
                bool success = await _databaseAdminService.SaveConnectionStringAsync(builder.ConnectionString, encryptPasswordCheckBox.Checked);
                
                // Afficher le résultat
                if (success)
                {
                    MessageBox.Show(@"Les paramètres de connexion ont été enregistrés avec succès. L'application doit être redémarrée pour prendre en compte les modifications.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    DialogResult = DialogResult.OK;
                    Close();
                }
                else
                {
                    MessageBox.Show(@"Impossible d'enregistrer les paramètres de connexion.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement des paramètres de connexion");
                MessageBox.Show($@"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Réactiver les contrôles
                EnableControls(true);
                
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Annuler
        /// </summary>
        private void CancelButton_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        /// <summary>
        /// Active ou désactive les contrôles du formulaire
        /// </summary>
        /// <param name="enabled">True pour activer les contrôles, sinon False</param>
        private void EnableControls(bool enabled)
        {
            serverTextBox.Enabled = enabled;
            databaseTextBox.Enabled = enabled;
            windowsAuthRadioButton.Enabled = enabled;
            sqlAuthRadioButton.Enabled = enabled;
            userIdTextBox.Enabled = enabled && sqlAuthRadioButton.Checked;
            passwordTextBox.Enabled = enabled && sqlAuthRadioButton.Checked;
            timeoutNumericUpDown.Enabled = enabled;
            trustServerCertificateCheckBox.Enabled = enabled;
            encryptCheckBox.Enabled = enabled;
            encryptPasswordCheckBox.Enabled = enabled;
            testConnectionButton.Enabled = enabled;
            saveButton.Enabled = enabled;
            cancelButton.Enabled = enabled;
        }
    }
}
