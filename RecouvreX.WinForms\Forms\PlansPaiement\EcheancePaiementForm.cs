using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.ComponentModel;

namespace RecouvreX.WinForms.Forms.PlansPaiement
{
    public partial class EcheancePaiementForm : Form
    {
        private readonly IPlanPaiementService _planPaiementService;
        private readonly Client _client;
        private readonly EcheancePaiement _echeance;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();
        private readonly int _currentUserId;

        public EcheancePaiementForm(
            IPlanPaiementService planPaiementService,
            Client client,
            EcheancePaiement echeance)
        {
            _planPaiementService = planPaiementService ?? throw new ArgumentNullException(nameof(planPaiementService));
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _echeance = echeance ?? throw new ArgumentNullException(nameof(echeance));
            _currentUserId = 1; // À remplacer par l'utilisateur actuel

            InitializeComponent();
            InitializeData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form
            this.Text = "Paiement d'échéance";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimizeBox = false;
            this.MaximizeBox = false;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            // Icône de l'application
            // this.Icon = Properties.Resources.AppIcon;

            // Main panel
            TableLayoutPanel mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 2,
                RowCount = 8,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 40),
                    new ColumnStyle(SizeType.Percent, 60)
                }
            };
            this.Controls.Add(mainPanel);

            // Title label
            Label titleLabel = new Label
            {
                Text = "Paiement d'échéance",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft
            };
            mainPanel.Controls.Add(titleLabel, 0, 0);
            mainPanel.SetColumnSpan(titleLabel, 2);

            // Informations sur l'échéance
            mainPanel.Controls.Add(new Label { Text = "Client :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 1);
            mainPanel.Controls.Add(new Label { Name = "clientLabel", TextAlign = ContentAlignment.MiddleLeft }, 1, 1);

            mainPanel.Controls.Add(new Label { Text = "Échéance :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 2);
            mainPanel.Controls.Add(new Label { Name = "echeanceLabel", TextAlign = ContentAlignment.MiddleLeft }, 1, 2);

            mainPanel.Controls.Add(new Label { Text = "Montant prévu :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 3);
            mainPanel.Controls.Add(new Label { Name = "montantPrevuLabel", TextAlign = ContentAlignment.MiddleLeft }, 1, 3);

            // Date de paiement
            mainPanel.Controls.Add(new Label { Text = "Date de paiement :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 4);
            DateTimePicker datePaiementPicker = new DateTimePicker
            {
                Name = "datePaiementPicker",
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today,
                Dock = DockStyle.Fill
            };
            mainPanel.Controls.Add(datePaiementPicker, 1, 4);

            // Montant payé
            mainPanel.Controls.Add(new Label { Text = "Montant payé :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 5);
            NumericUpDown montantPayeNumeric = new NumericUpDown
            {
                Name = "montantPayeNumeric",
                Dock = DockStyle.Fill,
                DecimalPlaces = 2,
                Minimum = 0,
                Maximum = 1000000,
                ThousandsSeparator = true
            };
            montantPayeNumeric.Validating += MontantPayeNumeric_Validating;
            mainPanel.Controls.Add(montantPayeNumeric, 1, 5);

            // Mode de paiement
            mainPanel.Controls.Add(new Label { Text = "Mode de paiement :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 6);
            ComboBox modePaiementComboBox = new ComboBox
            {
                Name = "modePaiementComboBox",
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            modePaiementComboBox.Items.Add("Virement");
            modePaiementComboBox.Items.Add("Chèque");
            modePaiementComboBox.Items.Add("Carte bancaire");
            modePaiementComboBox.Items.Add("Espèces");
            modePaiementComboBox.SelectedIndex = 0;
            modePaiementComboBox.Validating += ModePaiementComboBox_Validating;
            mainPanel.Controls.Add(modePaiementComboBox, 1, 6);

            // Buttons panel
            TableLayoutPanel buttonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 50),
                    new ColumnStyle(SizeType.Percent, 50)
                }
            };
            mainPanel.Controls.Add(buttonsPanel, 0, 7);
            mainPanel.SetColumnSpan(buttonsPanel, 2);

            Button saveButton = new Button
            {
                Name = "saveButton",
                Text = "Enregistrer",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            saveButton.Click += SaveButton_Click;
            buttonsPanel.Controls.Add(saveButton, 0, 0);

            Button cancelButton = new Button
            {
                Name = "cancelButton",
                Text = "Annuler",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            cancelButton.Click += CancelButton_Click;
            buttonsPanel.Controls.Add(cancelButton, 1, 0);

            this.ResumeLayout(false);
        }

        private void InitializeData()
        {
            // Remplir les informations sur l'échéance
            var clientLabel = Controls.Find("clientLabel", true).FirstOrDefault() as Label;
            if (clientLabel != null)
            {
                clientLabel.Text = _client.RaisonSociale;
            }

            var echeanceLabel = Controls.Find("echeanceLabel", true).FirstOrDefault() as Label;
            if (echeanceLabel != null)
            {
                echeanceLabel.Text = $"N°{_echeance.NumeroOrdre} - {_echeance.DateEcheance:dd/MM/yyyy}";
            }

            var montantPrevuLabel = Controls.Find("montantPrevuLabel", true).FirstOrDefault() as Label;
            if (montantPrevuLabel != null)
            {
                montantPrevuLabel.Text = $"{_echeance.MontantPrevu:C2}";
            }

            // Initialiser le montant payé avec le montant prévu
            var montantPayeNumeric = Controls.Find("montantPayeNumeric", true).FirstOrDefault() as NumericUpDown;
            if (montantPayeNumeric != null)
            {
                montantPayeNumeric.Value = _echeance.MontantPrevu;
            }
        }

        private void MontantPayeNumeric_Validating(object sender, CancelEventArgs e)
        {
            var montantPayeNumeric = sender as NumericUpDown;
            if (montantPayeNumeric == null)
                return;

            if (montantPayeNumeric.Value <= 0)
            {
                _errorProvider.SetError(montantPayeNumeric, "Le montant payé doit être supérieur à zéro.");
                e.Cancel = true;
            }
            else
            {
                _errorProvider.SetError(montantPayeNumeric, "");
            }
        }

        private void ModePaiementComboBox_Validating(object sender, CancelEventArgs e)
        {
            var modePaiementComboBox = sender as ComboBox;
            if (modePaiementComboBox == null)
                return;

            if (modePaiementComboBox.SelectedIndex < 0)
            {
                _errorProvider.SetError(modePaiementComboBox, "Veuillez sélectionner un mode de paiement.");
                e.Cancel = true;
            }
            else
            {
                _errorProvider.SetError(modePaiementComboBox, "");
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            if (!ValidateChildren())
                return;

            try
            {
                var datePaiementPicker = Controls.Find("datePaiementPicker", true).FirstOrDefault() as DateTimePicker;
                var montantPayeNumeric = Controls.Find("montantPayeNumeric", true).FirstOrDefault() as NumericUpDown;
                var modePaiementComboBox = Controls.Find("modePaiementComboBox", true).FirstOrDefault() as ComboBox;

                if (datePaiementPicker == null || montantPayeNumeric == null || modePaiementComboBox == null)
                    return;

                // Créer un nouveau paiement
                var paiement = new Paiement
                {
                    ClientId = _client.Id,
                    DatePaiement = datePaiementPicker.Value,
                    Montant = montantPayeNumeric.Value,
                    ModePaiement = modePaiementComboBox.SelectedItem.ToString(),
                    Notes = $"Paiement de l'échéance N°{_echeance.NumeroOrdre} du plan {_echeance.PlanPaiementId}",
                    EstActif = true
                };

                // TODO: Créer le paiement via le service de paiement
                // var createdPaiement = await _paiementService.CreatePaiementAsync(paiement, _currentUserId);

                // Pour l'instant, on simule la création d'un paiement
                var createdPaiement = new Paiement
                {
                    Id = 1, // Simulé
                    ClientId = paiement.ClientId,
                    DatePaiement = paiement.DatePaiement,
                    Montant = paiement.Montant,
                    ModePaiement = paiement.ModePaiement
                };

                // Marquer l'échéance comme payée
                bool result = await _planPaiementService.MarkEcheanceAsPaidAsync(
                    _echeance.Id,
                    createdPaiement.Id,
                    montantPayeNumeric.Value,
                    datePaiementPicker.Value,
                    _currentUserId);

                if (result)
                {
                    MessageBox.Show("L'échéance a été marquée comme payée avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    DialogResult = DialogResult.OK;
                    Close();
                }
                else
                {
                    MessageBox.Show("Erreur lors du marquage de l'échéance comme payée.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du paiement de l'échéance");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
}
