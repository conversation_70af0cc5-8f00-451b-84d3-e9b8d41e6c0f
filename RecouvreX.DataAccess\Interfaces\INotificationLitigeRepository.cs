using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des notifications de litiges
    /// </summary>
    public interface INotificationLitigeRepository
    {
        /// <summary>
        /// Récupère toutes les notifications
        /// </summary>
        /// <returns>Liste des notifications</returns>
        Task<IEnumerable<NotificationLitige>> GetAllAsync();

        /// <summary>
        /// Récupère une notification par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la notification</param>
        /// <returns>Notification</returns>
        Task<NotificationLitige> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les notifications pour un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des notifications</returns>
        Task<IEnumerable<NotificationLitige>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les notifications non lues pour un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des notifications non lues</returns>
        Task<IEnumerable<NotificationLitige>> GetUnreadByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les notifications pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des notifications</returns>
        Task<IEnumerable<NotificationLitige>> GetByLitigeIdAsync(int litigeId);

        /// <summary>
        /// Ajoute une notification
        /// </summary>
        /// <param name="notification">Notification à ajouter</param>
        /// <returns>Notification ajoutée</returns>
        Task<NotificationLitige> AddAsync(NotificationLitige notification);

        /// <summary>
        /// Marque une notification comme lue
        /// </summary>
        /// <param name="id">Identifiant de la notification</param>
        /// <returns>True si la notification a été marquée comme lue</returns>
        Task<bool> MarkAsReadAsync(int id);

        /// <summary>
        /// Marque toutes les notifications d'un utilisateur comme lues
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Nombre de notifications marquées comme lues</returns>
        Task<int> MarkAllAsReadAsync(int utilisateurId);

        /// <summary>
        /// Supprime une notification
        /// </summary>
        /// <param name="id">Identifiant de la notification</param>
        /// <returns>True si la notification a été supprimée</returns>
        Task<bool> DeleteAsync(int id);
    }
}
