using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.WinForms.Helpers;
using ScottPlot;
using ScottPlot.WinForms;
using Serilog;
using System.ComponentModel;

namespace RecouvreX.WinForms.Forms
{
    public partial class MainForm : Form
    {
        private readonly IAuthenticationService _authenticationService;
        private readonly IUtilisateurService _utilisateurService;
        private readonly IClientService _clientService;
        private readonly IFactureService _factureService;
        private readonly IPaiementService _paiementService;
        private readonly IRelanceService _relanceService;
        private readonly IAlerteService _alerteService;
        private readonly IModeleRelanceService _modeleRelanceService;
        private readonly IRegleRelanceService _regleRelanceService;
        private readonly IPlanificationRelanceService _planificationRelanceService;
        private readonly ICommunicationService _communicationService;
        private readonly IContactClientService _contactClientService;
        private readonly IConfiguration _configuration;
        private readonly IActionPrioritaireService _actionPrioritaireService;
        private readonly ILitigeService _litigeService;
        private readonly IServiceProvider _serviceProvider;
        private readonly IReportService _reportService;
        private readonly IRapportPersonnaliseService _rapportPersonnaliseService;
        private readonly IDatabaseAdminService _databaseAdminService;
        private Utilisateur? _currentUser;
        private int _currentUserId;
        private System.Windows.Forms.Timer? _sessionTimer;
        private int _sessionTimeoutMinutes;
        private System.Windows.Forms.Timer? _alerteTimer;

        public MainForm(
            IAuthenticationService authenticationService,
            IUtilisateurService utilisateurService,
            IClientService clientService,
            IFactureService factureService,
            IPaiementService paiementService,
            IRelanceService relanceService,
            IAlerteService alerteService,
            IModeleRelanceService modeleRelanceService,
            IRegleRelanceService regleRelanceService,
            IPlanificationRelanceService planificationRelanceService,
            ICommunicationService communicationService,
            IContactClientService contactClientService,
            IActionPrioritaireService actionPrioritaireService,
            ILitigeService litigeService,
            IReportService reportService,
            IRapportPersonnaliseService rapportPersonnaliseService,
            IDatabaseAdminService databaseAdminService,
            IConfiguration configuration,
            IServiceProvider serviceProvider)
        {
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _paiementService = paiementService ?? throw new ArgumentNullException(nameof(paiementService));
            _relanceService = relanceService ?? throw new ArgumentNullException(nameof(relanceService));
            _alerteService = alerteService ?? throw new ArgumentNullException(nameof(alerteService));
            _modeleRelanceService = modeleRelanceService ?? throw new ArgumentNullException(nameof(modeleRelanceService));
            _regleRelanceService = regleRelanceService ?? throw new ArgumentNullException(nameof(regleRelanceService));
            _planificationRelanceService = planificationRelanceService ?? throw new ArgumentNullException(nameof(planificationRelanceService));
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _contactClientService = contactClientService ?? throw new ArgumentNullException(nameof(contactClientService));
            _actionPrioritaireService = actionPrioritaireService ?? throw new ArgumentNullException(nameof(actionPrioritaireService));
            _litigeService = litigeService ?? throw new ArgumentNullException(nameof(litigeService));
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _rapportPersonnaliseService = rapportPersonnaliseService ?? throw new ArgumentNullException(nameof(rapportPersonnaliseService));
            _databaseAdminService = databaseAdminService ?? throw new ArgumentNullException(nameof(databaseAdminService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _sessionTimeoutMinutes = _configuration.GetValue<int>("AppSettings:SessionTimeout", 30);

            InitializeComponent();
        }



        private void CreateDashboard(Panel mainPanel)
        {
            // Titre du tableau de bord
            Label dashboardTitle = new Label();
            dashboardTitle.Text = "Tableau de bord";
            dashboardTitle.Font = new Font(dashboardTitle.Font.FontFamily, 16, FontStyle.Bold);
            dashboardTitle.AutoSize = true;
            dashboardTitle.Location = new Point(20, 20);
            mainPanel.Controls.Add(dashboardTitle);

            // Bouton de rafraîchissement
            Button refreshButton = new Button();
            refreshButton.Text = "Rafraîchir";
            refreshButton.Size = new Size(100, 30);
            refreshButton.Location = new Point(200, 15);
            refreshButton.Click += new EventHandler(this.RefreshDashboard_Click);
            mainPanel.Controls.Add(refreshButton);

            // Panneau des KPIs de recouvrement
            GroupBox kpisPanel = new GroupBox();
            kpisPanel.Text = "KPIs de recouvrement";
            kpisPanel.Size = new Size(660, 120);
            kpisPanel.Location = new Point(20, 60);
            mainPanel.Controls.Add(kpisPanel);

            // DSO (Days Sales Outstanding)
            Label dsoLabel = new Label();
            dsoLabel.Text = "DSO :";
            dsoLabel.AutoSize = true;
            dsoLabel.Location = new Point(20, 30);
            kpisPanel.Controls.Add(dsoLabel);

            Label dsoValueLabel = new Label();
            dsoValueLabel.Name = "dsoValueLabel";
            dsoValueLabel.Text = "Chargement...";
            dsoValueLabel.Font = new Font(dsoValueLabel.Font.FontFamily, 10, FontStyle.Bold);
            dsoValueLabel.AutoSize = true;
            dsoValueLabel.Location = new Point(120, 30);
            kpisPanel.Controls.Add(dsoValueLabel);

            // Taux de recouvrement
            Label tauxRecouvrementLabel = new Label();
            tauxRecouvrementLabel.Text = "Taux de recouvrement :";
            tauxRecouvrementLabel.AutoSize = true;
            tauxRecouvrementLabel.Location = new Point(220, 30);
            kpisPanel.Controls.Add(tauxRecouvrementLabel);

            Label tauxRecouvrementValueLabel = new Label();
            tauxRecouvrementValueLabel.Name = "tauxRecouvrementValueLabel";
            tauxRecouvrementValueLabel.Text = "Chargement...";
            tauxRecouvrementValueLabel.Font = new Font(tauxRecouvrementValueLabel.Font.FontFamily, 10, FontStyle.Bold);
            tauxRecouvrementValueLabel.AutoSize = true;
            tauxRecouvrementValueLabel.Location = new Point(370, 30);
            kpisPanel.Controls.Add(tauxRecouvrementValueLabel);

            // Âge moyen des créances
            Label ageMoyenLabel = new Label();
            ageMoyenLabel.Text = "Âge moyen des créances :";
            ageMoyenLabel.AutoSize = true;
            ageMoyenLabel.Location = new Point(450, 30);
            kpisPanel.Controls.Add(ageMoyenLabel);

            Label ageMoyenValueLabel = new Label();
            ageMoyenValueLabel.Name = "ageMoyenValueLabel";
            ageMoyenValueLabel.Text = "Chargement...";
            ageMoyenValueLabel.Font = new Font(ageMoyenValueLabel.Font.FontFamily, 10, FontStyle.Bold);
            ageMoyenValueLabel.AutoSize = true;
            ageMoyenValueLabel.Location = new Point(600, 30);
            kpisPanel.Controls.Add(ageMoyenValueLabel);

            // Montant par tranche d'ancienneté
            Label trancheAncienneteLabel = new Label();
            trancheAncienneteLabel.Text = "Montant par tranche d'ancienneté :";
            trancheAncienneteLabel.AutoSize = true;
            trancheAncienneteLabel.Location = new Point(20, 60);
            kpisPanel.Controls.Add(trancheAncienneteLabel);

            // 0-30 jours
            Label tranche1Label = new Label();
            tranche1Label.Text = "0-30j :";
            tranche1Label.AutoSize = true;
            tranche1Label.Location = new Point(20, 85);
            kpisPanel.Controls.Add(tranche1Label);

            Label tranche1ValueLabel = new Label();
            tranche1ValueLabel.Name = "tranche1ValueLabel";
            tranche1ValueLabel.Text = "Chargement...";
            tranche1ValueLabel.Font = new Font(tranche1ValueLabel.Font.FontFamily, 10, FontStyle.Bold);
            tranche1ValueLabel.AutoSize = true;
            tranche1ValueLabel.Location = new Point(70, 85);
            kpisPanel.Controls.Add(tranche1ValueLabel);

            // 31-60 jours
            Label tranche2Label = new Label();
            tranche2Label.Text = "31-60j :";
            tranche2Label.AutoSize = true;
            tranche2Label.Location = new Point(200, 85);
            kpisPanel.Controls.Add(tranche2Label);

            Label tranche2ValueLabel = new Label();
            tranche2ValueLabel.Name = "tranche2ValueLabel";
            tranche2ValueLabel.Text = "Chargement...";
            tranche2ValueLabel.Font = new Font(tranche2ValueLabel.Font.FontFamily, 10, FontStyle.Bold);
            tranche2ValueLabel.AutoSize = true;
            tranche2ValueLabel.Location = new Point(250, 85);
            kpisPanel.Controls.Add(tranche2ValueLabel);

            // 61-90 jours
            Label tranche3Label = new Label();
            tranche3Label.Text = "61-90j :";
            tranche3Label.AutoSize = true;
            tranche3Label.Location = new Point(380, 85);
            kpisPanel.Controls.Add(tranche3Label);

            Label tranche3ValueLabel = new Label();
            tranche3ValueLabel.Name = "tranche3ValueLabel";
            tranche3ValueLabel.Text = "Chargement...";
            tranche3ValueLabel.Font = new Font(tranche3ValueLabel.Font.FontFamily, 10, FontStyle.Bold);
            tranche3ValueLabel.AutoSize = true;
            tranche3ValueLabel.Location = new Point(430, 85);
            kpisPanel.Controls.Add(tranche3ValueLabel);

            // >90 jours
            Label tranche4Label = new Label();
            tranche4Label.Text = ">90j :";
            tranche4Label.AutoSize = true;
            tranche4Label.Location = new Point(560, 85);
            kpisPanel.Controls.Add(tranche4Label);

            Label tranche4ValueLabel = new Label();
            tranche4ValueLabel.Name = "tranche4ValueLabel";
            tranche4ValueLabel.Text = "Chargement...";
            tranche4ValueLabel.Font = new Font(tranche4ValueLabel.Font.FontFamily, 10, FontStyle.Bold);
            tranche4ValueLabel.AutoSize = true;
            tranche4ValueLabel.Location = new Point(600, 85);
            kpisPanel.Controls.Add(tranche4ValueLabel);

            // Panneau des factures en retard
            GroupBox overdueInvoicesPanel = new GroupBox();
            overdueInvoicesPanel.Text = "Factures en retard";
            overdueInvoicesPanel.Size = new Size(300, 200);
            overdueInvoicesPanel.Location = new Point(20, 200);
            mainPanel.Controls.Add(overdueInvoicesPanel);

            Label overdueInvoicesCountLabel = new Label();
            overdueInvoicesCountLabel.Name = "overdueInvoicesCountLabel";
            overdueInvoicesCountLabel.Text = "Chargement...";
            overdueInvoicesCountLabel.Font = new Font(overdueInvoicesCountLabel.Font.FontFamily, 14, FontStyle.Bold);
            overdueInvoicesCountLabel.AutoSize = true;
            overdueInvoicesCountLabel.Location = new Point(20, 30);
            overdueInvoicesPanel.Controls.Add(overdueInvoicesCountLabel);

            Label overdueInvoicesAmountLabel = new Label();
            overdueInvoicesAmountLabel.Name = "overdueInvoicesAmountLabel";
            overdueInvoicesAmountLabel.Text = "Montant total : Chargement...";
            overdueInvoicesAmountLabel.AutoSize = true;
            overdueInvoicesAmountLabel.Location = new Point(20, 60);
            overdueInvoicesPanel.Controls.Add(overdueInvoicesAmountLabel);

            Button viewOverdueInvoicesButton = new Button();
            viewOverdueInvoicesButton.Text = "Voir les factures en retard";
            viewOverdueInvoicesButton.Size = new Size(200, 30);
            viewOverdueInvoicesButton.Location = new Point(50, 100);
            viewOverdueInvoicesButton.Click += new EventHandler(this.OverdueInvoicesMenuItem_Click);
            overdueInvoicesPanel.Controls.Add(viewOverdueInvoicesButton);

            // Panneau des relances planifiées
            GroupBox plannedRemindersPanel = new GroupBox();
            plannedRemindersPanel.Text = "Relances planifiées aujourd'hui";
            plannedRemindersPanel.Size = new Size(300, 200);
            plannedRemindersPanel.Location = new Point(340, 200);
            mainPanel.Controls.Add(plannedRemindersPanel);

            Label plannedRemindersCountLabel = new Label();
            plannedRemindersCountLabel.Name = "plannedRemindersCountLabel";
            plannedRemindersCountLabel.Text = "Chargement...";
            plannedRemindersCountLabel.Font = new Font(plannedRemindersCountLabel.Font.FontFamily, 14, FontStyle.Bold);
            plannedRemindersCountLabel.AutoSize = true;
            plannedRemindersCountLabel.Location = new Point(20, 30);
            plannedRemindersPanel.Controls.Add(plannedRemindersCountLabel);

            Button viewPlannedRemindersButton = new Button();
            viewPlannedRemindersButton.Text = "Voir les relances planifiées";
            viewPlannedRemindersButton.Size = new Size(200, 30);
            viewPlannedRemindersButton.Location = new Point(50, 100);
            viewPlannedRemindersButton.Click += new EventHandler(this.PlannedRemindersMenuItem_Click);
            plannedRemindersPanel.Controls.Add(viewPlannedRemindersButton);

            // Panneau des paiements récents
            GroupBox recentPaymentsPanel = new GroupBox();
            recentPaymentsPanel.Text = "Paiements récents (7 derniers jours)";
            recentPaymentsPanel.Size = new Size(300, 200);
            recentPaymentsPanel.Location = new Point(660, 200);
            mainPanel.Controls.Add(recentPaymentsPanel);

            // Panneau des échéances à venir
            GroupBox echeancesPanel = new GroupBox();
            echeancesPanel.Text = "Échéances à venir";
            echeancesPanel.Size = new Size(300, 200);
            echeancesPanel.Location = new Point(660, 410);
            mainPanel.Controls.Add(echeancesPanel);

            Label echeancesLabel = new Label();
            echeancesLabel.Text = "Prochaines échéances de paiement";
            echeancesLabel.AutoSize = true;
            echeancesLabel.Location = new Point(10, 30);
            echeancesPanel.Controls.Add(echeancesLabel);

            Button viewEcheancesButton = new Button();
            viewEcheancesButton.Text = "Voir les échéances";
            viewEcheancesButton.Size = new Size(200, 30);
            viewEcheancesButton.Location = new Point(50, 60);
            viewEcheancesButton.Click += new EventHandler(this.EcheancierDashboardMenuItem_Click);
            echeancesPanel.Controls.Add(viewEcheancesButton);

            Button createPlanButton = new Button();
            createPlanButton.Text = "Créer un plan de paiement";
            createPlanButton.Size = new Size(200, 30);
            createPlanButton.Location = new Point(50, 100);
            createPlanButton.Click += new EventHandler(this.AddPlanPaiementMenuItem_Click);
            echeancesPanel.Controls.Add(createPlanButton);

            // Panneau des litiges
            GroupBox litigesPanel = new GroupBox();
            litigesPanel.Text = "Litiges";
            litigesPanel.Size = new Size(300, 200);
            litigesPanel.Location = new Point(980, 200);
            mainPanel.Controls.Add(litigesPanel);

            Label litigesCountLabel = new Label();
            litigesCountLabel.Name = "litigesCountLabel";
            litigesCountLabel.Text = "Chargement...";
            litigesCountLabel.Font = new Font(litigesCountLabel.Font.FontFamily, 14, FontStyle.Bold);
            litigesCountLabel.AutoSize = true;
            litigesCountLabel.Location = new Point(20, 30);
            litigesPanel.Controls.Add(litigesCountLabel);

            Label litigesEnRetardLabel = new Label();
            litigesEnRetardLabel.Name = "litigesEnRetardLabel";
            litigesEnRetardLabel.Text = "En retard : Chargement...";
            litigesEnRetardLabel.AutoSize = true;
            litigesEnRetardLabel.Location = new Point(20, 60);
            litigesPanel.Controls.Add(litigesEnRetardLabel);

            Label tempsResolutionLabel = new Label();
            tempsResolutionLabel.Name = "tempsResolutionLabel";
            tempsResolutionLabel.Text = "Temps moyen de résolution : Chargement...";
            tempsResolutionLabel.AutoSize = true;
            tempsResolutionLabel.Location = new Point(20, 85);
            litigesPanel.Controls.Add(tempsResolutionLabel);

            Button viewLitigesButton = new Button();
            viewLitigesButton.Text = "Voir les litiges";
            viewLitigesButton.Size = new Size(200, 30);
            viewLitigesButton.Location = new Point(50, 120);
            viewLitigesButton.Click += new EventHandler(this.ListLitigesMenuItem_Click);
            litigesPanel.Controls.Add(viewLitigesButton);

            // Panneau des actions prioritaires
            GroupBox actionsPrioritairesPanel = new GroupBox();
            actionsPrioritairesPanel.Text = "Actions prioritaires";
            actionsPrioritairesPanel.Size = new Size(460, 350);
            actionsPrioritairesPanel.Location = new Point(20, 420);
            mainPanel.Controls.Add(actionsPrioritairesPanel);

            // Ajouter le contrôle des actions prioritaires
            Controls.ActionsPrioritairesControl actionsPrioritairesControl = new Controls.ActionsPrioritairesControl(
                _actionPrioritaireService,
                _utilisateurService,
                _currentUserId);
            actionsPrioritairesControl.Dock = DockStyle.Fill;
            actionsPrioritairesPanel.Controls.Add(actionsPrioritairesControl);

            // Panneau des tâches prioritaires assignées à l'utilisateur
            GroupBox mesTachesPanel = new GroupBox();
            mesTachesPanel.Text = "Mes tâches prioritaires";
            mesTachesPanel.Size = new Size(460, 350);
            mesTachesPanel.Location = new Point(500, 420);
            mainPanel.Controls.Add(mesTachesPanel);

            // Ajouter le contrôle des tâches prioritaires
            Controls.MesTachesControl mesTachesControl = new Controls.MesTachesControl(
                _actionPrioritaireService,
                _utilisateurService,
                _currentUserId);
            mesTachesControl.Dock = DockStyle.Fill;
            mesTachesPanel.Controls.Add(mesTachesControl);

            // Panneau des graphiques
            GroupBox chartsPanel = new GroupBox();
            chartsPanel.Text = "Graphiques";
            chartsPanel.Size = new Size(940, 400);
            chartsPanel.Location = new Point(20, 420);
            mainPanel.Controls.Add(chartsPanel);

            // Créer un TabControl pour les différents graphiques
            TabControl chartsTabControl = new TabControl();
            chartsTabControl.Dock = DockStyle.Fill;
            chartsTabControl.Padding = new Point(10, 5);
            chartsPanel.Controls.Add(chartsTabControl);

            // Onglet 1 : Évolution des paiements
            TabPage paymentsEvolutionTab = new TabPage("Évolution des paiements");
            chartsTabControl.TabPages.Add(paymentsEvolutionTab);

            FormsPlot paymentsEvolutionPlot = new FormsPlot();
            paymentsEvolutionPlot.Name = "paymentsEvolutionPlot";
            paymentsEvolutionPlot.Dock = DockStyle.Fill;
            paymentsEvolutionPlot.Configuration.Quality = ScottPlot.Control.QualityMode.High;
            paymentsEvolutionPlot.Configuration.Pan = false;
            paymentsEvolutionPlot.Configuration.Zoom = false;
            paymentsEvolutionPlot.Configuration.ScrollWheelZoom = false;
            paymentsEvolutionTab.Controls.Add(paymentsEvolutionPlot);

            // Onglet 2 : Répartition des factures par statut
            TabPage invoiceStatusTab = new TabPage("Répartition par statut");
            chartsTabControl.TabPages.Add(invoiceStatusTab);

            FormsPlot invoiceStatusPlot = new FormsPlot();
            invoiceStatusPlot.Name = "invoiceStatusPlot";
            invoiceStatusPlot.Dock = DockStyle.Fill;
            invoiceStatusPlot.Configuration.Quality = ScottPlot.Control.QualityMode.High;
            invoiceStatusPlot.Configuration.Pan = false;
            invoiceStatusPlot.Configuration.Zoom = false;
            invoiceStatusPlot.Configuration.ScrollWheelZoom = false;
            invoiceStatusTab.Controls.Add(invoiceStatusPlot);

            // Onglet 3 : Performances de recouvrement par client
            TabPage recoveryPerformanceTab = new TabPage("Performance par client");
            chartsTabControl.TabPages.Add(recoveryPerformanceTab);

            FormsPlot recoveryPerformancePlot = new FormsPlot();
            recoveryPerformancePlot.Name = "recoveryPerformancePlot";
            recoveryPerformancePlot.Dock = DockStyle.Fill;
            recoveryPerformancePlot.Configuration.Quality = ScottPlot.Control.QualityMode.High;
            recoveryPerformancePlot.Configuration.Pan = false;
            recoveryPerformancePlot.Configuration.Zoom = false;
            recoveryPerformancePlot.Configuration.ScrollWheelZoom = false;
            recoveryPerformanceTab.Controls.Add(recoveryPerformancePlot);

            // Onglet 4 : Répartition des créances par ancienneté
            TabPage ageDistributionTab = new TabPage("Répartition par ancienneté");
            chartsTabControl.TabPages.Add(ageDistributionTab);

            FormsPlot ageDistributionPlot = new FormsPlot();
            ageDistributionPlot.Name = "ageDistributionPlot";
            ageDistributionPlot.Dock = DockStyle.Fill;
            ageDistributionPlot.Configuration.Quality = ScottPlot.Control.QualityMode.High;
            ageDistributionPlot.Configuration.Pan = false;
            ageDistributionPlot.Configuration.Zoom = false;
            ageDistributionPlot.Configuration.ScrollWheelZoom = false;
            ageDistributionTab.Controls.Add(ageDistributionPlot);

            // Onglet 5 : Répartition des litiges par catégorie
            TabPage litigesCategorieTab = new TabPage("Litiges par catégorie");
            chartsTabControl.TabPages.Add(litigesCategorieTab);

            FormsPlot litigesCategorieDistributionPlot = new FormsPlot();
            litigesCategorieDistributionPlot.Name = "litigesCategorieDistributionPlot";
            litigesCategorieDistributionPlot.Dock = DockStyle.Fill;
            litigesCategorieDistributionPlot.Configuration.Quality = ScottPlot.Control.QualityMode.High;
            litigesCategorieDistributionPlot.Configuration.Pan = false;
            litigesCategorieDistributionPlot.Configuration.Zoom = false;
            litigesCategorieDistributionPlot.Configuration.ScrollWheelZoom = false;
            litigesCategorieTab.Controls.Add(litigesCategorieDistributionPlot);

            // Onglet 6 : Répartition des litiges par étape
            TabPage litigesEtapeTab = new TabPage("Litiges par étape");
            chartsTabControl.TabPages.Add(litigesEtapeTab);

            FormsPlot litigesEtapeDistributionPlot = new FormsPlot();
            litigesEtapeDistributionPlot.Name = "litigesEtapeDistributionPlot";
            litigesEtapeDistributionPlot.Dock = DockStyle.Fill;
            litigesEtapeDistributionPlot.Configuration.Quality = ScottPlot.Control.QualityMode.High;
            litigesEtapeDistributionPlot.Configuration.Pan = false;
            litigesEtapeDistributionPlot.Configuration.Zoom = false;
            litigesEtapeDistributionPlot.Configuration.ScrollWheelZoom = false;
            litigesEtapeTab.Controls.Add(litigesEtapeDistributionPlot);

            // Ajouter des filtres temporels
            Panel filtersPanel = new Panel();
            filtersPanel.Size = new Size(940, 40);
            filtersPanel.Location = new Point(20, 830);
            mainPanel.Controls.Add(filtersPanel);

            Label periodLabel = new Label();
            periodLabel.Text = "Période :";
            periodLabel.AutoSize = true;
            periodLabel.Location = new Point(10, 10);
            filtersPanel.Controls.Add(periodLabel);

            ComboBox periodComboBox = new ComboBox();
            periodComboBox.Name = "periodComboBox";
            periodComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            periodComboBox.Items.AddRange(new object[] { "Mois en cours", "Trimestre en cours", "Année en cours", "12 derniers mois" });
            periodComboBox.SelectedIndex = 3; // 12 derniers mois par défaut
            periodComboBox.Location = new Point(70, 7);
            periodComboBox.Size = new Size(150, 25);
            periodComboBox.SelectedIndexChanged += new EventHandler(this.PeriodComboBox_SelectedIndexChanged);
            filtersPanel.Controls.Add(periodComboBox);

            Label recentPaymentsCountLabel = new Label();
            recentPaymentsCountLabel.Name = "recentPaymentsCountLabel";
            recentPaymentsCountLabel.Text = "Chargement...";
            recentPaymentsCountLabel.Font = new Font(recentPaymentsCountLabel.Font.FontFamily, 14, FontStyle.Bold);
            recentPaymentsCountLabel.AutoSize = true;
            recentPaymentsCountLabel.Location = new Point(20, 30);
            recentPaymentsPanel.Controls.Add(recentPaymentsCountLabel);

            Label recentPaymentsAmountLabel = new Label();
            recentPaymentsAmountLabel.Name = "recentPaymentsAmountLabel";
            recentPaymentsAmountLabel.Text = "Montant total : Chargement...";
            recentPaymentsAmountLabel.AutoSize = true;
            recentPaymentsAmountLabel.Location = new Point(20, 60);
            recentPaymentsPanel.Controls.Add(recentPaymentsAmountLabel);

            Button viewRecentPaymentsButton = new Button();
            viewRecentPaymentsButton.Text = "Voir les paiements récents";
            viewRecentPaymentsButton.Size = new Size(200, 30);
            viewRecentPaymentsButton.Location = new Point(50, 100);
            viewRecentPaymentsButton.Click += new EventHandler(this.ListPaymentsMenuItem_Click);
            recentPaymentsPanel.Controls.Add(viewRecentPaymentsButton);
        }

        private async void MainForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Récupérer l'utilisateur connecté
                var loginForm = this.Owner as LoginForm;
                if (loginForm != null && loginForm.LoggedInUser != null)
                {
                    _currentUser = loginForm.LoggedInUser;
                    _currentUserId = _currentUser.Id;
                }
                else
                {
                    // Si l'utilisateur n'est pas disponible, fermer l'application
                    MessageBox.Show("Erreur : Utilisateur non connecté.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Mettre à jour la barre d'état
                var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
                var userStatusLabel = statusStrip?.Items.Find("userStatusLabel", false).FirstOrDefault() as ToolStripStatusLabel;
                if (userStatusLabel != null)
                {
                    userStatusLabel.Text = $"Utilisateur : {_currentUser.NomComplet} ({_currentUser.NomUtilisateur})";
                }

                // Configurer le timer de session
                _sessionTimer = new System.Windows.Forms.Timer();
                _sessionTimer.Interval = 60000; // 1 minute
                _sessionTimer.Tick += SessionTimer_Tick;
                _sessionTimer.Start();

                // Mettre à jour le compteur de session
                UpdateSessionTimeout(_sessionTimeoutMinutes);

                // Configurer le timer de vérification des alertes
                _alerteTimer = new System.Windows.Forms.Timer();
                _alerteTimer.Interval = 300000; // 5 minutes
                _alerteTimer.Tick += AlerteTimer_Tick;
                _alerteTimer.Start();

                // Vérifier les alertes au démarrage
                await CheckAlertesAsync();

                // Charger les données du tableau de bord
                await LoadDashboardDataAsync();

                // Vérifier les permissions de l'utilisateur et ajuster le menu en conséquence
                await CheckUserPermissionsAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire principal");
                MessageBox.Show($"Une erreur s'est produite lors du chargement de l'application : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // Arrêter les timers
                _sessionTimer?.Stop();
                _alerteTimer?.Stop();

                // Déconnecter l'utilisateur
                if (_currentUser != null)
                {
                    _authenticationService.LogoutAsync(_currentUser.Id).Wait();
                    Log.Information("Utilisateur {Username} déconnecté", _currentUser.NomUtilisateur);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la fermeture du formulaire principal");
            }
        }

        private void SessionTimer_Tick(object sender, EventArgs e)
        {
            // Décrémenter le compteur de session
            _sessionTimeoutMinutes--;
            UpdateSessionTimeout(_sessionTimeoutMinutes);

            // Vérifier si la session a expiré
            if (_sessionTimeoutMinutes <= 0)
            {
                // Arrêter le timer
                _sessionTimer?.Stop();

                // Afficher un message
                MessageBox.Show("Votre session a expiré. Veuillez vous reconnecter.", "Session expirée", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Fermer l'application
                this.Close();
            }
        }

        private void UpdateSessionTimeout(int minutes)
        {
            var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
            var sessionStatusLabel = statusStrip?.Items.Find("sessionStatusLabel", false).FirstOrDefault() as ToolStripStatusLabel;
            if (sessionStatusLabel != null)
            {
                sessionStatusLabel.Text = $"Session : {minutes} minute(s) restante(s)";
            }
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                // Charger toutes les factures pour les KPIs
                var allFactures = await _factureService.GetAllAsync();

                // Charger tous les paiements pour les KPIs
                var allPaiements = await _paiementService.GetAllAsync();

                // Charger tous les clients pour les graphiques
                var allClients = await _clientService.GetAllAsync();

                // Charger les litiges pour les statistiques et les graphiques
                var allLitiges = await _litigeService.GetAllAsync();
                var litigeStats = await _litigeService.GetStatistiquesAsync();
                var allCategories = await _litigeService.GetAllCategoriesWithCountAsync();
                var allEtapes = await _litigeService.GetAllEtapesWithCountAsync();

                // Charger les notifications non lues
                await UpdateNotificationsCountAsync();

                // Calculer et afficher les KPIs
                UpdateKPIs(allFactures, allPaiements);

                // Mettre à jour les graphiques
                UpdateCharts(allFactures, allPaiements, allClients, allLitiges, allCategories, allEtapes);

                // Mettre à jour les statistiques des litiges
                UpdateLitigeStats(litigeStats);

                // Charger les factures en retard
                var overdueInvoices = await _factureService.GetOverdueInvoicesAsync();
                var overdueInvoicesCountLabel = this.Controls.Find("overdueInvoicesCountLabel", true).FirstOrDefault() as Label;
                var overdueInvoicesAmountLabel = this.Controls.Find("overdueInvoicesAmountLabel", true).FirstOrDefault() as Label;

                if (overdueInvoicesCountLabel != null && overdueInvoicesAmountLabel != null)
                {
                    int count = overdueInvoices.Count();
                    decimal amount = overdueInvoices.Sum(f => f.MontantRestant);

                    overdueInvoicesCountLabel.Text = count.ToString();
                    overdueInvoicesAmountLabel.Text = $"Montant total : {amount:C}";
                }

                // Charger les relances planifiées pour aujourd'hui
                var plannedReminders = await _relanceService.GetPlannedForDateAsync(DateTime.Today);
                var plannedRemindersCountLabel = this.Controls.Find("plannedRemindersCountLabel", true).FirstOrDefault() as Label;

                if (plannedRemindersCountLabel != null)
                {
                    int count = plannedReminders.Count();
                    plannedRemindersCountLabel.Text = count.ToString();
                }

                // Charger les paiements récents (7 derniers jours)
                var recentPayments = await _paiementService.GetByPeriodAsync(DateTime.Today.AddDays(-7), DateTime.Today);
                var recentPaymentsCountLabel = this.Controls.Find("recentPaymentsCountLabel", true).FirstOrDefault() as Label;
                var recentPaymentsAmountLabel = this.Controls.Find("recentPaymentsAmountLabel", true).FirstOrDefault() as Label;

                if (recentPaymentsCountLabel != null && recentPaymentsAmountLabel != null)
                {
                    int count = recentPayments.Count();
                    decimal amount = recentPayments.Sum(p => p.Montant);

                    recentPaymentsCountLabel.Text = count.ToString();
                    recentPaymentsAmountLabel.Text = $"Montant total : {amount:C}";
                }

                // Charger les actions prioritaires
                var actionsPrioritairesControl = this.Controls.Find("actionsPrioritairesControl", true).FirstOrDefault() as Controls.ActionsPrioritairesControl;
                if (actionsPrioritairesControl != null)
                {
                    await actionsPrioritairesControl.LoadDataAsync();
                }

                // Charger les tâches prioritaires assignées à l'utilisateur
                var mesTachesControl = this.Controls.Find("mesTachesControl", true).FirstOrDefault() as Controls.MesTachesControl;
                if (mesTachesControl != null)
                {
                    await mesTachesControl.LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données du tableau de bord");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des données : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Met à jour les KPIs du tableau de bord
        /// </summary>
        /// <param name="factures">Liste des factures</param>
        /// <param name="paiements">Liste des paiements</param>
        private void UpdateKPIs(IEnumerable<Facture> factures, IEnumerable<Paiement> paiements)
        {
            try
            {
                // DSO (Days Sales Outstanding)
                var dsoValueLabel = this.Controls.Find("dsoValueLabel", true).FirstOrDefault() as Label;
                if (dsoValueLabel != null)
                {
                    double dso = Helpers.DashboardHelper.CalculateDSO(factures, paiements);
                    dsoValueLabel.Text = $"{dso} jours";
                }

                // Taux de recouvrement
                var tauxRecouvrementValueLabel = this.Controls.Find("tauxRecouvrementValueLabel", true).FirstOrDefault() as Label;
                if (tauxRecouvrementValueLabel != null)
                {
                    double tauxRecouvrement = Helpers.DashboardHelper.CalculateTauxRecouvrement(factures);
                    tauxRecouvrementValueLabel.Text = $"{tauxRecouvrement} %";
                }

                // Âge moyen des créances
                var ageMoyenValueLabel = this.Controls.Find("ageMoyenValueLabel", true).FirstOrDefault() as Label;
                if (ageMoyenValueLabel != null)
                {
                    double ageMoyen = Helpers.DashboardHelper.CalculateAgeMoyenCreances(factures);
                    ageMoyenValueLabel.Text = $"{ageMoyen} jours";
                }

                // Montant par tranche d'ancienneté
                var montantsParTranche = Helpers.DashboardHelper.GetMontantParTrancheAnciennete(factures);

                var tranche1ValueLabel = this.Controls.Find("tranche1ValueLabel", true).FirstOrDefault() as Label;
                if (tranche1ValueLabel != null)
                {
                    tranche1ValueLabel.Text = $"{montantsParTranche["0-30j"]:C}";
                }

                var tranche2ValueLabel = this.Controls.Find("tranche2ValueLabel", true).FirstOrDefault() as Label;
                if (tranche2ValueLabel != null)
                {
                    tranche2ValueLabel.Text = $"{montantsParTranche["31-60j"]:C}";
                }

                var tranche3ValueLabel = this.Controls.Find("tranche3ValueLabel", true).FirstOrDefault() as Label;
                if (tranche3ValueLabel != null)
                {
                    tranche3ValueLabel.Text = $"{montantsParTranche["61-90j"]:C}";
                }

                var tranche4ValueLabel = this.Controls.Find("tranche4ValueLabel", true).FirstOrDefault() as Label;
                if (tranche4ValueLabel != null)
                {
                    tranche4ValueLabel.Text = $"{montantsParTranche[">90j"]:C}";
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour des KPIs");
                // Ne pas afficher de message d'erreur à l'utilisateur pour ne pas perturber l'expérience
            }
        }

        private async Task CheckUserPermissionsAsync()
        {
            try
            {
                // Vérifier les permissions de l'utilisateur
                bool canManageUsers = await _authenticationService.HasPermissionAsync(_currentUser.Id, "USERS_VIEW");
                bool canManageRoles = await _authenticationService.HasPermissionAsync(_currentUser.Id, "ROLES_MANAGE");

                // Ajuster le menu en fonction des permissions
                var mainMenu = this.Controls.Find("mainMenu", true).FirstOrDefault() as MenuStrip;
                var adminMenu = mainMenu?.Items.Find("adminMenu", false).FirstOrDefault() as ToolStripMenuItem;
                if (adminMenu != null)
                {
                    adminMenu.Visible = canManageUsers || canManageRoles;

                    var usersMenuItem = adminMenu.DropDownItems.Find("usersMenuItem", false).FirstOrDefault() as ToolStripMenuItem;
                    if (usersMenuItem != null)
                    {
                        usersMenuItem.Visible = canManageUsers;
                    }

                    var rolesMenuItem = adminMenu.DropDownItems.Find("rolesMenuItem", false).FirstOrDefault() as ToolStripMenuItem;
                    if (rolesMenuItem != null)
                    {
                        rolesMenuItem.Visible = canManageRoles;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la vérification des permissions de l'utilisateur");
            }
        }

        #region Gestionnaires d'événements du menu

        private void ExitMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private async void RefreshDashboard_Click(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.WaitCursor;

                // Recharger les données du tableau de bord
                await LoadDashboardDataAsync();

                MessageBox.Show("Les données du tableau de bord ont été rafraîchies.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du rafraîchissement du tableau de bord");
                MessageBox.Show($"Une erreur s'est produite lors du rafraîchissement des données : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.Default;
            }
        }

        /// <summary>
        /// Gère le changement de période pour les graphiques
        /// </summary>
        private async void PeriodComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.WaitCursor;

                // Récupérer la période sélectionnée
                var periodComboBox = sender as ComboBox;
                if (periodComboBox == null)
                    return;

                // Déterminer les dates de début et de fin en fonction de la période
                DateTime dateDebut;
                DateTime dateFin = DateTime.Today;

                switch (periodComboBox.SelectedIndex)
                {
                    case 0: // Mois en cours
                        dateDebut = new DateTime(dateFin.Year, dateFin.Month, 1);
                        break;
                    case 1: // Trimestre en cours
                        int currentQuarter = (dateFin.Month - 1) / 3 + 1;
                        dateDebut = new DateTime(dateFin.Year, (currentQuarter - 1) * 3 + 1, 1);
                        break;
                    case 2: // Année en cours
                        dateDebut = new DateTime(dateFin.Year, 1, 1);
                        break;
                    case 3: // 12 derniers mois
                    default:
                        dateDebut = dateFin.AddMonths(-11);
                        dateDebut = new DateTime(dateDebut.Year, dateDebut.Month, 1);
                        break;
                }

                // Charger les données pour la période sélectionnée
                var factures = await _factureService.GetByPeriodAsync(dateDebut, dateFin);
                var paiements = await _paiementService.GetByPeriodAsync(dateDebut, dateFin);
                var clients = await _clientService.GetAllAsync();
                var litiges = await _litigeService.GetByPeriodAsync(dateDebut, dateFin);
                var categories = await _litigeService.GetAllCategoriesWithCountAsync();
                var etapes = await _litigeService.GetAllEtapesWithCountAsync();

                // Mettre à jour les graphiques
                UpdateCharts(factures, paiements, clients, litiges, categories, etapes);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du changement de période");
                MessageBox.Show($"Une erreur s'est produite lors du changement de période : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.Default;
            }
        }

        /// <summary>
        /// Met à jour les graphiques du tableau de bord
        /// </summary>
        /// <param name="factures">Liste des factures</param>
        /// <param name="paiements">Liste des paiements</param>
        /// <param name="clients">Liste des clients</param>
        /// <param name="litiges">Liste des litiges</param>
        /// <param name="categories">Liste des catégories de litiges</param>
        /// <param name="etapes">Liste des étapes de litiges</param>
        private void UpdateCharts(
            IEnumerable<Facture> factures,
            IEnumerable<Paiement> paiements,
            IEnumerable<Client> clients,
            IEnumerable<Litige> litiges = null,
            IEnumerable<CategorieLitige> categories = null,
            IEnumerable<EtapeLitige> etapes = null)
        {
            try
            {
                // Graphique d'évolution des paiements
                var paymentsEvolutionPlot = this.Controls.Find("paymentsEvolutionPlot", true).FirstOrDefault() as FormsPlot;
                if (paymentsEvolutionPlot != null)
                {
                    paymentsEvolutionPlot.Plot.Clear();
                    ChartHelper.CreatePaymentEvolutionChart(paymentsEvolutionPlot.Plot, paiements);
                    paymentsEvolutionPlot.Refresh();
                }

                // Graphique de répartition des factures par statut
                var invoiceStatusPlot = this.Controls.Find("invoiceStatusPlot", true).FirstOrDefault() as FormsPlot;
                if (invoiceStatusPlot != null)
                {
                    invoiceStatusPlot.Plot.Clear();
                    ChartHelper.CreateInvoiceStatusChart(invoiceStatusPlot.Plot, factures);
                    invoiceStatusPlot.Refresh();
                }

                // Graphique des performances de recouvrement par client
                var recoveryPerformancePlot = this.Controls.Find("recoveryPerformancePlot", true).FirstOrDefault() as FormsPlot;
                if (recoveryPerformancePlot != null)
                {
                    recoveryPerformancePlot.Plot.Clear();
                    ChartHelper.CreateRecoveryPerformanceChart(recoveryPerformancePlot.Plot, factures, clients);
                    recoveryPerformancePlot.Refresh();
                }

                // Graphique de répartition des factures par tranche d'ancienneté
                var ageDistributionPlot = this.Controls.Find("ageDistributionPlot", true).FirstOrDefault() as FormsPlot;
                if (ageDistributionPlot != null)
                {
                    ageDistributionPlot.Plot.Clear();
                    ChartHelper.CreateAgeDistributionChart(ageDistributionPlot.Plot, factures);
                    ageDistributionPlot.Refresh();
                }

                // Graphique de répartition des litiges par catégorie
                if (litiges != null && categories != null)
                {
                    var litigesCategorieDistributionPlot = this.Controls.Find("litigesCategorieDistributionPlot", true).FirstOrDefault() as FormsPlot;
                    if (litigesCategorieDistributionPlot != null)
                    {
                        litigesCategorieDistributionPlot.Plot.Clear();
                        CreateLitigesCategorieChart(litigesCategorieDistributionPlot.Plot, litiges, categories);
                        litigesCategorieDistributionPlot.Refresh();
                    }
                }

                // Graphique de répartition des litiges par étape
                if (litiges != null && etapes != null)
                {
                    var litigesEtapeDistributionPlot = this.Controls.Find("litigesEtapeDistributionPlot", true).FirstOrDefault() as FormsPlot;
                    if (litigesEtapeDistributionPlot != null)
                    {
                        litigesEtapeDistributionPlot.Plot.Clear();
                        CreateLitigesEtapeChart(litigesEtapeDistributionPlot.Plot, litiges, etapes);
                        litigesEtapeDistributionPlot.Refresh();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour des graphiques");
                // Ne pas afficher de message d'erreur à l'utilisateur pour ne pas perturber l'expérience
            }
        }

        /// <summary>
        /// Met à jour les statistiques des litiges dans le tableau de bord
        /// </summary>
        /// <param name="stats">Statistiques des litiges</param>
        private void UpdateLitigeStats(Dictionary<string, object> stats)
        {
            try
            {
                // Mettre à jour les labels des statistiques des litiges
                var litigesCountLabel = this.Controls.Find("litigesCountLabel", true).FirstOrDefault() as Label;
                var litigesEnRetardLabel = this.Controls.Find("litigesEnRetardLabel", true).FirstOrDefault() as Label;
                var tempsResolutionLabel = this.Controls.Find("tempsResolutionLabel", true).FirstOrDefault() as Label;

                if (litigesCountLabel != null && stats.ContainsKey("NombreTotal"))
                {
                    litigesCountLabel.Text = stats["NombreTotal"].ToString();
                }

                if (litigesEnRetardLabel != null && stats.ContainsKey("NombreEnRetard"))
                {
                    litigesEnRetardLabel.Text = $"En retard : {stats["NombreEnRetard"]}";
                }

                if (tempsResolutionLabel != null && stats.ContainsKey("TempsResolutionMoyen"))
                {
                    tempsResolutionLabel.Text = $"Temps moyen de résolution : {stats["TempsResolutionMoyen"]} jours";
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour des statistiques des litiges");
                // Ne pas afficher de message d'erreur à l'utilisateur pour ne pas perturber l'expérience
            }
        }

        /// <summary>
        /// Crée un graphique de répartition des litiges par catégorie
        /// </summary>
        /// <param name="plot">Plot à utiliser</param>
        /// <param name="litiges">Liste des litiges</param>
        /// <param name="categories">Liste des catégories de litiges</param>
        private void CreateLitigesCategorieChart(ScottPlot.Plot plot, IEnumerable<Litige> litiges, IEnumerable<CategorieLitige> categories)
        {
            // Filtrer les litiges actifs
            var litigesActifs = litiges.Where(l => l.EstActif).ToList();

            // Compter le nombre de litiges par catégorie
            var litigesParCategorie = new Dictionary<string, int>();

            foreach (var categorie in categories.Where(c => c.EstActif))
            {
                int count = litigesActifs.Count(l => l.CategorieLitigeId == categorie.Id);
                if (count > 0)
                {
                    litigesParCategorie.Add(categorie.Nom, count);
                }
            }

            // Si aucun litige, afficher un message
            if (litigesParCategorie.Count == 0)
            {
                plot.Title("Aucun litige à afficher");
                return;
            }

            // Créer le graphique en camembert
            var values = litigesParCategorie.Values.Select(v => (double)v).ToArray();
            var labels = litigesParCategorie.Keys.ToArray();

            var pie = plot.AddPie(values);
            pie.SliceLabels = labels;
            pie.ShowLabels = true;
            pie.ShowPercentages = true;

            // Configurer le graphique
            plot.Title("Répartition des litiges par catégorie");
            plot.Legend();
        }

        /// <summary>
        /// Crée un graphique de répartition des litiges par étape
        /// </summary>
        /// <param name="plot">Plot à utiliser</param>
        /// <param name="litiges">Liste des litiges</param>
        /// <param name="etapes">Liste des étapes de litiges</param>
        private void CreateLitigesEtapeChart(ScottPlot.Plot plot, IEnumerable<Litige> litiges, IEnumerable<EtapeLitige> etapes)
        {
            // Filtrer les litiges actifs
            var litigesActifs = litiges.Where(l => l.EstActif).ToList();

            // Compter le nombre de litiges par étape
            var litigesParEtape = new Dictionary<string, int>();

            foreach (var etape in etapes.Where(e => e.EstActif).OrderBy(e => e.Ordre))
            {
                int count = litigesActifs.Count(l => l.EtapeLitigeId == etape.Id);
                if (count > 0)
                {
                    litigesParEtape.Add(etape.Nom, count);
                }
            }

            // Si aucun litige, afficher un message
            if (litigesParEtape.Count == 0)
            {
                plot.Title("Aucun litige à afficher");
                return;
            }

            // Créer le graphique à barres
            var positions = Enumerable.Range(0, litigesParEtape.Count).Select(i => (double)i).ToArray();
            var values = litigesParEtape.Values.Select(v => (double)v).ToArray();

            var bar = plot.AddBar(values, positions);

            // Configurer les étiquettes de l'axe X
            plot.XTicks(positions, litigesParEtape.Keys.ToArray());

            // Configurer le graphique
            plot.Title("Répartition des litiges par étape");
            plot.YLabel("Nombre de litiges");
            plot.XLabel("Étapes");
        }

        private void ListClientsMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Clients.ClientListForm(_clientService, _authenticationService, _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de liste des clients");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddClientMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Clients.ClientEditForm(_clientService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        MessageBox.Show("Le client a été ajouté avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout de client");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ScoreRisqueMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer le service de scoring de risque client
                var scoreRisqueClientService = _serviceProvider.GetRequiredService<IScoreRisqueClientService>();

                using (var form = new Clients.ScoreRisqueClientListForm(scoreRisqueClientService, _clientService, _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de scores de risque client");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SegmentationMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Clients.ClientSegmentationForm(_clientService, _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de segmentation des clients");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ListInvoicesMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Factures.FactureListForm(_factureService, _clientService, _authenticationService, _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de liste des factures");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddInvoiceMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir d'abord la liste des clients pour sélectionner un client
                using (var clientListForm = new Clients.ClientListForm(_clientService, _authenticationService, _currentUserId, true))
                {
                    if (clientListForm.ShowDialog() == DialogResult.OK && clientListForm.SelectedClientId.HasValue)
                    {
                        int clientId = clientListForm.SelectedClientId.Value;

                        // Créer une nouvelle facture pour ce client
                        var nouvelleFacture = new Facture
                        {
                            ClientId = clientId,
                            DateEmission = DateTime.Today,
                            DateEcheance = DateTime.Today.AddDays(30),
                            Statut = "En attente",
                            EstActif = true
                        };

                        // Ouvrir le formulaire d'ajout de facture pour le client sélectionné
                        using (var form = new Factures.FactureEditForm(_factureService, _clientService, _currentUserId, nouvelleFacture))
                        {
                            if (form.ShowDialog() == DialogResult.OK)
                            {
                                MessageBox.Show("La facture a été ajoutée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout de facture");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OverdueInvoicesMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Factures.FactureListForm(_factureService, _clientService, _authenticationService, _currentUserId, "En retard"))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de liste des factures en retard");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ListPaymentsMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Paiements.PaiementListForm(_paiementService, _clientService, _authenticationService, _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de liste des paiements");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void AddPaymentMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir d'abord la liste des factures pour sélectionner une facture
                using (var factureListForm = new Factures.FactureListForm(_factureService, _clientService, _authenticationService, _currentUserId, null, true))
                {
                    if (factureListForm.ShowDialog() == DialogResult.OK && factureListForm.SelectedFactureId.HasValue)
                    {
                        int factureId = factureListForm.SelectedFactureId.Value;

                        // Récupérer la facture
                        var facture = await _factureService.GetByIdAsync(factureId);
                        if (facture == null)
                        {
                            MessageBox.Show("La facture sélectionnée n'existe pas ou a été supprimée.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        // Créer un nouveau paiement pour cette facture
                        var nouveauPaiement = new Paiement
                        {
                            ClientId = facture.ClientId,
                            DatePaiement = DateTime.Today,
                            Montant = facture.MontantRestant,
                            EstActif = true
                        };

                        // Ouvrir le formulaire d'ajout de paiement pour la facture sélectionnée
                        using (var form = new Paiements.PaiementEditForm(_paiementService, _clientService, _currentUserId, nouveauPaiement))
                        {
                            if (form.ShowDialog() == DialogResult.OK)
                            {
                                MessageBox.Show("Le paiement a été ajouté avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout de paiement");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ListRemindersMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Relances.RelanceListForm(_relanceService, _factureService, _clientService, _authenticationService, _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de liste des relances");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PlannedRemindersMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // Créer une instance de RelanceListForm
                using (var form = new Relances.RelanceListForm(_relanceService, _factureService, _clientService, _authenticationService, _currentUserId))
                {
                    // Configurer le filtre pour les relances planifiées
                    var toolStrip = form.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                    if (toolStrip != null)
                    {
                        var statutComboBox = toolStrip.Items["statutComboBox"] as ToolStripComboBox;
                        if (statutComboBox != null)
                        {
                            // Sélectionner "Planifiée" dans la liste déroulante
                            for (int i = 0; i < statutComboBox.Items.Count; i++)
                            {
                                if (statutComboBox.Items[i].ToString() == "Planifiée")
                                {
                                    statutComboBox.SelectedIndex = i;
                                    break;
                                }
                            }
                        }
                    }

                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de liste des relances planifiées");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateRemindersMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // Demander confirmation
                var result = MessageBox.Show(
                    "Voulez-vous générer automatiquement des relances pour les factures en retard ?\n\n" +
                    "Cette opération peut prendre un certain temps.",
                    "Confirmation",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Afficher un indicateur de chargement
                    System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.WaitCursor;

                    // Générer les relances
                    int count = _relanceService.GenererRelancesAutomatiquesAsync(_currentUserId).Result;

                    // Afficher le résultat
                    MessageBox.Show($"{count} relance(s) ont été générées avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération automatique des relances");
                MessageBox.Show($"Une erreur s'est produite lors de la génération des relances : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.Default;
            }
        }

        private void ModeleRelanceMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Relances.ModeleRelanceListForm(_modeleRelanceService, _authenticationService, _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de gestion des modèles de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RegleRelanceMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Relances.RegleRelanceListForm(_regleRelanceService, _authenticationService, _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de gestion des règles de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PlanificationRelanceMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Relances.PlanificationRelanceListForm(
                    _planificationRelanceService,
                    _modeleRelanceService,
                    _factureService,
                    _clientService,
                    _authenticationService,
                    _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de gestion des planifications de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CommunicationMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Communications.CommunicationListForm(
                    _communicationService,
                    _factureService,
                    _clientService,
                    _contactClientService,
                    _authenticationService,
                    _configuration,
                    _serviceProvider,
                    _currentUserId,
                    null,
                    null))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de gestion des communications");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OverdueInvoicesReportMenuItem_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Fonctionnalité à implémenter : Rapport des factures en retard", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // Cette méthode a été remplacée par PaymentsByPeriodReportMenuItem_Click plus bas dans le code

        // Cette méthode a été remplacée par une implémentation plus complète plus bas dans le code

        private void UsersMenuItem_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Fonctionnalité à implémenter : Gestion des utilisateurs", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void RolesMenuItem_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Fonctionnalité à implémenter : Gestion des rôles et permissions", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void AlertesMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Alertes.AlerteListForm(_alerteService, _authenticationService, _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de gestion des alertes");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AuditLogMenuItem_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Fonctionnalité à implémenter : Journal d'audit", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DatabaseConnectionMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Admin.DatabaseConnectionForm(_databaseAdminService, _configuration))
                {
                    var result = form.ShowDialog();
                    if (result == DialogResult.OK)
                    {
                        MessageBox.Show("La configuration de la connexion à la base de données a été modifiée. L'application doit être redémarrée pour prendre en compte les modifications.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        Application.Restart();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de configuration de la connexion à la base de données");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DatabaseAdminMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Admin.DatabaseAdminForm(_databaseAdminService, _configuration))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'administration de la base de données");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void StandardReportsMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Rapports.ReportGeneratorForm(_reportService, _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de génération de rapports standards");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CustomReportsMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = _serviceProvider.GetRequiredService<Rapports.CustomReportListForm>())
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de gestion des rapports personnalisés");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PaymentsByPeriodReportMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Rapports.ReportGeneratorForm(_reportService, _currentUserId))
                {
                    // Sélectionner automatiquement le rapport "Paiements par période"
                    var typeRapportComboBox = form.Controls.Find("typeRapportComboBox", true).FirstOrDefault() as ComboBox;
                    if (typeRapportComboBox != null)
                    {
                        int index = typeRapportComboBox.Items.IndexOf("Paiements par période");
                        if (index >= 0)
                            typeRapportComboBox.SelectedIndex = index;
                    }
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de génération de rapports de paiements");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InvoicesByPeriodReportMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Rapports.ReportGeneratorForm(_reportService, _currentUserId))
                {
                    // Sélectionner automatiquement le rapport "Factures par période"
                    var typeRapportComboBox = form.Controls.Find("typeRapportComboBox", true).FirstOrDefault() as ComboBox;
                    if (typeRapportComboBox != null)
                    {
                        int index = typeRapportComboBox.Items.IndexOf("Factures par période");
                        if (index >= 0)
                            typeRapportComboBox.SelectedIndex = index;
                    }
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de génération de rapports de factures");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RecoveryRateReportMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Rapports.ReportGeneratorForm(_reportService, _currentUserId))
                {
                    // Sélectionner automatiquement le rapport "Taux de recouvrement"
                    var typeRapportComboBox = form.Controls.Find("typeRapportComboBox", true).FirstOrDefault() as ComboBox;
                    if (typeRapportComboBox != null)
                    {
                        int index = typeRapportComboBox.Items.IndexOf("Taux de recouvrement");
                        if (index >= 0)
                            typeRapportComboBox.SelectedIndex = index;
                    }
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de génération de rapports de taux de recouvrement");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ComparativeReportMenuItem_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Fonctionnalité à implémenter : Analyses comparatives", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void PredictiveReportMenuItem_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Fonctionnalité à implémenter : Analyses prédictives", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ListPlansPaiementMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new PlansPaiement.PlanPaiementListForm(
                    _serviceProvider.GetRequiredService<IPlanPaiementService>(),
                    _clientService,
                    _utilisateurService,
                    _authenticationService,
                    _serviceProvider))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de liste des plans de paiement");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddPlanPaiementMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new PlansPaiement.PlanPaiementEditForm(
                    _serviceProvider.GetRequiredService<IPlanPaiementService>(),
                    _clientService,
                    _utilisateurService,
                    _authenticationService,
                    null))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        MessageBox.Show("Le plan de paiement a été créé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de création de plan de paiement");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EcheancierDashboardMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new PlansPaiement.EcheancierDashboardForm(
                    _serviceProvider.GetRequiredService<IPlanPaiementService>(),
                    _clientService,
                    _authenticationService,
                    _serviceProvider))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du tableau de bord des échéances");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ActionsPrioritairesMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Actions.ActionPrioritaireListForm(_actionPrioritaireService, _clientService, _utilisateurService, _currentUserId))
                {
                    form.ShowDialog();

                    // Recharger les données du tableau de bord après la fermeture du formulaire
                    _ = LoadDashboardDataAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de gestion des actions prioritaires");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void GenerateActionsPrioritairesMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // Demander confirmation
                var result = MessageBox.Show(
                    "Voulez-vous générer automatiquement des actions prioritaires pour tous les clients ?",
                    "Confirmation",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Afficher un indicateur de chargement
                    this.Cursor = Cursors.WaitCursor;

                    // Générer les actions prioritaires
                    int count = await _actionPrioritaireService.GenerateActionsAsync(_currentUserId);

                    // Recharger les données du tableau de bord
                    await LoadDashboardDataAsync();

                    // Afficher un message de confirmation
                    MessageBox.Show($"{count} actions prioritaires ont été générées avec succès.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération des actions prioritaires");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }

        private void MesTachesMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // Créer un formulaire pour afficher les tâches prioritaires assignées à l'utilisateur
                using (var form = new Form())
                {
                    form.Text = "Mes tâches prioritaires";
                    form.Size = new Size(1000, 600);
                    form.StartPosition = FormStartPosition.CenterParent;
                    form.MinimizeBox = false;
                    form.MaximizeBox = false;
                    form.FormBorderStyle = FormBorderStyle.FixedDialog;

                    // Créer le contrôle des tâches prioritaires
                    var mesTachesControl = new Controls.MesTachesControl(
                        _actionPrioritaireService,
                        _utilisateurService,
                        _currentUserId);
                    mesTachesControl.Dock = DockStyle.Fill;
                    form.Controls.Add(mesTachesControl);

                    // Afficher le formulaire
                    form.ShowDialog();

                    // Recharger les données du tableau de bord après la fermeture du formulaire
                    _ = LoadDashboardDataAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire des tâches prioritaires");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AboutMenuItem_Click(object sender, EventArgs e)
        {
            string appName = _configuration.GetValue<string>("AppSettings:ApplicationName", "RecouvreX");
            string appVersion = _configuration.GetValue<string>("AppSettings:ApplicationVersion", "1.0.0");
            string companyName = _configuration.GetValue<string>("AppSettings:CompanyName", "Votre Entreprise");

            MessageBox.Show(
                $"{appName} version {appVersion}\n\n" +
                $"© {DateTime.Now.Year} {companyName}\n\n" +
                "Application de gestion de recouvrement des créances clients",
                "À propos",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        private void AlerteTimer_Tick(object sender, EventArgs e)
        {
            // Vérifier les alertes
            CheckAlertesAsync().ConfigureAwait(false);

            // Vérifier les notifications
            UpdateNotificationsCountAsync().ConfigureAwait(false);

            // Vérifier les litiges en retard
            CheckLitigesEnRetardAsync().ConfigureAwait(false);
        }

        /// <summary>
        /// Met à jour le compteur de notifications
        /// </summary>
        private async Task UpdateNotificationsCountAsync()
        {
            try
            {
                // Récupérer les notifications non lues
                var notifications = await _litigeService.GetUnreadNotificationsByUtilisateurIdAsync(_currentUserId);
                int count = notifications.Count();

                // Mettre à jour le label
                var notificationsCountLabel = this.MainMenuStrip.Items.Find("notificationsCountLabel", false).FirstOrDefault() as ToolStripLabel;
                if (notificationsCountLabel != null)
                {
                    if (count > 0)
                    {
                        notificationsCountLabel.Text = count.ToString();
                        notificationsCountLabel.Visible = true;
                    }
                    else
                    {
                        notificationsCountLabel.Text = "";
                        notificationsCountLabel.Visible = false;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour du compteur de notifications");
                // Ne pas afficher de message d'erreur à l'utilisateur pour ne pas perturber l'expérience
            }
        }

        /// <summary>
        /// Vérifie les litiges en retard et les escalade si nécessaire
        /// </summary>
        private async Task CheckLitigesEnRetardAsync()
        {
            try
            {
                // Vérifier et escalader les litiges en retard
                int count = await _litigeService.VerifierEtEscaladerLitigesEnRetardAsync();

                // Si des litiges ont été escaladés, mettre à jour le compteur de notifications
                if (count > 0)
                {
                    await UpdateNotificationsCountAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la vérification des litiges en retard");
                // Ne pas afficher de message d'erreur à l'utilisateur pour ne pas perturber l'expérience
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton de notifications
        /// </summary>
        private void NotificationsButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir le formulaire de notifications
                using (var form = new Litiges.NotificationsLitigeForm(_litigeService, _currentUserId))
                {
                    form.ShowDialog();
                }

                // Mettre à jour le compteur de notifications
                UpdateNotificationsCountAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de notifications");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task CheckAlertesAsync()
        {
            try
            {
                // Vérifier les alertes
                var alertesDeclenchees = await _alerteService.CheckAlertesAsync(_currentUserId);

                // Afficher les notifications pour les alertes déclenchées
                foreach (var alerte in alertesDeclenchees)
                {
                    // Afficher une notification
                    NotificationHelper.ShowNotification(alerte, this);

                    // Enregistrer la notification
                    await _alerteService.RecordNotificationAsync(alerte.Id, _currentUserId);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la vérification des alertes");
                // Ne pas afficher de message d'erreur à l'utilisateur pour ne pas perturber l'expérience
            }
        }

        private void ListLitigesMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Litiges.LitigeListForm(
                    _litigeService,
                    _clientService,
                    _factureService,
                    _utilisateurService,
                    _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de liste des litiges");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CategoriesLitigeMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Litiges.CategorieLitigeListForm(_litigeService, _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de gestion des catégories de litiges");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EtapesLitigeMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new Litiges.EtapeLitigeListForm(_litigeService, _currentUserId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de gestion des étapes de litiges");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
