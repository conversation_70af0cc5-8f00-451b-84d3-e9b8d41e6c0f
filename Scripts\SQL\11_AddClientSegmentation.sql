-- Script pour ajouter les champs de segmentation à la table Clients
USE [RecouvreX]
GO

-- Vérifier si les colonnes existent déjà
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[app].[Clients]') AND name = 'Segment')
BEGIN
    -- Ajouter la colonne Segment
    ALTER TABLE [app].[Clients]
    ADD [Segment] INT NOT NULL DEFAULT 0;
    
    PRINT 'Colonne Segment ajoutée à la table Clients.';
END
ELSE
BEGIN
    PRINT 'La colonne Segment existe déjà dans la table Clients.';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[app].[Clients]') AND name = 'DateSegmentation')
BEGIN
    -- Ajouter la colonne DateSegmentation
    ALTER TABLE [app].[Clients]
    ADD [DateSegmentation] DATETIME NULL;
    
    PRINT 'Colonne DateSegmentation ajoutée à la table Clients.';
END
ELSE
BEGIN
    PRINT 'La colonne DateSegmentation existe déjà dans la table Clients.';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[app].[Clients]') AND name = 'CommentaireSegmentation')
BEGIN
    -- Ajouter la colonne CommentaireSegmentation
    ALTER TABLE [app].[Clients]
    ADD [CommentaireSegmentation] NVARCHAR(500) NULL;
    
    PRINT 'Colonne CommentaireSegmentation ajoutée à la table Clients.';
END
ELSE
BEGIN
    PRINT 'La colonne CommentaireSegmentation existe déjà dans la table Clients.';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[app].[Clients]') AND name = 'SegmentationManuelle')
BEGIN
    -- Ajouter la colonne SegmentationManuelle
    ALTER TABLE [app].[Clients]
    ADD [SegmentationManuelle] BIT NOT NULL DEFAULT 0;
    
    PRINT 'Colonne SegmentationManuelle ajoutée à la table Clients.';
END
ELSE
BEGIN
    PRINT 'La colonne SegmentationManuelle existe déjà dans la table Clients.';
END

-- Créer un index sur la colonne Segment pour améliorer les performances des requêtes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Clients_Segment' AND object_id = OBJECT_ID(N'[app].[Clients]'))
BEGIN
    CREATE INDEX [IX_Clients_Segment] ON [app].[Clients] ([Segment]);
    
    PRINT 'Index IX_Clients_Segment créé sur la table Clients.';
END
ELSE
BEGIN
    PRINT 'L''index IX_Clients_Segment existe déjà sur la table Clients.';
END
GO
