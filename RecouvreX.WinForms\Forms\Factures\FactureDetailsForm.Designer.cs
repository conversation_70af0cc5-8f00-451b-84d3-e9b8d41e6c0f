namespace RecouvreX.WinForms.Forms.Factures
{
    partial class FactureDetailsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // FactureDetailsForm
            //
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.Name = "FactureDetailsForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Détails de la facture";
            this.Load += new System.EventHandler(this.FactureDetailsForm_Load);
            this.ResumeLayout(false);

            // Panneau principal
            System.Windows.Forms.TableLayoutPanel mainPanel = new System.Windows.Forms.TableLayoutPanel();
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Padding = new System.Windows.Forms.Padding(10);
            mainPanel.ColumnCount = 1;
            mainPanel.RowCount = 3;
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 200F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.Controls.Add(mainPanel);

            // Panneau d'informations de la facture
            System.Windows.Forms.GroupBox factureInfoGroupBox = new System.Windows.Forms.GroupBox();
            factureInfoGroupBox.Text = "Informations de la facture";
            factureInfoGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(factureInfoGroupBox, 0, 0);

            System.Windows.Forms.TableLayoutPanel factureInfoPanel = new System.Windows.Forms.TableLayoutPanel();
            factureInfoPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            factureInfoPanel.Padding = new System.Windows.Forms.Padding(5);
            factureInfoPanel.ColumnCount = 4;
            factureInfoPanel.RowCount = 4;
            factureInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            factureInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            factureInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            factureInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            factureInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 25F));
            factureInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 25F));
            factureInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 25F));
            factureInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 25F));
            factureInfoGroupBox.Controls.Add(factureInfoPanel);

            // Ligne 1
            System.Windows.Forms.Label numeroLabel = new System.Windows.Forms.Label();
            numeroLabel.Text = "Numéro :";
            numeroLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(numeroLabel, 0, 0);

            System.Windows.Forms.Label numeroValueLabel = new System.Windows.Forms.Label();
            numeroValueLabel.Name = "numeroValueLabel";
            numeroValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            numeroValueLabel.Font = new System.Drawing.Font(numeroValueLabel.Font, System.Drawing.FontStyle.Bold);
            factureInfoPanel.Controls.Add(numeroValueLabel, 1, 0);

            System.Windows.Forms.Label clientLabel = new System.Windows.Forms.Label();
            clientLabel.Text = "Client :";
            clientLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(clientLabel, 2, 0);

            System.Windows.Forms.Label clientValueLabel = new System.Windows.Forms.Label();
            clientValueLabel.Name = "clientValueLabel";
            clientValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientValueLabel.Font = new System.Drawing.Font(clientValueLabel.Font, System.Drawing.FontStyle.Bold);
            factureInfoPanel.Controls.Add(clientValueLabel, 3, 0);

            // Ligne 2
            System.Windows.Forms.Label dateEmissionLabel = new System.Windows.Forms.Label();
            dateEmissionLabel.Text = "Date d'émission :";
            dateEmissionLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(dateEmissionLabel, 0, 1);

            System.Windows.Forms.Label dateEmissionValueLabel = new System.Windows.Forms.Label();
            dateEmissionValueLabel.Name = "dateEmissionValueLabel";
            dateEmissionValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(dateEmissionValueLabel, 1, 1);

            System.Windows.Forms.Label dateEcheanceLabel = new System.Windows.Forms.Label();
            dateEcheanceLabel.Text = "Date d'échéance :";
            dateEcheanceLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(dateEcheanceLabel, 2, 1);

            System.Windows.Forms.Label dateEcheanceValueLabel = new System.Windows.Forms.Label();
            dateEcheanceValueLabel.Name = "dateEcheanceValueLabel";
            dateEcheanceValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(dateEcheanceValueLabel, 3, 1);

            // Ligne 3
            System.Windows.Forms.Label montantHTLabel = new System.Windows.Forms.Label();
            montantHTLabel.Text = "Montant HT :";
            montantHTLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(montantHTLabel, 0, 2);

            System.Windows.Forms.Label montantHTValueLabel = new System.Windows.Forms.Label();
            montantHTValueLabel.Name = "montantHTValueLabel";
            montantHTValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(montantHTValueLabel, 1, 2);

            System.Windows.Forms.Label montantTVALabel = new System.Windows.Forms.Label();
            montantTVALabel.Text = "Montant TVA :";
            montantTVALabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(montantTVALabel, 2, 2);

            System.Windows.Forms.Label montantTVAValueLabel = new System.Windows.Forms.Label();
            montantTVAValueLabel.Name = "montantTVAValueLabel";
            montantTVAValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(montantTVAValueLabel, 3, 2);

            // Ligne 4
            System.Windows.Forms.Label montantTTCLabel = new System.Windows.Forms.Label();
            montantTTCLabel.Text = "Montant TTC :";
            montantTTCLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(montantTTCLabel, 0, 3);

            System.Windows.Forms.Label montantTTCValueLabel = new System.Windows.Forms.Label();
            montantTTCValueLabel.Name = "montantTTCValueLabel";
            montantTTCValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            montantTTCValueLabel.Font = new System.Drawing.Font(montantTTCValueLabel.Font, System.Drawing.FontStyle.Bold);
            factureInfoPanel.Controls.Add(montantTTCValueLabel, 1, 3);

            System.Windows.Forms.Label statutLabel = new System.Windows.Forms.Label();
            statutLabel.Text = "Statut :";
            statutLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(statutLabel, 2, 3);

            System.Windows.Forms.Label statutValueLabel = new System.Windows.Forms.Label();
            statutValueLabel.Name = "statutValueLabel";
            statutValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            statutValueLabel.Font = new System.Drawing.Font(statutValueLabel.Font, System.Drawing.FontStyle.Bold);
            factureInfoPanel.Controls.Add(statutValueLabel, 3, 3);

            // Panneau des paiements
            System.Windows.Forms.GroupBox paiementsGroupBox = new System.Windows.Forms.GroupBox();
            paiementsGroupBox.Text = "Paiements";
            paiementsGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(paiementsGroupBox, 0, 1);

            System.Windows.Forms.TableLayoutPanel paiementsPanel = new System.Windows.Forms.TableLayoutPanel();
            paiementsPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            paiementsPanel.Padding = new System.Windows.Forms.Padding(5);
            paiementsPanel.ColumnCount = 1;
            paiementsPanel.RowCount = 2;
            paiementsPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            paiementsPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            paiementsPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            paiementsGroupBox.Controls.Add(paiementsPanel);

            // Barre d'outils des paiements
            System.Windows.Forms.ToolStrip paiementsToolStrip = new System.Windows.Forms.ToolStrip();
            paiementsToolStrip.Name = "paiementsToolStrip";
            paiementsToolStrip.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
            paiementsPanel.Controls.Add(paiementsToolStrip, 0, 0);

            System.Windows.Forms.ToolStripButton addPaiementButton = new System.Windows.Forms.ToolStripButton();
            addPaiementButton.Name = "addPaiementButton";
            addPaiementButton.Text = "Ajouter";
            addPaiementButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            addPaiementButton.Click += new System.EventHandler(this.AddPaiementButton_Click);
            paiementsToolStrip.Items.Add(addPaiementButton);

            System.Windows.Forms.ToolStripButton viewPaiementButton = new System.Windows.Forms.ToolStripButton();
            viewPaiementButton.Name = "viewPaiementButton";
            viewPaiementButton.Text = "Voir";
            viewPaiementButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            viewPaiementButton.Click += new System.EventHandler(this.ViewPaiementButton_Click);
            paiementsToolStrip.Items.Add(viewPaiementButton);

            // DataGridView des paiements
            System.Windows.Forms.DataGridView paiementsDataGridView = new System.Windows.Forms.DataGridView();
            paiementsDataGridView.Name = "paiementsDataGridView";
            paiementsDataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            paiementsDataGridView.AllowUserToAddRows = false;
            paiementsDataGridView.AllowUserToDeleteRows = false;
            paiementsDataGridView.ReadOnly = true;
            paiementsDataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            paiementsDataGridView.MultiSelect = false;
            paiementsDataGridView.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            paiementsDataGridView.RowHeadersVisible = false;
            paiementsDataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.PaiementsDataGridView_CellDoubleClick);
            paiementsPanel.Controls.Add(paiementsDataGridView, 0, 1);

            // Panneau des relances
            System.Windows.Forms.GroupBox relancesGroupBox = new System.Windows.Forms.GroupBox();
            relancesGroupBox.Text = "Relances";
            relancesGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(relancesGroupBox, 0, 2);

            System.Windows.Forms.TableLayoutPanel relancesPanel = new System.Windows.Forms.TableLayoutPanel();
            relancesPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            relancesPanel.Padding = new System.Windows.Forms.Padding(5);
            relancesPanel.ColumnCount = 1;
            relancesPanel.RowCount = 2;
            relancesPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            relancesPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            relancesPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            relancesGroupBox.Controls.Add(relancesPanel);

            // Barre d'outils des relances
            System.Windows.Forms.ToolStrip relancesToolStrip = new System.Windows.Forms.ToolStrip();
            relancesToolStrip.Name = "relancesToolStrip";
            relancesToolStrip.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
            relancesPanel.Controls.Add(relancesToolStrip, 0, 0);

            System.Windows.Forms.ToolStripButton addRelanceButton = new System.Windows.Forms.ToolStripButton();
            addRelanceButton.Name = "addRelanceButton";
            addRelanceButton.Text = "Ajouter";
            addRelanceButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            addRelanceButton.Click += new System.EventHandler(this.AddRelanceButton_Click);
            relancesToolStrip.Items.Add(addRelanceButton);

            System.Windows.Forms.ToolStripButton viewRelanceButton = new System.Windows.Forms.ToolStripButton();
            viewRelanceButton.Name = "viewRelanceButton";
            viewRelanceButton.Text = "Voir";
            viewRelanceButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            viewRelanceButton.Click += new System.EventHandler(this.ViewRelanceButton_Click);
            relancesToolStrip.Items.Add(viewRelanceButton);

            System.Windows.Forms.ToolStripButton generateLettreButton = new System.Windows.Forms.ToolStripButton();
            generateLettreButton.Name = "generateLettreButton";
            generateLettreButton.Text = "Générer lettre";
            generateLettreButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            generateLettreButton.Click += new System.EventHandler(this.GenerateLettreButton_Click);
            relancesToolStrip.Items.Add(generateLettreButton);

            System.Windows.Forms.ToolStripButton sendWhatsAppButton = new System.Windows.Forms.ToolStripButton();
            sendWhatsAppButton.Name = "sendWhatsAppButton";
            sendWhatsAppButton.Text = "Envoyer WhatsApp";
            sendWhatsAppButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            sendWhatsAppButton.Click += new System.EventHandler(this.SendWhatsAppButton_Click);
            relancesToolStrip.Items.Add(sendWhatsAppButton);

            // DataGridView des relances
            System.Windows.Forms.DataGridView relancesDataGridView = new System.Windows.Forms.DataGridView();
            relancesDataGridView.Name = "relancesDataGridView";
            relancesDataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            relancesDataGridView.AllowUserToAddRows = false;
            relancesDataGridView.AllowUserToDeleteRows = false;
            relancesDataGridView.ReadOnly = true;
            relancesDataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            relancesDataGridView.MultiSelect = false;
            relancesDataGridView.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            relancesDataGridView.RowHeadersVisible = false;
            relancesDataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.RelancesDataGridView_CellDoubleClick);
            relancesPanel.Controls.Add(relancesDataGridView, 0, 1);
        }

        #endregion
    }
}
