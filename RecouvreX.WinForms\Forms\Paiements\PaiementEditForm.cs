using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.ComponentModel;

namespace RecouvreX.WinForms.Forms.Paiements
{
    public partial class PaiementEditForm : Form
    {
        private readonly IPaiementService _paiementService;
        private readonly IClientService _clientService;
        private readonly int _currentUserId;
        private readonly Paiement? _paiement;
        private readonly bool _isEditMode;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();
        private List<Client> _clients = new List<Client>();
        private TextBox referenceTextBox;

        public PaiementEditForm(IPaiementService paiementService, IClientService clientService, int currentUserId, Paiement? paiement = null)
        {
            _paiementService = paiementService ?? throw new ArgumentNullException(nameof(paiementService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _currentUserId = currentUserId;
            _paiement = paiement;
            _isEditMode = paiement != null;

            InitializeComponent();

            // Mettre à jour le titre du formulaire après l'initialisation des composants
            this.Text = _isEditMode ? "Modifier un paiement" : "Ajouter un paiement";

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;

            // Activer/désactiver le champ de référence en fonction du mode
            if (referenceTextBox != null)
            {
                referenceTextBox.Enabled = _isEditMode;
            }
        }

        private async void PaiementEditForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Charger les clients
                await LoadClientsAsync();

                // Si en mode édition, remplir les champs avec les données du paiement
                if (_isEditMode && _paiement != null)
                {
                    FillFormWithPaiementData(_paiement);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'édition de paiement");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadClientsAsync()
        {
            try
            {
                // Récupérer les clients
                _clients = (await _clientService.GetAllAsync()).ToList();

                // Remplir la ComboBox
                var clientComboBox = this.Controls.Find("clientComboBox", true).FirstOrDefault() as ComboBox;
                if (clientComboBox != null)
                {
                    clientComboBox.Items.Clear();
                    clientComboBox.DisplayMember = "Text";
                    clientComboBox.ValueMember = "Value";

                    // Ajouter un élément vide
                    clientComboBox.Items.Add(new { Text = "-- Sélectionner un client --", Value = 0 });

                    // Ajouter les clients
                    foreach (var client in _clients)
                    {
                        clientComboBox.Items.Add(new { Text = $"{client.Code} - {client.RaisonSociale}", Value = client.Id });
                    }

                    clientComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des clients");
                throw;
            }
        }

        private void FillFormWithPaiementData(Paiement paiement)
        {
            var referenceTextBox = this.Controls.Find("referenceTextBox", true).FirstOrDefault() as TextBox;
            var clientComboBox = this.Controls.Find("clientComboBox", true).FirstOrDefault() as ComboBox;
            var datePaiementPicker = this.Controls.Find("datePaiementPicker", true).FirstOrDefault() as DateTimePicker;
            var montantNumericUpDown = this.Controls.Find("montantNumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var modePaiementComboBox = this.Controls.Find("modePaiementComboBox", true).FirstOrDefault() as ComboBox;
            var referenceBancaireTextBox = this.Controls.Find("referenceBancaireTextBox", true).FirstOrDefault() as TextBox;

            if (referenceTextBox != null) referenceTextBox.Text = paiement.Reference;

            if (clientComboBox != null)
            {
                // Sélectionner le client
                for (int i = 0; i < clientComboBox.Items.Count; i++)
                {
                    dynamic item = clientComboBox.Items[i];
                    if (item.Value == paiement.ClientId)
                    {
                        clientComboBox.SelectedIndex = i;
                        break;
                    }
                }
            }

            if (datePaiementPicker != null) datePaiementPicker.Value = paiement.DatePaiement;
            if (montantNumericUpDown != null) montantNumericUpDown.Value = paiement.Montant;

            if (modePaiementComboBox != null)
            {
                // Sélectionner le mode de paiement
                for (int i = 0; i < modePaiementComboBox.Items.Count; i++)
                {
                    if (modePaiementComboBox.Items[i].ToString() == paiement.ModePaiement)
                    {
                        modePaiementComboBox.SelectedIndex = i;
                        break;
                    }
                }
            }

            if (referenceBancaireTextBox != null) referenceBancaireTextBox.Text = paiement.ReferenceBancaire;
        }

        private void ClientComboBox_Validating(object sender, CancelEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                if (comboBox.SelectedIndex == 0) // L'élément vide
                {
                    _errorProvider.SetError(comboBox, "Veuillez sélectionner un client.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(comboBox, "");
                }
            }
        }

        private void MontantNumericUpDown_Validating(object sender, CancelEventArgs e)
        {
            var numericUpDown = sender as NumericUpDown;
            if (numericUpDown != null)
            {
                if (numericUpDown.Value <= 0)
                {
                    _errorProvider.SetError(numericUpDown, "Le montant doit être supérieur à zéro.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(numericUpDown, "");
                }
            }
        }

        private void ModePaiementComboBox_Validating(object sender, CancelEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                if (comboBox.SelectedIndex == -1) // Aucun élément sélectionné
                {
                    _errorProvider.SetError(comboBox, "Veuillez sélectionner un mode de paiement.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(comboBox, "");
                }
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant d'enregistrer.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer les valeurs du formulaire
                var clientComboBox = this.Controls.Find("clientComboBox", true).FirstOrDefault() as ComboBox;
                var datePaiementPicker = this.Controls.Find("datePaiementPicker", true).FirstOrDefault() as DateTimePicker;
                var montantNumericUpDown = this.Controls.Find("montantNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                var modePaiementComboBox = this.Controls.Find("modePaiementComboBox", true).FirstOrDefault() as ComboBox;
                var referenceBancaireTextBox = this.Controls.Find("referenceBancaireTextBox", true).FirstOrDefault() as TextBox;

                if (clientComboBox == null || datePaiementPicker == null || montantNumericUpDown == null ||
                    modePaiementComboBox == null || referenceBancaireTextBox == null)
                {
                    throw new InvalidOperationException("Un ou plusieurs champs sont introuvables.");
                }

                // Créer ou mettre à jour le paiement
                Paiement paiement;
                if (_isEditMode && _paiement != null)
                {
                    // Mode édition
                    paiement = _paiement;
                }
                else
                {
                    // Mode ajout
                    paiement = new Paiement();
                }

                // Remplir les propriétés du paiement
                paiement.ClientId = ((dynamic)clientComboBox.SelectedItem).Value;
                paiement.DatePaiement = datePaiementPicker.Value;
                paiement.Montant = montantNumericUpDown.Value;
                paiement.ModePaiement = modePaiementComboBox.SelectedItem.ToString();
                paiement.ReferenceBancaire = referenceBancaireTextBox.Text.Trim();

                // Enregistrer le paiement
                if (_isEditMode)
                {
                    // Mettre à jour le paiement existant
                    await _paiementService.UpdateAsync(paiement, _currentUserId);
                    MessageBox.Show("Le paiement a été mis à jour avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Créer un nouveau paiement
                    await _paiementService.CreateAsync(paiement, _currentUserId);
                    MessageBox.Show("Le paiement a été créé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement du paiement");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement du paiement : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
