using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RecouvreX.DataAccess.Interfaces;
using Serilog;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service pour la gestion des litiges
    /// </summary>
    public class LitigeService : ILitigeService
    {
        private readonly ILitigeRepository _litigeRepository;
        private readonly ICategorieLitigeRepository _categorieLitigeRepository;
        private readonly IEtapeLitigeRepository _etapeLitigeRepository;
        private readonly IFactureRepository _factureRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IHistoriqueEtapeLitigeRepository _historiqueEtapeLitigeRepository;
        private readonly ICommentaireLitigeRepository _commentaireLitigeRepository;
        private readonly INotificationLitigeRepository _notificationLitigeRepository;
        private readonly IDocumentLitigeRepository _documentLitigeRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="litigeRepository">Repository des litiges</param>
        /// <param name="categorieLitigeRepository">Repository des catégories de litiges</param>
        /// <param name="etapeLitigeRepository">Repository des étapes de litiges</param>
        /// <param name="factureRepository">Repository des factures</param>
        /// <param name="utilisateurRepository">Repository des utilisateurs</param>
        /// <param name="historiqueEtapeLitigeRepository">Repository des historiques d'étapes de litiges</param>
        /// <param name="commentaireLitigeRepository">Repository des commentaires de litiges</param>
        /// <param name="notificationLitigeRepository">Repository des notifications de litiges</param>
        /// <param name="documentLitigeRepository">Repository des documents de litiges</param>
        public LitigeService(
            ILitigeRepository litigeRepository,
            ICategorieLitigeRepository categorieLitigeRepository,
            IEtapeLitigeRepository etapeLitigeRepository,
            IFactureRepository factureRepository,
            IUtilisateurRepository utilisateurRepository,
            IHistoriqueEtapeLitigeRepository historiqueEtapeLitigeRepository,
            ICommentaireLitigeRepository commentaireLitigeRepository,
            INotificationLitigeRepository notificationLitigeRepository,
            IDocumentLitigeRepository documentLitigeRepository)
        {
            _litigeRepository = litigeRepository;
            _categorieLitigeRepository = categorieLitigeRepository;
            _etapeLitigeRepository = etapeLitigeRepository;
            _factureRepository = factureRepository;
            _utilisateurRepository = utilisateurRepository;
            _historiqueEtapeLitigeRepository = historiqueEtapeLitigeRepository;
            _commentaireLitigeRepository = commentaireLitigeRepository;
            _notificationLitigeRepository = notificationLitigeRepository;
            _documentLitigeRepository = documentLitigeRepository;
        }

        /// <summary>
        /// Récupère tous les litiges
        /// </summary>
        /// <returns>Liste de tous les litiges</returns>
        public async Task<IEnumerable<Litige>> GetAllAsync()
        {
            return await _litigeRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère les litiges pour une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début de la période</param>
        /// <param name="dateFin">Date de fin de la période</param>
        /// <returns>Liste des litiges pour la période</returns>
        public async Task<IEnumerable<Litige>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            // Récupérer tous les litiges
            var litiges = await _litigeRepository.GetAllAsync();

            // Filtrer les litiges par date de création
            return litiges.Where(l => l.DateCreation >= dateDebut && l.DateCreation <= dateFin);
        }

        /// <summary>
        /// Récupère un litige par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du litige</param>
        /// <returns>Litige trouvé ou null</returns>
        public async Task<Litige> GetByIdAsync(int id)
        {
            return await _litigeRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère un litige avec toutes ses informations associées
        /// </summary>
        /// <param name="id">Identifiant du litige</param>
        /// <returns>Litige avec ses informations associées</returns>
        public async Task<Litige> GetWithDetailsAsync(int id)
        {
            return await _litigeRepository.GetWithDetailsAsync(id);
        }

        /// <summary>
        /// Récupère tous les litiges d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des litiges de la facture</returns>
        public async Task<IEnumerable<Litige>> GetByFactureIdAsync(int factureId)
        {
            return await _litigeRepository.GetByFactureIdAsync(factureId);
        }

        /// <summary>
        /// Récupère tous les litiges d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des litiges du client</returns>
        public async Task<IEnumerable<Litige>> GetByClientIdAsync(int clientId)
        {
            return await _litigeRepository.GetByClientIdAsync(clientId);
        }

        /// <summary>
        /// Récupère tous les litiges par statut
        /// </summary>
        /// <param name="statut">Statut des litiges</param>
        /// <returns>Liste des litiges ayant le statut spécifié</returns>
        public async Task<IEnumerable<Litige>> GetByStatutAsync(string statut)
        {
            return await _litigeRepository.GetByStatutAsync(statut);
        }

        /// <summary>
        /// Récupère tous les litiges par catégorie
        /// </summary>
        /// <param name="categorieId">Identifiant de la catégorie</param>
        /// <returns>Liste des litiges de la catégorie spécifiée</returns>
        public async Task<IEnumerable<Litige>> GetByCategorieIdAsync(int categorieId)
        {
            return await _litigeRepository.GetByCategorieIdAsync(categorieId);
        }

        /// <summary>
        /// Récupère tous les litiges par étape
        /// </summary>
        /// <param name="etapeId">Identifiant de l'étape</param>
        /// <returns>Liste des litiges à l'étape spécifiée</returns>
        public async Task<IEnumerable<Litige>> GetByEtapeIdAsync(int etapeId)
        {
            return await _litigeRepository.GetByEtapeIdAsync(etapeId);
        }

        /// <summary>
        /// Récupère tous les litiges assignés à un responsable
        /// </summary>
        /// <param name="responsableId">Identifiant du responsable</param>
        /// <returns>Liste des litiges assignés au responsable spécifié</returns>
        public async Task<IEnumerable<Litige>> GetByResponsableIdAsync(int responsableId)
        {
            return await _litigeRepository.GetByResponsableIdAsync(responsableId);
        }

        /// <summary>
        /// Récupère tous les litiges dont la date d'échéance est dépassée
        /// </summary>
        /// <returns>Liste des litiges en retard</returns>
        public async Task<IEnumerable<Litige>> GetOverdueAsync()
        {
            return await _litigeRepository.GetOverdueAsync();
        }

        /// <summary>
        /// Crée un nouveau litige
        /// </summary>
        /// <param name="litige">Litige à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui crée le litige</param>
        /// <returns>Litige créé</returns>
        public async Task<Litige> CreateAsync(Litige litige, int creePar)
        {
            // Vérifier que la facture existe
            var facture = await _factureRepository.GetByIdAsync(litige.FactureId);
            if (facture == null)
            {
                throw new ArgumentException("La facture spécifiée n'existe pas.");
            }

            // Vérifier que la catégorie existe
            var categorie = await _categorieLitigeRepository.GetByIdAsync(litige.CategorieLitigeId);
            if (categorie == null)
            {
                throw new ArgumentException("La catégorie spécifiée n'existe pas.");
            }

            // Vérifier que l'étape existe
            var etape = await _etapeLitigeRepository.GetByIdAsync(litige.EtapeLitigeId);
            if (etape == null)
            {
                throw new ArgumentException("L'étape spécifiée n'existe pas.");
            }

            // Vérifier que le responsable existe
            var responsable = await _utilisateurRepository.GetByIdAsync(litige.ResponsableId);
            if (responsable == null)
            {
                throw new ArgumentException("Le responsable spécifié n'existe pas.");
            }

            // Définir les valeurs par défaut
            litige.DateOuverture = DateTime.Now;
            if (litige.DateEcheance == default)
            {
                litige.DateEcheance = DateTime.Now.AddDays(categorie.DelaiResolutionJours);
            }
            litige.Statut = "Ouvert";
            litige.CreePar = creePar;
            litige.DateCreation = DateTime.Now;
            litige.EstActif = true;

            // Créer le litige
            var litigeCreated = await _litigeRepository.AddAsync(litige, creePar);
            litige.Id = litigeCreated.Id;

            // Ajouter l'historique de l'étape initiale
            var historiqueEtape = new HistoriqueEtapeLitige
            {
                LitigeId = litige.Id,
                EtapeLitigeId = litige.EtapeLitigeId,
                DateDebut = DateTime.Now,
                UtilisateurId = creePar,
                Commentaire = "Création du litige"
            };

            await _litigeRepository.AddHistoriqueEtapeAsync(historiqueEtape);

            return litige;
        }

        /// <summary>
        /// Met à jour un litige existant
        /// </summary>
        /// <param name="litige">Litige à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui modifie le litige</param>
        /// <returns>Litige mis à jour</returns>
        public async Task<Litige> UpdateAsync(Litige litige, int modifiePar)
        {
            // Vérifier que le litige existe
            var existingLitige = await _litigeRepository.GetByIdAsync(litige.Id);
            if (existingLitige == null)
            {
                throw new ArgumentException("Le litige spécifié n'existe pas.");
            }

            // Vérifier que la facture existe
            var facture = await _factureRepository.GetByIdAsync(litige.FactureId);
            if (facture == null)
            {
                throw new ArgumentException("La facture spécifiée n'existe pas.");
            }

            // Vérifier que la catégorie existe
            var categorie = await _categorieLitigeRepository.GetByIdAsync(litige.CategorieLitigeId);
            if (categorie == null)
            {
                throw new ArgumentException("La catégorie spécifiée n'existe pas.");
            }

            // Vérifier que l'étape existe
            var etape = await _etapeLitigeRepository.GetByIdAsync(litige.EtapeLitigeId);
            if (etape == null)
            {
                throw new ArgumentException("L'étape spécifiée n'existe pas.");
            }

            // Vérifier que le responsable existe
            var responsable = await _utilisateurRepository.GetByIdAsync(litige.ResponsableId);
            if (responsable == null)
            {
                throw new ArgumentException("Le responsable spécifié n'existe pas.");
            }

            // Si l'étape a changé, mettre à jour l'historique
            if (existingLitige.EtapeLitigeId != litige.EtapeLitigeId)
            {
                await _litigeRepository.UpdateEtapeAsync(litige.Id, litige.EtapeLitigeId, modifiePar, "Mise à jour du litige");
            }

            // Mettre à jour les informations du litige
            litige.ModifiePar = modifiePar;
            litige.DateModification = DateTime.Now;

            await _litigeRepository.UpdateAsync(litige, modifiePar);

            return litige;
        }

        /// <summary>
        /// Supprime un litige
        /// </summary>
        /// <param name="id">Identifiant du litige à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui supprime le litige</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            // Vérifier que le litige existe
            var litige = await _litigeRepository.GetByIdAsync(id);
            if (litige == null)
            {
                throw new ArgumentException("Le litige spécifié n'existe pas.");
            }

            // Supprimer le litige (désactivation)
            litige.EstActif = false;
            litige.ModifiePar = supprimePar;
            litige.DateModification = DateTime.Now;

            await _litigeRepository.UpdateAsync(litige, supprimePar);

            return true;
        }

        /// <summary>
        /// Ajoute un commentaire à un litige
        /// </summary>
        /// <param name="commentaire">Commentaire à ajouter</param>
        /// <returns>Commentaire ajouté</returns>
        public async Task<CommentaireLitige> AddCommentaireAsync(CommentaireLitige commentaire)
        {
            // Vérifier que le litige existe
            var litige = await _litigeRepository.GetByIdAsync(commentaire.LitigeId);
            if (litige == null)
            {
                throw new ArgumentException("Le litige spécifié n'existe pas.");
            }

            // Vérifier que l'utilisateur existe
            var utilisateur = await _utilisateurRepository.GetByIdAsync(commentaire.UtilisateurId);
            if (utilisateur == null)
            {
                throw new ArgumentException("L'utilisateur spécifié n'existe pas.");
            }

            // Définir les valeurs par défaut
            commentaire.DateCommentaire = DateTime.Now;

            // Ajouter le commentaire
            return await _litigeRepository.AddCommentaireAsync(commentaire);
        }

        /// <summary>
        /// Récupère tous les commentaires d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des commentaires du litige</returns>
        public async Task<IEnumerable<CommentaireLitige>> GetCommentairesAsync(int litigeId)
        {
            return await _litigeRepository.GetCommentairesAsync(litigeId);
        }

        /// <summary>
        /// Change l'étape d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <param name="etapeId">Identifiant de la nouvelle étape</param>
        /// <param name="commentaire">Commentaire associé au changement d'étape</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si le changement a réussi, sinon False</returns>
        public async Task<bool> ChangeEtapeAsync(int litigeId, int etapeId, string commentaire, int modifiePar)
        {
            // Vérifier que le litige existe
            var litige = await _litigeRepository.GetByIdAsync(litigeId);
            if (litige == null)
            {
                throw new ArgumentException("Le litige spécifié n'existe pas.");
            }

            // Vérifier que l'étape existe
            var etape = await _etapeLitigeRepository.GetByIdAsync(etapeId);
            if (etape == null)
            {
                throw new ArgumentException("L'étape spécifiée n'existe pas.");
            }

            // Vérifier que l'utilisateur existe
            var utilisateur = await _utilisateurRepository.GetByIdAsync(modifiePar);
            if (utilisateur == null)
            {
                throw new ArgumentException("L'utilisateur spécifié n'existe pas.");
            }

            // Mettre à jour l'étape du litige
            var result = await _litigeRepository.UpdateEtapeAsync(litigeId, etapeId, modifiePar, commentaire);

            // Si c'est une étape finale, résoudre le litige
            if (result && etape.EstEtapeFinale)
            {
                await ResoudreLitigeAsync(litigeId, "Résolution automatique suite au passage à l'étape finale", modifiePar);
            }

            return result;
        }

        /// <summary>
        /// Résout un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <param name="solution">Solution appliquée</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la résolution a réussi, sinon False</returns>
        public async Task<bool> ResoudreLitigeAsync(int litigeId, string solution, int modifiePar)
        {
            // Vérifier que le litige existe
            var litige = await _litigeRepository.GetByIdAsync(litigeId);
            if (litige == null)
            {
                throw new ArgumentException("Le litige spécifié n'existe pas.");
            }

            // Vérifier que l'utilisateur existe
            var utilisateur = await _utilisateurRepository.GetByIdAsync(modifiePar);
            if (utilisateur == null)
            {
                throw new ArgumentException("L'utilisateur spécifié n'existe pas.");
            }

            // Mettre à jour le litige
            litige.Statut = "Résolu";
            litige.DateResolution = DateTime.Now;
            litige.Solution = solution;
            litige.ModifiePar = modifiePar;
            litige.DateModification = DateTime.Now;

            await _litigeRepository.UpdateAsync(litige, modifiePar);

            // Ajouter un commentaire
            var commentaire = new CommentaireLitige
            {
                LitigeId = litigeId,
                Contenu = $"Litige résolu : {solution}",
                DateCommentaire = DateTime.Now,
                UtilisateurId = modifiePar,
                EstInterne = false
            };

            await _litigeRepository.AddCommentaireAsync(commentaire);

            return true;
        }

        /// <summary>
        /// Récupère l'historique des étapes d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des historiques d'étapes du litige</returns>
        public async Task<IEnumerable<HistoriqueEtapeLitige>> GetHistoriqueEtapesAsync(int litigeId)
        {
            return await _litigeRepository.GetHistoriqueEtapesAsync(litigeId);
        }

        /// <summary>
        /// Récupère toutes les catégories de litiges
        /// </summary>
        /// <returns>Liste de toutes les catégories de litiges</returns>
        public async Task<IEnumerable<CategorieLitige>> GetAllCategoriesAsync()
        {
            return await _categorieLitigeRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère toutes les étapes de litiges
        /// </summary>
        /// <returns>Liste de toutes les étapes de litiges</returns>
        public async Task<IEnumerable<EtapeLitige>> GetAllEtapesAsync()
        {
            return await _etapeLitigeRepository.GetAllOrderedAsync();
        }

        /// <summary>
        /// Récupère les statistiques des litiges
        /// </summary>
        /// <returns>Statistiques des litiges</returns>
        public async Task<Dictionary<string, object>> GetStatistiquesAsync()
        {
            var stats = new Dictionary<string, object>();

            // Récupérer tous les litiges
            var litiges = await _litigeRepository.GetAllAsync();
            var litigesActifs = litiges.Where(l => l.EstActif).ToList();

            // Nombre total de litiges
            stats["NombreTotal"] = litigesActifs.Count;

            // Nombre de litiges par statut
            stats["ParStatut"] = litigesActifs
                .GroupBy(l => l.Statut)
                .Select(g => new { Statut = g.Key, Nombre = g.Count() })
                .ToDictionary(x => x.Statut, x => x.Nombre);

            // Nombre de litiges par catégorie
            var categories = await _categorieLitigeRepository.GetAllWithCountAsync();
            stats["ParCategorie"] = categories
                .Select(c => new { Categorie = c.Nom, Nombre = c.Litiges?.Count ?? 0 })
                .ToDictionary(x => x.Categorie, x => x.Nombre);

            // Nombre de litiges par étape
            var etapes = await _etapeLitigeRepository.GetAllWithCountAsync();
            stats["ParEtape"] = etapes
                .Select(e => new { Etape = e.Nom, Nombre = e.Litiges?.Count ?? 0 })
                .ToDictionary(x => x.Etape, x => x.Nombre);

            // Nombre de litiges en retard
            var litigesEnRetard = litigesActifs
                .Where(l => l.DateEcheance < DateTime.Now && l.DateResolution == null)
                .ToList();
            stats["NombreEnRetard"] = litigesEnRetard.Count;

            // Temps moyen de résolution (en jours)
            var litigesResolus = litigesActifs
                .Where(l => l.DateResolution != null)
                .ToList();
            if (litigesResolus.Any())
            {
                var tempsResolution = litigesResolus
                    .Select(l => (l.DateResolution.Value - l.DateOuverture).TotalDays)
                    .Average();
                stats["TempsResolutionMoyen"] = Math.Round(tempsResolution, 2);
            }
            else
            {
                stats["TempsResolutionMoyen"] = 0;
            }

            return stats;
        }

        #region Gestion des catégories de litiges

        /// <summary>
        /// Récupère toutes les catégories de litiges avec le nombre de litiges associés
        /// </summary>
        /// <returns>Liste des catégories de litiges avec le nombre de litiges</returns>
        public async Task<IEnumerable<CategorieLitige>> GetAllCategoriesWithCountAsync()
        {
            return await _categorieLitigeRepository.GetAllWithCountAsync();
        }

        /// <summary>
        /// Récupère une catégorie de litige par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la catégorie</param>
        /// <returns>Catégorie de litige trouvée ou null</returns>
        public async Task<CategorieLitige> GetCategorieByIdAsync(int id)
        {
            return await _categorieLitigeRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Crée une nouvelle catégorie de litige
        /// </summary>
        /// <param name="categorie">Catégorie à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui crée la catégorie</param>
        /// <returns>Catégorie créée</returns>
        public async Task<CategorieLitige> CreateCategorieAsync(CategorieLitige categorie, int creePar)
        {
            // Vérifier que le nom n'est pas déjà utilisé
            var existingCategorie = await _categorieLitigeRepository.GetByNomAsync(categorie.Nom);
            if (existingCategorie != null)
            {
                throw new ArgumentException($"Une catégorie avec le nom '{categorie.Nom}' existe déjà.");
            }

            // Définir les valeurs par défaut
            categorie.CreePar = creePar;
            categorie.DateCreation = DateTime.Now;
            categorie.EstActif = true;

            // Créer la catégorie
            return await _categorieLitigeRepository.AddAsync(categorie, creePar);
        }

        /// <summary>
        /// Met à jour une catégorie de litige existante
        /// </summary>
        /// <param name="categorie">Catégorie à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui modifie la catégorie</param>
        /// <returns>Catégorie mise à jour</returns>
        public async Task<CategorieLitige> UpdateCategorieAsync(CategorieLitige categorie, int modifiePar)
        {
            // Vérifier que la catégorie existe
            var existingCategorie = await _categorieLitigeRepository.GetByIdAsync(categorie.Id);
            if (existingCategorie == null)
            {
                throw new ArgumentException("La catégorie spécifiée n'existe pas.");
            }

            // Vérifier que le nom n'est pas déjà utilisé par une autre catégorie
            var categorieWithSameName = await _categorieLitigeRepository.GetByNomAsync(categorie.Nom);
            if (categorieWithSameName != null && categorieWithSameName.Id != categorie.Id)
            {
                throw new ArgumentException($"Une autre catégorie avec le nom '{categorie.Nom}' existe déjà.");
            }

            // Mettre à jour les informations de la catégorie
            categorie.ModifiePar = modifiePar;
            categorie.DateModification = DateTime.Now;

            await _categorieLitigeRepository.UpdateAsync(categorie, modifiePar);

            return categorie;
        }

        /// <summary>
        /// Supprime une catégorie de litige
        /// </summary>
        /// <param name="id">Identifiant de la catégorie à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui supprime la catégorie</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteCategorieAsync(int id, int supprimePar)
        {
            // Vérifier que la catégorie existe
            var categorie = await _categorieLitigeRepository.GetByIdAsync(id);
            if (categorie == null)
            {
                throw new ArgumentException("La catégorie spécifiée n'existe pas.");
            }

            // Vérifier que la catégorie n'est pas utilisée par des litiges
            var litiges = await _litigeRepository.GetByCategorieIdAsync(id);
            if (litiges.Any())
            {
                throw new InvalidOperationException("Cette catégorie est utilisée par des litiges et ne peut pas être supprimée.");
            }

            // Supprimer la catégorie (désactivation)
            categorie.EstActif = false;
            categorie.ModifiePar = supprimePar;
            categorie.DateModification = DateTime.Now;

            await _categorieLitigeRepository.UpdateAsync(categorie, supprimePar);

            return true;
        }

        #endregion

        #region Gestion des étapes de litiges

        /// <summary>
        /// Récupère toutes les étapes de litiges avec le nombre de litiges associés
        /// </summary>
        /// <returns>Liste des étapes de litiges avec le nombre de litiges</returns>
        public async Task<IEnumerable<EtapeLitige>> GetAllEtapesWithCountAsync()
        {
            return await _etapeLitigeRepository.GetAllWithCountAsync();
        }

        /// <summary>
        /// Récupère une étape de litige par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'étape</param>
        /// <returns>Étape de litige trouvée ou null</returns>
        public async Task<EtapeLitige> GetEtapeByIdAsync(int id)
        {
            return await _etapeLitigeRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Crée une nouvelle étape de litige
        /// </summary>
        /// <param name="etape">Étape à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui crée l'étape</param>
        /// <returns>Étape créée</returns>
        public async Task<EtapeLitige> CreateEtapeAsync(EtapeLitige etape, int creePar)
        {
            // Vérifier que le nom n'est pas déjà utilisé
            var existingEtape = await _etapeLitigeRepository.GetByNomAsync(etape.Nom);
            if (existingEtape != null)
            {
                throw new ArgumentException($"Une étape avec le nom '{etape.Nom}' existe déjà.");
            }

            // Vérifier que l'ordre n'est pas déjà utilisé
            var etapes = await _etapeLitigeRepository.GetAllAsync();
            if (etapes.Any(e => e.Ordre == etape.Ordre))
            {
                throw new ArgumentException($"Une étape avec l'ordre {etape.Ordre} existe déjà.");
            }

            // Définir les valeurs par défaut
            etape.CreePar = creePar;
            etape.DateCreation = DateTime.Now;
            etape.EstActif = true;

            // Créer l'étape
            return await _etapeLitigeRepository.AddAsync(etape, creePar);
        }

        /// <summary>
        /// Met à jour une étape de litige existante
        /// </summary>
        /// <param name="etape">Étape à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui modifie l'étape</param>
        /// <returns>Étape mise à jour</returns>
        public async Task<EtapeLitige> UpdateEtapeAsync(EtapeLitige etape, int modifiePar)
        {
            // Vérifier que l'étape existe
            var existingEtape = await _etapeLitigeRepository.GetByIdAsync(etape.Id);
            if (existingEtape == null)
            {
                throw new ArgumentException("L'étape spécifiée n'existe pas.");
            }

            // Vérifier que le nom n'est pas déjà utilisé par une autre étape
            var etapeWithSameName = await _etapeLitigeRepository.GetByNomAsync(etape.Nom);
            if (etapeWithSameName != null && etapeWithSameName.Id != etape.Id)
            {
                throw new ArgumentException($"Une autre étape avec le nom '{etape.Nom}' existe déjà.");
            }

            // Vérifier que l'ordre n'est pas déjà utilisé par une autre étape
            var etapes = await _etapeLitigeRepository.GetAllAsync();
            if (etapes.Any(e => e.Ordre == etape.Ordre && e.Id != etape.Id))
            {
                throw new ArgumentException($"Une autre étape avec l'ordre {etape.Ordre} existe déjà.");
            }

            // Mettre à jour les informations de l'étape
            etape.ModifiePar = modifiePar;
            etape.DateModification = DateTime.Now;

            await _etapeLitigeRepository.UpdateAsync(etape, modifiePar);

            return etape;
        }

        /// <summary>
        /// Supprime une étape de litige
        /// </summary>
        /// <param name="id">Identifiant de l'étape à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui supprime l'étape</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteEtapeAsync(int id, int supprimePar)
        {
            // Vérifier que l'étape existe
            var etape = await _etapeLitigeRepository.GetByIdAsync(id);
            if (etape == null)
            {
                throw new ArgumentException("L'étape spécifiée n'existe pas.");
            }

            // Vérifier que l'étape n'est pas utilisée par des litiges
            var litiges = await _litigeRepository.GetByEtapeIdAsync(id);
            if (litiges.Any())
            {
                throw new InvalidOperationException("Cette étape est utilisée par des litiges et ne peut pas être supprimée.");
            }

            // Supprimer l'étape (désactivation)
            etape.EstActif = false;
            etape.ModifiePar = supprimePar;
            etape.DateModification = DateTime.Now;

            await _etapeLitigeRepository.UpdateAsync(etape, supprimePar);

            return true;
        }

        #endregion

        #region Gestion des délais d'escalade

        /// <summary>
        /// Vérifie les litiges en retard et les escalade si nécessaire
        /// </summary>
        /// <returns>Nombre de litiges escaladés</returns>
        public async Task<int> VerifierEtEscaladerLitigesEnRetardAsync()
        {
            int count = 0;

            try
            {
                // Récupérer tous les litiges actifs qui ne sont pas résolus
                var litiges = (await _litigeRepository.GetAllAsync())
                    .Where(l => l.EstActif && l.Statut != "Résolu")
                    .ToList();

                // Récupérer toutes les étapes
                var etapes = await GetAllEtapesAsync();

                foreach (var litige in litiges)
                {
                    // Récupérer l'étape actuelle du litige
                    var etapeActuelle = etapes.FirstOrDefault(e => e.Id == litige.EtapeLitigeId);
                    if (etapeActuelle == null || !etapeActuelle.EscaladeAutomatique || !etapeActuelle.EtapeEscaladeId.HasValue)
                    {
                        continue; // Passer au litige suivant si l'étape n'existe pas ou n'a pas d'escalade configurée
                    }

                    // Récupérer l'historique des étapes du litige
                    var historiqueEtapes = await _historiqueEtapeLitigeRepository.GetByLitigeIdAsync(litige.Id);

                    // Récupérer la dernière entrée d'historique pour l'étape actuelle
                    var derniereEntree = historiqueEtapes
                        .Where(h => h.EtapeLitigeId == etapeActuelle.Id)
                        .OrderByDescending(h => h.DateChangement)
                        .FirstOrDefault();

                    if (derniereEntree == null)
                    {
                        continue; // Passer au litige suivant si aucune entrée d'historique n'est trouvée
                    }

                    // Calculer le nombre de jours écoulés depuis le dernier changement d'étape
                    int joursEcoules = (DateTime.Now - derniereEntree.DateChangement).Days;

                    // Vérifier si le délai est dépassé
                    if (joursEcoules > etapeActuelle.DelaiJours)
                    {
                        // Escalader le litige
                        bool success = await EscaladerLitigeAsync(
                            litige.Id,
                            $"Escalade automatique après {joursEcoules} jours (délai dépassé de {joursEcoules - etapeActuelle.DelaiJours} jours)",
                            1 // Utilisateur système
                        );

                        if (success)
                        {
                            count++;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la vérification des litiges en retard");
                throw;
            }

            return count;
        }

        /// <summary>
        /// Escalade un litige vers l'étape suivante
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <param name="commentaire">Commentaire associé à l'escalade</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'escalade</param>
        /// <returns>True si l'escalade a réussi, sinon False</returns>
        public async Task<bool> EscaladerLitigeAsync(int litigeId, string commentaire, int utilisateurId)
        {
            try
            {
                // Récupérer le litige
                var litige = await _litigeRepository.GetByIdAsync(litigeId);
                if (litige == null || !litige.EstActif)
                {
                    return false;
                }

                // Récupérer l'étape actuelle
                var etapeActuelle = await _etapeLitigeRepository.GetByIdAsync(litige.EtapeLitigeId);
                if (etapeActuelle == null || !etapeActuelle.EscaladeAutomatique || !etapeActuelle.EtapeEscaladeId.HasValue)
                {
                    return false;
                }

                // Récupérer l'étape d'escalade
                var etapeEscalade = await _etapeLitigeRepository.GetByIdAsync(etapeActuelle.EtapeEscaladeId.Value);
                if (etapeEscalade == null || !etapeEscalade.EstActif)
                {
                    return false;
                }

                // Mettre à jour l'étape du litige
                litige.EtapeLitigeId = etapeEscalade.Id;
                litige.DateModification = DateTime.Now;
                litige.ModifiePar = utilisateurId;

                // Si l'étape d'escalade est une étape finale, marquer le litige comme résolu
                if (etapeEscalade.EstEtapeFinale)
                {
                    litige.Statut = "Résolu";
                    litige.DateResolution = DateTime.Now;
                }
                else
                {
                    litige.Statut = "En cours";
                }

                // Mettre à jour le litige
                await _litigeRepository.UpdateAsync(litige, utilisateurId);

                // Ajouter une entrée dans l'historique des étapes
                var historiqueEtape = new HistoriqueEtapeLitige
                {
                    LitigeId = litige.Id,
                    EtapeLitigeId = etapeEscalade.Id,
                    DateChangement = DateTime.Now,
                    Commentaire = commentaire,
                    UtilisateurId = utilisateurId
                };

                await _historiqueEtapeLitigeRepository.AddAsync(historiqueEtape);

                // Créer une notification pour le responsable du litige
                if (litige.ResponsableId > 0)
                {
                    var notification = new NotificationLitige
                    {
                        LitigeId = litige.Id,
                        UtilisateurId = litige.ResponsableId,
                        Type = "Escalade",
                        Message = $"Le litige #{litige.Id} a été escaladé vers l'étape '{etapeEscalade.Nom}'. Commentaire : {commentaire}",
                        DateNotification = DateTime.Now,
                        EstLue = false
                    };

                    await CreateNotificationAsync(notification);
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'escalade du litige {LitigeId}", litigeId);
                throw;
            }
        }

        #endregion

        #region Gestion des notifications

        /// <summary>
        /// Récupère toutes les notifications pour un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des notifications</returns>
        public async Task<IEnumerable<NotificationLitige>> GetNotificationsByUtilisateurIdAsync(int utilisateurId)
        {
            return await _notificationLitigeRepository.GetByUtilisateurIdAsync(utilisateurId);
        }

        /// <summary>
        /// Récupère les notifications non lues pour un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des notifications non lues</returns>
        public async Task<IEnumerable<NotificationLitige>> GetUnreadNotificationsByUtilisateurIdAsync(int utilisateurId)
        {
            return await _notificationLitigeRepository.GetUnreadByUtilisateurIdAsync(utilisateurId);
        }

        /// <summary>
        /// Récupère les notifications pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des notifications</returns>
        public async Task<IEnumerable<NotificationLitige>> GetNotificationsByLitigeIdAsync(int litigeId)
        {
            return await _notificationLitigeRepository.GetByLitigeIdAsync(litigeId);
        }

        /// <summary>
        /// Crée une notification pour un litige
        /// </summary>
        /// <param name="notification">Notification à créer</param>
        /// <returns>Notification créée</returns>
        public async Task<NotificationLitige> CreateNotificationAsync(NotificationLitige notification)
        {
            return await _notificationLitigeRepository.AddAsync(notification);
        }

        /// <summary>
        /// Marque une notification comme lue
        /// </summary>
        /// <param name="notificationId">Identifiant de la notification</param>
        /// <returns>True si la notification a été marquée comme lue</returns>
        public async Task<bool> MarkNotificationAsReadAsync(int notificationId)
        {
            return await _notificationLitigeRepository.MarkAsReadAsync(notificationId);
        }

        /// <summary>
        /// Marque toutes les notifications d'un utilisateur comme lues
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Nombre de notifications marquées comme lues</returns>
        public async Task<int> MarkAllNotificationsAsReadAsync(int utilisateurId)
        {
            return await _notificationLitigeRepository.MarkAllAsReadAsync(utilisateurId);
        }

        #endregion

        #region Gestion des documents

        /// <summary>
        /// Récupère tous les documents pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des documents</returns>
        public async Task<IEnumerable<DocumentLitige>> GetDocumentsByLitigeIdAsync(int litigeId)
        {
            return await _documentLitigeRepository.GetByLitigeIdAsync(litigeId);
        }

        /// <summary>
        /// Récupère un document par son identifiant
        /// </summary>
        /// <param name="documentId">Identifiant du document</param>
        /// <returns>Document trouvé ou null</returns>
        public async Task<DocumentLitige> GetDocumentByIdAsync(int documentId)
        {
            return await _documentLitigeRepository.GetByIdAsync(documentId);
        }

        /// <summary>
        /// Ajoute un document à un litige
        /// </summary>
        /// <param name="document">Document à ajouter</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui ajoute le document</param>
        /// <returns>Document ajouté</returns>
        public async Task<DocumentLitige> AddDocumentAsync(DocumentLitige document, int creePar)
        {
            return await _documentLitigeRepository.AddAsync(document, creePar);
        }

        /// <summary>
        /// Met à jour un document
        /// </summary>
        /// <param name="document">Document à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui modifie le document</param>
        /// <returns>True si le document a été mis à jour</returns>
        public async Task<bool> UpdateDocumentAsync(DocumentLitige document, int modifiePar)
        {
            return await _documentLitigeRepository.UpdateAsync(document, modifiePar);
        }

        /// <summary>
        /// Supprime un document
        /// </summary>
        /// <param name="documentId">Identifiant du document</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui supprime le document</param>
        /// <returns>True si le document a été supprimé</returns>
        public async Task<bool> DeleteDocumentAsync(int documentId, int supprimePar)
        {
            return await _documentLitigeRepository.DeleteAsync(documentId, supprimePar);
        }

        #endregion
    }
}
