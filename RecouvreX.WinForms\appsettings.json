{"ConnectionStrings": {"DefaultConnection": "Server=.\\SQLEXPRESS;Database=RecouvreX;Trusted_Connection=True;TrustServerCertificate=True;"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 31, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "AppSettings": {"ApplicationName": "RecouvreX", "ApplicationVersion": "1.0.0", "CompanyName": "Votre Entreprise", "DefaultCulture": "fr-FR", "DefaultTheme": "Light", "AutoSaveInterval": 5, "MaxLoginAttempts": 5, "SessionTimeout": 30, "DocumentsPath": "Documents"}, "Email": {"SmtpServer": "smtp.example.com", "SmtpPort": 587, "Username": "<EMAIL>", "Password": "votre_mot_de_passe", "From": "<EMAIL>", "FromName": "RecouvreX", "EnableSsl": true}}