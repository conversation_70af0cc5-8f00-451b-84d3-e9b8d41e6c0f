using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.Relances
{
    public partial class RelanceListForm : Form
    {
        private readonly IRelanceService _relanceService;
        private readonly IFactureService _factureService;
        private readonly IClientService _clientService;
        private readonly IAuthenticationService _authenticationService;
        private readonly int _currentUserId;
        private List<Relance> _relances = new List<Relance>();
        private DataTable _dataTable = new DataTable();
        private Dictionary<int, string> _factureNumeros = new Dictionary<int, string>();
        private Dictionary<int, string> _clientNames = new Dictionary<int, string>();

        public RelanceListForm(IRelanceService relanceService, IFactureService factureService, IClientService clientService,
            IAuthenticationService authenticationService, int currentUserId)
        {
            _relanceService = relanceService ?? throw new ArgumentNullException(nameof(relanceService));
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }



        private async void RelanceListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Vérifier les permissions
                bool canView = await _authenticationService.HasPermissionAsync(_currentUserId, "RELANCES_VIEW");
                bool canAdd = await _authenticationService.HasPermissionAsync(_currentUserId, "RELANCES_ADD");
                bool canGenerate = await _authenticationService.HasPermissionAsync(_currentUserId, "RELANCES_GENERATE");

                if (!canView)
                {
                    MessageBox.Show("Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.",
                        "Accès refusé", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return;
                }

                // Configurer les boutons en fonction des permissions
                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                var addButton = toolStrip?.Items.Find("addButton", false).FirstOrDefault() as ToolStripButton;
                var generateButton = toolStrip?.Items.Find("generateButton", false).FirstOrDefault() as ToolStripButton;

                if (addButton != null) addButton.Enabled = canAdd;
                if (generateButton != null) generateButton.Enabled = canGenerate;

                // Initialiser la table de données
                InitializeDataTable();

                // Charger les relances
                await LoadRelancesAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement de la liste des relances");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des relances : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Facture", typeof(string));
            _dataTable.Columns.Add("Client", typeof(string));
            _dataTable.Columns.Add("Type", typeof(string));
            _dataTable.Columns.Add("Date", typeof(DateTime));
            _dataTable.Columns.Add("Niveau", typeof(int));
            _dataTable.Columns.Add("Statut", typeof(string));
            _dataTable.Columns.Add("Date Prochaine", typeof(DateTime));

            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (dataGridView.Columns["Date"] != null)
                    dataGridView.Columns["Date"].DefaultCellStyle.Format = "dd/MM/yyyy";
                if (dataGridView.Columns["Date Prochaine"] != null)
                    dataGridView.Columns["Date Prochaine"].DefaultCellStyle.Format = "dd/MM/yyyy";
            }
        }

        private async Task LoadRelancesAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer les relances
                _relances = (await _relanceService.GetAllAsync()).ToList();

                // Récupérer les numéros de factures et les noms des clients
                await LoadFactureAndClientDataAsync();

                // Appliquer les filtres
                ApplyFilters();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des relances");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des relances : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private async Task LoadFactureAndClientDataAsync()
        {
            try
            {
                // Récupérer les IDs des factures uniques
                var factureIds = _relances.Select(r => r.FactureId).Distinct().ToList();

                // Récupérer les numéros des factures et les IDs des clients
                _factureNumeros.Clear();
                _clientNames.Clear();
                var clientIds = new HashSet<int>();

                foreach (var factureId in factureIds)
                {
                    var facture = await _factureService.GetByIdAsync(factureId);
                    if (facture != null)
                    {
                        _factureNumeros[factureId] = facture.Numero;
                        clientIds.Add(facture.ClientId);
                    }
                }

                // Récupérer les noms des clients
                foreach (var clientId in clientIds)
                {
                    var client = await _clientService.GetByIdAsync(clientId);
                    if (client != null)
                    {
                        _clientNames[clientId] = client.RaisonSociale;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données des factures et des clients");
                throw;
            }
        }

        private void ApplyFilters()
        {
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var typeComboBox = toolStrip?.Items.Find("typeComboBox", false).FirstOrDefault() as ToolStripComboBox;
            var statutComboBox = toolStrip?.Items.Find("statutComboBox", false).FirstOrDefault() as ToolStripComboBox;

            if (typeComboBox != null && statutComboBox != null)
            {
                string selectedType = typeComboBox.SelectedItem?.ToString() ?? "Tous";
                string selectedStatut = statutComboBox.SelectedItem?.ToString() ?? "Tous";

                // Filtrer les relances par type et statut
                List<Relance> filteredRelances = _relances;

                // Filtre par type
                if (selectedType != "Tous")
                {
                    filteredRelances = filteredRelances.Where(r => r.Type == selectedType).ToList();
                }

                // Filtre par statut
                if (selectedStatut != "Tous")
                {
                    filteredRelances = filteredRelances.Where(r => r.Statut == selectedStatut).ToList();
                }

                // Mettre à jour la table de données
                UpdateDataTable(filteredRelances);

                // Mettre à jour les compteurs
                UpdateCounters(filteredRelances);
            }
        }

        private async void UpdateDataTable(List<Relance> relances)
        {
            _dataTable.Clear();

            foreach (var relance in relances)
            {
                string factureNumero = _factureNumeros.ContainsKey(relance.FactureId) ? _factureNumeros[relance.FactureId] : "Facture inconnue";

                // Récupérer le client via la facture
                string clientName = "Client inconnu";
                var facture = await _factureService.GetByIdAsync(relance.FactureId);
                if (facture != null && _clientNames.ContainsKey(facture.ClientId))
                {
                    clientName = _clientNames[facture.ClientId];
                }

                _dataTable.Rows.Add(
                    relance.Id,
                    factureNumero,
                    clientName,
                    relance.Type,
                    relance.DateRelance,
                    relance.Niveau,
                    relance.Statut,
                    relance.DateProchaineRelance
                );
            }
        }

        private async void UpdateCounters(List<Relance> relances)
        {
            var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
            var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
            var plannedLabel = statusStrip?.Items.Find("plannedLabel", false).FirstOrDefault() as ToolStripStatusLabel;

            if (countLabel != null)
            {
                countLabel.Text = $"Nombre de relances : {relances.Count}";
            }

            if (plannedLabel != null)
            {
                // Récupérer le nombre de relances planifiées pour aujourd'hui
                var plannedRelances = await _relanceService.GetPlannedForDateAsync(DateTime.Today);
                plannedLabel.Text = $"Relances planifiées aujourd'hui : {plannedRelances.Count()}";
            }
        }

        private void TypeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void StatutComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            // Recharger les relances
            await LoadRelancesAsync();
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir le formulaire d'ajout de relance
                using (var form = new RelanceEditForm(_relanceService, _factureService, _clientService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les relances
                        LoadRelancesAsync().Wait();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewButton_Click(object sender, EventArgs e)
        {
            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
            {
                int relanceId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                ViewRelance(relanceId);
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une relance à consulter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int relanceId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    ViewRelance(relanceId);
                }
            }
        }

        private void ViewRelance(int relanceId)
        {
            try
            {
                // Ouvrir le formulaire de détails de relance
                using (var form = new RelanceDetailsForm(_relanceService, _factureService, _clientService, _currentUserId, relanceId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de détails de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void GenerateButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Demander confirmation
                var result = MessageBox.Show(
                    "Voulez-vous générer automatiquement des relances pour les factures en retard ?\n\n" +
                    "Cette opération peut prendre un certain temps.",
                    "Confirmation",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Afficher un indicateur de chargement
                    Cursor.Current = Cursors.WaitCursor;

                    // Générer les relances
                    int count = await _relanceService.GenererRelancesAutomatiquesAsync(_currentUserId);

                    // Afficher le résultat
                    MessageBox.Show($"{count} relance(s) ont été générées avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Recharger les relances
                    await LoadRelancesAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération automatique des relances");
                MessageBox.Show($"Une erreur s'est produite lors de la génération des relances : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }
    }
}
