using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;

namespace RecouvreX.WinForms.Forms.Clients
{
    partial class ScoreRisqueClientListForm
    {
        /// <summary>
        /// Variable nécessaire au concepteur.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private Panel filterPanel;
        private Label categorieLabel;
        private ComboBox categorieComboBox;
        private Label searchLabel;
        private TextBox searchTextBox;
        private Button searchButton;
        private Button updateButton;
        private SplitContainer splitContainer;
        private Panel gridPanel;
        private DataGridView scoresDataGridView;
        private TabControl tabControl;
        private TabPage distributionTabPage;
        private Chart distributionChart;
        private TabPage scoresTabPage;
        private Chart scoresChart;

        /// <summary>
        /// Nettoyage des ressources utilisées.
        /// </summary>
        /// <param name="disposing">true si les ressources managées doivent être supprimées ; sinon, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Code généré par le Concepteur Windows Form

        /// <summary>
        /// Méthode requise pour la prise en charge du concepteur - ne modifiez pas
        /// le contenu de cette méthode avec l'éditeur de code.
        /// </summary>
        private void InitializeComponent()
        {
            this.mainPanel = new Panel();
            this.filterPanel = new Panel();
            this.categorieLabel = new Label();
            this.categorieComboBox = new ComboBox();
            this.searchLabel = new Label();
            this.searchTextBox = new TextBox();
            this.searchButton = new Button();
            this.updateButton = new Button();
            this.splitContainer = new SplitContainer();
            this.gridPanel = new Panel();
            this.scoresDataGridView = new DataGridView();
            this.tabControl = new TabControl();
            this.distributionTabPage = new TabPage();
            this.distributionChart = new Chart();
            this.scoresTabPage = new TabPage();
            this.scoresChart = new Chart();

            this.mainPanel.SuspendLayout();
            this.filterPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).BeginInit();
            this.splitContainer.Panel1.SuspendLayout();
            this.splitContainer.Panel2.SuspendLayout();
            this.splitContainer.SuspendLayout();
            this.gridPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.scoresDataGridView)).BeginInit();
            this.tabControl.SuspendLayout();
            this.distributionTabPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.distributionChart)).BeginInit();
            this.scoresTabPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.scoresChart)).BeginInit();
            this.SuspendLayout();

            // 
            // mainPanel
            // 
            this.mainPanel.Dock = DockStyle.Fill;
            this.mainPanel.Padding = new Padding(10);
            this.mainPanel.Controls.Add(this.splitContainer);
            this.mainPanel.Controls.Add(this.filterPanel);
            
            // 
            // filterPanel
            // 
            this.filterPanel.Dock = DockStyle.Top;
            this.filterPanel.Height = 50;
            this.filterPanel.Padding = new Padding(5);
            this.filterPanel.Controls.Add(this.categorieLabel);
            this.filterPanel.Controls.Add(this.categorieComboBox);
            this.filterPanel.Controls.Add(this.searchLabel);
            this.filterPanel.Controls.Add(this.searchTextBox);
            this.filterPanel.Controls.Add(this.searchButton);
            this.filterPanel.Controls.Add(this.updateButton);
            
            // 
            // categorieLabel
            // 
            this.categorieLabel.Text = "Catégorie :";
            this.categorieLabel.AutoSize = true;
            this.categorieLabel.Location = new Point(10, 15);
            
            // 
            // categorieComboBox
            // 
            this.categorieComboBox.Name = "categorieComboBox";
            this.categorieComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.categorieComboBox.Location = new Point(80, 12);
            this.categorieComboBox.Width = 150;
            this.categorieComboBox.Items.AddRange(new object[] { "Toutes", "A", "B", "C", "D", "E" });
            this.categorieComboBox.SelectedIndex = 0;
            this.categorieComboBox.SelectedIndexChanged += new EventHandler(this.CategorieComboBox_SelectedIndexChanged);
            
            // 
            // searchLabel
            // 
            this.searchLabel.Text = "Recherche :";
            this.searchLabel.AutoSize = true;
            this.searchLabel.Location = new Point(250, 15);
            
            // 
            // searchTextBox
            // 
            this.searchTextBox.Name = "searchTextBox";
            this.searchTextBox.Location = new Point(320, 12);
            this.searchTextBox.Width = 200;
            this.searchTextBox.KeyDown += new KeyEventHandler(this.SearchTextBox_KeyDown);
            
            // 
            // searchButton
            // 
            this.searchButton.Text = "Rechercher";
            this.searchButton.Location = new Point(530, 10);
            this.searchButton.Width = 100;
            this.searchButton.Height = 30;
            this.searchButton.Click += new EventHandler(this.SearchButton_Click);
            
            // 
            // updateButton
            // 
            this.updateButton.Name = "updateButton";
            this.updateButton.Text = "Mettre à jour les scores";
            this.updateButton.Location = new Point(650, 10);
            this.updateButton.Width = 150;
            this.updateButton.Height = 30;
            this.updateButton.Click += new EventHandler(this.UpdateButton_Click);
            
            // 
            // splitContainer
            // 
            this.splitContainer.Dock = DockStyle.Fill;
            this.splitContainer.Orientation = Orientation.Vertical;
            this.splitContainer.SplitterDistance = 300;
            
            // 
            // gridPanel
            // 
            this.gridPanel.Dock = DockStyle.Fill;
            this.gridPanel.Padding = new Padding(0, 10, 0, 10);
            this.gridPanel.Controls.Add(this.scoresDataGridView);
            this.splitContainer.Panel1.Controls.Add(this.gridPanel);
            
            // 
            // scoresDataGridView
            // 
            this.scoresDataGridView.Name = "scoresDataGridView";
            this.scoresDataGridView.Dock = DockStyle.Fill;
            this.scoresDataGridView.AllowUserToAddRows = false;
            this.scoresDataGridView.AllowUserToDeleteRows = false;
            this.scoresDataGridView.AllowUserToResizeRows = false;
            this.scoresDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.scoresDataGridView.ReadOnly = true;
            this.scoresDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.scoresDataGridView.RowHeadersVisible = false;
            this.scoresDataGridView.MultiSelect = false;
            this.scoresDataGridView.CellFormatting += new DataGridViewCellFormattingEventHandler(this.ScoresDataGridView_CellFormatting);
            
            // 
            // tabControl
            // 
            this.tabControl.Dock = DockStyle.Fill;
            this.tabControl.Controls.Add(this.distributionTabPage);
            this.tabControl.Controls.Add(this.scoresTabPage);
            this.splitContainer.Panel2.Controls.Add(this.tabControl);
            
            // 
            // distributionTabPage
            // 
            this.distributionTabPage.Text = "Distribution des catégories";
            this.distributionTabPage.Controls.Add(this.distributionChart);
            
            // 
            // distributionChart
            // 
            this.distributionChart.Name = "distributionChart";
            this.distributionChart.Dock = DockStyle.Fill;
            this.distributionChart.Palette = ChartColorPalette.BrightPastel;
            this.distributionChart.Titles.Add("Distribution des clients par catégorie de risque");
            this.distributionChart.ChartAreas.Add(new ChartArea("Main"));
            this.distributionChart.Series.Add(new Series("Distribution"));
            this.distributionChart.Series["Distribution"].ChartType = SeriesChartType.Pie;
            this.distributionChart.Series["Distribution"].IsValueShownAsLabel = true;
            this.distributionChart.Series["Distribution"].LabelFormat = "{0} ({1:P0})";
            
            // 
            // scoresTabPage
            // 
            this.scoresTabPage.Text = "Répartition des scores";
            this.scoresTabPage.Controls.Add(this.scoresChart);
            
            // 
            // scoresChart
            // 
            this.scoresChart.Name = "scoresChart";
            this.scoresChart.Dock = DockStyle.Fill;
            this.scoresChart.Palette = ChartColorPalette.BrightPastel;
            this.scoresChart.Titles.Add("Répartition des scores de risque");
            this.scoresChart.ChartAreas.Add(new ChartArea("Main"));
            this.scoresChart.Series.Add(new Series("Scores"));
            this.scoresChart.Series["Scores"].ChartType = SeriesChartType.Column;
            this.scoresChart.Series["Scores"].IsValueShownAsLabel = true;
            this.scoresChart.ChartAreas["Main"].AxisX.Title = "Score de risque";
            this.scoresChart.ChartAreas["Main"].AxisY.Title = "Nombre de clients";
            
            // 
            // ScoreRisqueClientListForm
            // 
            this.ClientSize = new System.Drawing.Size(1000, 600);
            this.Controls.Add(this.mainPanel);
            this.Name = "ScoreRisqueClientListForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Scores de risque client";
            this.Load += new System.EventHandler(this.ScoreRisqueClientListForm_Load);
            
            this.mainPanel.ResumeLayout(false);
            this.filterPanel.ResumeLayout(false);
            this.filterPanel.PerformLayout();
            this.splitContainer.Panel1.ResumeLayout(false);
            this.splitContainer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).EndInit();
            this.splitContainer.ResumeLayout(false);
            this.gridPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.scoresDataGridView)).EndInit();
            this.tabControl.ResumeLayout(false);
            this.distributionTabPage.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.distributionChart)).EndInit();
            this.scoresTabPage.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.scoresChart)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion
    }
}
