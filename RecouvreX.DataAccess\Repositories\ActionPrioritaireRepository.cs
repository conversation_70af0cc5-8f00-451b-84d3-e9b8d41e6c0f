using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les actions prioritaires
    /// </summary>
    public class ActionPrioritaireRepository : BaseRepository<ActionPrioritaire>, IActionPrioritaireRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public ActionPrioritaireRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "ActionsPrioritaires")
        {
        }

        /// <summary>
        /// Récupère les actions prioritaires d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des actions prioritaires du client</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetByClientIdAsync(int clientId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT a.*, c.*, f.*, u.*
                    FROM ActionsPrioritaires a
                    LEFT JOIN Clients c ON a.ClientId = c.Id
                    LEFT JOIN Factures f ON a.FactureId = f.Id
                    LEFT JOIN Utilisateurs u ON a.UtilisateurAssigneId = u.Id
                    WHERE a.ClientId = @ClientId AND a.EstActif = 1
                    ORDER BY a.NiveauPriorite DESC, a.DateEcheance ASC";

                var actionsDictionary = new Dictionary<int, ActionPrioritaire>();

                var actions = await connection.QueryAsync<ActionPrioritaire, Client, Facture, Utilisateur, ActionPrioritaire>(
                    query,
                    (action, client, facture, utilisateur) =>
                    {
                        if (!actionsDictionary.TryGetValue(action.Id, out var existingAction))
                        {
                            existingAction = action;
                            existingAction.Client = client;
                            existingAction.Facture = facture;
                            existingAction.UtilisateurAssigne = utilisateur;
                            actionsDictionary.Add(existingAction.Id, existingAction);
                        }

                        return existingAction;
                    },
                    new { ClientId = clientId },
                    splitOn: "Id,Id,Id");

                return actionsDictionary.Values;
            }
        }

        /// <summary>
        /// Récupère les actions prioritaires liées à une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des actions prioritaires liées à la facture</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetByFactureIdAsync(int factureId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT a.*, c.*, f.*, u.*
                    FROM ActionsPrioritaires a
                    LEFT JOIN Clients c ON a.ClientId = c.Id
                    LEFT JOIN Factures f ON a.FactureId = f.Id
                    LEFT JOIN Utilisateurs u ON a.UtilisateurAssigneId = u.Id
                    WHERE a.FactureId = @FactureId AND a.EstActif = 1
                    ORDER BY a.NiveauPriorite DESC, a.DateEcheance ASC";

                var actionsDictionary = new Dictionary<int, ActionPrioritaire>();

                var actions = await connection.QueryAsync<ActionPrioritaire, Client, Facture, Utilisateur, ActionPrioritaire>(
                    query,
                    (action, client, facture, utilisateur) =>
                    {
                        if (!actionsDictionary.TryGetValue(action.Id, out var existingAction))
                        {
                            existingAction = action;
                            existingAction.Client = client;
                            existingAction.Facture = facture;
                            existingAction.UtilisateurAssigne = utilisateur;
                            actionsDictionary.Add(existingAction.Id, existingAction);
                        }

                        return existingAction;
                    },
                    new { FactureId = factureId },
                    splitOn: "Id,Id,Id");

                return actionsDictionary.Values;
            }
        }

        /// <summary>
        /// Récupère les actions prioritaires assignées à un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des actions prioritaires assignées à l'utilisateur</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetByUtilisateurAssigneIdAsync(int utilisateurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT a.*, c.*, f.*, u.*
                    FROM ActionsPrioritaires a
                    LEFT JOIN Clients c ON a.ClientId = c.Id
                    LEFT JOIN Factures f ON a.FactureId = f.Id
                    LEFT JOIN Utilisateurs u ON a.UtilisateurAssigneId = u.Id
                    WHERE a.UtilisateurAssigneId = @UtilisateurId AND a.EstActif = 1 AND a.EstCompletee = 0
                    ORDER BY a.NiveauPriorite DESC, a.DateEcheance ASC";

                var actionsDictionary = new Dictionary<int, ActionPrioritaire>();

                var actions = await connection.QueryAsync<ActionPrioritaire, Client, Facture, Utilisateur, ActionPrioritaire>(
                    query,
                    (action, client, facture, utilisateur) =>
                    {
                        if (!actionsDictionary.TryGetValue(action.Id, out var existingAction))
                        {
                            existingAction = action;
                            existingAction.Client = client;
                            existingAction.Facture = facture;
                            existingAction.UtilisateurAssigne = utilisateur;
                            actionsDictionary.Add(existingAction.Id, existingAction);
                        }

                        return existingAction;
                    },
                    new { UtilisateurId = utilisateurId },
                    splitOn: "Id,Id,Id");

                return actionsDictionary.Values;
            }
        }

        /// <summary>
        /// Récupère les actions prioritaires par niveau de priorité
        /// </summary>
        /// <param name="niveauPriorite">Niveau de priorité</param>
        /// <returns>Liste des actions prioritaires du niveau spécifié</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetByNiveauPrioriteAsync(NiveauPriorite niveauPriorite)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT a.*, c.*, f.*, u.*
                    FROM ActionsPrioritaires a
                    LEFT JOIN Clients c ON a.ClientId = c.Id
                    LEFT JOIN Factures f ON a.FactureId = f.Id
                    LEFT JOIN Utilisateurs u ON a.UtilisateurAssigneId = u.Id
                    WHERE a.NiveauPriorite = @NiveauPriorite AND a.EstActif = 1 AND a.EstCompletee = 0
                    ORDER BY a.DateEcheance ASC";

                var actionsDictionary = new Dictionary<int, ActionPrioritaire>();

                var actions = await connection.QueryAsync<ActionPrioritaire, Client, Facture, Utilisateur, ActionPrioritaire>(
                    query,
                    (action, client, facture, utilisateur) =>
                    {
                        if (!actionsDictionary.TryGetValue(action.Id, out var existingAction))
                        {
                            existingAction = action;
                            existingAction.Client = client;
                            existingAction.Facture = facture;
                            existingAction.UtilisateurAssigne = utilisateur;
                            actionsDictionary.Add(existingAction.Id, existingAction);
                        }

                        return existingAction;
                    },
                    new { NiveauPriorite = (int)niveauPriorite },
                    splitOn: "Id,Id,Id");

                return actionsDictionary.Values;
            }
        }

        /// <summary>
        /// Récupère les actions prioritaires par type d'action
        /// </summary>
        /// <param name="typeAction">Type d'action</param>
        /// <returns>Liste des actions prioritaires du type spécifié</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetByTypeActionAsync(TypeActionPrioritaire typeAction)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT a.*, c.*, f.*, u.*
                    FROM ActionsPrioritaires a
                    LEFT JOIN Clients c ON a.ClientId = c.Id
                    LEFT JOIN Factures f ON a.FactureId = f.Id
                    LEFT JOIN Utilisateurs u ON a.UtilisateurAssigneId = u.Id
                    WHERE a.TypeAction = @TypeAction AND a.EstActif = 1 AND a.EstCompletee = 0
                    ORDER BY a.NiveauPriorite DESC, a.DateEcheance ASC";

                var actionsDictionary = new Dictionary<int, ActionPrioritaire>();

                var actions = await connection.QueryAsync<ActionPrioritaire, Client, Facture, Utilisateur, ActionPrioritaire>(
                    query,
                    (action, client, facture, utilisateur) =>
                    {
                        if (!actionsDictionary.TryGetValue(action.Id, out var existingAction))
                        {
                            existingAction = action;
                            existingAction.Client = client;
                            existingAction.Facture = facture;
                            existingAction.UtilisateurAssigne = utilisateur;
                            actionsDictionary.Add(existingAction.Id, existingAction);
                        }

                        return existingAction;
                    },
                    new { TypeAction = (int)typeAction },
                    splitOn: "Id,Id,Id");

                return actionsDictionary.Values;
            }
        }

        /// <summary>
        /// Récupère les actions prioritaires à échéance dans un nombre de jours spécifié
        /// </summary>
        /// <param name="jours">Nombre de jours</param>
        /// <returns>Liste des actions prioritaires à échéance dans le nombre de jours spécifié</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetByEcheanceAsync(int jours)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var dateEcheance = DateTime.Now.AddDays(jours);
                
                var query = @"
                    SELECT a.*, c.*, f.*, u.*
                    FROM ActionsPrioritaires a
                    LEFT JOIN Clients c ON a.ClientId = c.Id
                    LEFT JOIN Factures f ON a.FactureId = f.Id
                    LEFT JOIN Utilisateurs u ON a.UtilisateurAssigneId = u.Id
                    WHERE a.DateEcheance <= @DateEcheance AND a.EstActif = 1 AND a.EstCompletee = 0
                    ORDER BY a.NiveauPriorite DESC, a.DateEcheance ASC";

                var actionsDictionary = new Dictionary<int, ActionPrioritaire>();

                var actions = await connection.QueryAsync<ActionPrioritaire, Client, Facture, Utilisateur, ActionPrioritaire>(
                    query,
                    (action, client, facture, utilisateur) =>
                    {
                        if (!actionsDictionary.TryGetValue(action.Id, out var existingAction))
                        {
                            existingAction = action;
                            existingAction.Client = client;
                            existingAction.Facture = facture;
                            existingAction.UtilisateurAssigne = utilisateur;
                            actionsDictionary.Add(existingAction.Id, existingAction);
                        }

                        return existingAction;
                    },
                    new { DateEcheance = dateEcheance },
                    splitOn: "Id,Id,Id");

                return actionsDictionary.Values;
            }
        }

        /// <summary>
        /// Récupère les actions prioritaires en retard (échéance dépassée et non complétées)
        /// </summary>
        /// <returns>Liste des actions prioritaires en retard</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetEnRetardAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var dateActuelle = DateTime.Now;
                
                var query = @"
                    SELECT a.*, c.*, f.*, u.*
                    FROM ActionsPrioritaires a
                    LEFT JOIN Clients c ON a.ClientId = c.Id
                    LEFT JOIN Factures f ON a.FactureId = f.Id
                    LEFT JOIN Utilisateurs u ON a.UtilisateurAssigneId = u.Id
                    WHERE a.DateEcheance < @DateActuelle AND a.EstActif = 1 AND a.EstCompletee = 0
                    ORDER BY a.NiveauPriorite DESC, a.DateEcheance ASC";

                var actionsDictionary = new Dictionary<int, ActionPrioritaire>();

                var actions = await connection.QueryAsync<ActionPrioritaire, Client, Facture, Utilisateur, ActionPrioritaire>(
                    query,
                    (action, client, facture, utilisateur) =>
                    {
                        if (!actionsDictionary.TryGetValue(action.Id, out var existingAction))
                        {
                            existingAction = action;
                            existingAction.Client = client;
                            existingAction.Facture = facture;
                            existingAction.UtilisateurAssigne = utilisateur;
                            actionsDictionary.Add(existingAction.Id, existingAction);
                        }

                        return existingAction;
                    },
                    new { DateActuelle = dateActuelle },
                    splitOn: "Id,Id,Id");

                return actionsDictionary.Values;
            }
        }

        /// <summary>
        /// Marque une action prioritaire comme complétée
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire</param>
        /// <param name="commentaire">Commentaire sur la complétion</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire mise à jour</returns>
        public async Task<ActionPrioritaire> MarquerCompleteAsync(int id, string commentaire, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE ActionsPrioritaires
                    SET EstCompletee = 1,
                        DateCompletion = @DateCompletion,
                        CommentaireCompletion = @CommentaireCompletion,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id AND EstActif = 1;
                    
                    SELECT a.*, c.*, f.*, u.*
                    FROM ActionsPrioritaires a
                    LEFT JOIN Clients c ON a.ClientId = c.Id
                    LEFT JOIN Factures f ON a.FactureId = f.Id
                    LEFT JOIN Utilisateurs u ON a.UtilisateurAssigneId = u.Id
                    WHERE a.Id = @Id";

                var parameters = new
                {
                    Id = id,
                    DateCompletion = DateTime.Now,
                    CommentaireCompletion = commentaire,
                    DateModification = DateTime.Now,
                    ModifiePar = modifiePar
                };

                var actionPrioritaire = await connection.QueryAsync<ActionPrioritaire, Client, Facture, Utilisateur, ActionPrioritaire>(
                    query,
                    (action, client, facture, utilisateur) =>
                    {
                        action.Client = client;
                        action.Facture = facture;
                        action.UtilisateurAssigne = utilisateur;
                        return action;
                    },
                    parameters,
                    splitOn: "Id,Id,Id");

                return actionPrioritaire.FirstOrDefault();
            }
        }

        /// <summary>
        /// Assigne une action prioritaire à un utilisateur
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire mise à jour</returns>
        public async Task<ActionPrioritaire> AssignerAsync(int id, int utilisateurId, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE ActionsPrioritaires
                    SET UtilisateurAssigneId = @UtilisateurId,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id AND EstActif = 1;
                    
                    SELECT a.*, c.*, f.*, u.*
                    FROM ActionsPrioritaires a
                    LEFT JOIN Clients c ON a.ClientId = c.Id
                    LEFT JOIN Factures f ON a.FactureId = f.Id
                    LEFT JOIN Utilisateurs u ON a.UtilisateurAssigneId = u.Id
                    WHERE a.Id = @Id";

                var parameters = new
                {
                    Id = id,
                    UtilisateurId = utilisateurId,
                    DateModification = DateTime.Now,
                    ModifiePar = modifiePar
                };

                var actionPrioritaire = await connection.QueryAsync<ActionPrioritaire, Client, Facture, Utilisateur, ActionPrioritaire>(
                    query,
                    (action, client, facture, utilisateur) =>
                    {
                        action.Client = client;
                        action.Facture = facture;
                        action.UtilisateurAssigne = utilisateur;
                        return action;
                    },
                    parameters,
                    splitOn: "Id,Id,Id");

                return actionPrioritaire.FirstOrDefault();
            }
        }

        /// <summary>
        /// Récupère le nombre d'actions prioritaires par niveau de priorité
        /// </summary>
        /// <returns>Dictionnaire avec le niveau de priorité comme clé et le nombre d'actions comme valeur</returns>
        public async Task<Dictionary<NiveauPriorite, int>> GetCountByNiveauPrioriteAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT NiveauPriorite, COUNT(*) AS Count
                    FROM ActionsPrioritaires
                    WHERE EstActif = 1 AND EstCompletee = 0
                    GROUP BY NiveauPriorite
                    ORDER BY NiveauPriorite";

                var results = await connection.QueryAsync<(int NiveauPriorite, int Count)>(query);
                
                return results.ToDictionary(
                    r => (NiveauPriorite)r.NiveauPriorite, 
                    r => r.Count);
            }
        }
    }
}
