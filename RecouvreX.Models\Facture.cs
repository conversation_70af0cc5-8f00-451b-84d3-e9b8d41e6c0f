using System;
using System.Collections.Generic;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une facture dans le système
    /// </summary>
    public class Facture : BaseEntity
    {
        /// <summary>
        /// Numéro de la facture
        /// </summary>
        public string Numero { get; set; } = string.Empty;

        /// <summary>
        /// Identifiant du client associé
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// Client associé (navigation property)
        /// </summary>
        public Client Client { get; set; } = null!;

        /// <summary>
        /// Date d'émission de la facture
        /// </summary>
        public DateTime DateEmission { get; set; }

        /// <summary>
        /// Date d'échéance de la facture
        /// </summary>
        public DateTime DateEcheance { get; set; }

        /// <summary>
        /// Montant total hors taxes
        /// </summary>
        public decimal MontantHT { get; set; }

        /// <summary>
        /// Montant de la TVA
        /// </summary>
        public decimal MontantTVA { get; set; }

        /// <summary>
        /// Montant total TTC
        /// </summary>
        public decimal MontantTTC { get; set; }

        /// <summary>
        /// Montant déjà payé
        /// </summary>
        public decimal MontantPaye { get; set; }

        /// <summary>
        /// Montant restant à payer
        /// </summary>
        public decimal MontantRestant { get; set; }

        /// <summary>
        /// Montant restant dû (alias pour MontantRestant)
        /// </summary>
        public decimal MontantRestantDu => MontantRestant;

        /// <summary>
        /// Montant total de la facture (alias pour MontantTTC)
        /// </summary>
        public decimal MontantTotal => MontantTTC;

        /// <summary>
        /// Statut de la facture (En attente, Payée partiellement, Payée, En retard, etc.)
        /// </summary>
        public string Statut { get; set; } = string.Empty;

        /// <summary>
        /// Identifiant du commercial responsable
        /// </summary>
        public int? CommercialId { get; set; }

        /// <summary>
        /// Commercial responsable (navigation property)
        /// </summary>
        public Utilisateur Commercial { get; set; } = null!;

        /// <summary>
        /// Identifiant du livreur
        /// </summary>
        public int? LivreurId { get; set; }

        /// <summary>
        /// Livreur (navigation property)
        /// </summary>
        public Utilisateur Livreur { get; set; } = null!;

        /// <summary>
        /// Notes ou commentaires sur la facture
        /// </summary>
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// Liste des paiements associés à cette facture (navigation property)
        /// </summary>
        public ICollection<FacturePaiement> FacturePaiements { get; set; } = new List<FacturePaiement>();

        /// <summary>
        /// Liste des relances associées à cette facture (navigation property)
        /// </summary>
        public ICollection<Relance> Relances { get; set; } = new List<Relance>();

        /// <summary>
        /// Liste des documents associés à cette facture (navigation property)
        /// </summary>
        public ICollection<Document> Documents { get; set; } = new List<Document>();
    }
}
