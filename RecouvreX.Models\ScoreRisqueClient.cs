using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente le score de risque d'un client
    /// </summary>
    public class ScoreRisqueClient : BaseEntity
    {
        /// <summary>
        /// Identifiant du client
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// Client associé (navigation property)
        /// </summary>
        public Client Client { get; set; }

        /// <summary>
        /// Score de risque (0-100, où 0 = risque minimal, 100 = risque maximal)
        /// </summary>
        public int Score { get; set; }

        /// <summary>
        /// Catégorie de risque (A, B, C, D, E)
        /// A = 0-20 (Très faible risque)
        /// B = 21-40 (Faible risque)
        /// C = 41-60 (Risque moyen)
        /// D = 61-80 (Risque élevé)
        /// E = 81-100 (Risque très élevé)
        /// </summary>
        public string Categorie { get; set; }

        /// <summary>
        /// Date de la dernière mise à jour du score
        /// </summary>
        public DateTime DateMiseAJour { get; set; }

        /// <summary>
        /// Facteurs de risque (stockés au format JSON)
        /// </summary>
        public string FacteursRisque { get; set; }

        /// <summary>
        /// Délai moyen de paiement (en jours)
        /// </summary>
        public double DelaiMoyenPaiement { get; set; }

        /// <summary>
        /// Pourcentage de factures payées en retard
        /// </summary>
        public double PourcentageFacturesRetard { get; set; }

        /// <summary>
        /// Montant total des factures en retard
        /// </summary>
        public decimal MontantTotalRetard { get; set; }

        /// <summary>
        /// Nombre de relances nécessaires en moyenne
        /// </summary>
        public double NombreMoyenRelances { get; set; }

        /// <summary>
        /// Obtient la catégorie de risque en fonction du score
        /// </summary>
        /// <param name="score">Score de risque (0-100)</param>
        /// <returns>Catégorie de risque (A, B, C, D, E)</returns>
        public static string GetCategorie(int score)
        {
            if (score < 0 || score > 100)
                throw new ArgumentOutOfRangeException(nameof(score), "Le score doit être compris entre 0 et 100");

            if (score <= 20) return "A";
            if (score <= 40) return "B";
            if (score <= 60) return "C";
            if (score <= 80) return "D";
            return "E";
        }

        /// <summary>
        /// Obtient la description de la catégorie de risque
        /// </summary>
        /// <param name="categorie">Catégorie de risque (A, B, C, D, E)</param>
        /// <returns>Description de la catégorie</returns>
        public static string GetDescriptionCategorie(string categorie)
        {
            switch (categorie)
            {
                case "A": return "Très faible risque";
                case "B": return "Faible risque";
                case "C": return "Risque moyen";
                case "D": return "Risque élevé";
                case "E": return "Risque très élevé";
                default: return "Catégorie inconnue";
            }
        }

        /// <summary>
        /// Obtient la couleur associée à la catégorie de risque
        /// </summary>
        /// <param name="categorie">Catégorie de risque (A, B, C, D, E)</param>
        /// <returns>Code couleur hexadécimal</returns>
        public static string GetCouleurCategorie(string categorie)
        {
            switch (categorie)
            {
                case "A": return "#4CAF50"; // Vert
                case "B": return "#8BC34A"; // Vert clair
                case "C": return "#FFC107"; // Jaune
                case "D": return "#FF9800"; // Orange
                case "E": return "#F44336"; // Rouge
                default: return "#9E9E9E"; // Gris
            }
        }
    }
}
