using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.ComponentModel;
using System.Data;

namespace RecouvreX.WinForms.Forms.PlansPaiement
{
    public partial class PlanPaiementEditForm : Form
    {
        private readonly IPlanPaiementService _planPaiementService;
        private readonly IClientService _clientService;
        private readonly IUtilisateurService _utilisateurService;
        private readonly IAuthenticationService _authenticationService;
        private readonly int _currentUserId;
        private readonly PlanPaiement? _planPaiement;
        private readonly bool _isEditMode;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();
        private List<Client> _clients = new List<Client>();
        private List<Utilisateur> _utilisateurs = new List<Utilisateur>();
        private List<Facture> _factures = new List<Facture>();
        private List<Facture> _selectedFactures = new List<Facture>();
        private DataTable _facturesDataTable = new DataTable();
        private DataTable _selectedFacturesDataTable = new DataTable();
        private DataTable _echeancesDataTable = new DataTable();

        public PlanPaiementEditForm(
            IPlanPaiementService planPaiementService,
            IClientService clientService,
            IUtilisateurService utilisateurService,
            IAuthenticationService authenticationService,
            PlanPaiement? planPaiement)
        {
            _planPaiementService = planPaiementService ?? throw new ArgumentNullException(nameof(planPaiementService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _currentUserId = 1; // Valeur par défaut, à remplacer par la méthode appropriée
            _planPaiement = planPaiement;
            _isEditMode = planPaiement != null;

            InitializeComponent();
            InitializeDataTables();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form
            this.Text = _isEditMode ? "Modifier le plan de paiement" : "Nouveau plan de paiement";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimizeBox = true;
            this.MaximizeBox = true;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            // Icône de l'application
            // this.Icon = Properties.Resources.AppIcon;

            // Main panel
            TableLayoutPanel mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 1,
                RowCount = 3,
                RowStyles = {
                    new RowStyle(SizeType.Absolute, 40),
                    new RowStyle(SizeType.Absolute, 220),
                    new RowStyle(SizeType.Percent, 100)
                }
            };
            this.Controls.Add(mainPanel);

            // Title label
            Label titleLabel = new Label
            {
                Text = _isEditMode ? "Modifier le plan de paiement" : "Nouveau plan de paiement",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft
            };
            mainPanel.Controls.Add(titleLabel, 0, 0);

            // Info panel
            TableLayoutPanel infoPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 5,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 15),
                    new ColumnStyle(SizeType.Percent, 35),
                    new ColumnStyle(SizeType.Percent, 15),
                    new ColumnStyle(SizeType.Percent, 35)
                }
            };
            mainPanel.Controls.Add(infoPanel, 0, 1);

            // Référence
            infoPanel.Controls.Add(new Label { Text = "Référence :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 0);
            TextBox referenceTextBox = new TextBox
            {
                Name = "referenceTextBox",
                Dock = DockStyle.Fill,
                ReadOnly = _isEditMode // La référence ne devrait pas être modifiable en mode édition
            };
            referenceTextBox.Validating += ReferenceTextBox_Validating;
            infoPanel.Controls.Add(referenceTextBox, 1, 0);

            // Client
            infoPanel.Controls.Add(new Label { Text = "Client :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 2, 0);
            ComboBox clientComboBox = new ComboBox
            {
                Name = "clientComboBox",
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Enabled = !_isEditMode // Le client ne devrait pas être modifiable en mode édition
            };
            clientComboBox.Validating += ClientComboBox_Validating;
            clientComboBox.SelectedIndexChanged += ClientComboBox_SelectedIndexChanged;
            infoPanel.Controls.Add(clientComboBox, 3, 0);

            // Dates
            infoPanel.Controls.Add(new Label { Text = "Date de début :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 1);
            DateTimePicker dateDebutPicker = new DateTimePicker
            {
                Name = "dateDebutPicker",
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today,
                Dock = DockStyle.Fill
            };
            infoPanel.Controls.Add(dateDebutPicker, 1, 1);

            infoPanel.Controls.Add(new Label { Text = "Responsable :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 2, 1);
            ComboBox responsableComboBox = new ComboBox
            {
                Name = "responsableComboBox",
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            responsableComboBox.Validating += ResponsableComboBox_Validating;
            infoPanel.Controls.Add(responsableComboBox, 3, 1);

            // Échéances
            infoPanel.Controls.Add(new Label { Text = "Nombre d'échéances :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 2);
            NumericUpDown nombreEcheancesNumeric = new NumericUpDown
            {
                Name = "nombreEcheancesNumeric",
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 60,
                Value = 12
            };
            nombreEcheancesNumeric.ValueChanged += NombreEcheancesNumeric_ValueChanged;
            infoPanel.Controls.Add(nombreEcheancesNumeric, 1, 2);

            infoPanel.Controls.Add(new Label { Text = "Fréquence (jours) :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 2, 2);
            NumericUpDown frequenceNumeric = new NumericUpDown
            {
                Name = "frequenceNumeric",
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 365,
                Value = 30
            };
            frequenceNumeric.ValueChanged += FrequenceNumeric_ValueChanged;
            infoPanel.Controls.Add(frequenceNumeric, 3, 2);

            // Montants
            infoPanel.Controls.Add(new Label { Text = "Taux d'intérêt (%) :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 3);
            NumericUpDown tauxInteretNumeric = new NumericUpDown
            {
                Name = "tauxInteretNumeric",
                Dock = DockStyle.Fill,
                DecimalPlaces = 2,
                Minimum = 0,
                Maximum = 100,
                Value = 0
            };
            tauxInteretNumeric.ValueChanged += TauxInteretNumeric_ValueChanged;
            infoPanel.Controls.Add(tauxInteretNumeric, 1, 3);

            infoPanel.Controls.Add(new Label { Text = "Frais :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 2, 3);
            NumericUpDown fraisNumeric = new NumericUpDown
            {
                Name = "fraisNumeric",
                Dock = DockStyle.Fill,
                DecimalPlaces = 2,
                Minimum = 0,
                Maximum = 10000,
                ThousandsSeparator = true,
                Value = 0
            };
            fraisNumeric.ValueChanged += FraisNumeric_ValueChanged;
            infoPanel.Controls.Add(fraisNumeric, 3, 3);

            // Statut
            infoPanel.Controls.Add(new Label { Text = "Statut :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 4);
            ComboBox statutComboBox = new ComboBox
            {
                Name = "statutComboBox",
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            statutComboBox.Items.Add("En cours");
            statutComboBox.Items.Add("Terminé");
            statutComboBox.Items.Add("En retard");
            statutComboBox.Items.Add("Annulé");
            statutComboBox.SelectedIndex = 0;
            statutComboBox.Validating += StatutComboBox_Validating;
            infoPanel.Controls.Add(statutComboBox, 1, 4);

            infoPanel.Controls.Add(new Label { Text = "Accord signé :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 2, 4);
            CheckBox accordSigneCheckBox = new CheckBox
            {
                Name = "accordSigneCheckBox",
                Dock = DockStyle.Fill,
                Text = "Oui"
            };
            infoPanel.Controls.Add(accordSigneCheckBox, 3, 4);

            // Tabs panel
            TabControl tabControl = new TabControl
            {
                Dock = DockStyle.Fill
            };
            mainPanel.Controls.Add(tabControl, 0, 2);

            // Factures tab
            TabPage facturesTab = new TabPage("Factures");
            tabControl.TabPages.Add(facturesTab);

            TableLayoutPanel facturesPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 45),
                    new ColumnStyle(SizeType.Absolute, 80),
                    new ColumnStyle(SizeType.Percent, 55)
                }
            };
            facturesTab.Controls.Add(facturesPanel);

            // Factures disponibles
            TableLayoutPanel facturesDisponiblesPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                RowStyles = {
                    new RowStyle(SizeType.Absolute, 30),
                    new RowStyle(SizeType.Percent, 100)
                }
            };
            facturesPanel.Controls.Add(facturesDisponiblesPanel, 0, 0);

            facturesDisponiblesPanel.Controls.Add(new Label { Text = "Factures disponibles", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 0);

            DataGridView facturesDataGridView = new DataGridView
            {
                Name = "facturesDataGridView",
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                BackgroundColor = SystemColors.Window,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };
            facturesDataGridView.DataSource = _facturesDataTable;
            facturesDisponiblesPanel.Controls.Add(facturesDataGridView, 0, 1);

            // Boutons de transfert
            TableLayoutPanel transferButtonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                RowStyles = {
                    new RowStyle(SizeType.Percent, 50),
                    new RowStyle(SizeType.Percent, 50)
                }
            };
            facturesPanel.Controls.Add(transferButtonsPanel, 1, 0);

            Button addFactureButton = new Button
            {
                Name = "addFactureButton",
                Text = ">>",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            addFactureButton.Click += AddFactureButton_Click;
            transferButtonsPanel.Controls.Add(addFactureButton, 0, 0);

            Button removeFactureButton = new Button
            {
                Name = "removeFactureButton",
                Text = "<<",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            removeFactureButton.Click += RemoveFactureButton_Click;
            transferButtonsPanel.Controls.Add(removeFactureButton, 0, 1);

            // Factures sélectionnées
            TableLayoutPanel facturesSelectionneePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                RowStyles = {
                    new RowStyle(SizeType.Absolute, 30),
                    new RowStyle(SizeType.Percent, 100)
                }
            };
            facturesPanel.Controls.Add(facturesSelectionneePanel, 2, 0);

            facturesSelectionneePanel.Controls.Add(new Label { Text = "Factures sélectionnées", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 0);

            DataGridView selectedFacturesDataGridView = new DataGridView
            {
                Name = "selectedFacturesDataGridView",
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                BackgroundColor = SystemColors.Window,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };
            selectedFacturesDataGridView.DataSource = _selectedFacturesDataTable;
            facturesSelectionneePanel.Controls.Add(selectedFacturesDataGridView, 0, 1);

            // Échéances tab
            TabPage echeancesTab = new TabPage("Échéances");
            tabControl.TabPages.Add(echeancesTab);

            TableLayoutPanel echeancesPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                RowStyles = {
                    new RowStyle(SizeType.Absolute, 40),
                    new RowStyle(SizeType.Percent, 100)
                }
            };
            echeancesTab.Controls.Add(echeancesPanel);

            // Boutons des échéances
            TableLayoutPanel echeancesButtonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 100),
                    new ColumnStyle(SizeType.Absolute, 120),
                    new ColumnStyle(SizeType.Absolute, 120)
                }
            };
            echeancesPanel.Controls.Add(echeancesButtonsPanel, 0, 0);

            Button generateEcheancesButton = new Button
            {
                Name = "generateEcheancesButton",
                Text = "Générer les échéances",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            generateEcheancesButton.Click += GenerateEcheancesButton_Click;
            echeancesButtonsPanel.Controls.Add(generateEcheancesButton, 1, 0);

            Button clearEcheancesButton = new Button
            {
                Name = "clearEcheancesButton",
                Text = "Effacer",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            clearEcheancesButton.Click += ClearEcheancesButton_Click;
            echeancesButtonsPanel.Controls.Add(clearEcheancesButton, 2, 0);

            // Échéances DataGridView
            DataGridView echeancesDataGridView = new DataGridView
            {
                Name = "echeancesDataGridView",
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                ReadOnly = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                BackgroundColor = SystemColors.Window,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };
            echeancesDataGridView.DataSource = _echeancesDataTable;
            echeancesPanel.Controls.Add(echeancesDataGridView, 0, 1);

            // Notes tab
            TabPage notesTab = new TabPage("Notes");
            tabControl.TabPages.Add(notesTab);

            TextBox notesTextBox = new TextBox
            {
                Name = "notesTextBox",
                Dock = DockStyle.Fill,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            notesTab.Controls.Add(notesTextBox);

            // Buttons panel
            TableLayoutPanel buttonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Bottom,
                ColumnCount = 3,
                RowCount = 1,
                Height = 40,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 100),
                    new ColumnStyle(SizeType.Absolute, 120),
                    new ColumnStyle(SizeType.Absolute, 120)
                }
            };
            this.Controls.Add(buttonsPanel);

            Button saveButton = new Button
            {
                Name = "saveButton",
                Text = "Enregistrer",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            saveButton.Click += SaveButton_Click;
            buttonsPanel.Controls.Add(saveButton, 1, 0);

            Button cancelButton = new Button
            {
                Name = "cancelButton",
                Text = "Annuler",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            cancelButton.Click += CancelButton_Click;
            buttonsPanel.Controls.Add(cancelButton, 2, 0);

            this.ResumeLayout(false);
        }

        private void InitializeDataTables()
        {
            // Table des factures disponibles
            _facturesDataTable = new DataTable();
            _facturesDataTable.Columns.Add("Id", typeof(int));
            _facturesDataTable.Columns.Add("Numéro", typeof(string));
            _facturesDataTable.Columns.Add("Date émission", typeof(DateTime));
            _facturesDataTable.Columns.Add("Date échéance", typeof(DateTime));
            _facturesDataTable.Columns.Add("Montant TTC", typeof(decimal));
            _facturesDataTable.Columns.Add("Montant restant", typeof(decimal));
            _facturesDataTable.Columns.Add("Statut", typeof(string));

            // Table des factures sélectionnées
            _selectedFacturesDataTable = new DataTable();
            _selectedFacturesDataTable.Columns.Add("Id", typeof(int));
            _selectedFacturesDataTable.Columns.Add("Numéro", typeof(string));
            _selectedFacturesDataTable.Columns.Add("Date émission", typeof(DateTime));
            _selectedFacturesDataTable.Columns.Add("Date échéance", typeof(DateTime));
            _selectedFacturesDataTable.Columns.Add("Montant TTC", typeof(decimal));
            _selectedFacturesDataTable.Columns.Add("Montant restant", typeof(decimal));
            _selectedFacturesDataTable.Columns.Add("Montant couvert", typeof(decimal));

            // Table des échéances
            _echeancesDataTable = new DataTable();
            _echeancesDataTable.Columns.Add("NumeroOrdre", typeof(int));
            _echeancesDataTable.Columns.Add("DateEcheance", typeof(DateTime));
            _echeancesDataTable.Columns.Add("MontantPrevu", typeof(decimal));
        }

        private async void LoadData()
        {
            try
            {
                // Charger les clients
                _clients = (await _clientService.GetAllAsync()).ToList();
                var clientComboBox = Controls.Find("clientComboBox", true).FirstOrDefault() as ComboBox;
                if (clientComboBox != null)
                {
                    clientComboBox.Items.Clear();
                    clientComboBox.Items.Add("-- Sélectionner un client --");
                    foreach (var client in _clients.OrderBy(c => c.RaisonSociale))
                    {
                        clientComboBox.Items.Add(client.RaisonSociale);
                    }
                    clientComboBox.SelectedIndex = 0;
                }

                // Charger les utilisateurs
                _utilisateurs = (await _utilisateurService.GetAllAsync()).ToList();
                var responsableComboBox = Controls.Find("responsableComboBox", true).FirstOrDefault() as ComboBox;
                if (responsableComboBox != null)
                {
                    responsableComboBox.Items.Clear();
                    responsableComboBox.Items.Add("-- Sélectionner un responsable --");
                    foreach (var utilisateur in _utilisateurs.OrderBy(u => u.NomComplet))
                    {
                        responsableComboBox.Items.Add(utilisateur.NomComplet);
                    }
                    responsableComboBox.SelectedIndex = 0;
                }

                // Si on est en mode édition, charger les données du plan de paiement
                if (_isEditMode && _planPaiement != null)
                {
                    LoadPlanPaiementData();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données");
                MessageBox.Show($"Erreur lors du chargement des données : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadPlanPaiementData()
        {
            if (_planPaiement == null)
                return;

            try
            {
                // Charger le plan de paiement complet avec toutes ses relations
                var planComplet = await _planPaiementService.GetPlanCompleteAsync(_planPaiement.Id);
                if (planComplet == null)
                    return;

                // Référence
                var referenceTextBox = Controls.Find("referenceTextBox", true).FirstOrDefault() as TextBox;
                if (referenceTextBox != null)
                {
                    referenceTextBox.Text = planComplet.Reference;
                }

                // Client
                var clientComboBox = Controls.Find("clientComboBox", true).FirstOrDefault() as ComboBox;
                if (clientComboBox != null)
                {
                    var client = _clients.FirstOrDefault(c => c.Id == planComplet.ClientId);
                    if (client != null)
                    {
                        int index = clientComboBox.Items.IndexOf(client.RaisonSociale);
                        if (index >= 0)
                        {
                            clientComboBox.SelectedIndex = index;
                        }
                    }
                }

                // Dates
                var dateDebutPicker = Controls.Find("dateDebutPicker", true).FirstOrDefault() as DateTimePicker;
                if (dateDebutPicker != null)
                {
                    dateDebutPicker.Value = planComplet.DateDebut;
                }

                // Responsable
                var responsableComboBox = Controls.Find("responsableComboBox", true).FirstOrDefault() as ComboBox;
                if (responsableComboBox != null)
                {
                    var responsable = _utilisateurs.FirstOrDefault(u => u.Id == planComplet.ResponsableId);
                    if (responsable != null)
                    {
                        int index = responsableComboBox.Items.IndexOf(responsable.NomComplet);
                        if (index >= 0)
                        {
                            responsableComboBox.SelectedIndex = index;
                        }
                    }
                }

                // Statut
                var statutComboBox = Controls.Find("statutComboBox", true).FirstOrDefault() as ComboBox;
                if (statutComboBox != null)
                {
                    int index = statutComboBox.Items.IndexOf(planComplet.Statut);
                    if (index >= 0)
                    {
                        statutComboBox.SelectedIndex = index;
                    }
                }

                // Accord signé
                var accordSigneCheckBox = Controls.Find("accordSigneCheckBox", true).FirstOrDefault() as CheckBox;
                if (accordSigneCheckBox != null)
                {
                    accordSigneCheckBox.Checked = planComplet.AccordSigne;
                }

                // Notes
                var notesTextBox = Controls.Find("notesTextBox", true).FirstOrDefault() as TextBox;
                if (notesTextBox != null && planComplet.Notes != null)
                {
                    notesTextBox.Text = planComplet.Notes;
                }

                // Factures
                if (planComplet.Factures != null)
                {
                    _selectedFactures = planComplet.Factures.ToList();
                    UpdateSelectedFacturesDataTable();
                }

                // Échéances
                if (planComplet.Echeances != null)
                {
                    UpdateEcheancesDataTable(planComplet.Echeances.ToList());
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données du plan de paiement");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void ClientComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            var clientComboBox = sender as ComboBox;
            if (clientComboBox == null || clientComboBox.SelectedIndex <= 0)
                return;

            try
            {
                string selectedClientName = clientComboBox.SelectedItem.ToString();
                var client = _clients.FirstOrDefault(c => c.RaisonSociale == selectedClientName);
                if (client == null)
                    return;

                // Charger les factures du client
                var clientWithFactures = await _clientService.GetWithFacturesAsync(client.Id);
                _factures = clientWithFactures?.Factures?.ToList() ?? new List<Facture>();

                // Filtrer les factures qui ont un montant restant
                _factures = _factures.Where(f => f.MontantRestant > 0).ToList();

                // Mettre à jour la table des factures disponibles
                UpdateFacturesDataTable();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des factures du client");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateFacturesDataTable()
        {
            _facturesDataTable.Clear();

            // Exclure les factures déjà sélectionnées
            var facturesDisponibles = _factures.Where(f => !_selectedFactures.Any(sf => sf.Id == f.Id)).ToList();

            foreach (var facture in facturesDisponibles)
            {
                _facturesDataTable.Rows.Add(
                    facture.Id,
                    facture.Numero,
                    facture.DateEmission,
                    facture.DateEcheance,
                    facture.MontantTTC,
                    facture.MontantRestant,
                    facture.Statut
                );
            }
        }

        private void UpdateSelectedFacturesDataTable()
        {
            _selectedFacturesDataTable.Clear();

            foreach (var facture in _selectedFactures)
            {
                _selectedFacturesDataTable.Rows.Add(
                    facture.Id,
                    facture.Numero,
                    facture.DateEmission,
                    facture.DateEcheance,
                    facture.MontantTTC,
                    facture.MontantRestant,
                    facture.MontantRestant // Par défaut, on couvre tout le montant restant
                );
            }
        }

        private void UpdateEcheancesDataTable(List<EcheancePaiement> echeances)
        {
            _echeancesDataTable.Clear();

            foreach (var echeance in echeances.OrderBy(e => e.NumeroOrdre))
            {
                _echeancesDataTable.Rows.Add(
                    echeance.NumeroOrdre,
                    echeance.DateEcheance,
                    echeance.MontantPrevu
                );
            }
        }

        private void AddFactureButton_Click(object sender, EventArgs e)
        {
            var facturesDataGridView = Controls.Find("facturesDataGridView", true).FirstOrDefault() as DataGridView;
            if (facturesDataGridView == null || facturesDataGridView.SelectedRows.Count == 0)
                return;

            foreach (DataGridViewRow row in facturesDataGridView.SelectedRows)
            {
                int factureId = Convert.ToInt32(row.Cells["Id"].Value);
                var facture = _factures.FirstOrDefault(f => f.Id == factureId);
                if (facture != null && !_selectedFactures.Any(sf => sf.Id == factureId))
                {
                    _selectedFactures.Add(facture);
                }
            }

            UpdateSelectedFacturesDataTable();
            UpdateFacturesDataTable();
        }

        private void RemoveFactureButton_Click(object sender, EventArgs e)
        {
            var selectedFacturesDataGridView = Controls.Find("selectedFacturesDataGridView", true).FirstOrDefault() as DataGridView;
            if (selectedFacturesDataGridView == null || selectedFacturesDataGridView.SelectedRows.Count == 0)
                return;

            foreach (DataGridViewRow row in selectedFacturesDataGridView.SelectedRows)
            {
                int factureId = Convert.ToInt32(row.Cells["Id"].Value);
                _selectedFactures.RemoveAll(f => f.Id == factureId);
            }

            UpdateSelectedFacturesDataTable();
            UpdateFacturesDataTable();
        }

        private void GenerateEcheancesButton_Click(object sender, EventArgs e)
        {
            if (_selectedFactures.Count == 0)
            {
                MessageBox.Show("Veuillez sélectionner au moins une facture.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                var dateDebutPicker = Controls.Find("dateDebutPicker", true).FirstOrDefault() as DateTimePicker;
                var nombreEcheancesNumeric = Controls.Find("nombreEcheancesNumeric", true).FirstOrDefault() as NumericUpDown;
                var frequenceNumeric = Controls.Find("frequenceNumeric", true).FirstOrDefault() as NumericUpDown;
                var tauxInteretNumeric = Controls.Find("tauxInteretNumeric", true).FirstOrDefault() as NumericUpDown;
                var fraisNumeric = Controls.Find("fraisNumeric", true).FirstOrDefault() as NumericUpDown;

                if (dateDebutPicker == null || nombreEcheancesNumeric == null || frequenceNumeric == null || tauxInteretNumeric == null || fraisNumeric == null)
                    return;

                // Calculer le montant total des factures sélectionnées
                decimal montantTotal = _selectedFactures.Sum(f => f.MontantRestant);

                // Calculer les intérêts
                decimal tauxInteret = tauxInteretNumeric.Value;
                int nombreEcheances = (int)nombreEcheancesNumeric.Value;
                int frequence = (int)frequenceNumeric.Value;
                decimal montantInterets = montantTotal * (tauxInteret / 100) * (nombreEcheances * frequence / 365.0m);
                decimal frais = fraisNumeric.Value;
                decimal montantTotalAvecInterets = montantTotal + montantInterets + frais;

                // Générer les échéances
                List<EcheancePaiement> echeances = new List<EcheancePaiement>();
                decimal montantParEcheance = Math.Round(montantTotalAvecInterets / nombreEcheances, 2);
                decimal montantRestant = montantTotalAvecInterets;

                for (int i = 0; i < nombreEcheances; i++)
                {
                    decimal montantEcheance;
                    if (i == nombreEcheances - 1)
                    {
                        // Dernière échéance : ajuster pour éviter les problèmes d'arrondi
                        montantEcheance = montantRestant;
                    }
                    else
                    {
                        montantEcheance = montantParEcheance;
                        montantRestant -= montantEcheance;
                    }

                    echeances.Add(new EcheancePaiement
                    {
                        NumeroOrdre = i + 1,
                        DateEcheance = dateDebutPicker.Value.AddDays(i * frequence),
                        MontantPrevu = montantEcheance,
                        EstPayee = false,
                        EstActif = true
                    });
                }

                // Mettre à jour la table des échéances
                UpdateEcheancesDataTable(echeances);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération des échéances");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearEcheancesButton_Click(object sender, EventArgs e)
        {
            _echeancesDataTable.Clear();
        }

        private void NombreEcheancesNumeric_ValueChanged(object sender, EventArgs e)
        {
            // Recalculer la date de fin prévue
            var dateDebutPicker = Controls.Find("dateDebutPicker", true).FirstOrDefault() as DateTimePicker;
            var frequenceNumeric = Controls.Find("frequenceNumeric", true).FirstOrDefault() as NumericUpDown;
            var nombreEcheancesNumeric = sender as NumericUpDown;

            if (dateDebutPicker == null || frequenceNumeric == null || nombreEcheancesNumeric == null)
                return;

            // Vider les échéances existantes
            _echeancesDataTable.Clear();
        }

        private void FrequenceNumeric_ValueChanged(object sender, EventArgs e)
        {
            // Vider les échéances existantes
            _echeancesDataTable.Clear();
        }

        private void TauxInteretNumeric_ValueChanged(object sender, EventArgs e)
        {
            // Vider les échéances existantes
            _echeancesDataTable.Clear();
        }

        private void FraisNumeric_ValueChanged(object sender, EventArgs e)
        {
            // Vider les échéances existantes
            _echeancesDataTable.Clear();
        }

        private void ReferenceTextBox_Validating(object sender, CancelEventArgs e)
        {
            var referenceTextBox = sender as TextBox;
            if (referenceTextBox == null)
                return;

            if (string.IsNullOrWhiteSpace(referenceTextBox.Text) && !_isEditMode)
            {
                _errorProvider.SetError(referenceTextBox, "La référence ne peut pas être vide.");
                e.Cancel = true;
            }
            else
            {
                _errorProvider.SetError(referenceTextBox, "");
            }
        }

        private void ClientComboBox_Validating(object sender, CancelEventArgs e)
        {
            var clientComboBox = sender as ComboBox;
            if (clientComboBox == null)
                return;

            if (clientComboBox.SelectedIndex <= 0)
            {
                _errorProvider.SetError(clientComboBox, "Veuillez sélectionner un client.");
                e.Cancel = true;
            }
            else
            {
                _errorProvider.SetError(clientComboBox, "");
            }
        }

        private void ResponsableComboBox_Validating(object sender, CancelEventArgs e)
        {
            var responsableComboBox = sender as ComboBox;
            if (responsableComboBox == null)
                return;

            if (responsableComboBox.SelectedIndex <= 0)
            {
                _errorProvider.SetError(responsableComboBox, "Veuillez sélectionner un responsable.");
                e.Cancel = true;
            }
            else
            {
                _errorProvider.SetError(responsableComboBox, "");
            }
        }

        private void StatutComboBox_Validating(object sender, CancelEventArgs e)
        {
            var statutComboBox = sender as ComboBox;
            if (statutComboBox == null)
                return;

            if (statutComboBox.SelectedIndex < 0)
            {
                _errorProvider.SetError(statutComboBox, "Veuillez sélectionner un statut.");
                e.Cancel = true;
            }
            else
            {
                _errorProvider.SetError(statutComboBox, "");
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            if (!ValidateChildren())
                return;

            if (_selectedFactures.Count == 0)
            {
                MessageBox.Show("Veuillez sélectionner au moins une facture.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (_echeancesDataTable.Rows.Count == 0)
            {
                MessageBox.Show("Veuillez générer les échéances.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                var referenceTextBox = Controls.Find("referenceTextBox", true).FirstOrDefault() as TextBox;
                var clientComboBox = Controls.Find("clientComboBox", true).FirstOrDefault() as ComboBox;
                var dateDebutPicker = Controls.Find("dateDebutPicker", true).FirstOrDefault() as DateTimePicker;
                var responsableComboBox = Controls.Find("responsableComboBox", true).FirstOrDefault() as ComboBox;
                var statutComboBox = Controls.Find("statutComboBox", true).FirstOrDefault() as ComboBox;
                var accordSigneCheckBox = Controls.Find("accordSigneCheckBox", true).FirstOrDefault() as CheckBox;
                var notesTextBox = Controls.Find("notesTextBox", true).FirstOrDefault() as TextBox;
                var tauxInteretNumeric = Controls.Find("tauxInteretNumeric", true).FirstOrDefault() as NumericUpDown;
                var fraisNumeric = Controls.Find("fraisNumeric", true).FirstOrDefault() as NumericUpDown;
                var nombreEcheancesNumeric = Controls.Find("nombreEcheancesNumeric", true).FirstOrDefault() as NumericUpDown;
                var frequenceNumeric = Controls.Find("frequenceNumeric", true).FirstOrDefault() as NumericUpDown;

                if (referenceTextBox == null || clientComboBox == null || dateDebutPicker == null || responsableComboBox == null ||
                    statutComboBox == null || accordSigneCheckBox == null || notesTextBox == null || tauxInteretNumeric == null ||
                    fraisNumeric == null || nombreEcheancesNumeric == null || frequenceNumeric == null)
                    return;

                // Récupérer le client
                string selectedClientName = clientComboBox.SelectedItem.ToString();
                var client = _clients.FirstOrDefault(c => c.RaisonSociale == selectedClientName);
                if (client == null)
                    return;

                // Récupérer le responsable
                string selectedResponsableName = responsableComboBox.SelectedItem.ToString();
                var responsable = _utilisateurs.FirstOrDefault(u => u.NomComplet == selectedResponsableName);
                if (responsable == null)
                    return;

                // Calculer le montant total
                decimal montantTotal = _selectedFactures.Sum(f => f.MontantRestant);
                decimal montantInterets = montantTotal * (tauxInteretNumeric.Value / 100) * ((int)nombreEcheancesNumeric.Value * (int)frequenceNumeric.Value / 365.0m);
                decimal frais = fraisNumeric.Value;
                decimal montantTotalAvecInterets = montantTotal + montantInterets + frais;

                // Créer le plan de paiement
                var planPaiement = new PlanPaiement
                {
                    Reference = string.IsNullOrWhiteSpace(referenceTextBox.Text) ? null : referenceTextBox.Text,
                    ClientId = client.Id,
                    DateCreationPlan = DateTime.Now,
                    DateDebut = dateDebutPicker.Value,
                    DateFinPrevue = dateDebutPicker.Value.AddDays((int)nombreEcheancesNumeric.Value * (int)frequenceNumeric.Value),
                    MontantTotal = montantTotalAvecInterets,
                    MontantInterets = montantInterets,
                    MontantFrais = frais,
                    Statut = statutComboBox.SelectedItem.ToString(),
                    ResponsableId = responsable.Id,
                    Notes = notesTextBox.Text,
                    AccordSigne = accordSigneCheckBox.Checked,
                    DateSignatureAccord = accordSigneCheckBox.Checked ? DateTime.Now : (DateTime?)null,
                    EstActif = true
                };

                // Créer les échéances
                List<EcheancePaiement> echeances = new List<EcheancePaiement>();
                foreach (DataRow row in _echeancesDataTable.Rows)
                {
                    echeances.Add(new EcheancePaiement
                    {
                        NumeroOrdre = Convert.ToInt32(row["NumeroOrdre"]),
                        DateEcheance = Convert.ToDateTime(row["DateEcheance"]),
                        MontantPrevu = Convert.ToDecimal(row["MontantPrevu"]),
                        EstPayee = false,
                        EstActif = true
                    });
                }

                // Récupérer les montants couverts pour chaque facture
                List<int> factureIds = new List<int>();
                List<decimal> montantsCouvert = new List<decimal>();
                foreach (DataRow row in _selectedFacturesDataTable.Rows)
                {
                    factureIds.Add(Convert.ToInt32(row["Id"]));
                    montantsCouvert.Add(Convert.ToDecimal(row["Montant couvert"]));
                }

                // Enregistrer le plan de paiement
                PlanPaiement? createdPlan;
                if (_isEditMode && _planPaiement != null)
                {
                    // Mise à jour du plan existant
                    planPaiement.Id = _planPaiement.Id;
                    bool updateResult = await _planPaiementService.UpdatePlanAsync(planPaiement, _currentUserId);
                    createdPlan = updateResult ? planPaiement : null;
                    // Note: Dans une implémentation complète, il faudrait également mettre à jour les échéances et les factures associées
                }
                else
                {
                    // Création d'un nouveau plan
                    createdPlan = await _planPaiementService.CreatePlanAsync(planPaiement, echeances, factureIds, montantsCouvert, _currentUserId);
                }

                if (createdPlan != null)
                {
                    MessageBox.Show(_isEditMode ? "Le plan de paiement a été mis à jour avec succès." : "Le plan de paiement a été créé avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    DialogResult = DialogResult.OK;
                    Close();
                }
                else
                {
                    MessageBox.Show("Erreur lors de l'enregistrement du plan de paiement.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement du plan de paiement");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
}
