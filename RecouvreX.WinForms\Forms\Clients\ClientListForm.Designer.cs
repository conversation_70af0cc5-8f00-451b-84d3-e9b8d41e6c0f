namespace RecouvreX.WinForms.Forms.Clients
{
    partial class ClientListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // ClientListForm
            //
            this.ClientSize = new System.Drawing.Size(1000, 600);
            this.Name = "ClientListForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Liste des clients";
            this.Load += new System.EventHandler(this.ClientListForm_Load);
            this.ResumeLayout(false);

            // Barre d'outils
            System.Windows.Forms.ToolStrip toolStrip = new System.Windows.Forms.ToolStrip();
            toolStrip.Name = "toolStrip";
            toolStrip.Dock = System.Windows.Forms.DockStyle.Top;
            this.Controls.Add(toolStrip);

            // Bouton Ajouter
            System.Windows.Forms.ToolStripButton addButton = new System.Windows.Forms.ToolStripButton();
            addButton.Name = "addButton";
            addButton.Text = "Ajouter";
            addButton.Image = null; // Ajouter une icône
            addButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            addButton.Click += new System.EventHandler(this.AddButton_Click);
            toolStrip.Items.Add(addButton);

            // Bouton Modifier
            System.Windows.Forms.ToolStripButton editButton = new System.Windows.Forms.ToolStripButton();
            editButton.Name = "editButton";
            editButton.Text = "Modifier";
            editButton.Image = null; // Ajouter une icône
            editButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            editButton.Click += new System.EventHandler(this.EditButton_Click);
            toolStrip.Items.Add(editButton);

            // Bouton Supprimer
            System.Windows.Forms.ToolStripButton deleteButton = new System.Windows.Forms.ToolStripButton();
            deleteButton.Name = "deleteButton";
            deleteButton.Text = "Supprimer";
            deleteButton.Image = null; // Ajouter une icône
            deleteButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            deleteButton.Click += new System.EventHandler(this.DeleteButton_Click);
            toolStrip.Items.Add(deleteButton);

            // Séparateur
            toolStrip.Items.Add(new System.Windows.Forms.ToolStripSeparator());

            // Bouton Rafraîchir
            System.Windows.Forms.ToolStripButton refreshButton = new System.Windows.Forms.ToolStripButton();
            refreshButton.Name = "refreshButton";
            refreshButton.Text = "Rafraîchir";
            refreshButton.Image = null; // Ajouter une icône
            refreshButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            refreshButton.Click += new System.EventHandler(this.RefreshButton_Click);
            toolStrip.Items.Add(refreshButton);

            // Séparateur
            toolStrip.Items.Add(new System.Windows.Forms.ToolStripSeparator());

            // Champ de recherche
            System.Windows.Forms.ToolStripLabel searchLabel = new System.Windows.Forms.ToolStripLabel();
            searchLabel.Text = "Rechercher :";
            toolStrip.Items.Add(searchLabel);

            System.Windows.Forms.ToolStripTextBox searchTextBox = new System.Windows.Forms.ToolStripTextBox();
            searchTextBox.Name = "searchTextBox";
            searchTextBox.Width = 200;
            searchTextBox.KeyDown += new System.Windows.Forms.KeyEventHandler(this.SearchTextBox_KeyDown);
            toolStrip.Items.Add(searchTextBox);

            System.Windows.Forms.ToolStripButton searchButton = new System.Windows.Forms.ToolStripButton();
            searchButton.Name = "searchButton";
            searchButton.Text = "Rechercher";
            searchButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            searchButton.Click += new System.EventHandler(this.SearchButton_Click);
            toolStrip.Items.Add(searchButton);

            // DataGridView pour afficher les clients
            System.Windows.Forms.DataGridView dataGridView = new System.Windows.Forms.DataGridView();
            dataGridView.Name = "dataGridView";
            dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.ReadOnly = true;
            dataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;
            dataGridView.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView.RowHeadersVisible = false;
            dataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.DataGridView_CellDoubleClick);
            this.Controls.Add(dataGridView);

            // Barre d'état
            System.Windows.Forms.StatusStrip statusStrip = new System.Windows.Forms.StatusStrip();
            statusStrip.Name = "statusStrip";
            statusStrip.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Controls.Add(statusStrip);

            System.Windows.Forms.ToolStripStatusLabel countLabel = new System.Windows.Forms.ToolStripStatusLabel();
            countLabel.Name = "countLabel";
            statusStrip.Items.Add(countLabel);
        }

        #endregion
    }
}
