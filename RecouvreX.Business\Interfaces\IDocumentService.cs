using RecouvreX.Models;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des documents
    /// </summary>
    public interface IDocumentService
    {
        /// <summary>
        /// Récupère tous les documents
        /// </summary>
        /// <returns>Liste des documents</returns>
        Task<IEnumerable<Document>> GetAllAsync();

        /// <summary>
        /// Récupère un document par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du document</param>
        /// <returns>Document trouvé ou null</returns>
        Task<Document> GetByIdAsync(int id);

        /// <summary>
        /// Récupère tous les documents associés à une entité
        /// </summary>
        /// <param name="typeEntite">Type d'entité (Facture, Paiement, Relance, Client)</param>
        /// <param name="entiteId">Identifiant de l'entité</param>
        /// <returns>Liste des documents associés à l'entité</returns>
        Task<IEnumerable<Document>> GetByEntityAsync(string typeEntite, int entiteId);

        /// <summary>
        /// Récupère les documents par type
        /// </summary>
        /// <param name="type">Type de document</param>
        /// <returns>Liste des documents du type spécifié</returns>
        Task<IEnumerable<Document>> GetByTypeAsync(string type);

        /// <summary>
        /// Récupère un document avec son contenu
        /// </summary>
        /// <param name="documentId">Identifiant du document</param>
        /// <returns>Document avec son contenu</returns>
        Task<Document?> GetWithContentAsync(int documentId);

        /// <summary>
        /// Recherche des documents par nom
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des documents correspondant au terme de recherche</returns>
        Task<IEnumerable<Document>> SearchByNameAsync(string searchTerm);

        /// <summary>
        /// Crée un nouveau document à partir d'un fichier
        /// </summary>
        /// <param name="filePath">Chemin du fichier</param>
        /// <param name="nom">Nom du document</param>
        /// <param name="type">Type du document</param>
        /// <param name="description">Description du document</param>
        /// <param name="typeEntite">Type d'entité associée</param>
        /// <param name="entiteId">Identifiant de l'entité associée</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Document créé avec son identifiant généré</returns>
        Task<Document> CreateFromFileAsync(string filePath, string nom, string type, string description, string typeEntite, int entiteId, int creePar);

        /// <summary>
        /// Crée un nouveau document à partir d'un flux de données
        /// </summary>
        /// <param name="stream">Flux de données</param>
        /// <param name="nom">Nom du document</param>
        /// <param name="type">Type du document</param>
        /// <param name="mimeType">Type MIME du document</param>
        /// <param name="description">Description du document</param>
        /// <param name="typeEntite">Type d'entité associée</param>
        /// <param name="entiteId">Identifiant de l'entité associée</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Document créé avec son identifiant généré</returns>
        Task<Document> CreateFromStreamAsync(Stream stream, string nom, string type, string mimeType, string description, string typeEntite, int entiteId, int creePar);

        /// <summary>
        /// Met à jour un document existant
        /// </summary>
        /// <param name="document">Document à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Document mis à jour</returns>
        Task<Document> UpdateAsync(Document document, int modifiePar);

        /// <summary>
        /// Supprime un document
        /// </summary>
        /// <param name="id">Identifiant du document à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Enregistre le contenu d'un document dans un fichier
        /// </summary>
        /// <param name="documentId">Identifiant du document</param>
        /// <param name="filePath">Chemin du fichier de destination</param>
        /// <returns>True si l'enregistrement a réussi, sinon False</returns>
        Task<bool> SaveToFileAsync(int documentId, string filePath);

        /// <summary>
        /// Récupère le contenu d'un document sous forme de flux de données
        /// </summary>
        /// <param name="documentId">Identifiant du document</param>
        /// <returns>Flux de données contenant le document</returns>
        Task<Stream> GetContentAsStreamAsync(int documentId);
    }
}
