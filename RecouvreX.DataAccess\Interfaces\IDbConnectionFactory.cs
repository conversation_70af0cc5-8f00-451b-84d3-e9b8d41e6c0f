using System.Data;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour la factory de connexion à la base de données
    /// </summary>
    public interface IDbConnectionFactory
    {
        /// <summary>
        /// Crée et ouvre une nouvelle connexion à la base de données
        /// </summary>
        /// <returns>Connexion ouverte à la base de données</returns>
        IDbConnection CreateConnection();

        /// <summary>
        /// Crée et ouvre une nouvelle connexion à la base de données de manière asynchrone
        /// </summary>
        /// <returns>Connexion ouverte à la base de données</returns>
        Task<IDbConnection> CreateConnectionAsync();
    }
}
