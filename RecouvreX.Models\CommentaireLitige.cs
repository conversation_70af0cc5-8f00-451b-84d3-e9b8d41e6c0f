using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un commentaire sur un litige
    /// </summary>
    public class CommentaireLitige : BaseEntity
    {
        /// <summary>
        /// Identifiant du litige associé
        /// </summary>
        public int LitigeId { get; set; }

        /// <summary>
        /// Litige associé (navigation property)
        /// </summary>
        public Litige Litige { get; set; }

        /// <summary>
        /// Contenu du commentaire
        /// </summary>
        public string Contenu { get; set; }

        /// <summary>
        /// Date du commentaire
        /// </summary>
        public DateTime DateCommentaire { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a créé le commentaire
        /// </summary>
        public int UtilisateurId { get; set; }

        /// <summary>
        /// Utilisateur qui a créé le commentaire (navigation property)
        /// </summary>
        public Utilisateur Utilisateur { get; set; }

        /// <summary>
        /// Indique si le commentaire est interne (visible uniquement par les utilisateurs)
        /// </summary>
        public bool EstInterne { get; set; }
    }
}
