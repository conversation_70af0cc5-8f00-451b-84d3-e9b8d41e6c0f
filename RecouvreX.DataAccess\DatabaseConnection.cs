using Microsoft.Data.SqlClient;
using RecouvreX.DataAccess.Interfaces;
using System;
using System.Data;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess
{
    /// <summary>
    /// Classe de gestion de la connexion à la base de données
    /// </summary>
    public class DatabaseConnection : IDbConnectionFactory
    {
        private readonly string _connectionString;

        /// <summary>
        /// Constructeur avec chaîne de connexion
        /// </summary>
        /// <param name="connectionString">Chaîne de connexion à la base de données</param>
        public DatabaseConnection(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        /// <summary>
        /// Crée et ouvre une nouvelle connexion à la base de données
        /// </summary>
        /// <returns>Connexion ouverte à la base de données</returns>
        public IDbConnection CreateConnection()
        {
            var connection = new SqlConnection(_connectionString);
            connection.Open();
            return connection;
        }

        /// <summary>
        /// Crée et ouvre une nouvelle connexion à la base de données de manière asynchrone
        /// </summary>
        /// <returns>Connexion ouverte à la base de données</returns>
        public async Task<IDbConnection> CreateConnectionAsync()
        {
            var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            return connection;
        }

        /// <summary>
        /// Teste la connexion à la base de données
        /// </summary>
        /// <returns>True si la connexion est établie avec succès, sinon False</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Construit une chaîne de connexion à partir des paramètres
        /// </summary>
        /// <param name="server">Nom ou adresse IP du serveur</param>
        /// <param name="database">Nom de la base de données</param>
        /// <param name="userId">Identifiant utilisateur (si authentification SQL)</param>
        /// <param name="password">Mot de passe (si authentification SQL)</param>
        /// <param name="integratedSecurity">True pour utiliser l'authentification Windows, sinon False</param>
        /// <param name="trustServerCertificate">True pour faire confiance au certificat du serveur, sinon False</param>
        /// <returns>Chaîne de connexion</returns>
        public static string BuildConnectionString(string server, string database, string userId = null, string password = null, bool integratedSecurity = false, bool trustServerCertificate = true)
        {
            var builder = new SqlConnectionStringBuilder
            {
                DataSource = server,
                InitialCatalog = database,
                TrustServerCertificate = trustServerCertificate
            };

            if (integratedSecurity)
            {
                builder.IntegratedSecurity = true;
            }
            else
            {
                builder.UserID = userId;
                builder.Password = password;
            }

            return builder.ConnectionString;
        }
    }
}
