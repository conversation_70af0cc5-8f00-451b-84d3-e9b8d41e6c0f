# Guide d'Installation et de Déploiement - RecouvreX

Ce guide détaille les étapes d'installation et de déploiement de l'application RecouvreX pour la gestion du recouvrement de créances clients.

## Table des matières

1. [Prérequis](#prérequis)
2. [Installation de la base de données](#installation-de-la-base-de-données)
3. [Installation de l'application](#installation-de-lapplication)
4. [Configuration](#configuration)
5. [Déploiement en entreprise](#déploiement-en-entreprise)
6. [Mise à jour](#mise-à-jour)
7. [Sauvegarde et restauration](#sauvegarde-et-restauration)
8. [Résolution des problèmes](#résolution-des-problèmes)

## Prérequis

### Matériel recommandé
- **Processeur** : Intel Core i5 ou équivalent (ou supérieur)
- **Mémoire RAM** : 8 Go minimum (16 Go recommandé)
- **Espace disque** : 1 Go pour l'application + espace pour la base de données (prévoir 5 Go minimum)
- **Résolution d'écran** : 1920x1080 ou supérieure

### Logiciels requis
- **Système d'exploitation** : Windows 10 ou Windows 11 (64 bits)
- **Framework** : .NET 6.0 Runtime ou supérieur
- **Base de données** : SQL Server Express 2019 ou supérieur
- **Navigateur** : Microsoft Edge, Google Chrome ou Mozilla Firefox (pour la documentation)

## Installation de la base de données

### Installation de SQL Server Express

1. Téléchargez SQL Server Express 2019 (ou version supérieure) depuis le site de Microsoft.
2. Exécutez le programme d'installation et suivez les instructions.
3. Sélectionnez l'installation de base et notez les informations de connexion.
4. Assurez-vous que le service SQL Server est configuré pour démarrer automatiquement.
5. Installez SQL Server Management Studio (SSMS) pour faciliter la gestion de la base de données.

### Création de la base de données

1. Ouvrez SQL Server Management Studio et connectez-vous à l'instance SQL Server.
2. Exécutez le script de création de la base de données fourni avec l'application (`scripts/create_database.sql`).
3. Vérifiez que la base de données "RecouvreX" a été créée avec succès.
4. Exécutez le script de création des tables et des données initiales (`scripts/create_tables.sql`).
5. Vérifiez que les tables ont été créées avec succès.

### Configuration des droits d'accès

1. Créez un utilisateur SQL Server dédié à l'application :
   ```sql
   CREATE LOGIN RecouvreXUser WITH PASSWORD = '******************';
   USE RecouvreX;
   CREATE USER RecouvreXUser FOR LOGIN RecouvreXUser;
   EXEC sp_addrolemember 'db_datareader', 'RecouvreXUser';
   EXEC sp_addrolemember 'db_datawriter', 'RecouvreXUser';
   ```
2. Ou utilisez l'authentification Windows si l'application est déployée dans un environnement de domaine.

## Installation de l'application

### Installation du .NET Runtime

1. Téléchargez et installez le .NET 6.0 Runtime (ou version supérieure) depuis le site de Microsoft.
2. Vérifiez l'installation en exécutant la commande `dotnet --info` dans une invite de commandes.

### Installation de l'application RecouvreX

#### Installation à partir du programme d'installation

1. Téléchargez le programme d'installation de RecouvreX (`RecouvreX_Setup.exe`).
2. Exécutez le programme d'installation et suivez les instructions.
3. Sélectionnez le répertoire d'installation (par défaut : `C:\Program Files\RecouvreX`).
4. Sélectionnez les composants à installer (application, documentation, exemples).
5. Terminez l'installation.

#### Installation manuelle

1. Téléchargez l'archive de l'application RecouvreX (`RecouvreX.zip`).
2. Extrayez l'archive dans le répertoire souhaité (par exemple : `C:\RecouvreX`).
3. Créez un raccourci vers `RecouvreX.exe` sur le bureau ou dans le menu Démarrer.

## Configuration

### Configuration de la connexion à la base de données

1. Ouvrez le fichier `appsettings.json` dans le répertoire d'installation de l'application.
2. Modifiez la chaîne de connexion selon votre configuration :
   ```json
   "ConnectionStrings": {
     "DefaultConnection": "Server=localhost\\SQLEXPRESS;Database=RecouvreX;User Id=RecouvreXUser;Password=******************;"
   }
   ```
   Ou pour l'authentification Windows :
   ```json
   "ConnectionStrings": {
     "DefaultConnection": "Server=localhost\\SQLEXPRESS;Database=RecouvreX;Trusted_Connection=True;MultipleActiveResultSets=true"
   }
   ```
3. Sauvegardez le fichier.

### Configuration des paramètres de l'application

1. Dans le même fichier `appsettings.json`, modifiez les paramètres de l'application selon vos besoins :
   ```json
   "AppSettings": {
     "CompanyName": "Votre Entreprise",
     "CompanyLogo": "logo.png",
     "DefaultCurrency": "EUR",
     "DefaultPaymentDelay": 30,
     "AutomaticReminders": true
   }
   ```
2. Sauvegardez le fichier.

### Configuration de la journalisation

1. Dans le fichier `appsettings.json`, configurez les paramètres de journalisation :
   ```json
   "Logging": {
     "LogLevel": {
       "Default": "Information",
       "Microsoft": "Warning"
     },
     "File": {
       "Path": "logs/recouvreX.log",
       "RollingInterval": "Day",
       "RetainedFileCountLimit": 31
     }
   }
   ```
2. Assurez-vous que le répertoire `logs` existe dans le répertoire d'installation de l'application.

## Déploiement en entreprise

### Déploiement sur plusieurs postes

#### Utilisation d'un partage réseau

1. Installez l'application sur un partage réseau accessible à tous les utilisateurs.
2. Configurez la connexion à la base de données pour pointer vers le serveur SQL Server central.
3. Créez des raccourcis sur les postes des utilisateurs pointant vers l'exécutable sur le partage réseau.

#### Installation sur chaque poste

1. Créez un package d'installation personnalisé avec la configuration de votre entreprise.
2. Déployez le package sur chaque poste utilisateur à l'aide d'un outil de déploiement (SCCM, PDQ Deploy, etc.).
3. Assurez-vous que tous les postes ont accès au serveur SQL Server central.

### Configuration d'un serveur de base de données central

1. Installez SQL Server sur un serveur dédié.
2. Configurez les règles de pare-feu pour permettre les connexions depuis les postes clients.
3. Créez la base de données et les utilisateurs nécessaires.
4. Configurez les sauvegardes automatiques de la base de données.

## Mise à jour

### Mise à jour de l'application

1. Téléchargez la nouvelle version de l'application.
2. Sauvegardez le fichier `appsettings.json` actuel.
3. Fermez l'application sur tous les postes.
4. Installez la nouvelle version en suivant les instructions d'installation.
5. Restaurez votre fichier `appsettings.json` personnalisé si nécessaire.
6. Vérifiez que l'application fonctionne correctement.

### Mise à jour de la base de données

1. Sauvegardez la base de données actuelle.
2. Exécutez les scripts de migration fournis avec la nouvelle version (`scripts/migrate_vX.Y.Z.sql`).
3. Vérifiez que la migration s'est déroulée correctement.

## Sauvegarde et restauration

### Sauvegarde de la base de données

#### Sauvegarde manuelle

1. Ouvrez SQL Server Management Studio et connectez-vous à l'instance SQL Server.
2. Faites un clic droit sur la base de données "RecouvreX" et sélectionnez "Tâches" > "Sauvegarder...".
3. Configurez les options de sauvegarde et exécutez la sauvegarde.

#### Sauvegarde automatique

1. Créez un plan de maintenance dans SQL Server Management Studio :
   - Faites un clic droit sur "Plans de maintenance" dans l'explorateur d'objets.
   - Sélectionnez "Nouvel plan de maintenance".
   - Configurez une sauvegarde complète quotidienne de la base de données "RecouvreX".
   - Configurez une sauvegarde différentielle toutes les 4 heures (optionnel).
   - Configurez une sauvegarde du journal de transactions toutes les heures (optionnel, si la base de données est en mode de récupération complète).
2. Vérifiez régulièrement que les sauvegardes sont créées correctement.

### Restauration de la base de données

1. Ouvrez SQL Server Management Studio et connectez-vous à l'instance SQL Server.
2. Faites un clic droit sur "Bases de données" et sélectionnez "Restaurer la base de données...".
3. Sélectionnez la sauvegarde à restaurer et configurez les options de restauration.
4. Exécutez la restauration.

## Résolution des problèmes

### Problèmes de connexion à la base de données

1. Vérifiez que le service SQL Server est en cours d'exécution.
2. Vérifiez que la chaîne de connexion dans `appsettings.json` est correcte.
3. Vérifiez que l'utilisateur de la base de données a les droits nécessaires.
4. Vérifiez que le pare-feu Windows autorise les connexions SQL Server.

### Problèmes de performance

1. Vérifiez les ressources système (CPU, mémoire, disque) pendant l'utilisation de l'application.
2. Optimisez les index de la base de données si nécessaire.
3. Augmentez la mémoire allouée à SQL Server si possible.
4. Vérifiez que la base de données est régulièrement maintenue (reconstruction des index, mise à jour des statistiques).

### Journaux d'erreurs

1. Consultez les journaux d'application dans le répertoire `logs`.
2. Consultez les journaux d'événements Windows pour les erreurs liées à SQL Server ou à .NET Runtime.
3. En cas d'erreur persistante, contactez le support technique avec les journaux d'erreurs.
