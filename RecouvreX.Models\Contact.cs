namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un contact client dans le système
    /// </summary>
    public class Contact : BaseEntity
    {
        /// <summary>
        /// Identifiant du client associé
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// Client associé (navigation property)
        /// </summary>
        public Client Client { get; set; }

        /// <summary>
        /// Nom du contact
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Prénom du contact
        /// </summary>
        public string Prenom { get; set; }

        /// <summary>
        /// Fonction du contact dans l'entreprise
        /// </summary>
        public string Fonction { get; set; }

        /// <summary>
        /// Numéro de téléphone du contact
        /// </summary>
        public string Telephone { get; set; }

        /// <summary>
        /// Adresse email du contact
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Indique si c'est le contact principal
        /// </summary>
        public bool EstPrincipal { get; set; }

        /// <summary>
        /// Notes ou commentaires sur le contact
        /// </summary>
        public string Notes { get; set; }
    }
}
