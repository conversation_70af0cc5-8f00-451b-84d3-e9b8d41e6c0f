using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un document lié à un litige
    /// </summary>
    public class DocumentLitige : BaseEntity
    {
        /// <summary>
        /// Identifiant du litige associé
        /// </summary>
        public int LitigeId { get; set; }

        /// <summary>
        /// Litige associé (navigation property)
        /// </summary>
        public Litige Litige { get; set; }

        /// <summary>
        /// Nom du document
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Description du document
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Chemin du fichier sur le disque
        /// </summary>
        public string CheminFichier { get; set; }

        /// <summary>
        /// Type de fichier (extension)
        /// </summary>
        public string TypeFichier { get; set; }

        /// <summary>
        /// Taille du fichier en octets
        /// </summary>
        public long TailleFichier { get; set; }

        /// <summary>
        /// Date d'ajout du document
        /// </summary>
        public DateTime DateAjout { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a ajouté le document
        /// </summary>
        public int UtilisateurId { get; set; }

        /// <summary>
        /// Utilisateur qui a ajouté le document (navigation property)
        /// </summary>
        public Utilisateur Utilisateur { get; set; }
    }
}
