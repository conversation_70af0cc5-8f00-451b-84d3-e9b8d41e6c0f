using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour la gestion des communications
    /// </summary>
    public class CommunicationRepository : BaseRepository<Communication>, ICommunicationRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public CommunicationRepository(DatabaseConnection dbConnection) : base(dbConnection, "Communications")
        {
        }

        /// <summary>
        /// Récupère les communications par facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des communications pour la facture spécifiée</returns>
        public async Task<IEnumerable<Communication>> GetByFactureIdAsync(int factureId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE FactureId = @FactureId AND EstActif = 1 ORDER BY DateCommunication DESC";
                return await connection.QueryAsync<Communication>(query, new { FactureId = factureId });
            }
        }

        /// <summary>
        /// Récupère les communications par client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des communications pour le client spécifié</returns>
        public async Task<IEnumerable<Communication>> GetByClientIdAsync(int clientId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT c.*
                    FROM Communications c
                    INNER JOIN Factures f ON c.FactureId = f.Id
                    WHERE f.ClientId = @ClientId AND c.EstActif = 1
                    ORDER BY c.DateCommunication DESC";
                return await connection.QueryAsync<Communication>(query, new { ClientId = clientId });
            }
        }

        /// <summary>
        /// Récupère les communications par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des communications pour l'utilisateur spécifié</returns>
        public async Task<IEnumerable<Communication>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE UtilisateurId = @UtilisateurId AND EstActif = 1 ORDER BY DateCommunication DESC";
                return await connection.QueryAsync<Communication>(query, new { UtilisateurId = utilisateurId });
            }
        }

        /// <summary>
        /// Récupère les communications par type
        /// </summary>
        /// <param name="type">Type de communication</param>
        /// <returns>Liste des communications du type spécifié</returns>
        public async Task<IEnumerable<Communication>> GetByTypeAsync(string type)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE Type = @Type AND EstActif = 1 ORDER BY DateCommunication DESC";
                return await connection.QueryAsync<Communication>(query, new { Type = type });
            }
        }

        /// <summary>
        /// Récupère les communications par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des communications pour la période spécifiée</returns>
        public async Task<IEnumerable<Communication>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE DateCommunication BETWEEN @DateDebut AND @DateFin AND EstActif = 1 ORDER BY DateCommunication DESC";
                return await connection.QueryAsync<Communication>(query, new { DateDebut = dateDebut, DateFin = dateFin });
            }
        }

        /// <summary>
        /// Récupère les communications nécessitant un suivi
        /// </summary>
        /// <returns>Liste des communications nécessitant un suivi</returns>
        public async Task<IEnumerable<Communication>> GetRequiringSuiviAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE SuiviNecessaire = 1 AND EstActif = 1 ORDER BY DateSuivi";
                return await connection.QueryAsync<Communication>(query);
            }
        }

        /// <summary>
        /// Récupère les communications avec leurs relations (facture, utilisateur, etc.)
        /// </summary>
        /// <returns>Liste des communications avec leurs relations</returns>
        public async Task<IEnumerable<Communication>> GetWithRelationsAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT c.*, f.*, u.*, cc.*
                    FROM Communications c
                    LEFT JOIN Factures f ON c.FactureId = f.Id
                    LEFT JOIN Utilisateurs u ON c.UtilisateurId = u.Id
                    LEFT JOIN ContactsClient cc ON c.ContactClientId = cc.Id
                    WHERE c.EstActif = 1
                    ORDER BY c.DateCommunication DESC";

                var communicationDict = new Dictionary<int, Communication>();

                await connection.QueryAsync<Communication, Facture, Utilisateur, ContactClient, Communication>(
                    query,
                    (communication, facture, utilisateur, contactClient) =>
                    {
                        if (!communicationDict.TryGetValue(communication.Id, out var communicationEntry))
                        {
                            communicationEntry = communication;
                            communicationDict.Add(communication.Id, communicationEntry);
                        }

                        if (facture != null)
                            communicationEntry.Facture = facture;

                        if (utilisateur != null)
                            communicationEntry.Utilisateur = utilisateur;

                        if (contactClient != null)
                            communicationEntry.ContactClient = contactClient;

                        return communicationEntry;
                    },
                    splitOn: "Id,Id,Id");

                return communicationDict.Values;
            }
        }

        /// <summary>
        /// Marque une communication comme lue
        /// </summary>
        /// <param name="id">Identifiant de la communication</param>
        /// <param name="dateLecture">Date de lecture</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> MarkAsReadAsync(int id, DateTime dateLecture)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $@"
                    UPDATE {_tableName}
                    SET EstLu = 1,
                        DateLecture = @DateLecture,
                        DateModification = @DateModification
                    WHERE Id = @Id AND EstActif = 1";

                var parameters = new
                {
                    Id = id,
                    DateLecture = dateLecture,
                    DateModification = DateTime.Now
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }

        /// <summary>
        /// Met à jour le statut de suivi d'une communication
        /// </summary>
        /// <param name="id">Identifiant de la communication</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="notesSuivi">Notes de suivi</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateSuiviAsync(int id, bool suiviNecessaire, DateTime? dateSuivi, string notesSuivi, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $@"
                    UPDATE {_tableName}
                    SET SuiviNecessaire = @SuiviNecessaire,
                        DateSuivi = @DateSuivi,
                        NotesSuivi = @NotesSuivi,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id AND EstActif = 1";

                var parameters = new
                {
                    Id = id,
                    SuiviNecessaire = suiviNecessaire,
                    DateSuivi = dateSuivi,
                    NotesSuivi = notesSuivi,
                    DateModification = DateTime.Now,
                    ModifiePar = modifiePar
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }
    }
}
