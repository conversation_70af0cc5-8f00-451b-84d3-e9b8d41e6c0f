using RecouvreX.Models.Reporting;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des rapports personnalisés
    /// </summary>
    public interface IRapportPersonnaliseService
    {
        /// <summary>
        /// Récupère tous les rapports personnalisés
        /// </summary>
        /// <returns>Liste des rapports personnalisés</returns>
        Task<IEnumerable<RapportPersonnalise>> GetAllAsync();

        /// <summary>
        /// Récupère les rapports personnalisés d'un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des rapports personnalisés de l'utilisateur</returns>
        Task<IEnumerable<RapportPersonnalise>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère un rapport personnalisé par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du rapport</param>
        /// <returns>Rapport personnalisé</returns>
        Task<RapportPersonnalise> GetByIdAsync(int id);

        /// <summary>
        /// Crée un nouveau rapport personnalisé
        /// </summary>
        /// <param name="rapport">Rapport personnalisé à créer</param>
        /// <param name="userId">Identifiant de l'utilisateur qui crée le rapport</param>
        /// <returns>Rapport personnalisé créé</returns>
        Task<RapportPersonnalise> CreateAsync(RapportPersonnalise rapport, int userId);

        /// <summary>
        /// Met à jour un rapport personnalisé
        /// </summary>
        /// <param name="rapport">Rapport personnalisé à mettre à jour</param>
        /// <param name="userId">Identifiant de l'utilisateur qui met à jour le rapport</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateAsync(RapportPersonnalise rapport, int userId);

        /// <summary>
        /// Supprime un rapport personnalisé
        /// </summary>
        /// <param name="id">Identifiant du rapport à supprimer</param>
        /// <param name="userId">Identifiant de l'utilisateur qui supprime le rapport</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int userId);

        /// <summary>
        /// Génère un rapport personnalisé
        /// </summary>
        /// <param name="rapportId">Identifiant du rapport à générer</param>
        /// <param name="userId">Identifiant de l'utilisateur qui génère le rapport</param>
        /// <returns>Données du rapport au format JSON</returns>
        Task<string> GenerateReportAsync(int rapportId, int userId);

        /// <summary>
        /// Génère un rapport personnalisé avec des paramètres spécifiques
        /// </summary>
        /// <param name="configuration">Configuration du rapport</param>
        /// <param name="dateDebut">Date de début (optionnelle)</param>
        /// <param name="dateFin">Date de fin (optionnelle)</param>
        /// <param name="userId">Identifiant de l'utilisateur qui génère le rapport</param>
        /// <returns>Données du rapport au format JSON</returns>
        Task<string> GenerateReportAsync(ConfigurationRapport configuration, DateTime? dateDebut, DateTime? dateFin, int userId);

        /// <summary>
        /// Exporte un rapport personnalisé
        /// </summary>
        /// <param name="rapportId">Identifiant du rapport à exporter</param>
        /// <param name="format">Format d'export (PDF, Excel, CSV)</param>
        /// <param name="filePath">Chemin du fichier de destination</param>
        /// <param name="userId">Identifiant de l'utilisateur qui exporte le rapport</param>
        /// <returns>True si l'exportation a réussi, sinon False</returns>
        Task<bool> ExportReportAsync(int rapportId, string format, string filePath, int userId);

        /// <summary>
        /// Envoie un rapport personnalisé par email
        /// </summary>
        /// <param name="rapportId">Identifiant du rapport à envoyer</param>
        /// <param name="destinataires">Liste des adresses email des destinataires</param>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="message">Corps de l'email</param>
        /// <param name="format">Format d'export (PDF, Excel, CSV)</param>
        /// <param name="userId">Identifiant de l'utilisateur qui envoie le rapport</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        Task<bool> SendReportByEmailAsync(int rapportId, List<string> destinataires, string objet, string message, string format, int userId);

        /// <summary>
        /// Planifie un rapport personnalisé pour génération périodique
        /// </summary>
        /// <param name="rapportId">Identifiant du rapport à planifier</param>
        /// <param name="frequence">Fréquence de génération</param>
        /// <param name="jour">Jour de génération (pour hebdomadaire et mensuel)</param>
        /// <param name="heure">Heure de génération</param>
        /// <param name="destinataires">Liste des adresses email des destinataires</param>
        /// <param name="format">Format d'export (PDF, Excel, CSV)</param>
        /// <param name="userId">Identifiant de l'utilisateur qui planifie le rapport</param>
        /// <returns>True si la planification a réussi, sinon False</returns>
        Task<bool> ScheduleReportAsync(int rapportId, string frequence, int? jour, TimeSpan heure, List<string> destinataires, string format, int userId);

        /// <summary>
        /// Annule la planification d'un rapport personnalisé
        /// </summary>
        /// <param name="rapportId">Identifiant du rapport</param>
        /// <param name="userId">Identifiant de l'utilisateur qui annule la planification</param>
        /// <returns>True si l'annulation a réussi, sinon False</returns>
        Task<bool> UnscheduleReportAsync(int rapportId, int userId);

        /// <summary>
        /// Récupère les rapports personnalisés programmés à générer
        /// </summary>
        /// <returns>Liste des rapports personnalisés programmés</returns>
        Task<IEnumerable<RapportPersonnalise>> GetScheduledReportsAsync();

        /// <summary>
        /// Récupère les types de rapports disponibles
        /// </summary>
        /// <returns>Liste des types de rapports disponibles</returns>
        Task<IEnumerable<string>> GetAvailableReportTypesAsync();

        /// <summary>
        /// Récupère les colonnes disponibles pour un type de rapport
        /// </summary>
        /// <param name="reportType">Type de rapport</param>
        /// <returns>Liste des colonnes disponibles</returns>
        Task<IEnumerable<ColonneRapport>> GetAvailableColumnsAsync(string reportType);
    }
}
