using System.ComponentModel;

namespace RecouvreX.WinForms.Forms.Administration
{
    public partial class ResetPasswordForm : Form
    {
        private readonly ErrorProvider _errorProvider = new ErrorProvider();

        public string NewPassword { get; private set; } = string.Empty;

        public ResetPasswordForm()
        {
            InitializeComponent();

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;
        }

        private void NewPasswordTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "Le mot de passe est obligatoire.");
                    e.Cancel = true;
                }
                else if (textBox.Text.Length < 8)
                {
                    _errorProvider.SetError(textBox, "Le mot de passe doit contenir au moins 8 caractères.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private void ConfirmPasswordTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                var newPasswordTextBox = this.Controls.Find("newPasswordTextBox", true).FirstOrDefault() as TextBox;
                if (newPasswordTextBox != null && textBox.Text != newPasswordTextBox.Text)
                {
                    _errorProvider.SetError(textBox, "Les mots de passe ne correspondent pas.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void OkButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant de continuer.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer le nouveau mot de passe
                var newPasswordTextBox = this.Controls.Find("newPasswordTextBox", true).FirstOrDefault() as TextBox;
                if (newPasswordTextBox != null)
                {
                    NewPassword = newPasswordTextBox.Text;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
