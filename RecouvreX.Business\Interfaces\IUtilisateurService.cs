using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des utilisateurs
    /// </summary>
    public interface IUtilisateurService
    {
        /// <summary>
        /// Récupère tous les utilisateurs
        /// </summary>
        /// <returns>Liste des utilisateurs</returns>
        Task<IEnumerable<Utilisateur>> GetAllAsync();

        /// <summary>
        /// Récupère un utilisateur par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'utilisateur</param>
        /// <returns>Utilisateur trouvé ou null</returns>
        Task<Utilisateur> GetByIdAsync(int id);

        /// <summary>
        /// Récupère un utilisateur par son nom d'utilisateur
        /// </summary>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <returns>Utilisateur trouvé ou null</returns>
        Task<Utilisateur> GetByNomUtilisateurAsync(string nomUtilisateur);

        /// <summary>
        /// Crée un nouvel utilisateur
        /// </summary>
        /// <param name="utilisateur">Utilisateur à créer</param>
        /// <param name="motDePasse">Mot de passe en clair</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Utilisateur créé avec son identifiant généré</returns>
        Task<Utilisateur> CreateAsync(Utilisateur utilisateur, string motDePasse, int creePar);

        /// <summary>
        /// Met à jour un utilisateur existant
        /// </summary>
        /// <param name="utilisateur">Utilisateur à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Utilisateur mis à jour</returns>
        Task<Utilisateur> UpdateAsync(Utilisateur utilisateur, int modifiePar);

        /// <summary>
        /// Supprime un utilisateur
        /// </summary>
        /// <param name="id">Identifiant de l'utilisateur à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Récupère les utilisateurs par rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Liste des utilisateurs ayant le rôle spécifié</returns>
        Task<IEnumerable<Utilisateur>> GetByRoleIdAsync(int roleId);

        /// <summary>
        /// Récupère les utilisateurs par rôle (nom du rôle)
        /// </summary>
        /// <param name="roleName">Nom du rôle</param>
        /// <returns>Liste des utilisateurs ayant le rôle spécifié</returns>
        Task<IEnumerable<Utilisateur>> GetByRoleNameAsync(string roleName);

        /// <summary>
        /// Recherche des utilisateurs par nom ou email
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des utilisateurs correspondant au terme de recherche</returns>
        Task<IEnumerable<Utilisateur>> SearchAsync(string searchTerm);

        /// <summary>
        /// Réinitialise le mot de passe d'un utilisateur
        /// </summary>
        /// <param name="id">Identifiant de l'utilisateur</param>
        /// <param name="nouveauMotDePasse">Nouveau mot de passe en clair</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la réinitialisation a réussi, sinon False</returns>
        Task<bool> ResetPasswordAsync(int id, string nouveauMotDePasse, int modifiePar);

        /// <summary>
        /// Active ou désactive un utilisateur
        /// </summary>
        /// <param name="id">Identifiant de l'utilisateur</param>
        /// <param name="estActif">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la modification a réussi, sinon False</returns>
        Task<bool> SetActiveStatusAsync(int id, bool estActif, int modifiePar);
    }
}
