using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des clients
    /// </summary>
    public interface IClientService
    {
        /// <summary>
        /// Récupère tous les clients
        /// </summary>
        /// <returns>Liste des clients</returns>
        Task<IEnumerable<Client>> GetAllAsync();

        /// <summary>
        /// Récupère un client par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du client</param>
        /// <returns>Client trouvé ou null</returns>
        Task<Client> GetByIdAsync(int id);

        /// <summary>
        /// Récupère un client par son code
        /// </summary>
        /// <param name="code">Code du client</param>
        /// <returns>Client trouvé ou null</returns>
        Task<Client> GetByCodeAsync(string code);

        /// <summary>
        /// Récupère un client avec ses contacts
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Client avec ses contacts</returns>
        Task<Client> GetWithContactsAsync(int clientId);

        /// <summary>
        /// Récupère un client avec ses factures
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Client avec ses factures</returns>
        Task<Client> GetWithFacturesAsync(int clientId);

        /// <summary>
        /// Récupère tous les clients d'un commercial
        /// </summary>
        /// <param name="commercialId">Identifiant du commercial</param>
        /// <returns>Liste des clients du commercial</returns>
        Task<IEnumerable<Client>> GetByCommercialIdAsync(int commercialId);

        /// <summary>
        /// Recherche des clients par nom ou raison sociale
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des clients correspondant au terme de recherche</returns>
        Task<IEnumerable<Client>> SearchByNameAsync(string searchTerm);

        /// <summary>
        /// Crée un nouveau client
        /// </summary>
        /// <param name="client">Client à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Client créé avec son identifiant généré</returns>
        Task<Client> CreateAsync(Client client, int creePar);

        /// <summary>
        /// Met à jour un client existant
        /// </summary>
        /// <param name="client">Client à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Client mis à jour</returns>
        Task<Client> UpdateAsync(Client client, int modifiePar);

        /// <summary>
        /// Supprime un client
        /// </summary>
        /// <param name="id">Identifiant du client à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Ajoute un contact à un client
        /// </summary>
        /// <param name="contact">Contact à ajouter</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact ajouté avec son identifiant généré</returns>
        Task<Contact> AddContactAsync(Contact contact, int creePar);

        /// <summary>
        /// Met à jour un contact
        /// </summary>
        /// <param name="contact">Contact à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact mis à jour</returns>
        Task<Contact> UpdateContactAsync(Contact contact, int modifiePar);

        /// <summary>
        /// Supprime un contact
        /// </summary>
        /// <param name="contactId">Identifiant du contact à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteContactAsync(int contactId, int supprimePar);

        /// <summary>
        /// Récupère les clients avec des factures en retard
        /// </summary>
        /// <returns>Liste des clients avec des factures en retard</returns>
        Task<IEnumerable<Client>> GetWithOverdueInvoicesAsync();

        /// <summary>
        /// Calcule le solde d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Solde actuel du client</returns>
        Task<decimal> CalculateSoldeAsync(int clientId);

        /// <summary>
        /// Récupère les clients par segment
        /// </summary>
        /// <param name="segment">Segment de client</param>
        /// <returns>Liste des clients du segment spécifié</returns>
        Task<IEnumerable<Client>> GetBySegmentAsync(SegmentClient segment);

        /// <summary>
        /// Met à jour le segment d'un client manuellement
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="segment">Nouveau segment</param>
        /// <param name="commentaire">Commentaire sur la segmentation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Client mis à jour</returns>
        Task<Client> UpdateSegmentAsync(int clientId, SegmentClient segment, string commentaire, int modifiePar);

        /// <summary>
        /// Segmente automatiquement tous les clients
        /// </summary>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre de clients segmentés</returns>
        Task<int> SegmenterClientsAutomatiquementAsync(int modifiePar);

        /// <summary>
        /// Récupère la répartition des clients par segment
        /// </summary>
        /// <returns>Dictionnaire avec le segment comme clé et le nombre de clients comme valeur</returns>
        Task<Dictionary<SegmentClient, int>> GetClientDistributionBySegmentAsync();
    }
}
