using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion du journal d'audit
    /// </summary>
    public class JournalAuditService : IJournalAuditService
    {
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="journalAuditRepository">Repository du journal d'audit</param>
        public JournalAuditService(IJournalAuditRepository journalAuditRepository)
        {
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère toutes les entrées du journal d'audit
        /// </summary>
        /// <returns>Liste des entrées du journal</returns>
        public async Task<IEnumerable<JournalAudit>> GetAllAsync()
        {
            return await _journalAuditRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère une entrée du journal par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'entrée</param>
        /// <returns>Entrée trouvée ou null</returns>
        public async Task<JournalAudit> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _journalAuditRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère les entrées du journal par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des entrées de l'utilisateur</returns>
        public async Task<IEnumerable<JournalAudit>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            if (utilisateurId <= 0)
                return new List<JournalAudit>();

            return await _journalAuditRepository.GetByUtilisateurIdAsync(utilisateurId);
        }

        /// <summary>
        /// Récupère les entrées du journal par type d'action
        /// </summary>
        /// <param name="typeAction">Type d'action</param>
        /// <returns>Liste des entrées du type spécifié</returns>
        public async Task<IEnumerable<JournalAudit>> GetByTypeActionAsync(string typeAction)
        {
            if (string.IsNullOrEmpty(typeAction))
                return new List<JournalAudit>();

            return await _journalAuditRepository.GetByTypeActionAsync(typeAction);
        }

        /// <summary>
        /// Récupère les entrées du journal par entité
        /// </summary>
        /// <param name="typeEntite">Type d'entité</param>
        /// <param name="entiteId">Identifiant de l'entité</param>
        /// <returns>Liste des entrées concernant l'entité spécifiée</returns>
        public async Task<IEnumerable<JournalAudit>> GetByEntityAsync(string typeEntite, int entiteId)
        {
            if (string.IsNullOrEmpty(typeEntite) || entiteId <= 0)
                return new List<JournalAudit>();

            return await _journalAuditRepository.GetByEntityAsync(typeEntite, entiteId);
        }

        /// <summary>
        /// Récupère les entrées du journal par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des entrées dans la période spécifiée</returns>
        public async Task<IEnumerable<JournalAudit>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            return await _journalAuditRepository.GetByPeriodAsync(dateDebut, dateFin);
        }

        /// <summary>
        /// Recherche dans le journal d'audit
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des entrées correspondant au terme de recherche</returns>
        public async Task<IEnumerable<JournalAudit>> SearchAsync(string searchTerm)
        {
            if (string.IsNullOrEmpty(searchTerm))
                return new List<JournalAudit>();

            return await _journalAuditRepository.SearchAsync(searchTerm);
        }

        /// <summary>
        /// Ajoute une entrée dans le journal d'audit
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <param name="nomUtilisateur">Nom d'utilisateur qui effectue l'action</param>
        /// <param name="typeAction">Type d'action (Création, Modification, Suppression, etc.)</param>
        /// <param name="typeEntite">Type d'entité concernée</param>
        /// <param name="entiteId">Identifiant de l'entité concernée</param>
        /// <param name="description">Description de l'action</param>
        /// <param name="donneesAvant">Données avant modification (format JSON)</param>
        /// <param name="donneesApres">Données après modification (format JSON)</param>
        /// <param name="adresseIP">Adresse IP de l'utilisateur</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        public async Task<JournalAudit> AddEntryAsync(
            int utilisateurId,
            string nomUtilisateur,
            string typeAction,
            string typeEntite,
            int? entiteId,
            string description,
            string donneesAvant = null,
            string donneesApres = null,
            string adresseIP = null)
        {
            if (utilisateurId <= 0 || string.IsNullOrEmpty(nomUtilisateur) || string.IsNullOrEmpty(typeAction))
                throw new ArgumentException("Paramètres invalides pour l'ajout d'une entrée dans le journal d'audit");

            var journalAudit = new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = utilisateurId,
                NomUtilisateur = nomUtilisateur,
                TypeAction = typeAction,
                TypeEntite = typeEntite,
                EntiteId = entiteId,
                Description = description,
                DonneesAvant = donneesAvant,
                DonneesApres = donneesApres,
                AdresseIP = adresseIP
            };

            return await _journalAuditRepository.AddAsync(journalAudit);
        }

        /// <summary>
        /// Journalise une action de connexion
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="adresseIP">Adresse IP de l'utilisateur</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        public async Task<JournalAudit> LogLoginAsync(int utilisateurId, string nomUtilisateur, string adresseIP)
        {
            return await AddEntryAsync(
                utilisateurId,
                nomUtilisateur,
                TypeAudit.Connexion,
                "Utilisateur",
                utilisateurId,
                $"Connexion de l'utilisateur {nomUtilisateur}",
                null,
                null,
                adresseIP);
        }

        /// <summary>
        /// Journalise une action de déconnexion
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="adresseIP">Adresse IP de l'utilisateur</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        public async Task<JournalAudit> LogLogoutAsync(int utilisateurId, string nomUtilisateur, string adresseIP)
        {
            return await AddEntryAsync(
                utilisateurId,
                nomUtilisateur,
                TypeAudit.Deconnexion,
                "Utilisateur",
                utilisateurId,
                $"Déconnexion de l'utilisateur {nomUtilisateur}",
                null,
                null,
                adresseIP);
        }

        /// <summary>
        /// Journalise une action de création d'entité
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="typeEntite">Type d'entité créée</param>
        /// <param name="entiteId">Identifiant de l'entité créée</param>
        /// <param name="description">Description de l'entité créée</param>
        /// <param name="donneesEntite">Données de l'entité créée (format JSON)</param>
        /// <param name="adresseIP">Adresse IP de l'utilisateur</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        public async Task<JournalAudit> LogCreationAsync(
            int utilisateurId,
            string nomUtilisateur,
            string typeEntite,
            int entiteId,
            string description,
            string donneesEntite,
            string adresseIP = null)
        {
            return await AddEntryAsync(
                utilisateurId,
                nomUtilisateur,
                TypeAudit.Creation,
                typeEntite,
                entiteId,
                description,
                null,
                donneesEntite,
                adresseIP);
        }

        /// <summary>
        /// Journalise une action de modification d'entité
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="typeEntite">Type d'entité modifiée</param>
        /// <param name="entiteId">Identifiant de l'entité modifiée</param>
        /// <param name="description">Description de la modification</param>
        /// <param name="donneesAvant">Données avant modification (format JSON)</param>
        /// <param name="donneesApres">Données après modification (format JSON)</param>
        /// <param name="adresseIP">Adresse IP de l'utilisateur</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        public async Task<JournalAudit> LogModificationAsync(
            int utilisateurId,
            string nomUtilisateur,
            string typeEntite,
            int entiteId,
            string description,
            string donneesAvant,
            string donneesApres,
            string adresseIP = null)
        {
            return await AddEntryAsync(
                utilisateurId,
                nomUtilisateur,
                TypeAudit.Modification,
                typeEntite,
                entiteId,
                description,
                donneesAvant,
                donneesApres,
                adresseIP);
        }

        /// <summary>
        /// Journalise une action de suppression d'entité
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="typeEntite">Type d'entité supprimée</param>
        /// <param name="entiteId">Identifiant de l'entité supprimée</param>
        /// <param name="description">Description de l'entité supprimée</param>
        /// <param name="donneesEntite">Données de l'entité supprimée (format JSON)</param>
        /// <param name="adresseIP">Adresse IP de l'utilisateur</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        public async Task<JournalAudit> LogSuppressionAsync(
            int utilisateurId,
            string nomUtilisateur,
            string typeEntite,
            int entiteId,
            string description,
            string donneesEntite,
            string adresseIP = null)
        {
            return await AddEntryAsync(
                utilisateurId,
                nomUtilisateur,
                TypeAudit.Suppression,
                typeEntite,
                entiteId,
                description,
                donneesEntite,
                null,
                adresseIP);
        }
    }
}
