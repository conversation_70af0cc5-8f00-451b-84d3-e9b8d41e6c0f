using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.PlansPaiement
{
    public partial class PlanPaiementDetailsForm : Form
    {
        private readonly IPlanPaiementService _planPaiementService;
        private readonly IClientService _clientService;
        private readonly IUtilisateurService _utilisateurService;
        private readonly IAuthenticationService _authenticationService;
        private readonly IServiceProvider _serviceProvider;
        private readonly int _currentUserId;
        private readonly int _planPaiementId;
        private PlanPaiement? _planPaiement;
        private Client? _client;
        private Utilisateur? _responsable;
        private List<EcheancePaiement> _echeances = new List<EcheancePaiement>();
        private List<Facture> _factures = new List<Facture>();
        private DataTable _echeancesDataTable = new DataTable();
        private TableLayoutPanel mainPanel;
        private Label titleLabel;
        private TableLayoutPanel infoPanel;
        private TabControl tabControl;
        private TabPage echeancesTab;
        private TableLayoutPanel echeancesPanel;
        private TableLayoutPanel echeancesButtonsPanel;
        private Button markAsPaidButton;
        private Button editEcheanceButton;
        private DataGridView echeancesDataGridView;
        private TabPage facturesTab;
        private DataGridView facturesDataGridView;
        private TabPage notesTab;
        private TextBox notesTextBox;
        private TableLayoutPanel buttonsPanel;
        private Button generateAccordButton;
        private Button editButton;
        private Button closeButton;
        private DataTable _facturesDataTable = new DataTable();

        public PlanPaiementDetailsForm(
            IPlanPaiementService planPaiementService,
            IClientService clientService,
            IUtilisateurService utilisateurService,
            IAuthenticationService authenticationService,
            IServiceProvider serviceProvider,
            int planPaiementId)
        {
            _planPaiementService = planPaiementService ?? throw new ArgumentNullException(nameof(planPaiementService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _currentUserId = 1; // Valeur par défaut, à remplacer par la méthode appropriée
            _planPaiementId = planPaiementId;

            InitializeComponent();
            InitializeDataTables();
            LoadData();
        }

        private void InitializeComponent()
        {
            mainPanel = new TableLayoutPanel();
            titleLabel = new Label();
            infoPanel = new TableLayoutPanel();
            tabControl = new TabControl();
            echeancesTab = new TabPage();
            echeancesPanel = new TableLayoutPanel();
            echeancesButtonsPanel = new TableLayoutPanel();
            markAsPaidButton = new Button();
            editEcheanceButton = new Button();
            echeancesDataGridView = new DataGridView();
            facturesTab = new TabPage();
            facturesDataGridView = new DataGridView();
            notesTab = new TabPage();
            notesTextBox = new TextBox();
            buttonsPanel = new TableLayoutPanel();
            generateAccordButton = new Button();
            editButton = new Button();
            closeButton = new Button();
            mainPanel.SuspendLayout();
            tabControl.SuspendLayout();
            echeancesTab.SuspendLayout();
            echeancesPanel.SuspendLayout();
            echeancesButtonsPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)echeancesDataGridView).BeginInit();
            facturesTab.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)facturesDataGridView).BeginInit();
            notesTab.SuspendLayout();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            mainPanel.Controls.Add(titleLabel, 0, 0);
            mainPanel.Controls.Add(infoPanel, 0, 1);
            mainPanel.Controls.Add(tabControl, 0, 2);
            mainPanel.Location = new Point(0, 12);
            mainPanel.Name = "mainPanel";
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 72F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 8F));
            mainPanel.Size = new Size(972, 555);
            mainPanel.TabIndex = 0;
            // 
            // titleLabel
            // 
            titleLabel.Location = new Point(3, 0);
            titleLabel.Name = "titleLabel";
            titleLabel.Size = new Size(100, 20);
            titleLabel.TabIndex = 0;
            // 
            // infoPanel
            // 
            infoPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            infoPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            infoPanel.Location = new Point(3, 23);
            infoPanel.Name = "infoPanel";
            infoPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            infoPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            infoPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            infoPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            infoPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            infoPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            infoPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            infoPanel.Size = new Size(685, 66);
            infoPanel.TabIndex = 1;
            // 
            // tabControl
            // 
            tabControl.Controls.Add(echeancesTab);
            tabControl.Controls.Add(facturesTab);
            tabControl.Controls.Add(notesTab);
            tabControl.Location = new Point(3, 95);
            tabControl.Name = "tabControl";
            tabControl.SelectedIndex = 0;
            tabControl.Size = new Size(966, 457);
            tabControl.TabIndex = 2;
            // 
            // echeancesTab
            // 
            echeancesTab.Controls.Add(echeancesButtonsPanel);
            echeancesTab.Controls.Add(echeancesPanel);
            echeancesTab.Location = new Point(4, 24);
            echeancesTab.Name = "echeancesTab";
            echeancesTab.Size = new Size(958, 429);
            echeancesTab.TabIndex = 0;
            echeancesTab.Text = "Échéances";
            // 
            // echeancesPanel
            // 
            echeancesPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            echeancesPanel.Controls.Add(echeancesDataGridView, 0, 1);
            echeancesPanel.Location = new Point(0, 3);
            echeancesPanel.Name = "echeancesPanel";
            echeancesPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 47F));
            echeancesPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 14F));
            echeancesPanel.Size = new Size(958, 520);
            echeancesPanel.TabIndex = 0;
            // 
            // echeancesButtonsPanel
            // 
            echeancesButtonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            echeancesButtonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            echeancesButtonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            echeancesButtonsPanel.Controls.Add(markAsPaidButton, 1, 0);
            echeancesButtonsPanel.Controls.Add(editEcheanceButton, 2, 0);
            echeancesButtonsPanel.Location = new Point(563, 534);
            echeancesButtonsPanel.Name = "echeancesButtonsPanel";
            echeancesButtonsPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            echeancesButtonsPanel.Size = new Size(200, 41);
            echeancesButtonsPanel.TabIndex = 0;
            // 
            // markAsPaidButton
            // 
            markAsPaidButton.Location = new Point(23, 3);
            markAsPaidButton.Name = "markAsPaidButton";
            markAsPaidButton.Size = new Size(14, 23);
            markAsPaidButton.TabIndex = 0;
            markAsPaidButton.Click += MarkAsPaidButton_Click;
            // 
            // editEcheanceButton
            // 
            editEcheanceButton.Location = new Point(43, 3);
            editEcheanceButton.Name = "editEcheanceButton";
            editEcheanceButton.Size = new Size(75, 23);
            editEcheanceButton.TabIndex = 1;
            editEcheanceButton.Click += EditEcheanceButton_Click;
            // 
            // echeancesDataGridView
            // 
            echeancesDataGridView.Location = new Point(3, 50);
            echeancesDataGridView.Name = "echeancesDataGridView";
            echeancesDataGridView.Size = new Size(952, 383);
            echeancesDataGridView.TabIndex = 1;
            // 
            // facturesTab
            // 
            facturesTab.Controls.Add(facturesDataGridView);
            facturesTab.Location = new Point(4, 24);
            facturesTab.Name = "facturesTab";
            facturesTab.Size = new Size(958, 429);
            facturesTab.TabIndex = 1;
            facturesTab.Text = "Factures associées";
            // 
            // facturesDataGridView
            // 
            facturesDataGridView.Location = new Point(0, 0);
            facturesDataGridView.Name = "facturesDataGridView";
            facturesDataGridView.Size = new Size(955, 572);
            facturesDataGridView.TabIndex = 0;
            // 
            // notesTab
            // 
            notesTab.Controls.Add(notesTextBox);
            notesTab.Location = new Point(4, 24);
            notesTab.Name = "notesTab";
            notesTab.Size = new Size(958, 429);
            notesTab.TabIndex = 2;
            notesTab.Text = "Notes";
            // 
            // notesTextBox
            // 
            notesTextBox.Location = new Point(0, 0);
            notesTextBox.Multiline = true;
            notesTextBox.Name = "notesTextBox";
            notesTextBox.Size = new Size(958, 575);
            notesTextBox.TabIndex = 0;
            // 
            // buttonsPanel
            // 
            buttonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 33F));
            buttonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 78F));
            buttonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 69F));
            buttonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 49F));
            buttonsPanel.Controls.Add(generateAccordButton, 1, 0);
            buttonsPanel.Controls.Add(editButton, 2, 0);
            buttonsPanel.Controls.Add(closeButton, 3, 0);
            buttonsPanel.Location = new Point(647, 604);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            buttonsPanel.Size = new Size(322, 58);
            buttonsPanel.TabIndex = 1;
            // 
            // generateAccordButton
            // 
            generateAccordButton.Location = new Point(36, 3);
            generateAccordButton.Name = "generateAccordButton";
            generateAccordButton.Size = new Size(72, 23);
            generateAccordButton.TabIndex = 0;
            generateAccordButton.Click += GenerateAccordButton_Click;
            // 
            // editButton
            // 
            editButton.Location = new Point(114, 3);
            editButton.Name = "editButton";
            editButton.Size = new Size(63, 23);
            editButton.TabIndex = 1;
            editButton.Click += EditButton_Click;
            // 
            // closeButton
            // 
            closeButton.Location = new Point(183, 3);
            closeButton.Name = "closeButton";
            closeButton.Size = new Size(75, 23);
            closeButton.TabIndex = 2;
            closeButton.Click += CloseButton_Click;
            // 
            // PlanPaiementDetailsForm
            // 
            ClientSize = new Size(984, 661);
            Controls.Add(mainPanel);
            Controls.Add(buttonsPanel);
            Name = "PlanPaiementDetailsForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "Détails du plan de paiement";
            mainPanel.ResumeLayout(false);
            tabControl.ResumeLayout(false);
            echeancesTab.ResumeLayout(false);
            echeancesPanel.ResumeLayout(false);
            echeancesButtonsPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)echeancesDataGridView).EndInit();
            facturesTab.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)facturesDataGridView).EndInit();
            notesTab.ResumeLayout(false);
            notesTab.PerformLayout();
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
        }

        private void InitializeDataTables()
        {
            // Table des échéances
            _echeancesDataTable = new DataTable();
            _echeancesDataTable.Columns.Add("Id", typeof(int));
            _echeancesDataTable.Columns.Add("N°", typeof(int));
            _echeancesDataTable.Columns.Add("Date échéance", typeof(DateTime));
            _echeancesDataTable.Columns.Add("Montant prévu", typeof(decimal));
            _echeancesDataTable.Columns.Add("Payée", typeof(bool));
            _echeancesDataTable.Columns.Add("Date paiement", typeof(DateTime));
            _echeancesDataTable.Columns.Add("Montant payé", typeof(decimal));
            _echeancesDataTable.Columns.Add("En retard", typeof(bool));
            _echeancesDataTable.Columns.Add("Jours retard", typeof(int));

            // Table des factures
            _facturesDataTable = new DataTable();
            _facturesDataTable.Columns.Add("Id", typeof(int));
            _facturesDataTable.Columns.Add("Numéro", typeof(string));
            _facturesDataTable.Columns.Add("Date émission", typeof(DateTime));
            _facturesDataTable.Columns.Add("Date échéance", typeof(DateTime));
            _facturesDataTable.Columns.Add("Montant TTC", typeof(decimal));
            _facturesDataTable.Columns.Add("Montant couvert", typeof(decimal));
            _facturesDataTable.Columns.Add("Statut", typeof(string));
        }

        private async void LoadData()
        {
            try
            {
                // Charger le plan de paiement avec toutes ses relations
                _planPaiement = await _planPaiementService.GetPlanCompleteAsync(_planPaiementId);
                if (_planPaiement == null)
                {
                    MessageBox.Show("Le plan de paiement demandé n'existe pas ou a été supprimé.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Charger le client
                _client = await _clientService.GetByIdAsync(_planPaiement.ClientId);

                // Charger le responsable
                _responsable = await _utilisateurService.GetByIdAsync(_planPaiement.ResponsableId);

                // Mettre à jour les informations générales
                UpdateGeneralInfo();

                // Mettre à jour les échéances
                if (_planPaiement.Echeances != null)
                {
                    _echeances = _planPaiement.Echeances.ToList();
                    UpdateEcheancesDataTable();
                }

                // Mettre à jour les factures
                if (_planPaiement.Factures != null)
                {
                    _factures = _planPaiement.Factures.ToList();
                    UpdateFacturesDataTable();
                }

                // Mettre à jour les notes
                var notesTextBox = Controls.Find("notesTextBox", true).FirstOrDefault() as TextBox;
                if (notesTextBox != null && _planPaiement.Notes != null)
                {
                    notesTextBox.Text = _planPaiement.Notes;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données du plan de paiement {PlanPaiementId}", _planPaiementId);
                MessageBox.Show($"Erreur lors du chargement des données : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateGeneralInfo()
        {
            if (_planPaiement == null)
                return;

            // Mettre à jour le titre
            var titleLabel = Controls.Find("titleLabel", true).FirstOrDefault() as Label;
            if (titleLabel != null)
            {
                titleLabel.Text = $"Plan de paiement - {_planPaiement.Reference}";
            }

            // Mettre à jour les informations générales
            var referenceLabel = Controls.Find("referenceLabel", true).FirstOrDefault() as Label;
            if (referenceLabel != null)
            {
                referenceLabel.Text = _planPaiement.Reference;
            }

            var clientLabel = Controls.Find("clientLabel", true).FirstOrDefault() as Label;
            if (clientLabel != null && _client != null)
            {
                clientLabel.Text = _client.RaisonSociale;
            }

            var dateCreationLabel = Controls.Find("dateCreationLabel", true).FirstOrDefault() as Label;
            if (dateCreationLabel != null)
            {
                dateCreationLabel.Text = _planPaiement.DateCreationPlan.ToString("dd/MM/yyyy");
            }

            var dateDebutLabel = Controls.Find("dateDebutLabel", true).FirstOrDefault() as Label;
            if (dateDebutLabel != null)
            {
                dateDebutLabel.Text = _planPaiement.DateDebut.ToString("dd/MM/yyyy");
            }

            var dateFinLabel = Controls.Find("dateFinLabel", true).FirstOrDefault() as Label;
            if (dateFinLabel != null)
            {
                dateFinLabel.Text = _planPaiement.DateFinPrevue.ToString("dd/MM/yyyy");
            }

            var montantTotalLabel = Controls.Find("montantTotalLabel", true).FirstOrDefault() as Label;
            if (montantTotalLabel != null)
            {
                montantTotalLabel.Text = $"{_planPaiement.MontantTotal:C2} (Payé: {_planPaiement.MontantPaye:C2}, Restant: {_planPaiement.MontantRestant:C2})";
            }

            var statutLabel = Controls.Find("statutLabel", true).FirstOrDefault() as Label;
            if (statutLabel != null)
            {
                statutLabel.Text = _planPaiement.Statut;

                // Colorer le statut
                switch (_planPaiement.Statut)
                {
                    case "En cours":
                        statutLabel.ForeColor = Color.Blue;
                        break;
                    case "Terminé":
                        statutLabel.ForeColor = Color.Green;
                        break;
                    case "En retard":
                        statutLabel.ForeColor = Color.Red;
                        break;
                    case "Annulé":
                        statutLabel.ForeColor = Color.Gray;
                        break;
                    default:
                        statutLabel.ForeColor = SystemColors.ControlText;
                        break;
                }
            }
        }

        private void UpdateEcheancesDataTable()
        {
            _echeancesDataTable.Clear();

            foreach (var echeance in _echeances.OrderBy(e => e.NumeroOrdre))
            {
                _echeancesDataTable.Rows.Add(
                    echeance.Id,
                    echeance.NumeroOrdre,
                    echeance.DateEcheance,
                    echeance.MontantPrevu,
                    echeance.EstPayee,
                    echeance.DatePaiement,
                    echeance.MontantPaye,
                    echeance.EstEnRetard,
                    echeance.JoursRetard
                );
            }

            // Mettre à jour l'apparence du DataGridView
            var echeancesDataGridView = Controls.Find("echeancesDataGridView", true).FirstOrDefault() as DataGridView;
            if (echeancesDataGridView != null)
            {
                echeancesDataGridView.ClearSelection();

                // Appliquer une mise en forme conditionnelle
                foreach (DataGridViewRow row in echeancesDataGridView.Rows)
                {
                    bool estPayee = (bool)row.Cells["Payée"].Value;
                    bool estEnRetard = (bool)row.Cells["En retard"].Value;

                    if (estPayee)
                    {
                        row.DefaultCellStyle.BackColor = Color.LightGreen;
                    }
                    else if (estEnRetard)
                    {
                        row.DefaultCellStyle.BackColor = Color.LightPink;
                    }
                }
            }
        }

        private void UpdateFacturesDataTable()
        {
            _facturesDataTable.Clear();

            foreach (var facture in _factures)
            {
                // Récupérer le montant couvert pour cette facture
                decimal montantCouvert = 0;
                if (_planPaiement?.Factures != null)
                {
                    var association = _planPaiement.Factures.FirstOrDefault(f => f.Id == facture.Id);
                    if (association != null)
                    {
                        // Ici, nous devrions avoir accès au montant couvert, mais il n'est pas directement accessible
                        // dans le modèle actuel. Dans une implémentation complète, il faudrait l'ajouter.
                        montantCouvert = facture.MontantRestant; // Approximation
                    }
                }

                _facturesDataTable.Rows.Add(
                    facture.Id,
                    facture.Numero,
                    facture.DateEmission,
                    facture.DateEcheance,
                    facture.MontantTTC,
                    montantCouvert,
                    facture.Statut
                );
            }
        }

        private void MarkAsPaidButton_Click(object sender, EventArgs e)
        {
            var echeancesDataGridView = Controls.Find("echeancesDataGridView", true).FirstOrDefault() as DataGridView;
            if (echeancesDataGridView == null || echeancesDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("Veuillez sélectionner une échéance à marquer comme payée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            int echeanceId = Convert.ToInt32(echeancesDataGridView.SelectedRows[0].Cells["Id"].Value);
            var echeance = _echeances.FirstOrDefault(e => e.Id == echeanceId);
            if (echeance == null)
                return;

            if (echeance.EstPayee)
            {
                MessageBox.Show("Cette échéance est déjà marquée comme payée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // Ouvrir un formulaire pour saisir les détails du paiement
                using (var form = new EcheancePaiementForm(_planPaiementService, _client, echeance))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les données
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du marquage de l'échéance comme payée");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditEcheanceButton_Click(object sender, EventArgs e)
        {
            var echeancesDataGridView = Controls.Find("echeancesDataGridView", true).FirstOrDefault() as DataGridView;
            if (echeancesDataGridView == null || echeancesDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("Veuillez sélectionner une échéance à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            int echeanceId = Convert.ToInt32(echeancesDataGridView.SelectedRows[0].Cells["Id"].Value);
            var echeance = _echeances.FirstOrDefault(e => e.Id == echeanceId);
            if (echeance == null)
                return;

            try
            {
                // Ouvrir un formulaire pour modifier l'échéance
                using (var form = new EcheanceEditForm(_planPaiementService, echeance, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les données
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la modification de l'échéance");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new PlanPaiementEditForm(_planPaiementService, _clientService, _utilisateurService, _authenticationService, _planPaiement))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les données
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de modification du plan de paiement");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void GenerateAccordButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_planPaiement == null)
                    return;

                // Ouvrir le formulaire de génération d'accord de paiement
                using (var form = new AccordPaiementForm(
                    _planPaiementService,
                    _clientService,
                    _utilisateurService,
                    _serviceProvider.GetRequiredService<IContactClientService>(),
                    _serviceProvider.GetRequiredService<ICommunicationService>(),
                    _serviceProvider.GetRequiredService<IConfiguration>(),
                    _planPaiement.Id,
                    _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les données
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération de l'accord de paiement");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
