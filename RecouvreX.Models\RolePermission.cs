namespace RecouvreX.Models
{
    /// <summary>
    /// Représente l'association entre un rôle et une permission (relation N-N)
    /// </summary>
    public class RolePermission : BaseEntity
    {
        /// <summary>
        /// Identifiant du rôle
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// Rôle associé (navigation property)
        /// </summary>
        public Role Role { get; set; }

        /// <summary>
        /// Identifiant de la permission
        /// </summary>
        public int PermissionId { get; set; }

        /// <summary>
        /// Permission associée (navigation property)
        /// </summary>
        public Permission Permission { get; set; }
    }
}
