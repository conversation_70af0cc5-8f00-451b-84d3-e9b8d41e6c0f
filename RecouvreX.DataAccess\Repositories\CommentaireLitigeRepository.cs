using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les commentaires de litiges
    /// </summary>
    public class CommentaireLitigeRepository : ICommentaireLitigeRepository
    {
        private readonly DatabaseConnection _dbConnection;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public CommentaireLitigeRepository(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection ?? throw new ArgumentNullException(nameof(dbConnection));
        }

        /// <summary>
        /// Récupère tous les commentaires
        /// </summary>
        /// <returns>Liste des commentaires</returns>
        public async Task<IEnumerable<CommentaireLitige>> GetAllAsync()
        {
            try
            {
                using (var connection = _dbConnection.CreateConnection())
                {
                    return await connection.QueryAsync<CommentaireLitige>(
                        "SELECT * FROM CommentaireLitiges WHERE EstActif = 1 ORDER BY DateCreation DESC");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération de tous les commentaires");
                throw;
            }
        }

        /// <summary>
        /// Récupère un commentaire par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du commentaire</param>
        /// <returns>Commentaire trouvé ou null</returns>
        public async Task<CommentaireLitige> GetByIdAsync(int id)
        {
            try
            {
                using (var connection = _dbConnection.CreateConnection())
                {
                    return await connection.QueryFirstOrDefaultAsync<CommentaireLitige>(
                        "SELECT * FROM CommentaireLitiges WHERE Id = @Id AND EstActif = 1",
                        new { Id = id });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération du commentaire {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Récupère les commentaires pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des commentaires pour le litige</returns>
        public async Task<IEnumerable<CommentaireLitige>> GetByLitigeIdAsync(int litigeId)
        {
            try
            {
                using (var connection = _dbConnection.CreateConnection())
                {
                    return await connection.QueryAsync<CommentaireLitige>(
                        "SELECT * FROM CommentaireLitiges WHERE LitigeId = @LitigeId AND EstActif = 1 ORDER BY DateCreation DESC",
                        new { LitigeId = litigeId });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des commentaires pour le litige {LitigeId}", litigeId);
                throw;
            }
        }

        /// <summary>
        /// Ajoute un commentaire
        /// </summary>
        /// <param name="commentaire">Commentaire à ajouter</param>
        /// <returns>Commentaire ajouté</returns>
        public async Task<CommentaireLitige> AddAsync(CommentaireLitige commentaire)
        {
            try
            {
                using (var connection = _dbConnection.CreateConnection())
                {
                    var id = await connection.QuerySingleAsync<int>(
                        @"INSERT INTO CommentaireLitiges (LitigeId, Texte, UtilisateurId, EstActif, DateCreation, CreePar)
                          VALUES (@LitigeId, @Texte, @UtilisateurId, 1, GETDATE(), @UtilisateurId);
                          SELECT CAST(SCOPE_IDENTITY() as int)",
                        commentaire);

                    commentaire.Id = id;
                    return commentaire;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ajout du commentaire");
                throw;
            }
        }

        /// <summary>
        /// Met à jour un commentaire
        /// </summary>
        /// <param name="commentaire">Commentaire à mettre à jour</param>
        /// <returns>True si le commentaire a été mis à jour</returns>
        public async Task<bool> UpdateAsync(CommentaireLitige commentaire)
        {
            try
            {
                using (var connection = _dbConnection.CreateConnection())
                {
                    var result = await connection.ExecuteAsync(
                        @"UPDATE CommentaireLitiges
                          SET Texte = @Texte,
                              DateModification = GETDATE(),
                              ModifiePar = @UtilisateurId
                          WHERE Id = @Id",
                        commentaire);

                    return result > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour du commentaire {Id}", commentaire.Id);
                throw;
            }
        }

        /// <summary>
        /// Supprime un commentaire
        /// </summary>
        /// <param name="id">Identifiant du commentaire</param>
        /// <returns>True si le commentaire a été supprimé</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                using (var connection = _dbConnection.CreateConnection())
                {
                    var result = await connection.ExecuteAsync(
                        @"UPDATE CommentaireLitiges
                          SET EstActif = 0,
                              DateModification = GETDATE()
                          WHERE Id = @Id",
                        new { Id = id });

                    return result > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression du commentaire {Id}", id);
                throw;
            }
        }
    }
}
