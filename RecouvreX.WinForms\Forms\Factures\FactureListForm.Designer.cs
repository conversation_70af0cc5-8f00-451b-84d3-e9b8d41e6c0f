namespace RecouvreX.WinForms.Forms.Factures
{
    partial class FactureListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // FactureListForm
            //
            this.ClientSize = new System.Drawing.Size(1000, 600);
            this.Name = "FactureListForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Liste des factures";
            this.Load += new System.EventHandler(this.FactureListForm_Load);
            this.ResumeLayout(false);

            // Barre d'outils
            System.Windows.Forms.ToolStrip toolStrip = new System.Windows.Forms.ToolStrip();
            toolStrip.Name = "toolStrip";
            toolStrip.Dock = System.Windows.Forms.DockStyle.Top;
            this.Controls.Add(toolStrip);

            // Bouton Ajouter
            System.Windows.Forms.ToolStripButton addButton = new System.Windows.Forms.ToolStripButton();
            addButton.Name = "addButton";
            addButton.Text = "Ajouter";
            addButton.Image = null; // Ajouter une icône
            addButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            addButton.Click += new System.EventHandler(this.AddButton_Click);
            toolStrip.Items.Add(addButton);

            // Bouton Voir
            System.Windows.Forms.ToolStripButton viewButton = new System.Windows.Forms.ToolStripButton();
            viewButton.Name = "viewButton";
            viewButton.Text = "Voir";
            viewButton.Image = null; // Ajouter une icône
            viewButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            viewButton.Click += new System.EventHandler(this.ViewButton_Click);
            toolStrip.Items.Add(viewButton);

            // Séparateur
            toolStrip.Items.Add(new System.Windows.Forms.ToolStripSeparator());

            // Bouton Rafraîchir
            System.Windows.Forms.ToolStripButton refreshButton = new System.Windows.Forms.ToolStripButton();
            refreshButton.Name = "refreshButton";
            refreshButton.Text = "Rafraîchir";
            refreshButton.Image = null; // Ajouter une icône
            refreshButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            refreshButton.Click += new System.EventHandler(this.RefreshButton_Click);
            toolStrip.Items.Add(refreshButton);

            // Séparateur
            toolStrip.Items.Add(new System.Windows.Forms.ToolStripSeparator());

            // Filtre par statut
            System.Windows.Forms.ToolStripLabel statutLabel = new System.Windows.Forms.ToolStripLabel();
            statutLabel.Text = "Statut :";
            toolStrip.Items.Add(statutLabel);

            System.Windows.Forms.ToolStripComboBox statutComboBox = new System.Windows.Forms.ToolStripComboBox();
            statutComboBox.Name = "statutComboBox";
            statutComboBox.Width = 150;
            statutComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            statutComboBox.Items.Add("Tous");
            statutComboBox.Items.Add("En attente");
            statutComboBox.Items.Add("Payée partiellement");
            statutComboBox.Items.Add("Payée");
            statutComboBox.Items.Add("En retard");
            statutComboBox.Items.Add("Annulée");
            statutComboBox.Items.Add("Litige");
            statutComboBox.SelectedIndex = 0;
            statutComboBox.SelectedIndexChanged += new System.EventHandler(this.StatutComboBox_SelectedIndexChanged);
            toolStrip.Items.Add(statutComboBox);

            // Séparateur
            toolStrip.Items.Add(new System.Windows.Forms.ToolStripSeparator());

            // Champ de recherche
            System.Windows.Forms.ToolStripLabel searchLabel = new System.Windows.Forms.ToolStripLabel();
            searchLabel.Text = "Rechercher :";
            toolStrip.Items.Add(searchLabel);

            System.Windows.Forms.ToolStripTextBox searchTextBox = new System.Windows.Forms.ToolStripTextBox();
            searchTextBox.Name = "searchTextBox";
            searchTextBox.Width = 200;
            searchTextBox.KeyDown += new System.Windows.Forms.KeyEventHandler(this.SearchTextBox_KeyDown);
            toolStrip.Items.Add(searchTextBox);

            System.Windows.Forms.ToolStripButton searchButton = new System.Windows.Forms.ToolStripButton();
            searchButton.Name = "searchButton";
            searchButton.Text = "Rechercher";
            searchButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            searchButton.Click += new System.EventHandler(this.SearchButton_Click);
            toolStrip.Items.Add(searchButton);

            // DataGridView pour afficher les factures
            System.Windows.Forms.DataGridView dataGridView = new System.Windows.Forms.DataGridView();
            dataGridView.Name = "dataGridView";
            dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.ReadOnly = true;
            dataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;
            dataGridView.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView.RowHeadersVisible = false;
            dataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.DataGridView_CellDoubleClick);
            this.Controls.Add(dataGridView);

            // Barre d'état
            System.Windows.Forms.StatusStrip statusStrip = new System.Windows.Forms.StatusStrip();
            statusStrip.Name = "statusStrip";
            statusStrip.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Controls.Add(statusStrip);

            System.Windows.Forms.ToolStripStatusLabel countLabel = new System.Windows.Forms.ToolStripStatusLabel();
            countLabel.Name = "countLabel";
            statusStrip.Items.Add(countLabel);

            System.Windows.Forms.ToolStripStatusLabel totalLabel = new System.Windows.Forms.ToolStripStatusLabel();
            totalLabel.Name = "totalLabel";
            statusStrip.Items.Add(totalLabel);
        }

        #endregion
    }
}
