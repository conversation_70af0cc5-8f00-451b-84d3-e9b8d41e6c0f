using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.Paiements
{
    public partial class PaiementDetailsForm : Form
    {
        private readonly IPaiementService _paiementService;
        private readonly IClientService _clientService;
        private readonly IFactureService _factureService;
        private readonly int _currentUserId;
        private readonly int _paiementId;
        private Paiement? _paiement;
        private Client? _client;
        private List<FacturePaiement> _facturePaiements = new List<FacturePaiement>();
        private DataTable _facturesDataTable = new DataTable();

        public PaiementDetailsForm(IPaiementService paiementService, IClientService clientService, int currentUserId, int paiementId)
        {
            _paiementService = paiementService ?? throw new ArgumentNullException(nameof(paiementService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _factureService = null!; // À injecter
            _currentUserId = currentUserId;
            _paiementId = paiementId;

            InitializeComponent();
        }

        public PaiementDetailsForm(IPaiementService paiementService, IClientService clientService,
            IFactureService factureService, int currentUserId, int paiementId)
        {
            _paiementService = paiementService ?? throw new ArgumentNullException(nameof(paiementService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _currentUserId = currentUserId;
            _paiementId = paiementId;

            InitializeComponent();
        }



        private async void PaiementDetailsForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialiser la table de données des factures
                InitializeFacturesDataTable();

                // Charger les données du paiement
                await LoadPaiementDataAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des détails du paiement");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des détails du paiement : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeFacturesDataTable()
        {
            _facturesDataTable = new DataTable();
            _facturesDataTable.Columns.Add("FactureId", typeof(int));
            _facturesDataTable.Columns.Add("Numéro", typeof(string));
            _facturesDataTable.Columns.Add("Date Émission", typeof(DateTime));
            _facturesDataTable.Columns.Add("Date Échéance", typeof(DateTime));
            _facturesDataTable.Columns.Add("Montant TTC", typeof(decimal));
            _facturesDataTable.Columns.Add("Montant Affecté", typeof(decimal));
            _facturesDataTable.Columns.Add("Statut", typeof(string));

            var facturesDataGridView = this.Controls.Find("facturesDataGridView", true).FirstOrDefault() as DataGridView;
            if (facturesDataGridView != null)
            {
                facturesDataGridView.DataSource = _facturesDataTable;

                // Masquer la colonne FactureId
                if (facturesDataGridView.Columns["FactureId"] != null)
                    facturesDataGridView.Columns["FactureId"].Visible = false;

                // Configurer les colonnes
                if (facturesDataGridView.Columns["Date Émission"] != null)
                    facturesDataGridView.Columns["Date Émission"].DefaultCellStyle.Format = "dd/MM/yyyy";
                if (facturesDataGridView.Columns["Date Échéance"] != null)
                    facturesDataGridView.Columns["Date Échéance"].DefaultCellStyle.Format = "dd/MM/yyyy";
                if (facturesDataGridView.Columns["Montant TTC"] != null)
                    facturesDataGridView.Columns["Montant TTC"].DefaultCellStyle.Format = "C2";
                if (facturesDataGridView.Columns["Montant Affecté"] != null)
                    facturesDataGridView.Columns["Montant Affecté"].DefaultCellStyle.Format = "C2";
            }
        }

        private async Task LoadPaiementDataAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer le paiement avec ses factures associées
                _paiement = await _paiementService.GetWithFacturesAsync(_paiementId);
                if (_paiement == null)
                {
                    MessageBox.Show("Le paiement demandé n'existe pas.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Récupérer le client
                _client = await _clientService.GetByIdAsync(_paiement.ClientId);
                if (_client == null)
                {
                    MessageBox.Show("Le client associé à ce paiement n'existe pas.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Mettre à jour le titre du formulaire
                this.Text = $"Détails du paiement - {_paiement.Reference}";

                // Remplir les informations du paiement
                FillPaiementInfo(_paiement, _client);

                // Récupérer les factures associées
                _facturePaiements = _paiement.FacturePaiements?.ToList() ?? new List<FacturePaiement>();
                await UpdateFacturesDataTableAsync(_facturePaiements);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données du paiement");
                throw;
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void FillPaiementInfo(Paiement paiement, Client client)
        {
            var referenceValueLabel = this.Controls.Find("referenceValueLabel", true).FirstOrDefault() as Label;
            var clientValueLabel = this.Controls.Find("clientValueLabel", true).FirstOrDefault() as Label;
            var datePaiementValueLabel = this.Controls.Find("datePaiementValueLabel", true).FirstOrDefault() as Label;
            var montantValueLabel = this.Controls.Find("montantValueLabel", true).FirstOrDefault() as Label;
            var modePaiementValueLabel = this.Controls.Find("modePaiementValueLabel", true).FirstOrDefault() as Label;
            var referenceBancaireValueLabel = this.Controls.Find("referenceBancaireValueLabel", true).FirstOrDefault() as Label;

            if (referenceValueLabel != null) referenceValueLabel.Text = paiement.Reference;
            if (clientValueLabel != null) clientValueLabel.Text = $"{client.Code} - {client.RaisonSociale}";
            if (datePaiementValueLabel != null) datePaiementValueLabel.Text = paiement.DatePaiement.ToString("dd/MM/yyyy");
            if (montantValueLabel != null) montantValueLabel.Text = paiement.Montant.ToString("C2");
            if (modePaiementValueLabel != null) modePaiementValueLabel.Text = paiement.ModePaiement;
            if (referenceBancaireValueLabel != null) referenceBancaireValueLabel.Text = paiement.ReferenceBancaire;
        }

        private async Task UpdateFacturesDataTableAsync(List<FacturePaiement> facturePaiements)
        {
            _facturesDataTable.Clear();

            if (_factureService != null)
            {
                foreach (var facturePaiement in facturePaiements)
                {
                    var facture = await _factureService.GetByIdAsync(facturePaiement.FactureId);
                    if (facture != null)
                    {
                        _facturesDataTable.Rows.Add(
                            facture.Id,
                            facture.Numero,
                            facture.DateEmission,
                            facture.DateEcheance,
                            facture.MontantTTC,
                            facturePaiement.MontantAffecte,
                            facture.Statut
                        );
                    }
                }
            }
        }

        private async void AssociateFactureButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_paiement == null)
                {
                    MessageBox.Show("Veuillez d'abord charger un paiement.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Vérifier si le paiement a encore un montant disponible
                decimal montantDisponible = _paiement.Montant - _paiement.MontantUtilise;
                if (montantDisponible <= 0)
                {
                    MessageBox.Show("Ce paiement est déjà entièrement utilisé.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Récupérer les factures du client qui ne sont pas entièrement payées
                var factures = await _factureService.GetByClientIdAsync(_paiement.ClientId);
                var facturesNonPayees = factures.Where(f => f.Statut != "Payée").ToList();

                if (facturesNonPayees.Count == 0)
                {
                    MessageBox.Show("Il n'y a pas de factures non payées pour ce client.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Ouvrir un formulaire de sélection de facture
                using (var form = new Common.SelectionFactureForm(facturesNonPayees, montantDisponible))
                {
                    if (form.ShowDialog() == DialogResult.OK && form.SelectedFactureId.HasValue && form.MontantAffecte.HasValue)
                    {
                        int factureId = form.SelectedFactureId.Value;
                        decimal montantAffecte = form.MontantAffecte.Value;

                        // Associer la facture au paiement
                        bool success = await _paiementService.AssociateToFactureAsync(_paiement.Id, factureId, montantAffecte, _currentUserId);

                        if (success)
                        {
                            // Recharger les données
                            await LoadPaiementDataAsync();
                            MessageBox.Show("La facture a été associée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("Une erreur s'est produite lors de l'association de la facture.",
                                "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'association d'une facture");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DissociateFactureButton_Click(object sender, EventArgs e)
        {
            var facturesDataGridView = this.Controls.Find("facturesDataGridView", true).FirstOrDefault() as DataGridView;
            if (facturesDataGridView != null && facturesDataGridView.SelectedRows.Count > 0)
            {
                int factureId = Convert.ToInt32(facturesDataGridView.SelectedRows[0].Cells["FactureId"].Value);
                string factureNumero = facturesDataGridView.SelectedRows[0].Cells["Numéro"].Value.ToString();

                var result = MessageBox.Show($"Êtes-vous sûr de vouloir dissocier la facture '{factureNumero}' de ce paiement ?",
                    "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    DissociateFacture(factureId);
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une facture à dissocier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async void DissociateFacture(int factureId)
        {
            try
            {
                // Dissocier la facture du paiement
                bool success = await _paiementService.DissociateFromFactureAsync(_paiementId, factureId, _currentUserId);
                if (success)
                {
                    MessageBox.Show("La facture a été dissociée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Recharger les données
                    await LoadPaiementDataAsync();
                }
                else
                {
                    MessageBox.Show("Impossible de dissocier la facture.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la dissociation de la facture");
                MessageBox.Show($"Une erreur s'est produite lors de la dissociation de la facture : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewFactureButton_Click(object sender, EventArgs e)
        {
            var facturesDataGridView = this.Controls.Find("facturesDataGridView", true).FirstOrDefault() as DataGridView;
            if (facturesDataGridView != null && facturesDataGridView.SelectedRows.Count > 0)
            {
                int factureId = Convert.ToInt32(facturesDataGridView.SelectedRows[0].Cells["FactureId"].Value);
                ViewFacture(factureId);
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une facture à consulter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void FacturesDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int factureId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["FactureId"].Value);
                    ViewFacture(factureId);
                }
            }
        }

        private void ViewFacture(int factureId)
        {
            try
            {
                // Ouvrir le formulaire de détails de facture
                using (var form = new Factures.FactureDetailsForm(_factureService, _clientService, _currentUserId, factureId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de détails de facture");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
