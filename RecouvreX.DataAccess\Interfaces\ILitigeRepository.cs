using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des litiges
    /// </summary>
    public interface ILitigeRepository : IRepository<Litige>
    {
        /// <summary>
        /// Récupère un litige avec toutes ses informations associées
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Litige avec ses informations associées</returns>
        Task<Litige> GetWithDetailsAsync(int litigeId);

        /// <summary>
        /// Récupère tous les litiges d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des litiges de la facture</returns>
        Task<IEnumerable<Litige>> GetByFactureIdAsync(int factureId);

        /// <summary>
        /// Récupère tous les litiges d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des litiges du client</returns>
        Task<IEnumerable<Litige>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère tous les litiges par statut
        /// </summary>
        /// <param name="statut">Statut des litiges</param>
        /// <returns>Liste des litiges ayant le statut spécifié</returns>
        Task<IEnumerable<Litige>> GetByStatutAsync(string statut);

        /// <summary>
        /// Récupère tous les litiges par catégorie
        /// </summary>
        /// <param name="categorieId">Identifiant de la catégorie</param>
        /// <returns>Liste des litiges de la catégorie spécifiée</returns>
        Task<IEnumerable<Litige>> GetByCategorieIdAsync(int categorieId);

        /// <summary>
        /// Récupère tous les litiges par étape
        /// </summary>
        /// <param name="etapeId">Identifiant de l'étape</param>
        /// <returns>Liste des litiges à l'étape spécifiée</returns>
        Task<IEnumerable<Litige>> GetByEtapeIdAsync(int etapeId);

        /// <summary>
        /// Récupère tous les litiges assignés à un responsable
        /// </summary>
        /// <param name="responsableId">Identifiant du responsable</param>
        /// <returns>Liste des litiges assignés au responsable spécifié</returns>
        Task<IEnumerable<Litige>> GetByResponsableIdAsync(int responsableId);

        /// <summary>
        /// Récupère tous les litiges dont la date d'échéance est dépassée
        /// </summary>
        /// <returns>Liste des litiges en retard</returns>
        Task<IEnumerable<Litige>> GetOverdueAsync();

        /// <summary>
        /// Récupère tous les litiges créés ou modifiés dans une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des litiges créés ou modifiés dans la période spécifiée</returns>
        Task<IEnumerable<Litige>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Ajoute un commentaire à un litige
        /// </summary>
        /// <param name="commentaire">Commentaire à ajouter</param>
        /// <returns>Commentaire ajouté</returns>
        Task<CommentaireLitige> AddCommentaireAsync(CommentaireLitige commentaire);

        /// <summary>
        /// Récupère tous les commentaires d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des commentaires du litige</returns>
        Task<IEnumerable<CommentaireLitige>> GetCommentairesAsync(int litigeId);

        /// <summary>
        /// Ajoute une entrée dans l'historique des étapes d'un litige
        /// </summary>
        /// <param name="historiqueEtape">Historique d'étape à ajouter</param>
        /// <returns>Historique d'étape ajouté</returns>
        Task<HistoriqueEtapeLitige> AddHistoriqueEtapeAsync(HistoriqueEtapeLitige historiqueEtape);

        /// <summary>
        /// Récupère l'historique des étapes d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des historiques d'étapes du litige</returns>
        Task<IEnumerable<HistoriqueEtapeLitige>> GetHistoriqueEtapesAsync(int litigeId);

        /// <summary>
        /// Met à jour le statut d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateStatutAsync(int litigeId, string statut, int modifiePar);

        /// <summary>
        /// Met à jour l'étape d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <param name="etapeId">Identifiant de la nouvelle étape</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <param name="commentaire">Commentaire associé au changement d'étape</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateEtapeAsync(int litigeId, int etapeId, int modifiePar, string commentaire);
    }
}
