namespace RecouvreX.WinForms.Forms.Relances
{
    partial class RelanceDetailsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // RelanceDetailsForm
            //
            this.ClientSize = new System.Drawing.Size(700, 500);
            this.Name = "RelanceDetailsForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Détails de la relance";
            this.Load += new System.EventHandler(this.RelanceDetailsForm_Load);
            this.ResumeLayout(false);

            // Panneau principal
            System.Windows.Forms.TableLayoutPanel mainPanel = new System.Windows.Forms.TableLayoutPanel();
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Padding = new System.Windows.Forms.Padding(10);
            mainPanel.ColumnCount = 1;
            mainPanel.RowCount = 3;
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 150F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 150F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.Controls.Add(mainPanel);

            // Panneau d'informations de la relance
            System.Windows.Forms.GroupBox relanceInfoGroupBox = new System.Windows.Forms.GroupBox();
            relanceInfoGroupBox.Text = "Informations de la relance";
            relanceInfoGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(relanceInfoGroupBox, 0, 0);

            System.Windows.Forms.TableLayoutPanel relanceInfoPanel = new System.Windows.Forms.TableLayoutPanel();
            relanceInfoPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            relanceInfoPanel.Padding = new System.Windows.Forms.Padding(5);
            relanceInfoPanel.ColumnCount = 4;
            relanceInfoPanel.RowCount = 3;
            relanceInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            relanceInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            relanceInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            relanceInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            relanceInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            relanceInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            relanceInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            relanceInfoGroupBox.Controls.Add(relanceInfoPanel);

            // Ligne 1
            System.Windows.Forms.Label typeLabel = new System.Windows.Forms.Label();
            typeLabel.Text = "Type :";
            typeLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            relanceInfoPanel.Controls.Add(typeLabel, 0, 0);

            System.Windows.Forms.Label typeValueLabel = new System.Windows.Forms.Label();
            typeValueLabel.Name = "typeValueLabel";
            typeValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            typeValueLabel.Font = new System.Drawing.Font(typeValueLabel.Font, System.Drawing.FontStyle.Bold);
            relanceInfoPanel.Controls.Add(typeValueLabel, 1, 0);

            System.Windows.Forms.Label dateRelanceLabel = new System.Windows.Forms.Label();
            dateRelanceLabel.Text = "Date de relance :";
            dateRelanceLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            relanceInfoPanel.Controls.Add(dateRelanceLabel, 2, 0);

            System.Windows.Forms.Label dateRelanceValueLabel = new System.Windows.Forms.Label();
            dateRelanceValueLabel.Name = "dateRelanceValueLabel";
            dateRelanceValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            relanceInfoPanel.Controls.Add(dateRelanceValueLabel, 3, 0);

            // Ligne 2
            System.Windows.Forms.Label niveauLabel = new System.Windows.Forms.Label();
            niveauLabel.Text = "Niveau :";
            niveauLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            relanceInfoPanel.Controls.Add(niveauLabel, 0, 1);

            System.Windows.Forms.Label niveauValueLabel = new System.Windows.Forms.Label();
            niveauValueLabel.Name = "niveauValueLabel";
            niveauValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            relanceInfoPanel.Controls.Add(niveauValueLabel, 1, 1);

            System.Windows.Forms.Label statutLabel = new System.Windows.Forms.Label();
            statutLabel.Text = "Statut :";
            statutLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            relanceInfoPanel.Controls.Add(statutLabel, 2, 1);

            System.Windows.Forms.Label statutValueLabel = new System.Windows.Forms.Label();
            statutValueLabel.Name = "statutValueLabel";
            statutValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            statutValueLabel.Font = new System.Drawing.Font(statutValueLabel.Font, System.Drawing.FontStyle.Bold);
            relanceInfoPanel.Controls.Add(statutValueLabel, 3, 1);

            // Ligne 3
            System.Windows.Forms.Label utilisateurLabel = new System.Windows.Forms.Label();
            utilisateurLabel.Text = "Utilisateur :";
            utilisateurLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            relanceInfoPanel.Controls.Add(utilisateurLabel, 0, 2);

            System.Windows.Forms.Label utilisateurValueLabel = new System.Windows.Forms.Label();
            utilisateurValueLabel.Name = "utilisateurValueLabel";
            utilisateurValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            relanceInfoPanel.Controls.Add(utilisateurValueLabel, 1, 2);

            System.Windows.Forms.Label dateProchaineRelanceLabel = new System.Windows.Forms.Label();
            dateProchaineRelanceLabel.Text = "Prochaine relance :";
            dateProchaineRelanceLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            relanceInfoPanel.Controls.Add(dateProchaineRelanceLabel, 2, 2);

            System.Windows.Forms.Label dateProchaineRelanceValueLabel = new System.Windows.Forms.Label();
            dateProchaineRelanceValueLabel.Name = "dateProchaineRelanceValueLabel";
            dateProchaineRelanceValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            relanceInfoPanel.Controls.Add(dateProchaineRelanceValueLabel, 3, 2);

            // Panneau d'informations de la facture
            System.Windows.Forms.GroupBox factureInfoGroupBox = new System.Windows.Forms.GroupBox();
            factureInfoGroupBox.Text = "Informations de la facture";
            factureInfoGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(factureInfoGroupBox, 0, 1);

            System.Windows.Forms.TableLayoutPanel factureInfoPanel = new System.Windows.Forms.TableLayoutPanel();
            factureInfoPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            factureInfoPanel.Padding = new System.Windows.Forms.Padding(5);
            factureInfoPanel.ColumnCount = 4;
            factureInfoPanel.RowCount = 3;
            factureInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            factureInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            factureInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            factureInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            factureInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            factureInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            factureInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            factureInfoGroupBox.Controls.Add(factureInfoPanel);

            // Ligne 1
            System.Windows.Forms.Label numeroFactureLabel = new System.Windows.Forms.Label();
            numeroFactureLabel.Text = "Numéro :";
            numeroFactureLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(numeroFactureLabel, 0, 0);

            System.Windows.Forms.Label numeroFactureValueLabel = new System.Windows.Forms.Label();
            numeroFactureValueLabel.Name = "numeroFactureValueLabel";
            numeroFactureValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            numeroFactureValueLabel.Font = new System.Drawing.Font(numeroFactureValueLabel.Font, System.Drawing.FontStyle.Bold);
            factureInfoPanel.Controls.Add(numeroFactureValueLabel, 1, 0);

            System.Windows.Forms.Label clientLabel = new System.Windows.Forms.Label();
            clientLabel.Text = "Client :";
            clientLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(clientLabel, 2, 0);

            System.Windows.Forms.Label clientValueLabel = new System.Windows.Forms.Label();
            clientValueLabel.Name = "clientValueLabel";
            clientValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientValueLabel.Font = new System.Drawing.Font(clientValueLabel.Font, System.Drawing.FontStyle.Bold);
            factureInfoPanel.Controls.Add(clientValueLabel, 3, 0);

            // Ligne 2
            System.Windows.Forms.Label dateEmissionLabel = new System.Windows.Forms.Label();
            dateEmissionLabel.Text = "Date d'émission :";
            dateEmissionLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(dateEmissionLabel, 0, 1);

            System.Windows.Forms.Label dateEmissionValueLabel = new System.Windows.Forms.Label();
            dateEmissionValueLabel.Name = "dateEmissionValueLabel";
            dateEmissionValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(dateEmissionValueLabel, 1, 1);

            System.Windows.Forms.Label dateEcheanceLabel = new System.Windows.Forms.Label();
            dateEcheanceLabel.Text = "Date d'échéance :";
            dateEcheanceLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(dateEcheanceLabel, 2, 1);

            System.Windows.Forms.Label dateEcheanceValueLabel = new System.Windows.Forms.Label();
            dateEcheanceValueLabel.Name = "dateEcheanceValueLabel";
            dateEcheanceValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(dateEcheanceValueLabel, 3, 1);

            // Ligne 3
            System.Windows.Forms.Label montantTTCLabel = new System.Windows.Forms.Label();
            montantTTCLabel.Text = "Montant TTC :";
            montantTTCLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(montantTTCLabel, 0, 2);

            System.Windows.Forms.Label montantTTCValueLabel = new System.Windows.Forms.Label();
            montantTTCValueLabel.Name = "montantTTCValueLabel";
            montantTTCValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(montantTTCValueLabel, 1, 2);

            System.Windows.Forms.Label montantRestantLabel = new System.Windows.Forms.Label();
            montantRestantLabel.Text = "Montant restant :";
            montantRestantLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureInfoPanel.Controls.Add(montantRestantLabel, 2, 2);

            System.Windows.Forms.Label montantRestantValueLabel = new System.Windows.Forms.Label();
            montantRestantValueLabel.Name = "montantRestantValueLabel";
            montantRestantValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            montantRestantValueLabel.Font = new System.Drawing.Font(montantRestantValueLabel.Font, System.Drawing.FontStyle.Bold);
            factureInfoPanel.Controls.Add(montantRestantValueLabel, 3, 2);

            // Panneau du contenu de la relance
            System.Windows.Forms.GroupBox contenuGroupBox = new System.Windows.Forms.GroupBox();
            contenuGroupBox.Text = "Contenu de la relance";
            contenuGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(contenuGroupBox, 0, 2);

            System.Windows.Forms.TextBox contenuTextBox = new System.Windows.Forms.TextBox();
            contenuTextBox.Name = "contenuTextBox";
            contenuTextBox.Dock = System.Windows.Forms.DockStyle.Fill;
            contenuTextBox.Multiline = true;
            contenuTextBox.ReadOnly = true;
            contenuTextBox.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            contenuGroupBox.Controls.Add(contenuTextBox);

            // Boutons d'action
            System.Windows.Forms.FlowLayoutPanel buttonsPanel = new System.Windows.Forms.FlowLayoutPanel();
            buttonsPanel.FlowDirection = System.Windows.Forms.FlowDirection.RightToLeft;
            buttonsPanel.Dock = System.Windows.Forms.DockStyle.Bottom;
            buttonsPanel.Height = 40;
            buttonsPanel.Padding = new System.Windows.Forms.Padding(0, 5, 0, 0);
            this.Controls.Add(buttonsPanel);

            System.Windows.Forms.Button closeButton = new System.Windows.Forms.Button();
            closeButton.Name = "closeButton";
            closeButton.Text = "Fermer";
            closeButton.Size = new System.Drawing.Size(100, 30);
            closeButton.Click += new System.EventHandler(this.CloseButton_Click);
            buttonsPanel.Controls.Add(closeButton);

            System.Windows.Forms.Button editButton = new System.Windows.Forms.Button();
            editButton.Name = "editButton";
            editButton.Text = "Modifier";
            editButton.Size = new System.Drawing.Size(100, 30);
            editButton.Click += new System.EventHandler(this.EditButton_Click);
            buttonsPanel.Controls.Add(editButton);

            System.Windows.Forms.Button viewFactureButton = new System.Windows.Forms.Button();
            viewFactureButton.Name = "viewFactureButton";
            viewFactureButton.Text = "Voir la facture";
            viewFactureButton.Size = new System.Drawing.Size(120, 30);
            viewFactureButton.Click += new System.EventHandler(this.ViewFactureButton_Click);
            buttonsPanel.Controls.Add(viewFactureButton);
        }

        #endregion
    }
}
