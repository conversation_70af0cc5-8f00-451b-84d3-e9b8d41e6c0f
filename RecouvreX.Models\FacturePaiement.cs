namespace RecouvreX.Models
{
    /// <summary>
    /// Représente l'association entre une facture et un paiement (relation N-N)
    /// </summary>
    public class FacturePaiement : BaseEntity
    {
        /// <summary>
        /// Identifiant de la facture
        /// </summary>
        public int FactureId { get; set; }

        /// <summary>
        /// Facture associée (navigation property)
        /// </summary>
        public Facture Facture { get; set; }

        /// <summary>
        /// Identifiant du paiement
        /// </summary>
        public int PaiementId { get; set; }

        /// <summary>
        /// Paiement associé (navigation property)
        /// </summary>
        public Paiement Paiement { get; set; }

        /// <summary>
        /// Montant du paiement affecté à cette facture
        /// </summary>
        public decimal MontantAffecte { get; set; }

        /// <summary>
        /// Notes ou commentaires sur l'affectation
        /// </summary>
        public string Notes { get; set; }
    }
}
