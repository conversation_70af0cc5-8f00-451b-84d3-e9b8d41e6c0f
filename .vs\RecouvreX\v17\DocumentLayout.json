{"Version": 1, "WorkspaceRootPath": "E:\\AG APP\\RecouvreX Dapper 01\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{E9CEF544-D4AF-41C2-8CFF-61736F47E046}|RecouvreX.DataAccess\\RecouvreX.DataAccess.csproj|e:\\ag app\\recouvrex dapper 01\\recouvrex.dataaccess\\databaseconnection.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{E9CEF544-D4AF-41C2-8CFF-61736F47E046}|RecouvreX.DataAccess\\RecouvreX.DataAccess.csproj|solutionrelative:recouvrex.dataaccess\\databaseconnection.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}|RecouvreX.WinForms\\RecouvreX.WinForms.csproj|e:\\ag app\\recouvrex dapper 01\\recouvrex.winforms\\program.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}|RecouvreX.WinForms\\RecouvreX.WinForms.csproj|solutionrelative:recouvrex.winforms\\program.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{F6E01715-C76C-4211-82BC-FA33ED177337}|RecouvreX.Business\\RecouvreX.Business.csproj|e:\\ag app\\recouvrex dapper 01\\recouvrex.business\\dependencyinjection\\recouvrexservicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{F6E01715-C76C-4211-82BC-FA33ED177337}|RecouvreX.Business\\RecouvreX.Business.csproj|solutionrelative:recouvrex.business\\dependencyinjection\\recouvrexservicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{F6E01715-C76C-4211-82BC-FA33ED177337}|RecouvreX.Business\\RecouvreX.Business.csproj|e:\\ag app\\recouvrex dapper 01\\recouvrex.business\\dependencyinjection\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{F6E01715-C76C-4211-82BC-FA33ED177337}|RecouvreX.Business\\RecouvreX.Business.csproj|solutionrelative:recouvrex.business\\dependencyinjection\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{E9CEF544-D4AF-41C2-8CFF-61736F47E046}|RecouvreX.DataAccess\\RecouvreX.DataAccess.csproj|e:\\ag app\\recouvrex dapper 01\\recouvrex.dataaccess\\repositories\\categorielitigerepository.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{E9CEF544-D4AF-41C2-8CFF-61736F47E046}|RecouvreX.DataAccess\\RecouvreX.DataAccess.csproj|solutionrelative:recouvrex.dataaccess\\repositories\\categorielitigerepository.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}, {"AbsoluteMoniker": "D:0:0:{E9CEF544-D4AF-41C2-8CFF-61736F47E046}|RecouvreX.DataAccess\\RecouvreX.DataAccess.csproj|e:\\ag app\\recouvrex dapper 01\\recouvrex.dataaccess\\repositories\\actionprioritairerepository.cs||{A6C744A8-0E4A-4FC6-886A-0***********}", "RelativeMoniker": "D:0:0:{E9CEF544-D4AF-41C2-8CFF-61736F47E046}|RecouvreX.DataAccess\\RecouvreX.DataAccess.csproj|solutionrelative:recouvrex.dataaccess\\repositories\\actionprioritairerepository.cs||{A6C744A8-0E4A-4FC6-886A-0***********}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "E:\\AG APP\\RecouvreX Dapper 01\\RecouvreX.WinForms\\Program.cs", "RelativeDocumentMoniker": "RecouvreX.WinForms\\Program.cs", "ToolTip": "E:\\AG APP\\RecouvreX Dapper 01\\RecouvreX.WinForms\\Program.cs", "RelativeToolTip": "RecouvreX.WinForms\\Program.cs", "ViewState": "AgIAAEYAAAAAAAAAAAAgwFoAAABJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T22:19:51.677Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "RecouvreXServiceCollectionExtensions.cs", "DocumentMoniker": "E:\\AG APP\\RecouvreX Dapper 01\\RecouvreX.Business\\DependencyInjection\\RecouvreXServiceCollectionExtensions.cs", "RelativeDocumentMoniker": "RecouvreX.Business\\DependencyInjection\\RecouvreXServiceCollectionExtensions.cs", "ToolTip": "E:\\AG APP\\RecouvreX Dapper 01\\RecouvreX.Business\\DependencyInjection\\RecouvreXServiceCollectionExtensions.cs", "RelativeToolTip": "RecouvreX.Business\\DependencyInjection\\RecouvreXServiceCollectionExtensions.cs", "ViewState": "AgIAABYAAAAAAAAAAAAgwBEAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T22:19:04.741Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ServiceCollectionExtensions.cs", "DocumentMoniker": "E:\\AG APP\\RecouvreX Dapper 01\\RecouvreX.Business\\DependencyInjection\\ServiceCollectionExtensions.cs", "RelativeDocumentMoniker": "RecouvreX.Business\\DependencyInjection\\ServiceCollectionExtensions.cs", "ToolTip": "E:\\AG APP\\RecouvreX Dapper 01\\RecouvreX.Business\\DependencyInjection\\ServiceCollectionExtensions.cs", "RelativeToolTip": "RecouvreX.Business\\DependencyInjection\\ServiceCollectionExtensions.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T22:18:55.622Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "CategorieLitigeRepository.cs", "DocumentMoniker": "E:\\AG APP\\RecouvreX Dapper 01\\RecouvreX.DataAccess\\Repositories\\CategorieLitigeRepository.cs", "RelativeDocumentMoniker": "RecouvreX.DataAccess\\Repositories\\CategorieLitigeRepository.cs", "ToolTip": "E:\\AG APP\\RecouvreX Dapper 01\\RecouvreX.DataAccess\\Repositories\\CategorieLitigeRepository.cs", "RelativeToolTip": "RecouvreX.DataAccess\\Repositories\\CategorieLitigeRepository.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAIwBIAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T21:38:09.322Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ActionPrioritaireRepository.cs", "DocumentMoniker": "E:\\AG APP\\RecouvreX Dapper 01\\RecouvreX.DataAccess\\Repositories\\ActionPrioritaireRepository.cs", "RelativeDocumentMoniker": "RecouvreX.DataAccess\\Repositories\\ActionPrioritaireRepository.cs", "ToolTip": "E:\\AG APP\\RecouvreX Dapper 01\\RecouvreX.DataAccess\\Repositories\\ActionPrioritaireRepository.cs", "RelativeToolTip": "RecouvreX.DataAccess\\Repositories\\ActionPrioritaireRepository.cs", "ViewState": "AgIAAJYAAAAAAAAAAAAYwHQAAABNAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T21:37:39.434Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "DatabaseConnection.cs", "DocumentMoniker": "E:\\AG APP\\RecouvreX Dapper 01\\RecouvreX.DataAccess\\DatabaseConnection.cs", "RelativeDocumentMoniker": "RecouvreX.DataAccess\\DatabaseConnection.cs", "ToolTip": "E:\\AG APP\\RecouvreX Dapper 01\\RecouvreX.DataAccess\\DatabaseConnection.cs", "RelativeToolTip": "RecouvreX.DataAccess\\DatabaseConnection.cs", "ViewState": "AgIAABkAAAAAAAAAAAAgwCwAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T21:37:26.274Z", "EditorCaption": ""}]}]}]}