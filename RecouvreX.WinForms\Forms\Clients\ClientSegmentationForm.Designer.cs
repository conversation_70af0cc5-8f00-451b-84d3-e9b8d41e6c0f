using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using RecouvreX.Models.Enums;

namespace RecouvreX.WinForms.Forms.Clients
{
    partial class ClientSegmentationForm
    {
        /// <summary>
        /// Variable nécessaire au concepteur.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private Panel filterPanel;
        private ComboBox segmentComboBox;
        private TextBox searchTextBox;
        private Button searchButton;
        private Button segmentButton;
        private SplitContainer splitContainer;
        private DataGridView clientsDataGridView;
        private Chart distributionChart;

        /// <summary>
        /// Nettoyage des ressources utilisées.
        /// </summary>
        /// <param name="disposing">true si les ressources managées doivent être supprimées ; sinon, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Code généré par le Concepteur Windows Form

        /// <summary>
        /// Méthode requise pour la prise en charge du concepteur - ne modifiez pas
        /// le contenu de cette méthode avec l'éditeur de code.
        /// </summary>
        private void InitializeComponent()
        {
            this.mainPanel = new Panel();
            this.filterPanel = new Panel();
            this.segmentComboBox = new ComboBox();
            this.searchTextBox = new TextBox();
            this.searchButton = new Button();
            this.segmentButton = new Button();
            this.splitContainer = new SplitContainer();
            this.clientsDataGridView = new DataGridView();
            this.distributionChart = new Chart();

            this.mainPanel.SuspendLayout();
            this.filterPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).BeginInit();
            this.splitContainer.Panel1.SuspendLayout();
            this.splitContainer.Panel2.SuspendLayout();
            this.splitContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.clientsDataGridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.distributionChart)).BeginInit();
            this.SuspendLayout();

            // 
            // mainPanel
            // 
            this.mainPanel.Dock = DockStyle.Fill;
            this.mainPanel.Padding = new Padding(10);
            this.mainPanel.Controls.Add(this.splitContainer);
            this.mainPanel.Controls.Add(this.filterPanel);
            
            // 
            // filterPanel
            // 
            this.filterPanel.Dock = DockStyle.Top;
            this.filterPanel.Height = 50;
            this.filterPanel.Padding = new Padding(5);
            this.filterPanel.Controls.Add(this.segmentComboBox);
            this.filterPanel.Controls.Add(this.searchTextBox);
            this.filterPanel.Controls.Add(this.searchButton);
            this.filterPanel.Controls.Add(this.segmentButton);
            
            // Ajouter des labels pour les filtres
            Label segmentLabel = new Label();
            segmentLabel.Text = "Segment :";
            segmentLabel.AutoSize = true;
            segmentLabel.Location = new Point(10, 15);
            this.filterPanel.Controls.Add(segmentLabel);
            
            Label searchLabel = new Label();
            searchLabel.Text = "Recherche :";
            searchLabel.AutoSize = true;
            searchLabel.Location = new Point(250, 15);
            this.filterPanel.Controls.Add(searchLabel);
            
            // 
            // segmentComboBox
            // 
            this.segmentComboBox.Name = "segmentComboBox";
            this.segmentComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.segmentComboBox.Location = new Point(80, 12);
            this.segmentComboBox.Width = 150;
            this.segmentComboBox.Items.AddRange(new object[] { "Tous", "A - Stratégique", "B - Important", "C - Standard", "Non segmenté" });
            this.segmentComboBox.SelectedIndex = 0;
            this.segmentComboBox.SelectedIndexChanged += new EventHandler(this.SegmentComboBox_SelectedIndexChanged);
            
            // 
            // searchTextBox
            // 
            this.searchTextBox.Name = "searchTextBox";
            this.searchTextBox.Location = new Point(320, 12);
            this.searchTextBox.Width = 200;
            this.searchTextBox.KeyDown += new KeyEventHandler(this.SearchTextBox_KeyDown);
            
            // 
            // searchButton
            // 
            this.searchButton.Text = "Rechercher";
            this.searchButton.Location = new Point(530, 10);
            this.searchButton.Width = 100;
            this.searchButton.Height = 30;
            this.searchButton.Click += new EventHandler(this.SearchButton_Click);
            
            // 
            // segmentButton
            // 
            this.segmentButton.Name = "segmentButton";
            this.segmentButton.Text = "Segmentation automatique";
            this.segmentButton.Location = new Point(650, 10);
            this.segmentButton.Width = 180;
            this.segmentButton.Height = 30;
            this.segmentButton.Click += new EventHandler(this.SegmentButton_Click);
            
            // 
            // splitContainer
            // 
            this.splitContainer.Dock = DockStyle.Fill;
            this.splitContainer.Orientation = Orientation.Vertical;
            this.splitContainer.SplitterDistance = 300;
            
            // Panneau supérieur (tableau des clients)
            Panel gridPanel = new Panel();
            gridPanel.Dock = DockStyle.Fill;
            gridPanel.Padding = new Padding(0, 10, 0, 10);
            gridPanel.Controls.Add(this.clientsDataGridView);
            this.splitContainer.Panel1.Controls.Add(gridPanel);
            
            // 
            // clientsDataGridView
            // 
            this.clientsDataGridView.Name = "clientsDataGridView";
            this.clientsDataGridView.Dock = DockStyle.Fill;
            this.clientsDataGridView.AllowUserToAddRows = false;
            this.clientsDataGridView.AllowUserToDeleteRows = false;
            this.clientsDataGridView.AllowUserToResizeRows = false;
            this.clientsDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.clientsDataGridView.ReadOnly = true;
            this.clientsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.clientsDataGridView.RowHeadersVisible = false;
            this.clientsDataGridView.MultiSelect = false;
            this.clientsDataGridView.CellFormatting += new DataGridViewCellFormattingEventHandler(this.ClientsDataGridView_CellFormatting);
            this.clientsDataGridView.CellDoubleClick += new DataGridViewCellEventHandler(this.ClientsDataGridView_CellDoubleClick);
            
            // Panneau inférieur (graphique de distribution)
            Panel chartPanel = new Panel();
            chartPanel.Dock = DockStyle.Fill;
            chartPanel.Padding = new Padding(0, 10, 0, 0);
            chartPanel.Controls.Add(this.distributionChart);
            this.splitContainer.Panel2.Controls.Add(chartPanel);
            
            // 
            // distributionChart
            // 
            this.distributionChart.Name = "distributionChart";
            this.distributionChart.Dock = DockStyle.Fill;
            this.distributionChart.Palette = ChartColorPalette.BrightPastel;
            this.distributionChart.Titles.Add("Distribution des clients par segment");
            this.distributionChart.ChartAreas.Add(new ChartArea("Main"));
            this.distributionChart.Series.Add(new Series("Distribution"));
            this.distributionChart.Series["Distribution"].ChartType = SeriesChartType.Pie;
            this.distributionChart.Series["Distribution"].IsValueShownAsLabel = true;
            this.distributionChart.Series["Distribution"].LabelFormat = "{0} ({1:P0})";
            
            // 
            // ClientSegmentationForm
            // 
            this.ClientSize = new System.Drawing.Size(1000, 600);
            this.Controls.Add(this.mainPanel);
            this.Name = "ClientSegmentationForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Segmentation des clients";
            this.Load += new System.EventHandler(this.ClientSegmentationForm_Load);
            
            this.mainPanel.ResumeLayout(false);
            this.filterPanel.ResumeLayout(false);
            this.filterPanel.PerformLayout();
            this.splitContainer.Panel1.ResumeLayout(false);
            this.splitContainer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).EndInit();
            this.splitContainer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.clientsDataGridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.distributionChart)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion
    }
}
