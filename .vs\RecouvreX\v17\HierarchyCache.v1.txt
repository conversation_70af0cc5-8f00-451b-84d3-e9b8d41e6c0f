﻿++Solution ' RecouvreX ' ‎ (5 sur 5 de projets)
i:{00000000-0000-0000-0000-000000000000}:RecouvreX.sln
++RecouvreX.WinForms
i:{00000000-0000-0000-0000-000000000000}:RecouvreX.WinForms
++Dépendances
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1300
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1297
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:>1299
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:>1296
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:>1298
++Controls
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\controls\
++ActionsPrioritairesControl.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\controls\actionsprioritairescontrol.cs
++MesTachesControl.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\controls\mestachescontrol.cs
++Forms
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\
++Actions
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\actions\
++ActionPrioritaireListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\actions\actionprioritairelistform.cs
++AssignerActionForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\actions\assigneractionform.cs
++Administration
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\
++Alertes
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\alertes\
++Clients
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\
++Common
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\common\
++Communications
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\
++AppelForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\appelform.cs
++CommunicationListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\communicationlistform.cs
++EmailForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\emailform.cs
++NoteForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\noteform.cs
++WhatsAppForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\whatsappform.cs
++Factures
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\factures\
++Litiges
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\
++CategorieLitigeEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\categorielitigeeditform.cs
++CategorieLitigeEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\categorielitigeeditform.designer.cs
++CategorieLitigeListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\categorielitigelistform.cs
++CategorieLitigeListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\categorielitigelistform.designer.cs
++CategorieLitigeListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\categorielitigelistform.resx
++DocumentLitigeForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\documentlitigeform.cs
++EtapeLitigeEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\etapelitigeeditform.cs
++EtapeLitigeListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\etapelitigelistform.cs
++LitigeEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\litigeeditform.cs
++LitigeEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\litigeeditform.designer.cs
++LitigeListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\litigelistform.cs
++LitigeListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\litigelistform.designer.cs
++NotificationsLitigeForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\notificationslitigeform.cs
++Paiements
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\paiements\
++Rapports
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\rapports\
++Relances
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\
++LoginForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\loginform.cs
++MainForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\mainform.cs
++Helpers
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\helpers\
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\helpers\
++appsettings.json
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\appsettings.json
++Program.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\program.cs
++Analyseurs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1352
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1335
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:>1320
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:>1301
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:>1302
++Frameworks
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1360
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1341
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:>1326
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:>1309
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:>1310
++Packages
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1363
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1343
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:>1328
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:>1313
++Projets
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1348
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1331
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:>1317
++ActionsPrioritairesControl.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\controls\actionsprioritairescontrol.designer.cs
++ActionsPrioritairesControl.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\controls\actionsprioritairescontrol.resx
++MesTachesControl.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\controls\mestachescontrol.designer.cs
++MesTachesControl.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\controls\mestachescontrol.resx
++ActionPrioritaireListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\actions\actionprioritairelistform.designer.cs
++ActionPrioritaireListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\actions\actionprioritairelistform.resx
++AssignerActionForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\actions\assigneractionform.designer.cs
++ResetPasswordForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\resetpasswordform.cs
++RoleEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\roleeditform.cs
++RoleListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\rolelistform.cs
++RolePermissionsForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\rolepermissionsform.cs
++UtilisateurEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\utilisateureditform.cs
++UtilisateurListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\utilisateurlistform.cs
++AlerteEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\alertes\alerteeditform.cs
++AlerteListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\alertes\alertelistform.cs
++ClientDetailsForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clientdetailsform.cs
++ClientEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clienteditform.cs
++ClientListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clientlistform.cs
++ClientSegmentationForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clientsegmentationform.cs
++ClientSegmentEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clientsegmenteditform.cs
++ContactEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\contacteditform.cs
++ScoreRisqueClientListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\scorerisqueclientlistform.cs
++SelectionFactureForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\common\selectionfactureform.cs
++AppelForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\appelform.designer.cs
++CommunicationListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\communicationlistform.designer.cs
++CommunicationListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\communicationlistform.resx
++EmailForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\emailform.designer.cs
++EmailForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\emailform.resx
++WhatsAppForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\whatsappform.resx
++FactureDetailsForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\factures\facturedetailsform.cs
++FactureEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\factures\factureeditform.cs
++FactureListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\factures\facturelistform.cs
++EtapeLitigeEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\etapelitigeeditform.designer.cs
++PaiementDetailsForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\paiements\paiementdetailsform.cs
++PaiementEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\paiements\paiementeditform.cs
++PaiementListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\paiements\paiementlistform.cs
++ReportGeneratorForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\rapports\reportgeneratorform.cs
++CommentaireForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\commentaireform.cs
++LettreRelanceForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\lettrerelanceform.cs
++ModeleRelanceEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\modelerelanceeditform.cs
++ModeleRelanceListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\modelerelancelistform.cs
++PlanificationRelanceListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\planificationrelancelistform.cs
++RegleRelanceEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\reglerelanceeditform.cs
++RegleRelanceListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\reglerelancelistform.cs
++RelanceDetailsForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\relancedetailsform.cs
++RelanceEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\relanceeditform.cs
++RelanceListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\relancelistform.cs
++LoginForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\loginform.designer.cs
++LoginForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\loginform.resx
++MainForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\mainform.designer.cs
++MainForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\mainform.resx
++ChartHelper.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\helpers\charthelper.cs
++CommunicationHelper.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\helpers\communicationhelper.cs
++DashboardHelper.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\helpers\dashboardhelper.cs
++ModeleRelanceHelper.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\helpers\modelerelancehelper.cs
++NotificationHelper.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\helpers\notificationhelper.cs
++PlanificationHelper.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\helpers\planificationhelper.cs
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.2\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.2\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.2\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.2\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.2\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.2\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.2\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.2\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++System.Text.Json.SourceGeneration
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:c:\users\<USER>\.nuget\packages\system.text.json\7.0.0\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\6.0.36\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\6.0.36\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\6.0.36\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\6.0.36\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Windows.Forms.Analyzers
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\6.0.36\analyzers\dotnet\system.windows.forms.analyzers.dll
++System.Windows.Forms.Analyzers.CSharp
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\6.0.36\analyzers\dotnet\cs\system.windows.forms.analyzers.csharp.dll
++Microsoft.NETCore.App
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1361
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1342
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:>1327
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:>1312
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:>1311
++Microsoft.WindowsDesktop.App.WindowsForms
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1362
++itext7 (8.0.2)
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1370
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1347
++Microsoft.Extensions.Configuration (7.0.0)
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1371
++Microsoft.Extensions.Configuration.Json (7.0.0)
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1366
++Microsoft.Extensions.DependencyInjection (7.0.0)
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1368
++ScottPlot.WinForms (4.1.67)
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1365
++Serilog.Extensions.Logging (7.0.0)
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1364
++Serilog.Settings.Configuration (7.0.0)
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1372
++Serilog.Sinks.File (7.0.0)
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1369
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:>1315
++WinForms.DataVisualization (1.9.1)
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1367
++RecouvreX.Business
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1351
i:{00000000-0000-0000-0000-000000000000}:RecouvreX.Business
++RecouvreX.Common
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1350
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1333
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:>1319
i:{00000000-0000-0000-0000-000000000000}:RecouvreX.Common
++RecouvreX.Models
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>1349
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1332
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:>1318
i:{00000000-0000-0000-0000-000000000000}:RecouvreX.Models
++ResetPasswordForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\resetpasswordform.designer.cs
++ResetPasswordForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\resetpasswordform.resx
++RoleEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\roleeditform.designer.cs
++RoleEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\roleeditform.resx
++RoleListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\rolelistform.designer.cs
++RoleListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\rolelistform.resx
++RolePermissionsForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\rolepermissionsform.designer.cs
++RolePermissionsForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\rolepermissionsform.resx
++UtilisateurEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\utilisateureditform.designer.cs
++UtilisateurEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\utilisateureditform.resx
++UtilisateurListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\utilisateurlistform.designer.cs
++UtilisateurListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\administration\utilisateurlistform.resx
++AlerteEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\alertes\alerteeditform.designer.cs
++AlerteEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\alertes\alerteeditform.resx
++AlerteListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\alertes\alertelistform.designer.cs
++AlerteListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\alertes\alertelistform.resx
++ClientDetailsForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clientdetailsform.designer.cs
++ClientDetailsForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clientdetailsform.resx
++ClientEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clienteditform.designer.cs
++ClientEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clienteditform.resx
++ClientListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clientlistform.designer.cs
++ClientListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clientlistform.resx
++ClientSegmentationForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clientsegmentationform.designer.cs
++ClientSegmentationForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clientsegmentationform.resx
++ClientSegmentEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clientsegmenteditform.designer.cs
++ContactEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\contacteditform.designer.cs
++ContactEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\contacteditform.resx
++ScoreRisqueClientListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\scorerisqueclientlistform.designer.cs
++ScoreRisqueClientListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\scorerisqueclientlistform.resx
++SelectionFactureForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\common\selectionfactureform.designer.cs
++SelectionFactureForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\common\selectionfactureform.resx
++FactureDetailsForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\factures\facturedetailsform.designer.cs
++FactureDetailsForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\factures\facturedetailsform.resx
++FactureEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\factures\factureeditform.designer.cs
++FactureEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\factures\factureeditform.resx
++FactureListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\factures\facturelistform.designer.cs
++FactureListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\factures\facturelistform.resx
++PaiementDetailsForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\paiements\paiementdetailsform.designer.cs
++PaiementDetailsForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\paiements\paiementdetailsform.resx
++PaiementEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\paiements\paiementeditform.designer.cs
++PaiementEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\paiements\paiementeditform.resx
++PaiementListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\paiements\paiementlistform.designer.cs
++PaiementListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\paiements\paiementlistform.resx
++ReportGeneratorForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\rapports\reportgeneratorform.designer.cs
++ReportGeneratorForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\rapports\reportgeneratorform.resx
++CommentaireForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\commentaireform.designer.cs
++ModeleRelanceEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\modelerelanceeditform.designer.cs
++ModeleRelanceListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\modelerelancelistform.designer.cs
++PlanificationRelanceListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\planificationrelancelistform.designer.cs
++RegleRelanceEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\reglerelanceeditform.designer.cs
++RegleRelanceListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\reglerelancelistform.designer.cs
++RelanceDetailsForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\relancedetailsform.designer.cs
++RelanceDetailsForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\relancedetailsform.resx
++RelanceEditForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\relanceeditform.designer.cs
++RelanceEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\relanceeditform.resx
++RelanceListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\relancelistform.designer.cs
++RelanceListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\relancelistform.resx
++DependencyInjection
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\dependencyinjection\
++Interfaces
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\
++Services
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\
++Class1.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\class1.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\class1.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\class1.cs
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:e:\recouvrex  app\recouvrex.common\class1.cs
++ServiceCollectionExtensions.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\dependencyinjection\servicecollectionextensions.cs
++EmailHelper.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\helpers\emailhelper.cs
++PdfHelper.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\helpers\pdfhelper.cs
++WhatsAppHelper.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\helpers\whatsapphelper.cs
++IActionPrioritaireService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\iactionprioritaireservice.cs
++IAlerteIndicateurService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\ialerteindicateurservice.cs
++IAlerteService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\ialerteservice.cs
++IAuthenticationService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\iauthenticationservice.cs
++IClientService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\iclientservice.cs
++ICommunicationService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\icommunicationservice.cs
++IContactClientService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\icontactclientservice.cs
++IContactService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\icontactservice.cs
++IDocumentService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\idocumentservice.cs
++IFactureService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\ifactureservice.cs
++IJournalAuditService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\ijournalauditservice.cs
++ILitigeService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\ilitigeservice.cs
++IModeleRelanceService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\imodelerelanceservice.cs
++IPaiementService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\ipaiementservice.cs
++IPlanificationRelanceService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\iplanificationrelanceservice.cs
++IRapportService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\irapportservice.cs
++IRegleRelanceService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\ireglerelanceservice.cs
++IRelanceService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\irelanceservice.cs
++IReportingService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\ireportingservice.cs
++IReportService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\ireportservice.cs
++IRoleService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\iroleservice.cs
++IScoreRisqueClientService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\iscorerisqueclientservice.cs
++IUtilisateurService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\iutilisateurservice.cs
++ActionPrioritaireService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\actionprioritaireservice.cs
++AlerteService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\alerteservice.cs
++AuthenticationService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\authenticationservice.cs
++ClientService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\clientservice.cs
++CommunicationService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\communicationservice.cs
++ContactClientService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\contactclientservice.cs
++ContactService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\contactservice.cs
++DocumentService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\documentservice.cs
++FactureService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\factureservice.cs
++JournalAuditService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\journalauditservice.cs
++LitigeService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\litigeservice.cs
++ModeleRelanceService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\modelerelanceservice.cs
++PaiementService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\paiementservice.cs
++PlanificationRelanceService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\planificationrelanceservice.cs
++RegleRelanceService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\reglerelanceservice.cs
++RelanceService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\relanceservice.cs
++ReportService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\reportservice.cs
++RoleService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\roleservice.cs
++ScoreRisqueClientService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\scorerisqueclientservice.cs
++UtilisateurService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\utilisateurservice.cs
++Microsoft.Extensions.Configuration.Abstractions (7.0.0)
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1344
++Microsoft.Extensions.DependencyInjection (6.0.1)
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1346
++Newtonsoft.Json (13.0.3)
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1345
++RecouvreX.DataAccess
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:>1334
i:{00000000-0000-0000-0000-000000000000}:RecouvreX.DataAccess
++Repositories
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\
++DatabaseConnection.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\databaseconnection.cs
++IActionPrioritaireRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\iactionprioritairerepository.cs
++IAlerteIndicateurRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\ialerteindicateurrepository.cs
++IAlerteRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\ialerterepository.cs
++ICategorieLitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\icategorielitigerepository.cs
++IClientRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\iclientrepository.cs
++ICommunicationRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\icommunicationrepository.cs
++IContactClientRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\icontactclientrepository.cs
++IContactRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\icontactrepository.cs
++IDbConnectionFactory.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\idbconnectionfactory.cs
++IDocumentLitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\idocumentlitigerepository.cs
++IDocumentRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\idocumentrepository.cs
++IEtapeLitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\ietapelitigerepository.cs
++IFactureRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\ifacturerepository.cs
++IJournalAuditRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\ijournalauditrepository.cs
++ILitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\ilitigerepository.cs
++IModeleRelanceRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\imodelerelancerepository.cs
++INotificationLitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\inotificationlitigerepository.cs
++IPaiementRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\ipaiementrepository.cs
++IPermissionRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\ipermissionrepository.cs
++IPlanificationRelanceRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\iplanificationrelancerepository.cs
++IRapportRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\irapportrepository.cs
++IRegleRelanceRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\ireglerelancerepository.cs
++IRelanceRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\irelancerepository.cs
++IRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\irepository.cs
++IRoleRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\irolerepository.cs
++IScoreRisqueClientRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\iscorerisqueclientrepository.cs
++IUtilisateurRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\iutilisateurrepository.cs
++IVariableDynamiqueRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\ivariabledynamiquerepository.cs
++ActionPrioritaireRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\actionprioritairerepository.cs
++AlerteIndicateurRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\alerteindicateurrepository.cs
++AlerteRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\alerterepository.cs
++BaseRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\baserepository.cs
++CategorieLitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\categorielitigerepository.cs
++ClientRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\clientrepository.cs
++CommunicationRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\communicationrepository.cs
++ContactClientRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\contactclientrepository.cs
++ContactRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\contactrepository.cs
++DocumentLitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\documentlitigerepository.cs
++DocumentRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\documentrepository.cs
++EtapeLitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\etapelitigerepository.cs
++FactureRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\facturerepository.cs
++JournalAuditRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\journalauditrepository.cs
++LitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\litigerepository.cs
++ModeleRelanceRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\modelerelancerepository.cs
++NotificationLitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\notificationlitigerepository.cs
++PaiementRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\paiementrepository.cs
++PermissionRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\permissionrepository.cs
++PlanificationRelanceRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\planificationrelancerepository.cs
++RapportRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\rapportrepository.cs
++RegleRelanceRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\reglerelancerepository.cs
++RelanceRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\relancerepository.cs
++RoleRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\rolerepository.cs
++ScoreRisqueClientRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\scorerisqueclientrepository.cs
++UtilisateurRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\utilisateurrepository.cs
++VariableDynamiqueRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\variabledynamiquerepository.cs
++Dapper (2.1.66)
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:>1330
++Microsoft.Data.SqlClient (6.0.2)
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:>1329
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:>2887
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:>2888
++Enums
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\
++Reporting
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\reporting\
++ActionPrioritaire.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\actionprioritaire.cs
++Alerte.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\alerte.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\reporting\alerte.cs
++BaseEntity.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\baseentity.cs
++CategorieLitige.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\categorielitige.cs
++Client.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\client.cs
++CommentaireLitige.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\commentairelitige.cs
++Communication.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\communication.cs
++Contact.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\contact.cs
++ContactClient.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\contactclient.cs
++Document.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\document.cs
++DocumentLitige.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\documentlitige.cs
++EtapeLitige.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\etapelitige.cs
++Facture.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\facture.cs
++FacturePaiement.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\facturepaiement.cs
++HistoriqueEtapeLitige.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\historiqueetapelitige.cs
++JournalAudit.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\journalaudit.cs
++Litige.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\litige.cs
++ModeleRelance.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\modelerelance.cs
++NotificationLitige.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\notificationlitige.cs
++Paiement.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\paiement.cs
++Permission.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\permission.cs
++PlanificationRelance.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\planificationrelance.cs
++RegleRelance.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\reglerelance.cs
++Relance.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\relance.cs
++Role.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\role.cs
++RolePermission.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\rolepermission.cs
++RoleWithStats.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\rolewithstats.cs
++ScoreRisqueClient.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\scorerisqueclient.cs
++Utilisateur.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\utilisateur.cs
++VariableDynamique.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\variabledynamique.cs
++CanalRelance.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\canalrelance.cs
++ConditionAlerte.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\conditionalerte.cs
++DirectionCommunication.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\directioncommunication.cs
++ModePaiement.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\modepaiement.cs
++NiveauPriorite.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\niveaupriorite.cs
++ResultatCommunication.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\resultatcommunication.cs
++RolesUtilisateur.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\rolesutilisateur.cs
++SegmentClient.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\segmentclient.cs
++StatutFacture.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\statutfacture.cs
++StatutPlanificationRelance.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\statutplanificationrelance.cs
++StatutRelance.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\statutrelance.cs
++TypeActionPrioritaire.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\typeactionprioritaire.cs
++TypeAlerte.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\typealerte.cs
++TypeAudit.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\typeaudit.cs
++TypeCommunication.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\typecommunication.cs
++TypeDocument.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\typedocument.cs
++TypeModeleRelance.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\typemodelerelance.cs
++TypeRelance.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\typerelance.cs
++DonneeGraphique.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\reporting\donneegraphique.cs
++IndicateurPerformance.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\reporting\indicateurperformance.cs
++Rapport.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\reporting\rapport.cs
++StatistiqueClient.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\reporting\statistiqueclient.cs
++StatistiqueRecouvrement.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\reporting\statistiquerecouvrement.cs
++Security
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:e:\recouvrex  app\recouvrex.common\security\
++PasswordHasher.cs
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:e:\recouvrex  app\recouvrex.common\security\passwordhasher.cs
++BCrypt.Net-Next (4.0.3)
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:>1314
++Serilog (4.2.0)
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:>1316
++IHistoriqueEtapeLitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\ihistoriqueetapelitigerepository.cs
++ICommentaireLitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\icommentairelitigerepository.cs
++HistoriqueEtapeLitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\historiqueetapelitigerepository.cs
++CommentaireLitigeRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\commentairelitigerepository.cs
++PlanPaiement.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\planpaiement.cs
++EcheancePaiement.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\echeancepaiement.cs
++StatutPlanPaiement.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\enums\statutplanpaiement.cs
++IPlanPaiementRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\iplanpaiementrepository.cs
++IEcheancePaiementRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\iecheancepaiementrepository.cs
++PlanPaiementRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\planpaiementrepository.cs
++EcheancePaiementRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\echeancepaiementrepository.cs
++IPlanPaiementService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\iplanpaiementservice.cs
++PlanPaiementService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\planpaiementservice.cs
++PlansPaiement
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\planspaiement\
++PlanPaiementListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\planspaiement\planpaiementlistform.cs
++PlanPaiementDetailsForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\planspaiement\planpaiementdetailsform.cs
++EcheancePaiementForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\planspaiement\echeancepaiementform.cs
++EcheanceEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\planspaiement\echeanceeditform.cs
++PlanPaiementEditForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\planspaiement\planpaiementeditform.cs
++EcheancierDashboardForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\planspaiement\echeancierdashboardform.cs
++AccordPaiementHelper.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\helpers\accordpaiementhelper.cs
++AccordPaiementForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\planspaiement\accordpaiementform.cs
++RapportPersonnalise.cs
i:{19a28481-dfd5-4284-b1a7-eac6f30931bb}:e:\recouvrex  app\recouvrex.models\reporting\rapportpersonnalise.cs
++IRapportPersonnaliseService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\irapportpersonnaliseservice.cs
++IRapportPersonnaliseRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\irapportpersonnaliserepository.cs
++RapportPersonnaliseRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\rapportpersonnaliserepository.cs
++RapportPersonnaliseService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\rapportpersonnaliseservice.cs
++IEmailService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\iemailservice.cs
++EmailService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\emailservice.cs
++CustomReportBuilderForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\rapports\customreportbuilderform.cs
++CustomReportListForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\rapports\customreportlistform.cs
++ReportPreviewForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\rapports\reportpreviewform.cs
++ExportFormatForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\rapports\exportformatform.cs
++SendReportByEmailForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\rapports\sendreportbyemailform.cs
++RecouvreXServiceCollectionExtensions.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\dependencyinjection\recouvrexservicecollectionextensions.cs
++ISegmentClientRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\isegmentclientrepository.cs
++SegmentClientRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\repositories\segmentclientrepository.cs
++ISegmentClientService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\isegmentclientservice.cs
++SegmentClientService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\segmentclientservice.cs
++IBaseRepository.cs
i:{e9cef544-d4af-41c2-8cff-61736f47e046}:e:\recouvrex  app\recouvrex.dataaccess\interfaces\ibaserepository.cs
++AssignerActionForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\actions\assigneractionform.resx
++ClientSegmentEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\clients\clientsegmenteditform.resx
++AppelForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\appelform.resx
++NoteForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\noteform.resx
++NoteForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\noteform.designer.cs
++WhatsAppForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\communications\whatsappform.designer.cs
++DocumentLitigeForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\documentlitigeform.designer.cs
++DocumentLitigeForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\documentlitigeform.resx
++EtapeLitigeEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\etapelitigeeditform.resx
++EtapeLitigeListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\etapelitigelistform.resx
++EtapeLitigeListForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\etapelitigelistform.designer.cs
++LitigeEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\litigeeditform.resx
++LitigeListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\litigelistform.resx
++NotificationsLitigeForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\notificationslitigeform.resx
++CommentaireForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\commentaireform.resx
++LettreRelanceForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\lettrerelanceform.resx
++NotificationsLitigeForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\litiges\notificationslitigeform.designer.cs
++LettreRelanceForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\lettrerelanceform.designer.cs
++ModeleRelanceListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\modelerelancelistform.resx
++PlanificationRelanceListForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\planificationrelancelistform.resx
++ModeleRelanceEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\modelerelanceeditform.resx
++RegleRelanceEditForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\relances\reglerelanceeditform.resx
++IDatabaseAdminService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\interfaces\idatabaseadminservice.cs
++DatabaseAdminService.cs
i:{f6e01715-c76c-4211-82bc-fa33ed177337}:e:\recouvrex  app\recouvrex.business\services\databaseadminservice.cs
++EncryptionHelper.cs
i:{c34668f0-d9d4-49c8-a479-cd85a614f189}:e:\recouvrex  app\recouvrex.common\security\encryptionhelper.cs
++Admin
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\admin\
++DatabaseConnectionForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\admin\databaseconnectionform.cs
++DatabaseConnectionForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\admin\databaseconnectionform.designer.cs
++DatabaseAdminForm.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\admin\databaseadminform.cs
++DatabaseAdminForm.Designer.cs
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\admin\databaseadminform.designer.cs
++DatabaseAdminForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\admin\databaseadminform.resx
++DatabaseConnectionForm.resx
i:{5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b}:e:\recouvrex  app\recouvrex.winforms\forms\admin\databaseconnectionform.resx
++Microsoft.Data.SqlClient (5.1.5)
