using RecouvreX.Models.Reporting;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de reporting
    /// </summary>
    public interface IReportingService
    {
        /// <summary>
        /// Récupère les statistiques de recouvrement pour une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Statistiques de recouvrement pour la période spécifiée</returns>
        Task<StatistiqueRecouvrement> GetStatistiquesRecouvrementAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère les statistiques de recouvrement par client
        /// </summary>
        /// <param name="clientId">Identifiant du client (0 pour tous les clients)</param>
        /// <param name="dateDebut">Date de début (optionnelle)</param>
        /// <param name="dateFin">Date de fin (optionnelle)</param>
        /// <returns>Liste des statistiques de recouvrement par client</returns>
        Task<IEnumerable<StatistiqueClient>> GetStatistiquesClientAsync(int clientId = 0, DateTime? dateDebut = null, DateTime? dateFin = null);

        /// <summary>
        /// Récupère les indicateurs de performance pour le tableau de bord
        /// </summary>
        /// <returns>Liste des indicateurs de performance</returns>
        Task<IEnumerable<IndicateurPerformance>> GetIndicateursPerformanceAsync();

        /// <summary>
        /// Récupère les données pour un graphique de montants recouvrés par période
        /// </summary>
        /// <param name="periodeType">Type de période (jour, semaine, mois, trimestre, année)</param>
        /// <param name="nombrePeriodes">Nombre de périodes à inclure</param>
        /// <returns>Données pour le graphique</returns>
        Task<DonneeGraphique> GetGraphiqueMontantsRecouvresAsync(string periodeType, int nombrePeriodes);

        /// <summary>
        /// Récupère les données pour un graphique de délais de paiement par client
        /// </summary>
        /// <param name="nombreClients">Nombre de clients à inclure</param>
        /// <param name="dateDebut">Date de début (optionnelle)</param>
        /// <param name="dateFin">Date de fin (optionnelle)</param>
        /// <returns>Données pour le graphique</returns>
        Task<DonneeGraphique> GetGraphiqueDelaisPaiementAsync(int nombreClients, DateTime? dateDebut = null, DateTime? dateFin = null);

        /// <summary>
        /// Récupère les données pour un graphique de répartition des factures par statut
        /// </summary>
        /// <returns>Données pour le graphique</returns>
        Task<DonneeGraphique> GetGraphiqueRepartitionFacturesAsync();

        /// <summary>
        /// Récupère les données pour un graphique d'efficacité des relances
        /// </summary>
        /// <param name="dateDebut">Date de début (optionnelle)</param>
        /// <param name="dateFin">Date de fin (optionnelle)</param>
        /// <returns>Données pour le graphique</returns>
        Task<DonneeGraphique> GetGraphiqueEfficaciteRelancesAsync(DateTime? dateDebut = null, DateTime? dateFin = null);

        /// <summary>
        /// Génère un rapport
        /// </summary>
        /// <param name="type">Type de rapport</param>
        /// <param name="titre">Titre du rapport</param>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <param name="format">Format du rapport</param>
        /// <param name="parametres">Paramètres supplémentaires (au format JSON)</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui génère le rapport</param>
        /// <returns>Rapport généré</returns>
        Task<Rapport> GenererRapportAsync(string type, string titre, DateTime dateDebut, DateTime dateFin, string format, string parametres, int utilisateurId);

        /// <summary>
        /// Programme un rapport pour génération périodique
        /// </summary>
        /// <param name="rapport">Rapport à programmer</param>
        /// <param name="frequence">Fréquence de génération</param>
        /// <param name="dateProchainRapport">Date de prochaine génération</param>
        /// <param name="destinataires">Liste des destinataires (au format JSON)</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui programme le rapport</param>
        /// <returns>Rapport programmé</returns>
        Task<Rapport> ProgrammerRapportAsync(Rapport rapport, string frequence, DateTime dateProchainRapport, string destinataires, int utilisateurId);

        /// <summary>
        /// Exécute les rapports programmés
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui exécute les rapports</param>
        /// <returns>Nombre de rapports générés</returns>
        Task<int> ExecuteScheduledReportsAsync(int utilisateurId);
    }
}
