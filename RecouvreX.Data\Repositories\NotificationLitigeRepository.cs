using Dapper;
using Microsoft.Extensions.Configuration;
using RecouvreX.Data.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;
using System.Data.SqlClient;

namespace RecouvreX.Data.Repositories
{
    /// <summary>
    /// Repository pour les notifications de litiges
    /// </summary>
    public class NotificationLitigeRepository : INotificationLitigeRepository
    {
        private readonly string _connectionString;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="configuration">Configuration de l'application</param>
        public NotificationLitigeRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        /// <summary>
        /// Récupère toutes les notifications
        /// </summary>
        /// <returns>Liste de toutes les notifications</returns>
        public async Task<IEnumerable<NotificationLitige>> GetAllAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT n.*, l.*, u.*
                        FROM NotificationsLitige n
                        LEFT JOIN Litiges l ON n.LitigeId = l.Id
                        LEFT JOIN Utilisateurs u ON n.UtilisateurId = u.Id
                        ORDER BY n.DateNotification DESC";

                    var notificationsDict = new Dictionary<int, NotificationLitige>();

                    var notifications = await connection.QueryAsync<NotificationLitige, Litige, Utilisateur, NotificationLitige>(
                        query,
                        (notification, litige, utilisateur) =>
                        {
                            if (!notificationsDict.TryGetValue(notification.Id, out var existingNotification))
                            {
                                existingNotification = notification;
                                existingNotification.Litige = litige;
                                existingNotification.Utilisateur = utilisateur;
                                notificationsDict.Add(existingNotification.Id, existingNotification);
                            }

                            return existingNotification;
                        },
                        splitOn: "Id,Id"
                    );

                    return notificationsDict.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération de toutes les notifications");
                throw;
            }
        }

        /// <summary>
        /// Récupère une notification par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la notification</param>
        /// <returns>Notification trouvée ou null</returns>
        public async Task<NotificationLitige> GetByIdAsync(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT n.*, l.*, u.*
                        FROM NotificationsLitige n
                        LEFT JOIN Litiges l ON n.LitigeId = l.Id
                        LEFT JOIN Utilisateurs u ON n.UtilisateurId = u.Id
                        WHERE n.Id = @Id";

                    var notifications = await connection.QueryAsync<NotificationLitige, Litige, Utilisateur, NotificationLitige>(
                        query,
                        (notification, litige, utilisateur) =>
                        {
                            notification.Litige = litige;
                            notification.Utilisateur = utilisateur;
                            return notification;
                        },
                        new { Id = id },
                        splitOn: "Id,Id"
                    );

                    return notifications.FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération de la notification {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Récupère toutes les notifications pour un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des notifications de l'utilisateur</returns>
        public async Task<IEnumerable<NotificationLitige>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT n.*, l.*, u.*
                        FROM NotificationsLitige n
                        LEFT JOIN Litiges l ON n.LitigeId = l.Id
                        LEFT JOIN Utilisateurs u ON n.UtilisateurId = u.Id
                        WHERE n.UtilisateurId = @UtilisateurId
                        ORDER BY n.DateNotification DESC";

                    var notificationsDict = new Dictionary<int, NotificationLitige>();

                    var notifications = await connection.QueryAsync<NotificationLitige, Litige, Utilisateur, NotificationLitige>(
                        query,
                        (notification, litige, utilisateur) =>
                        {
                            if (!notificationsDict.TryGetValue(notification.Id, out var existingNotification))
                            {
                                existingNotification = notification;
                                existingNotification.Litige = litige;
                                existingNotification.Utilisateur = utilisateur;
                                notificationsDict.Add(existingNotification.Id, existingNotification);
                            }

                            return existingNotification;
                        },
                        new { UtilisateurId = utilisateurId },
                        splitOn: "Id,Id"
                    );

                    return notificationsDict.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des notifications pour l'utilisateur {UtilisateurId}", utilisateurId);
                throw;
            }
        }

        /// <summary>
        /// Récupère les notifications non lues pour un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des notifications non lues de l'utilisateur</returns>
        public async Task<IEnumerable<NotificationLitige>> GetUnreadByUtilisateurIdAsync(int utilisateurId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT n.*, l.*, u.*
                        FROM NotificationsLitige n
                        LEFT JOIN Litiges l ON n.LitigeId = l.Id
                        LEFT JOIN Utilisateurs u ON n.UtilisateurId = u.Id
                        WHERE n.UtilisateurId = @UtilisateurId AND n.EstLue = 0
                        ORDER BY n.DateNotification DESC";

                    var notificationsDict = new Dictionary<int, NotificationLitige>();

                    var notifications = await connection.QueryAsync<NotificationLitige, Litige, Utilisateur, NotificationLitige>(
                        query,
                        (notification, litige, utilisateur) =>
                        {
                            if (!notificationsDict.TryGetValue(notification.Id, out var existingNotification))
                            {
                                existingNotification = notification;
                                existingNotification.Litige = litige;
                                existingNotification.Utilisateur = utilisateur;
                                notificationsDict.Add(existingNotification.Id, existingNotification);
                            }

                            return existingNotification;
                        },
                        new { UtilisateurId = utilisateurId },
                        splitOn: "Id,Id"
                    );

                    return notificationsDict.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des notifications non lues pour l'utilisateur {UtilisateurId}", utilisateurId);
                throw;
            }
        }

        /// <summary>
        /// Récupère toutes les notifications pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des notifications du litige</returns>
        public async Task<IEnumerable<NotificationLitige>> GetByLitigeIdAsync(int litigeId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT n.*, l.*, u.*
                        FROM NotificationsLitige n
                        LEFT JOIN Litiges l ON n.LitigeId = l.Id
                        LEFT JOIN Utilisateurs u ON n.UtilisateurId = u.Id
                        WHERE n.LitigeId = @LitigeId
                        ORDER BY n.DateNotification DESC";

                    var notificationsDict = new Dictionary<int, NotificationLitige>();

                    var notifications = await connection.QueryAsync<NotificationLitige, Litige, Utilisateur, NotificationLitige>(
                        query,
                        (notification, litige, utilisateur) =>
                        {
                            if (!notificationsDict.TryGetValue(notification.Id, out var existingNotification))
                            {
                                existingNotification = notification;
                                existingNotification.Litige = litige;
                                existingNotification.Utilisateur = utilisateur;
                                notificationsDict.Add(existingNotification.Id, existingNotification);
                            }

                            return existingNotification;
                        },
                        new { LitigeId = litigeId },
                        splitOn: "Id,Id"
                    );

                    return notificationsDict.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des notifications pour le litige {LitigeId}", litigeId);
                throw;
            }
        }

        /// <summary>
        /// Ajoute une notification
        /// </summary>
        /// <param name="notification">Notification à ajouter</param>
        /// <returns>Notification ajoutée</returns>
        public async Task<NotificationLitige> AddAsync(NotificationLitige notification)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        INSERT INTO NotificationsLitige (LitigeId, UtilisateurId, Type, Message, DateNotification, EstLue)
                        VALUES (@LitigeId, @UtilisateurId, @Type, @Message, @DateNotification, @EstLue);
                        SELECT CAST(SCOPE_IDENTITY() as int)";

                    var id = await connection.QuerySingleAsync<int>(query, new
                    {
                        notification.LitigeId,
                        notification.UtilisateurId,
                        notification.Type,
                        notification.Message,
                        DateNotification = notification.DateNotification,
                        notification.EstLue
                    });

                    notification.Id = id;
                    return notification;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ajout d'une notification");
                throw;
            }
        }

        /// <summary>
        /// Marque une notification comme lue
        /// </summary>
        /// <param name="id">Identifiant de la notification</param>
        /// <returns>True si la notification a été marquée comme lue</returns>
        public async Task<bool> MarkAsReadAsync(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        UPDATE NotificationsLitige
                        SET EstLue = 1
                        WHERE Id = @Id";

                    var rowsAffected = await connection.ExecuteAsync(query, new { Id = id });
                    return rowsAffected > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du marquage de la notification {Id} comme lue", id);
                throw;
            }
        }

        /// <summary>
        /// Marque toutes les notifications d'un utilisateur comme lues
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Nombre de notifications marquées comme lues</returns>
        public async Task<int> MarkAllAsReadAsync(int utilisateurId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        UPDATE NotificationsLitige
                        SET EstLue = 1
                        WHERE UtilisateurId = @UtilisateurId AND EstLue = 0";

                    return await connection.ExecuteAsync(query, new { UtilisateurId = utilisateurId });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du marquage de toutes les notifications de l'utilisateur {UtilisateurId} comme lues", utilisateurId);
                throw;
            }
        }
    }
}
