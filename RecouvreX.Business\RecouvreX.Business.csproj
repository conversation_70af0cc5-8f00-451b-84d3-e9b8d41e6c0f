<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="itext7" Version="8.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RecouvreX.DataAccess\RecouvreX.DataAccess.csproj" />
    <ProjectReference Include="..\RecouvreX.Models\RecouvreX.Models.csproj" />
    <ProjectReference Include="..\RecouvreX.Common\RecouvreX.Common.csproj" />
  </ItemGroup>

</Project>
