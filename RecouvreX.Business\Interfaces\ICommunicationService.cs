using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des communications
    /// </summary>
    public interface ICommunicationService
    {
        /// <summary>
        /// Récupère toutes les communications
        /// </summary>
        /// <returns>Liste des communications</returns>
        Task<IEnumerable<Communication>> GetAllAsync();

        /// <summary>
        /// Récupère une communication par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la communication</param>
        /// <returns>Communication trouvée ou null</returns>
        Task<Communication> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les communications par facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des communications pour la facture spécifiée</returns>
        Task<IEnumerable<Communication>> GetByFactureIdAsync(int factureId);

        /// <summary>
        /// Récupère les communications par client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des communications pour le client spécifié</returns>
        Task<IEnumerable<Communication>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère les communications par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des communications pour l'utilisateur spécifié</returns>
        Task<IEnumerable<Communication>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les communications par type
        /// </summary>
        /// <param name="type">Type de communication</param>
        /// <returns>Liste des communications du type spécifié</returns>
        Task<IEnumerable<Communication>> GetByTypeAsync(string type);

        /// <summary>
        /// Récupère les communications par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des communications pour la période spécifiée</returns>
        Task<IEnumerable<Communication>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère les communications nécessitant un suivi
        /// </summary>
        /// <returns>Liste des communications nécessitant un suivi</returns>
        Task<IEnumerable<Communication>> GetRequiringSuiviAsync();

        /// <summary>
        /// Crée une nouvelle communication
        /// </summary>
        /// <param name="communication">Communication à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Communication créée avec son identifiant généré</returns>
        Task<Communication> CreateAsync(Communication communication, int creePar);

        /// <summary>
        /// Met à jour une communication existante
        /// </summary>
        /// <param name="communication">Communication à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Communication mise à jour</returns>
        Task<Communication> UpdateAsync(Communication communication, int modifiePar);

        /// <summary>
        /// Supprime une communication
        /// </summary>
        /// <param name="id">Identifiant de la communication à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Marque une communication comme lue
        /// </summary>
        /// <param name="id">Identifiant de la communication</param>
        /// <param name="dateLecture">Date de lecture (par défaut, la date actuelle)</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> MarkAsReadAsync(int id, DateTime? dateLecture = null);

        /// <summary>
        /// Met à jour le statut de suivi d'une communication
        /// </summary>
        /// <param name="id">Identifiant de la communication</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="notesSuivi">Notes de suivi</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateSuiviAsync(int id, bool suiviNecessaire, DateTime? dateSuivi, string notesSuivi, int modifiePar);

        /// <summary>
        /// Enregistre un appel téléphonique
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="contactClientId">Identifiant du contact client (optionnel)</param>
        /// <param name="direction">Direction de l'appel (Entrant/Sortant)</param>
        /// <param name="duree">Durée de l'appel en secondes</param>
        /// <param name="resultat">Résultat de l'appel</param>
        /// <param name="contenu">Contenu de l'appel</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Communication créée avec son identifiant généré</returns>
        Task<Communication> EnregistrerAppelAsync(int factureId, int? contactClientId, string direction, int duree, string resultat, string contenu, bool suiviNecessaire, DateTime? dateSuivi, int utilisateurId);

        /// <summary>
        /// Enregistre un email
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="contactClientId">Identifiant du contact client (optionnel)</param>
        /// <param name="direction">Direction de l'email (Entrant/Sortant)</param>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="contenu">Contenu de l'email</param>
        /// <param name="cheminFichier">Chemin vers le fichier attaché (optionnel)</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Communication créée avec son identifiant généré</returns>
        Task<Communication> EnregistrerEmailAsync(int factureId, int? contactClientId, string direction, string objet, string contenu, string cheminFichier, bool suiviNecessaire, DateTime? dateSuivi, int utilisateurId);

        /// <summary>
        /// Ajoute une note
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="contenu">Contenu de la note</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Communication créée avec son identifiant généré</returns>
        Task<Communication> AjouterNoteAsync(int factureId, string contenu, bool suiviNecessaire, DateTime? dateSuivi, int utilisateurId);

        /// <summary>
        /// Enregistre un SMS ou un message WhatsApp
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="contactClientId">Identifiant du contact client (optionnel)</param>
        /// <param name="direction">Direction du message (Entrant/Sortant)</param>
        /// <param name="contenu">Contenu du message</param>
        /// <param name="resultat">Résultat de l'envoi</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Communication créée avec son identifiant généré</returns>
        Task<Communication> EnregistrerSMSAsync(int factureId, int? contactClientId, string direction, string contenu, string resultat, bool suiviNecessaire, DateTime? dateSuivi, int utilisateurId);

        /// <summary>
        /// Envoie un email et l'enregistre dans les communications
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="contactClientId">Identifiant du contact client</param>
        /// <param name="destinataire">Adresse email du destinataire</param>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="contenu">Contenu de l'email</param>
        /// <param name="piecesJointes">Liste des pièces jointes (optionnel)</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <param name="configuration">Configuration de l'application</param>
        /// <returns>Tuple contenant la communication créée et un booléen indiquant si l'envoi a réussi</returns>
        Task<(Communication Communication, bool EmailEnvoye)> EnvoyerEtEnregistrerEmailAsync(
            int factureId,
            int? contactClientId,
            string destinataire,
            string objet,
            string contenu,
            List<string> piecesJointes,
            bool suiviNecessaire,
            DateTime? dateSuivi,
            int utilisateurId,
            IConfiguration configuration);
    }
}
