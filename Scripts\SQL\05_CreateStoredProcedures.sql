-- Script de création des procédures stockées pour la base de données RecouvreX
-- Ce script crée les procédures stockées utiles pour l'application

USE RecouvreX;
GO

-- Procédure pour mettre à jour le statut des factures en retard
CREATE OR ALTER PROCEDURE app.UpdateOverdueInvoices
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Mettre à jour les factures dont la date d'échéance est dépassée
    UPDATE app.Factures
    SET Statut = 'En retard',
        DateModification = GETDATE(),
        ModifiePar = 1 -- Utilisateur système
    WHERE DateEcheance < GETDATE()
      AND Statut IN ('En attente', 'Payée partiellement')
      AND EstActif = 1;
      
    -- Retourner le nombre de factures mises à jour
    RETURN @@ROWCOUNT;
END;
GO

-- Procédure pour calculer le solde d'un client
CREATE OR ALTER PROCEDURE app.CalculateClientBalance
    @ClientId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @SoldeTotal DECIMAL(18, 2);
    
    -- Calculer le total des factures
    SELECT @SoldeTotal = COALESCE(SUM(MontantRestant), 0)
    FROM app.Factures
    WHERE ClientId = @ClientId
      AND EstActif = 1;
      
    -- Mettre à jour le solde du client
    UPDATE app.Clients
    SET SoldeActuel = @SoldeTotal,
        DateModification = GETDATE(),
        ModifiePar = 1 -- Utilisateur système
    WHERE Id = @ClientId;
    
    -- Retourner le nouveau solde
    SELECT @SoldeTotal AS NouveauSolde;
END;
GO

-- Procédure pour obtenir les factures en retard par client
CREATE OR ALTER PROCEDURE app.GetOverdueInvoicesByClient
    @ClientId INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT f.Id, f.Numero, f.DateEmission, f.DateEcheance, 
           f.MontantTTC, f.MontantPaye, f.MontantRestant,
           DATEDIFF(DAY, f.DateEcheance, GETDATE()) AS JoursRetard,
           c.Id AS ClientId, c.RaisonSociale AS ClientNom,
           u.NomComplet AS CommercialNom
    FROM app.Factures f
    INNER JOIN app.Clients c ON f.ClientId = c.Id
    LEFT JOIN app.Utilisateurs u ON f.CommercialId = u.Id
    WHERE f.Statut = 'En retard'
      AND f.EstActif = 1
      AND (@ClientId IS NULL OR f.ClientId = @ClientId)
    ORDER BY JoursRetard DESC;
END;
GO

-- Procédure pour obtenir les paiements par facture
CREATE OR ALTER PROCEDURE app.GetPaymentsByInvoice
    @FactureId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT p.Id, p.Reference, p.DatePaiement, p.Montant, p.ModePaiement,
           fp.MontantAffecte, fp.Id AS FacturePaiementId
    FROM app.Paiements p
    INNER JOIN app.FacturePaiements fp ON p.Id = fp.PaiementId
    WHERE fp.FactureId = @FactureId
      AND p.EstActif = 1
      AND fp.EstActif = 1
    ORDER BY p.DatePaiement DESC;
END;
GO

-- Procédure pour obtenir les relances par facture
CREATE OR ALTER PROCEDURE app.GetRemindersByInvoice
    @FactureId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT r.Id, r.Type, r.DateRelance, r.Niveau, r.Statut,
           r.Contenu, r.ReponseClient, r.DateProchaineRelance,
           u.NomComplet AS UtilisateurNom
    FROM app.Relances r
    LEFT JOIN app.Utilisateurs u ON r.UtilisateurId = u.Id
    WHERE r.FactureId = @FactureId
      AND r.EstActif = 1
    ORDER BY r.DateRelance DESC;
END;
GO

-- Procédure pour obtenir les factures à relancer
CREATE OR ALTER PROCEDURE app.GetInvoicesToRemind
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Factures en retard sans relance récente (moins de 7 jours)
    SELECT f.Id, f.Numero, f.DateEmission, f.DateEcheance, 
           f.MontantTTC, f.MontantRestant,
           DATEDIFF(DAY, f.DateEcheance, GETDATE()) AS JoursRetard,
           c.Id AS ClientId, c.RaisonSociale AS ClientNom,
           u.Id AS CommercialId, u.NomComplet AS CommercialNom,
           COALESCE(MAX(r.Niveau), 0) AS DernierNiveauRelance,
           COALESCE(MAX(r.DateRelance), NULL) AS DateDerniereRelance
    FROM app.Factures f
    INNER JOIN app.Clients c ON f.ClientId = c.Id
    LEFT JOIN app.Utilisateurs u ON f.CommercialId = u.Id
    LEFT JOIN app.Relances r ON f.Id = r.FactureId AND r.EstActif = 1
    WHERE f.Statut = 'En retard'
      AND f.EstActif = 1
    GROUP BY f.Id, f.Numero, f.DateEmission, f.DateEcheance, 
             f.MontantTTC, f.MontantRestant,
             c.Id, c.RaisonSociale,
             u.Id, u.NomComplet
    HAVING COALESCE(MAX(r.DateRelance), '1900-01-01') < DATEADD(DAY, -7, GETDATE())
       OR MAX(r.DateRelance) IS NULL
    ORDER BY JoursRetard DESC;
END;
GO

-- Procédure pour obtenir les statistiques de recouvrement
CREATE OR ALTER PROCEDURE app.GetRecoveryStatistics
    @DateDebut DATE = NULL,
    @DateFin DATE = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Utiliser la période par défaut si non spécifiée
    SET @DateDebut = COALESCE(@DateDebut, DATEADD(MONTH, -1, GETDATE()));
    SET @DateFin = COALESCE(@DateFin, GETDATE());
    
    -- Statistiques globales
    SELECT 
        COUNT(DISTINCT f.Id) AS NombreFactures,
        SUM(f.MontantTTC) AS MontantTotalFactures,
        SUM(f.MontantPaye) AS MontantTotalPaye,
        SUM(f.MontantRestant) AS MontantTotalRestant,
        CASE WHEN SUM(f.MontantTTC) > 0 
             THEN (SUM(f.MontantPaye) / SUM(f.MontantTTC)) * 100 
             ELSE 0 
        END AS TauxRecouvrement,
        COUNT(DISTINCT CASE WHEN f.Statut = 'En retard' THEN f.Id END) AS NombreFacturesRetard,
        SUM(CASE WHEN f.Statut = 'En retard' THEN f.MontantRestant ELSE 0 END) AS MontantTotalRetard
    FROM app.Factures f
    WHERE f.DateEmission BETWEEN @DateDebut AND @DateFin
      AND f.EstActif = 1;
      
    -- Statistiques par commercial
    SELECT 
        u.Id AS CommercialId,
        u.NomComplet AS CommercialNom,
        COUNT(DISTINCT f.Id) AS NombreFactures,
        SUM(f.MontantTTC) AS MontantTotalFactures,
        SUM(f.MontantPaye) AS MontantTotalPaye,
        CASE WHEN SUM(f.MontantTTC) > 0 
             THEN (SUM(f.MontantPaye) / SUM(f.MontantTTC)) * 100 
             ELSE 0 
        END AS TauxRecouvrement
    FROM app.Factures f
    INNER JOIN app.Utilisateurs u ON f.CommercialId = u.Id
    WHERE f.DateEmission BETWEEN @DateDebut AND @DateFin
      AND f.EstActif = 1
    GROUP BY u.Id, u.NomComplet
    ORDER BY TauxRecouvrement DESC;
    
    -- Statistiques par mode de paiement
    SELECT 
        p.ModePaiement,
        COUNT(DISTINCT p.Id) AS NombrePaiements,
        SUM(p.Montant) AS MontantTotal
    FROM app.Paiements p
    WHERE p.DatePaiement BETWEEN @DateDebut AND @DateFin
      AND p.EstActif = 1
    GROUP BY p.ModePaiement
    ORDER BY MontantTotal DESC;
END;
GO

-- Procédure pour vérifier les permissions d'un utilisateur
CREATE OR ALTER PROCEDURE app.CheckUserPermission
    @UtilisateurId INT,
    @PermissionCode NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @HasPermission BIT = 0;
    
    SELECT @HasPermission = 1
    FROM app.Utilisateurs u
    INNER JOIN app.RolePermissions rp ON u.RoleId = rp.RoleId
    INNER JOIN app.Permissions p ON rp.PermissionId = p.Id
    WHERE u.Id = @UtilisateurId
      AND p.Code = @PermissionCode
      AND u.EstActif = 1
      AND rp.EstActif = 1
      AND p.EstActif = 1;
      
    SELECT @HasPermission AS HasPermission;
END;
GO

PRINT 'Procédures stockées créées avec succès.';
GO
