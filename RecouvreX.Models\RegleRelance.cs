using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une règle de planification des relances
    /// </summary>
    public class RegleRelance : BaseEntity
    {
        /// <summary>
        /// Nom de la règle
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Description de la règle
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Nombre de jours après l'échéance pour la première relance
        /// </summary>
        public int JoursApresPremiere { get; set; }

        /// <summary>
        /// Nombre de jours entre la première et la deuxième relance
        /// </summary>
        public int JoursEntrePremiereDeuxieme { get; set; }

        /// <summary>
        /// Nombre de jours entre la deuxième et la troisième relance
        /// </summary>
        public int JoursEntreDeuxiemeTroisieme { get; set; }

        /// <summary>
        /// Identifiant du modèle de relance pour le niveau 1
        /// </summary>
        public int? ModeleRelanceNiveau1Id { get; set; }

        /// <summary>
        /// Identifiant du modèle de relance pour le niveau 2
        /// </summary>
        public int? ModeleRelanceNiveau2Id { get; set; }

        /// <summary>
        /// Identifiant du modèle de relance pour le niveau 3
        /// </summary>
        public int? ModeleRelanceNiveau3Id { get; set; }

        /// <summary>
        /// Modèle de relance pour le niveau 1 (navigation property)
        /// </summary>
        public ModeleRelance ModeleRelanceNiveau1 { get; set; }

        /// <summary>
        /// Modèle de relance pour le niveau 2 (navigation property)
        /// </summary>
        public ModeleRelance ModeleRelanceNiveau2 { get; set; }

        /// <summary>
        /// Modèle de relance pour le niveau 3 (navigation property)
        /// </summary>
        public ModeleRelance ModeleRelanceNiveau3 { get; set; }

        /// <summary>
        /// Type de client concerné par la règle (null pour tous les clients)
        /// </summary>
        public string TypeClient { get; set; }

        /// <summary>
        /// Montant minimum des factures concernées par la règle
        /// </summary>
        public decimal? MontantMinimum { get; set; }

        /// <summary>
        /// Indique si la règle est active
        /// </summary>
        public bool EstActive { get; set; }

        /// <summary>
        /// Indique si la validation manuelle est requise avant l'envoi des relances
        /// </summary>
        public bool RequiertValidation { get; set; }

        /// <summary>
        /// Priorité de la règle (les règles avec une priorité plus élevée sont appliquées en premier)
        /// </summary>
        public int Priorite { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a créé la règle
        /// </summary>
        public int UtilisateurId { get; set; }

        /// <summary>
        /// Utilisateur qui a créé la règle (navigation property)
        /// </summary>
        public Utilisateur Utilisateur { get; set; }
    }
}
