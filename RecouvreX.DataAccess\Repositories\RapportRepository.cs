using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models.Reporting;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour la gestion des rapports
    /// </summary>
    public class RapportRepository : BaseRepository<Rapport>, IRapportRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public RapportRepository(DatabaseConnection dbConnection) : base(dbConnection, "Rapports")
        {
        }

        /// <summary>
        /// Récupère les rapports par type
        /// </summary>
        /// <param name="type">Type de rapport</param>
        /// <returns>Liste des rapports du type spécifié</returns>
        public async Task<IEnumerable<Rapport>> GetByTypeAsync(string type)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE Type = @Type AND EstActif = 1 ORDER BY DateCreation DESC";
                return await connection.QueryAsync<Rapport>(query, new { Type = type });
            }
        }

        /// <summary>
        /// Récupère les rapports par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des rapports créés par l'utilisateur spécifié</returns>
        public async Task<IEnumerable<Rapport>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE UtilisateurId = @UtilisateurId AND EstActif = 1 ORDER BY DateCreation DESC";
                return await connection.QueryAsync<Rapport>(query, new { UtilisateurId = utilisateurId });
            }
        }

        /// <summary>
        /// Récupère les rapports par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des rapports pour la période spécifiée</returns>
        public async Task<IEnumerable<Rapport>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE DateCreation BETWEEN @DateDebut AND @DateFin AND EstActif = 1 ORDER BY DateCreation DESC";
                return await connection.QueryAsync<Rapport>(query, new { DateDebut = dateDebut, DateFin = dateFin });
            }
        }

        /// <summary>
        /// Récupère les rapports programmés à générer
        /// </summary>
        /// <param name="dateReference">Date de référence (par défaut, la date actuelle)</param>
        /// <returns>Liste des rapports programmés à générer</returns>
        public async Task<IEnumerable<Rapport>> GetScheduledReportsAsync(DateTime? dateReference = null)
        {
            var date = dateReference ?? DateTime.Now;

            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $@"
                    SELECT * FROM {_tableName}
                    WHERE EstProgramme = 1
                    AND DateProchainRapport <= @Date
                    AND EstActif = 1
                    ORDER BY DateProchainRapport";
                return await connection.QueryAsync<Rapport>(query, new { Date = date });
            }
        }

        /// <summary>
        /// Met à jour la date de prochaine génération d'un rapport programmé
        /// </summary>
        /// <param name="id">Identifiant du rapport</param>
        /// <param name="dateProchainRapport">Date de prochaine génération</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateNextGenerationDateAsync(int id, DateTime dateProchainRapport, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $@"
                    UPDATE {_tableName}
                    SET DateProchainRapport = @DateProchainRapport,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id AND EstActif = 1";

                var parameters = new
                {
                    Id = id,
                    DateProchainRapport = dateProchainRapport,
                    DateModification = DateTime.Now,
                    ModifiePar = modifiePar
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }
    }
}
