using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des litiges
    /// </summary>
    public interface ILitigeService
    {
        /// <summary>
        /// Récupère tous les litiges
        /// </summary>
        /// <returns>Liste de tous les litiges</returns>
        Task<IEnumerable<Litige>> GetAllAsync();

        /// <summary>
        /// Récupère les litiges pour une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début de la période</param>
        /// <param name="dateFin">Date de fin de la période</param>
        /// <returns>Liste des litiges pour la période</returns>
        Task<IEnumerable<Litige>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère un litige par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du litige</param>
        /// <returns>Litige trouvé ou null</returns>
        Task<Litige> GetByIdAsync(int id);

        /// <summary>
        /// Récupère un litige avec toutes ses informations associées
        /// </summary>
        /// <param name="id">Identifiant du litige</param>
        /// <returns>Litige avec ses informations associées</returns>
        Task<Litige> GetWithDetailsAsync(int id);

        /// <summary>
        /// Récupère tous les litiges d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des litiges de la facture</returns>
        Task<IEnumerable<Litige>> GetByFactureIdAsync(int factureId);

        /// <summary>
        /// Récupère tous les litiges d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des litiges du client</returns>
        Task<IEnumerable<Litige>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère tous les litiges par statut
        /// </summary>
        /// <param name="statut">Statut des litiges</param>
        /// <returns>Liste des litiges ayant le statut spécifié</returns>
        Task<IEnumerable<Litige>> GetByStatutAsync(string statut);

        /// <summary>
        /// Récupère tous les litiges par catégorie
        /// </summary>
        /// <param name="categorieId">Identifiant de la catégorie</param>
        /// <returns>Liste des litiges de la catégorie spécifiée</returns>
        Task<IEnumerable<Litige>> GetByCategorieIdAsync(int categorieId);

        /// <summary>
        /// Récupère tous les litiges par étape
        /// </summary>
        /// <param name="etapeId">Identifiant de l'étape</param>
        /// <returns>Liste des litiges à l'étape spécifiée</returns>
        Task<IEnumerable<Litige>> GetByEtapeIdAsync(int etapeId);

        /// <summary>
        /// Récupère tous les litiges assignés à un responsable
        /// </summary>
        /// <param name="responsableId">Identifiant du responsable</param>
        /// <returns>Liste des litiges assignés au responsable spécifié</returns>
        Task<IEnumerable<Litige>> GetByResponsableIdAsync(int responsableId);

        /// <summary>
        /// Récupère tous les litiges dont la date d'échéance est dépassée
        /// </summary>
        /// <returns>Liste des litiges en retard</returns>
        Task<IEnumerable<Litige>> GetOverdueAsync();

        /// <summary>
        /// Crée un nouveau litige
        /// </summary>
        /// <param name="litige">Litige à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui crée le litige</param>
        /// <returns>Litige créé</returns>
        Task<Litige> CreateAsync(Litige litige, int creePar);

        /// <summary>
        /// Met à jour un litige existant
        /// </summary>
        /// <param name="litige">Litige à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui modifie le litige</param>
        /// <returns>Litige mis à jour</returns>
        Task<Litige> UpdateAsync(Litige litige, int modifiePar);

        /// <summary>
        /// Supprime un litige
        /// </summary>
        /// <param name="id">Identifiant du litige à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui supprime le litige</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Ajoute un commentaire à un litige
        /// </summary>
        /// <param name="commentaire">Commentaire à ajouter</param>
        /// <returns>Commentaire ajouté</returns>
        Task<CommentaireLitige> AddCommentaireAsync(CommentaireLitige commentaire);

        /// <summary>
        /// Récupère tous les commentaires d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des commentaires du litige</returns>
        Task<IEnumerable<CommentaireLitige>> GetCommentairesAsync(int litigeId);

        /// <summary>
        /// Change l'étape d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <param name="etapeId">Identifiant de la nouvelle étape</param>
        /// <param name="commentaire">Commentaire associé au changement d'étape</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si le changement a réussi, sinon False</returns>
        Task<bool> ChangeEtapeAsync(int litigeId, int etapeId, string commentaire, int modifiePar);

        /// <summary>
        /// Résout un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <param name="solution">Solution appliquée</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la résolution a réussi, sinon False</returns>
        Task<bool> ResoudreLitigeAsync(int litigeId, string solution, int modifiePar);

        /// <summary>
        /// Récupère l'historique des étapes d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des historiques d'étapes du litige</returns>
        Task<IEnumerable<HistoriqueEtapeLitige>> GetHistoriqueEtapesAsync(int litigeId);

        /// <summary>
        /// Récupère toutes les catégories de litiges
        /// </summary>
        /// <returns>Liste de toutes les catégories de litiges</returns>
        Task<IEnumerable<CategorieLitige>> GetAllCategoriesAsync();

        /// <summary>
        /// Récupère toutes les étapes de litiges
        /// </summary>
        /// <returns>Liste de toutes les étapes de litiges</returns>
        Task<IEnumerable<EtapeLitige>> GetAllEtapesAsync();

        /// <summary>
        /// Récupère les statistiques des litiges
        /// </summary>
        /// <returns>Statistiques des litiges</returns>
        Task<Dictionary<string, object>> GetStatistiquesAsync();

        #region Gestion des catégories de litiges

        /// <summary>
        /// Récupère toutes les catégories de litiges avec le nombre de litiges associés
        /// </summary>
        /// <returns>Liste des catégories de litiges avec le nombre de litiges</returns>
        Task<IEnumerable<CategorieLitige>> GetAllCategoriesWithCountAsync();

        /// <summary>
        /// Récupère une catégorie de litige par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la catégorie</param>
        /// <returns>Catégorie de litige trouvée ou null</returns>
        Task<CategorieLitige> GetCategorieByIdAsync(int id);

        /// <summary>
        /// Crée une nouvelle catégorie de litige
        /// </summary>
        /// <param name="categorie">Catégorie à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui crée la catégorie</param>
        /// <returns>Catégorie créée</returns>
        Task<CategorieLitige> CreateCategorieAsync(CategorieLitige categorie, int creePar);

        /// <summary>
        /// Met à jour une catégorie de litige existante
        /// </summary>
        /// <param name="categorie">Catégorie à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui modifie la catégorie</param>
        /// <returns>Catégorie mise à jour</returns>
        Task<CategorieLitige> UpdateCategorieAsync(CategorieLitige categorie, int modifiePar);

        /// <summary>
        /// Supprime une catégorie de litige
        /// </summary>
        /// <param name="id">Identifiant de la catégorie à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui supprime la catégorie</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteCategorieAsync(int id, int supprimePar);

        #endregion

        #region Gestion des étapes de litiges

        /// <summary>
        /// Récupère toutes les étapes de litiges avec le nombre de litiges associés
        /// </summary>
        /// <returns>Liste des étapes de litiges avec le nombre de litiges</returns>
        Task<IEnumerable<EtapeLitige>> GetAllEtapesWithCountAsync();

        /// <summary>
        /// Récupère une étape de litige par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'étape</param>
        /// <returns>Étape de litige trouvée ou null</returns>
        Task<EtapeLitige> GetEtapeByIdAsync(int id);

        /// <summary>
        /// Crée une nouvelle étape de litige
        /// </summary>
        /// <param name="etape">Étape à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui crée l'étape</param>
        /// <returns>Étape créée</returns>
        Task<EtapeLitige> CreateEtapeAsync(EtapeLitige etape, int creePar);

        /// <summary>
        /// Met à jour une étape de litige existante
        /// </summary>
        /// <param name="etape">Étape à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui modifie l'étape</param>
        /// <returns>Étape mise à jour</returns>
        Task<EtapeLitige> UpdateEtapeAsync(EtapeLitige etape, int modifiePar);

        /// <summary>
        /// Supprime une étape de litige
        /// </summary>
        /// <param name="id">Identifiant de l'étape à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui supprime l'étape</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteEtapeAsync(int id, int supprimePar);

        #endregion

        #region Gestion des délais d'escalade

        /// <summary>
        /// Vérifie les litiges en retard et les escalade si nécessaire
        /// </summary>
        /// <returns>Nombre de litiges escaladés</returns>
        Task<int> VerifierEtEscaladerLitigesEnRetardAsync();

        /// <summary>
        /// Escalade un litige vers l'étape suivante
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <param name="commentaire">Commentaire associé à l'escalade</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'escalade</param>
        /// <returns>True si l'escalade a réussi, sinon False</returns>
        Task<bool> EscaladerLitigeAsync(int litigeId, string commentaire, int utilisateurId);

        #endregion

        #region Gestion des notifications

        /// <summary>
        /// Récupère toutes les notifications pour un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des notifications</returns>
        Task<IEnumerable<NotificationLitige>> GetNotificationsByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les notifications non lues pour un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des notifications non lues</returns>
        Task<IEnumerable<NotificationLitige>> GetUnreadNotificationsByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les notifications pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des notifications</returns>
        Task<IEnumerable<NotificationLitige>> GetNotificationsByLitigeIdAsync(int litigeId);

        /// <summary>
        /// Crée une notification pour un litige
        /// </summary>
        /// <param name="notification">Notification à créer</param>
        /// <returns>Notification créée</returns>
        Task<NotificationLitige> CreateNotificationAsync(NotificationLitige notification);

        /// <summary>
        /// Marque une notification comme lue
        /// </summary>
        /// <param name="notificationId">Identifiant de la notification</param>
        /// <returns>True si la notification a été marquée comme lue</returns>
        Task<bool> MarkNotificationAsReadAsync(int notificationId);

        /// <summary>
        /// Marque toutes les notifications d'un utilisateur comme lues
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Nombre de notifications marquées comme lues</returns>
        Task<int> MarkAllNotificationsAsReadAsync(int utilisateurId);

        #endregion

        #region Gestion des documents

        /// <summary>
        /// Récupère tous les documents pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des documents</returns>
        Task<IEnumerable<DocumentLitige>> GetDocumentsByLitigeIdAsync(int litigeId);

        /// <summary>
        /// Récupère un document par son identifiant
        /// </summary>
        /// <param name="documentId">Identifiant du document</param>
        /// <returns>Document trouvé ou null</returns>
        Task<DocumentLitige> GetDocumentByIdAsync(int documentId);

        /// <summary>
        /// Ajoute un document à un litige
        /// </summary>
        /// <param name="document">Document à ajouter</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui ajoute le document</param>
        /// <returns>Document ajouté</returns>
        Task<DocumentLitige> AddDocumentAsync(DocumentLitige document, int creePar);

        /// <summary>
        /// Met à jour un document
        /// </summary>
        /// <param name="document">Document à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui modifie le document</param>
        /// <returns>True si le document a été mis à jour</returns>
        Task<bool> UpdateDocumentAsync(DocumentLitige document, int modifiePar);

        /// <summary>
        /// Supprime un document
        /// </summary>
        /// <param name="documentId">Identifiant du document</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui supprime le document</param>
        /// <returns>True si le document a été supprimé</returns>
        Task<bool> DeleteDocumentAsync(int documentId, int supprimePar);

        #endregion
    }
}
