using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.Litiges
{
    /// <summary>
    /// Formulaire d'affichage des notifications de litiges
    /// </summary>
    public partial class NotificationsLitigeForm : Form
    {
        private readonly ILitigeService _litigeService;
        private readonly int _currentUserId;
        private List<NotificationLitige> _notifications = new List<NotificationLitige>();

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="litigeService">Service de gestion des litiges</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        public NotificationsLitigeForm(ILitigeService litigeService, int currentUserId)
        {
            _litigeService = litigeService ?? throw new ArgumentNullException(nameof(litigeService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }

        // La méthode InitializeComponent() a été déplacée dans le fichier NotificationsLitigeForm.Designer.cs

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void NotificationsLitigeForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Charger l'icône du formulaire
                try
                {
                    this.Icon = new Icon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "notification.ico"));
                }
                catch
                {
                    // Ignorer les erreurs d'icône
                }

                // Charger les notifications
                await LoadNotificationsAsync(false);

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors du chargement du formulaire de notifications de litiges");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Charge les notifications de l'utilisateur
        /// </summary>
        private async Task LoadNotificationsAsync(bool showAll)
        {
            try
            {
                // Récupérer les notifications
                if (showAll)
                {
                    _notifications = (await _litigeService.GetNotificationsByUtilisateurIdAsync(_currentUserId)).ToList();
                }
                else
                {
                    _notifications = (await _litigeService.GetUnreadNotificationsByUtilisateurIdAsync(_currentUserId)).ToList();
                }

                // Afficher les notifications dans le DataGridView
                DisplayNotifications();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des notifications");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Affiche les notifications dans le DataGridView
        /// </summary>
        private void DisplayNotifications()
        {
            var dataGridView = this.Controls.Find("notificationsDataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                // Réinitialiser le DataGridView
                dataGridView.DataSource = null;
                dataGridView.Columns.Clear();

                // Configurer les colonnes du DataGridView (méthode du Designer)
                ConfigureDataGridView(dataGridView);

                // Créer une table de données
                var dataTable = new DataTable();
                dataTable.Columns.Add("Id", typeof(int));
                dataTable.Columns.Add("Type", typeof(string));
                dataTable.Columns.Add("Message", typeof(string));
                dataTable.Columns.Add("Litige", typeof(string));
                dataTable.Columns.Add("DateNotification", typeof(string));
                dataTable.Columns.Add("EstLue", typeof(bool));

                // Remplir la table avec les données des notifications
                foreach (var notification in _notifications.OrderByDescending(n => n.DateNotification))
                {
                    dataTable.Rows.Add(
                        notification.Id,
                        GetNotificationTypeDisplay(notification.Type),
                        notification.Message,
                        $"#{notification.Litige?.Id} - {notification.Litige?.Facture?.Numero}",
                        notification.DateNotification.ToString("dd/MM/yyyy HH:mm"),
                        notification.EstLue
                    );
                }

                // Assigner la table au DataGridView
                dataGridView.DataSource = dataTable;

                // Formater les lignes en fonction de l'état de lecture
                dataGridView.CellFormatting += (s, e) =>
                {
                    if (e.RowIndex >= 0 && dataGridView.Rows[e.RowIndex].Cells["EstLue"] != null)
                    {
                        bool estLue = Convert.ToBoolean(dataGridView.Rows[e.RowIndex].Cells["EstLue"].Value);
                        if (!estLue)
                        {
                            e.CellStyle.Font = new Font(dataGridView.Font, FontStyle.Bold);
                        }
                    }
                };
            }
        }

        /// <summary>
        /// Retourne l'affichage du type de notification
        /// </summary>
        private string GetNotificationTypeDisplay(string type)
        {
            return type switch
            {
                "Escalade" => "Escalade",
                "EtapeChangee" => "Changement d'étape",
                "EcheanceProche" => "Échéance proche",
                "EcheanceDepassee" => "Échéance dépassée",
                _ => type
            };
        }

        /// <summary>
        /// Événement de changement d'état de la case à cocher Afficher tout
        /// </summary>
        private async void ShowAllCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                var checkBox = sender as CheckBox;
                if (checkBox != null)
                {
                    await LoadNotificationsAsync(checkBox.Checked);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du changement de filtre des notifications");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de double-clic sur une cellule du DataGridView
        /// </summary>
        private void NotificationsDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                ViewSelectedLitige();
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Voir le litige
        /// </summary>
        private void ViewLitigeButton_Click(object sender, EventArgs e)
        {
            ViewSelectedLitige();
        }

        /// <summary>
        /// Affiche le litige sélectionné
        /// </summary>
        private void ViewSelectedLitige()
        {
            try
            {
                var dataGridView = this.Controls.Find("notificationsDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int notificationId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    var notification = _notifications.FirstOrDefault(n => n.Id == notificationId);
                    if (notification != null && notification.Litige != null)
                    {
                        // Marquer la notification comme lue
                        _ = MarkNotificationAsReadAsync(notificationId);

                        // Ouvrir le formulaire de détail du litige
                        using (var form = new LitigeEditForm(
                            _litigeService,
                            null,
                            null,
                            null,
                            _currentUserId,
                            null,
                            null,
                            notification.LitigeId))
                        {
                            form.ShowDialog();
                        }

                        // Actualiser les notifications
                        var showAllCheckBox = this.Controls.Find("showAllCheckBox", true).FirstOrDefault() as CheckBox;
                        _ = LoadNotificationsAsync(showAllCheckBox?.Checked ?? false);
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une notification.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'affichage du litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Marquer comme lu
        /// </summary>
        private void MarkAsReadButton_Click(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = this.Controls.Find("notificationsDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int notificationId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    _ = MarkNotificationAsReadAsync(notificationId);
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une notification.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du marquage de la notification comme lue");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Marque une notification comme lue
        /// </summary>
        private async Task MarkNotificationAsReadAsync(int notificationId)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Marquer la notification comme lue
                await _litigeService.MarkNotificationAsReadAsync(notificationId);

                // Recharger les notifications
                var showAllCheckBox = this.Controls.Find("showAllCheckBox", true).FirstOrDefault() as CheckBox;
                await LoadNotificationsAsync(showAllCheckBox?.Checked ?? false);

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors du marquage de la notification comme lue");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Marquer tout comme lu
        /// </summary>
        private void MarkAllAsReadButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_notifications.Any(n => !n.EstLue))
                {
                    // Demander confirmation
                    if (MessageBox.Show("Êtes-vous sûr de vouloir marquer toutes les notifications comme lues ?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        _ = MarkAllNotificationsAsReadAsync();
                    }
                }
                else
                {
                    MessageBox.Show("Aucune notification non lue à marquer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du marquage de toutes les notifications comme lues");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Marque toutes les notifications comme lues
        /// </summary>
        private async Task MarkAllNotificationsAsReadAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Marquer toutes les notifications comme lues
                int count = await _litigeService.MarkAllNotificationsAsReadAsync(_currentUserId);

                // Recharger les notifications
                var showAllCheckBox = this.Controls.Find("showAllCheckBox", true).FirstOrDefault() as CheckBox;
                await LoadNotificationsAsync(showAllCheckBox?.Checked ?? false);

                // Restaurer le curseur
                this.Cursor = Cursors.Default;

                MessageBox.Show($"{count} notification(s) marquée(s) comme lue(s).", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors du marquage de toutes les notifications comme lues");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Actualiser
        /// </summary>
        private void RefreshButton_Click(object sender, EventArgs e)
        {
            try
            {
                var showAllCheckBox = this.Controls.Find("showAllCheckBox", true).FirstOrDefault() as CheckBox;
                _ = LoadNotificationsAsync(showAllCheckBox?.Checked ?? false);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'actualisation des notifications");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Fermer
        /// </summary>
        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }
}
