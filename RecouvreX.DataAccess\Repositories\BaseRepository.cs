using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Classe de base pour tous les repositories
    /// </summary>
    /// <typeparam name="T">Type d'entité</typeparam>
    public abstract class BaseRepository<T> : IRepository<T> where T : BaseEntity
    {
        protected readonly DatabaseConnection _dbConnection;
        protected readonly string _tableName;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        /// <param name="tableName">Nom de la table</param>
        protected BaseRepository(DatabaseConnection dbConnection, string tableName)
        {
            _dbConnection = dbConnection ?? throw new ArgumentNullException(nameof(dbConnection));
            _tableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
        }

        /// <summary>
        /// Récupère toutes les entités
        /// </summary>
        /// <returns>Liste des entités</returns>
        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE EstActif = 1";
                return await connection.QueryAsync<T>(query);
            }
        }

        /// <summary>
        /// Récupère une entité par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'entité</param>
        /// <returns>Entité trouvée ou null</returns>
        public virtual async Task<T> GetByIdAsync(int id)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE Id = @Id AND EstActif = 1";
                return await connection.QueryFirstOrDefaultAsync<T>(query, new { Id = id });
            }
        }

        /// <summary>
        /// Ajoute une nouvelle entité
        /// </summary>
        /// <param name="entity">Entité à ajouter</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Entité ajoutée avec son identifiant généré</returns>
        public virtual async Task<T> AddAsync(T entity, int userId)
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));

            entity.DateCreation = DateTime.Now;
            entity.CreePar = userId;
            entity.EstActif = true;

            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Construire la requête d'insertion dynamiquement
                var properties = typeof(T).GetProperties()
                    .Where(p => p.Name != "Id" && p.CanWrite && !p.PropertyType.IsClass)
                    .ToList();

                var columnNames = string.Join(", ", properties.Select(p => p.Name));
                var parameterNames = string.Join(", ", properties.Select(p => "@" + p.Name));

                var query = $@"
                    INSERT INTO {_tableName} ({columnNames})
                    VALUES ({parameterNames});
                    SELECT CAST(SCOPE_IDENTITY() as int)";

                var id = await connection.QuerySingleAsync<int>(query, entity);
                entity.Id = id;

                return entity;
            }
        }

        /// <summary>
        /// Met à jour une entité existante
        /// </summary>
        /// <param name="entity">Entité à mettre à jour</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Entité mise à jour</returns>
        public virtual async Task<T> UpdateAsync(T entity, int userId)
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));

            entity.DateModification = DateTime.Now;
            entity.ModifiePar = userId;

            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Construire la requête de mise à jour dynamiquement
                var properties = typeof(T).GetProperties()
                    .Where(p => p.Name != "Id" && p.CanWrite && !p.PropertyType.IsClass)
                    .ToList();

                var setClause = string.Join(", ", properties.Select(p => $"{p.Name} = @{p.Name}"));

                var query = $@"
                    UPDATE {_tableName}
                    SET {setClause}
                    WHERE Id = @Id";

                await connection.ExecuteAsync(query, entity);

                return entity;
            }
        }

        /// <summary>
        /// Supprime logiquement une entité (désactivation)
        /// </summary>
        /// <param name="id">Identifiant de l'entité à supprimer</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public virtual async Task<bool> DeleteAsync(int id, int userId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $@"
                    UPDATE {_tableName}
                    SET EstActif = 0, DateModification = @DateModification, ModifiePar = @ModifiePar
                    WHERE Id = @Id";

                var parameters = new
                {
                    Id = id,
                    DateModification = DateTime.Now,
                    ModifiePar = userId
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }

        /// <summary>
        /// Récupère les entités selon un critère de recherche
        /// </summary>
        /// <param name="whereClause">Clause WHERE SQL</param>
        /// <param name="parameters">Paramètres de la clause WHERE</param>
        /// <returns>Liste des entités correspondant au critère</returns>
        public virtual async Task<IEnumerable<T>> FindAsync(string whereClause, object parameters)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE {whereClause} AND EstActif = 1";
                return await connection.QueryAsync<T>(query, parameters);
            }
        }

        /// <summary>
        /// Récupère la connexion à la base de données
        /// </summary>
        /// <returns>Connexion à la base de données</returns>
        public virtual DatabaseConnection GetDatabaseConnection()
        {
            return _dbConnection;
        }
    }
}
