namespace RecouvreX.WinForms.Forms.Relances
{
    partial class RegleRelanceEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            mainPanel = new TableLayoutPanel();
            nomLabel = new Label();
            nomTextBox = new TextBox();
            typeClientLabel = new Label();
            typeClientComboBox = new ComboBox();
            descriptionLabel = new Label();
            descriptionTextBox = new TextBox();
            montantMinimumLabel = new Label();
            montantMinimumNumericUpDown = new NumericUpDown();
            joursApresPremierLabel = new Label();
            joursApresPremiere = new NumericUpDown();
            joursEntrePremiereDeuxiemeLabel = new Label();
            joursEntrePremiereDeuxieme = new NumericUpDown();
            joursEntreDeuxiemeTroisiemeLabel = new Label();
            joursEntreDeuxiemeTroisieme = new NumericUpDown();
            modeleNiveau1Label = new Label();
            modeleNiveau1ComboBox = new ComboBox();
            modeleNiveau2Label = new Label();
            modeleNiveau2ComboBox = new ComboBox();
            modeleNiveau3Label = new Label();
            modeleNiveau3ComboBox = new ComboBox();
            prioriteLabel = new Label();
            prioriteNumericUpDown = new NumericUpDown();
            requiertValidationLabel = new Label();
            requiertValidationCheckBox = new CheckBox();
            estActiveLabel = new Label();
            estActiveCheckBox = new CheckBox();
            buttonsPanel = new FlowLayoutPanel();
            cancelButton = new Button();
            saveButton = new Button();
            mainPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)montantMinimumNumericUpDown).BeginInit();
            ((System.ComponentModel.ISupportInitialize)joursApresPremiere).BeginInit();
            ((System.ComponentModel.ISupportInitialize)joursEntrePremiereDeuxieme).BeginInit();
            ((System.ComponentModel.ISupportInitialize)joursEntreDeuxiemeTroisieme).BeginInit();
            ((System.ComponentModel.ISupportInitialize)prioriteNumericUpDown).BeginInit();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.ColumnCount = 2;
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            mainPanel.Controls.Add(nomLabel, 0, 0);
            mainPanel.Controls.Add(nomTextBox, 1, 0);
            mainPanel.Controls.Add(typeClientLabel, 0, 1);
            mainPanel.Controls.Add(typeClientComboBox, 1, 1);
            mainPanel.Controls.Add(descriptionLabel, 0, 2);
            mainPanel.Controls.Add(descriptionTextBox, 1, 2);
            mainPanel.Controls.Add(montantMinimumLabel, 0, 3);
            mainPanel.Controls.Add(montantMinimumNumericUpDown, 1, 3);
            mainPanel.Controls.Add(joursApresPremierLabel, 0, 4);
            mainPanel.Controls.Add(joursApresPremiere, 1, 4);
            mainPanel.Controls.Add(joursEntrePremiereDeuxiemeLabel, 0, 5);
            mainPanel.Controls.Add(joursEntrePremiereDeuxieme, 1, 5);
            mainPanel.Controls.Add(joursEntreDeuxiemeTroisiemeLabel, 0, 6);
            mainPanel.Controls.Add(joursEntreDeuxiemeTroisieme, 1, 6);
            mainPanel.Controls.Add(modeleNiveau1Label, 0, 7);
            mainPanel.Controls.Add(modeleNiveau1ComboBox, 1, 7);
            mainPanel.Controls.Add(modeleNiveau2Label, 0, 8);
            mainPanel.Controls.Add(modeleNiveau2ComboBox, 1, 8);
            mainPanel.Controls.Add(modeleNiveau3Label, 0, 9);
            mainPanel.Controls.Add(modeleNiveau3ComboBox, 1, 9);
            mainPanel.Controls.Add(prioriteLabel, 0, 10);
            mainPanel.Controls.Add(prioriteNumericUpDown, 1, 10);
            mainPanel.Controls.Add(requiertValidationLabel, 0, 11);
            mainPanel.Controls.Add(requiertValidationCheckBox, 1, 11);
            mainPanel.Controls.Add(estActiveLabel, 0, 12);
            mainPanel.Controls.Add(estActiveCheckBox, 1, 12);
            mainPanel.Controls.Add(buttonsPanel, 1, 13);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Location = new Point(0, 0);
            mainPanel.Name = "mainPanel";
            mainPanel.Padding = new Padding(10);
            mainPanel.RowCount = 14;
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            mainPanel.Size = new Size(600, 600);
            mainPanel.TabIndex = 0;
            // 
            // nomLabel
            // 
            nomLabel.Dock = DockStyle.Fill;
            nomLabel.Location = new Point(13, 10);
            nomLabel.Name = "nomLabel";
            nomLabel.Size = new Size(168, 30);
            nomLabel.TabIndex = 0;
            nomLabel.Text = "Nom :";
            nomLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // nomTextBox
            // 
            nomTextBox.Dock = DockStyle.Fill;
            nomTextBox.Location = new Point(187, 13);
            nomTextBox.Name = "nomTextBox";
            nomTextBox.Size = new Size(400, 23);
            nomTextBox.TabIndex = 1;
            // 
            // typeClientLabel
            // 
            typeClientLabel.Dock = DockStyle.Fill;
            typeClientLabel.Location = new Point(13, 40);
            typeClientLabel.Name = "typeClientLabel";
            typeClientLabel.Size = new Size(168, 30);
            typeClientLabel.TabIndex = 2;
            typeClientLabel.Text = "Type client :";
            typeClientLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // typeClientComboBox
            // 
            typeClientComboBox.Dock = DockStyle.Fill;
            typeClientComboBox.Items.AddRange(new object[] { "", "Particulier", "Professionnel", "Administration" });
            typeClientComboBox.Location = new Point(187, 43);
            typeClientComboBox.Name = "typeClientComboBox";
            typeClientComboBox.Size = new Size(400, 23);
            typeClientComboBox.TabIndex = 3;
            // 
            // descriptionLabel
            // 
            descriptionLabel.Dock = DockStyle.Fill;
            descriptionLabel.Location = new Point(13, 70);
            descriptionLabel.Name = "descriptionLabel";
            descriptionLabel.Size = new Size(168, 60);
            descriptionLabel.TabIndex = 4;
            descriptionLabel.Text = "Description :";
            descriptionLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // descriptionTextBox
            // 
            descriptionTextBox.Dock = DockStyle.Fill;
            descriptionTextBox.Location = new Point(187, 73);
            descriptionTextBox.Multiline = true;
            descriptionTextBox.Name = "descriptionTextBox";
            descriptionTextBox.ScrollBars = ScrollBars.Vertical;
            descriptionTextBox.Size = new Size(400, 54);
            descriptionTextBox.TabIndex = 5;
            // 
            // montantMinimumLabel
            // 
            montantMinimumLabel.Dock = DockStyle.Fill;
            montantMinimumLabel.Location = new Point(13, 130);
            montantMinimumLabel.Name = "montantMinimumLabel";
            montantMinimumLabel.Size = new Size(168, 30);
            montantMinimumLabel.TabIndex = 6;
            montantMinimumLabel.Text = "Montant minimum (€) :";
            montantMinimumLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // montantMinimumNumericUpDown
            // 
            montantMinimumNumericUpDown.DecimalPlaces = 2;
            montantMinimumNumericUpDown.Dock = DockStyle.Fill;
            montantMinimumNumericUpDown.Increment = new decimal(new int[] { 100, 0, 0, 0 });
            montantMinimumNumericUpDown.Location = new Point(187, 133);
            montantMinimumNumericUpDown.Maximum = new decimal(new int[] { 1000000, 0, 0, 0 });
            montantMinimumNumericUpDown.Name = "montantMinimumNumericUpDown";
            montantMinimumNumericUpDown.Size = new Size(400, 23);
            montantMinimumNumericUpDown.TabIndex = 7;
            montantMinimumNumericUpDown.ThousandsSeparator = true;
            // 
            // joursApresPremierLabel
            // 
            joursApresPremierLabel.Dock = DockStyle.Fill;
            joursApresPremierLabel.Location = new Point(13, 160);
            joursApresPremierLabel.Name = "joursApresPremierLabel";
            joursApresPremierLabel.Size = new Size(168, 30);
            joursApresPremierLabel.TabIndex = 8;
            joursApresPremierLabel.Text = "Jours après échéance :";
            joursApresPremierLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // joursApresPremiere
            // 
            joursApresPremiere.Dock = DockStyle.Fill;
            joursApresPremiere.Location = new Point(187, 163);
            joursApresPremiere.Maximum = new decimal(new int[] { 90, 0, 0, 0 });
            joursApresPremiere.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            joursApresPremiere.Name = "joursApresPremiere";
            joursApresPremiere.Size = new Size(400, 23);
            joursApresPremiere.TabIndex = 9;
            joursApresPremiere.Value = new decimal(new int[] { 7, 0, 0, 0 });
            // 
            // joursEntrePremiereDeuxiemeLabel
            // 
            joursEntrePremiereDeuxiemeLabel.Dock = DockStyle.Fill;
            joursEntrePremiereDeuxiemeLabel.Location = new Point(13, 190);
            joursEntrePremiereDeuxiemeLabel.Name = "joursEntrePremiereDeuxiemeLabel";
            joursEntrePremiereDeuxiemeLabel.Size = new Size(168, 30);
            joursEntrePremiereDeuxiemeLabel.TabIndex = 10;
            joursEntrePremiereDeuxiemeLabel.Text = "Jours entre 1ère et 2ème :";
            joursEntrePremiereDeuxiemeLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // joursEntrePremiereDeuxieme
            // 
            joursEntrePremiereDeuxieme.Dock = DockStyle.Fill;
            joursEntrePremiereDeuxieme.Location = new Point(187, 193);
            joursEntrePremiereDeuxieme.Maximum = new decimal(new int[] { 90, 0, 0, 0 });
            joursEntrePremiereDeuxieme.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            joursEntrePremiereDeuxieme.Name = "joursEntrePremiereDeuxieme";
            joursEntrePremiereDeuxieme.Size = new Size(400, 23);
            joursEntrePremiereDeuxieme.TabIndex = 11;
            joursEntrePremiereDeuxieme.Value = new decimal(new int[] { 7, 0, 0, 0 });
            // 
            // joursEntreDeuxiemeTroisiemeLabel
            // 
            joursEntreDeuxiemeTroisiemeLabel.Dock = DockStyle.Fill;
            joursEntreDeuxiemeTroisiemeLabel.Location = new Point(13, 220);
            joursEntreDeuxiemeTroisiemeLabel.Name = "joursEntreDeuxiemeTroisiemeLabel";
            joursEntreDeuxiemeTroisiemeLabel.Size = new Size(168, 30);
            joursEntreDeuxiemeTroisiemeLabel.TabIndex = 12;
            joursEntreDeuxiemeTroisiemeLabel.Text = "Jours entre 2ème et 3ème :";
            joursEntreDeuxiemeTroisiemeLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // joursEntreDeuxiemeTroisieme
            // 
            joursEntreDeuxiemeTroisieme.Dock = DockStyle.Fill;
            joursEntreDeuxiemeTroisieme.Location = new Point(187, 223);
            joursEntreDeuxiemeTroisieme.Maximum = new decimal(new int[] { 90, 0, 0, 0 });
            joursEntreDeuxiemeTroisieme.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            joursEntreDeuxiemeTroisieme.Name = "joursEntreDeuxiemeTroisieme";
            joursEntreDeuxiemeTroisieme.Size = new Size(400, 23);
            joursEntreDeuxiemeTroisieme.TabIndex = 13;
            joursEntreDeuxiemeTroisieme.Value = new decimal(new int[] { 7, 0, 0, 0 });
            // 
            // modeleNiveau1Label
            // 
            modeleNiveau1Label.Dock = DockStyle.Fill;
            modeleNiveau1Label.Location = new Point(13, 250);
            modeleNiveau1Label.Name = "modeleNiveau1Label";
            modeleNiveau1Label.Size = new Size(168, 30);
            modeleNiveau1Label.TabIndex = 14;
            modeleNiveau1Label.Text = "Modèle niveau 1 :";
            modeleNiveau1Label.TextAlign = ContentAlignment.MiddleRight;
            // 
            // modeleNiveau1ComboBox
            // 
            modeleNiveau1ComboBox.Dock = DockStyle.Fill;
            modeleNiveau1ComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            modeleNiveau1ComboBox.Location = new Point(187, 253);
            modeleNiveau1ComboBox.Name = "modeleNiveau1ComboBox";
            modeleNiveau1ComboBox.Size = new Size(400, 23);
            modeleNiveau1ComboBox.TabIndex = 15;
            // 
            // modeleNiveau2Label
            // 
            modeleNiveau2Label.Dock = DockStyle.Fill;
            modeleNiveau2Label.Location = new Point(13, 280);
            modeleNiveau2Label.Name = "modeleNiveau2Label";
            modeleNiveau2Label.Size = new Size(168, 30);
            modeleNiveau2Label.TabIndex = 16;
            modeleNiveau2Label.Text = "Modèle niveau 2 :";
            modeleNiveau2Label.TextAlign = ContentAlignment.MiddleRight;
            // 
            // modeleNiveau2ComboBox
            // 
            modeleNiveau2ComboBox.Dock = DockStyle.Fill;
            modeleNiveau2ComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            modeleNiveau2ComboBox.Location = new Point(187, 283);
            modeleNiveau2ComboBox.Name = "modeleNiveau2ComboBox";
            modeleNiveau2ComboBox.Size = new Size(400, 23);
            modeleNiveau2ComboBox.TabIndex = 17;
            // 
            // modeleNiveau3Label
            // 
            modeleNiveau3Label.Dock = DockStyle.Fill;
            modeleNiveau3Label.Location = new Point(13, 310);
            modeleNiveau3Label.Name = "modeleNiveau3Label";
            modeleNiveau3Label.Size = new Size(168, 30);
            modeleNiveau3Label.TabIndex = 18;
            modeleNiveau3Label.Text = "Modèle niveau 3 :";
            modeleNiveau3Label.TextAlign = ContentAlignment.MiddleRight;
            // 
            // modeleNiveau3ComboBox
            // 
            modeleNiveau3ComboBox.Dock = DockStyle.Fill;
            modeleNiveau3ComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            modeleNiveau3ComboBox.Location = new Point(187, 313);
            modeleNiveau3ComboBox.Name = "modeleNiveau3ComboBox";
            modeleNiveau3ComboBox.Size = new Size(400, 23);
            modeleNiveau3ComboBox.TabIndex = 19;
            // 
            // prioriteLabel
            // 
            prioriteLabel.Dock = DockStyle.Fill;
            prioriteLabel.Location = new Point(13, 340);
            prioriteLabel.Name = "prioriteLabel";
            prioriteLabel.Size = new Size(168, 30);
            prioriteLabel.TabIndex = 20;
            prioriteLabel.Text = "Priorité :";
            prioriteLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // prioriteNumericUpDown
            // 
            prioriteNumericUpDown.Dock = DockStyle.Fill;
            prioriteNumericUpDown.Location = new Point(187, 343);
            prioriteNumericUpDown.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            prioriteNumericUpDown.Name = "prioriteNumericUpDown";
            prioriteNumericUpDown.Size = new Size(400, 23);
            prioriteNumericUpDown.TabIndex = 21;
            prioriteNumericUpDown.Value = new decimal(new int[] { 1, 0, 0, 0 });
            // 
            // requiertValidationLabel
            // 
            requiertValidationLabel.Dock = DockStyle.Fill;
            requiertValidationLabel.Location = new Point(13, 370);
            requiertValidationLabel.Name = "requiertValidationLabel";
            requiertValidationLabel.Size = new Size(168, 30);
            requiertValidationLabel.TabIndex = 22;
            requiertValidationLabel.Text = "Requiert validation :";
            requiertValidationLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // requiertValidationCheckBox
            // 
            requiertValidationCheckBox.Dock = DockStyle.Fill;
            requiertValidationCheckBox.Location = new Point(187, 373);
            requiertValidationCheckBox.Name = "requiertValidationCheckBox";
            requiertValidationCheckBox.Size = new Size(400, 24);
            requiertValidationCheckBox.TabIndex = 23;
            requiertValidationCheckBox.Text = "Les relances doivent être validées manuellement avant envoi";
            // 
            // estActiveLabel
            // 
            estActiveLabel.Dock = DockStyle.Fill;
            estActiveLabel.Location = new Point(13, 400);
            estActiveLabel.Name = "estActiveLabel";
            estActiveLabel.Size = new Size(168, 40);
            estActiveLabel.TabIndex = 24;
            estActiveLabel.Text = "Active :";
            estActiveLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // estActiveCheckBox
            // 
            estActiveCheckBox.Checked = true;
            estActiveCheckBox.CheckState = CheckState.Checked;
            estActiveCheckBox.Dock = DockStyle.Fill;
            estActiveCheckBox.Location = new Point(187, 403);
            estActiveCheckBox.Name = "estActiveCheckBox";
            estActiveCheckBox.Size = new Size(400, 34);
            estActiveCheckBox.TabIndex = 25;
            // 
            // buttonsPanel
            // 
            buttonsPanel.Controls.Add(cancelButton);
            buttonsPanel.Controls.Add(saveButton);
            buttonsPanel.Dock = DockStyle.Fill;
            buttonsPanel.FlowDirection = FlowDirection.RightToLeft;
            buttonsPanel.Location = new Point(187, 443);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.Size = new Size(400, 144);
            buttonsPanel.TabIndex = 26;
            // 
            // cancelButton
            // 
            cancelButton.Location = new Point(297, 3);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(100, 30);
            cancelButton.TabIndex = 0;
            cancelButton.Text = "Annuler";
            cancelButton.Click += CancelButton_Click;
            // 
            // saveButton
            // 
            saveButton.Location = new Point(191, 3);
            saveButton.Name = "saveButton";
            saveButton.Size = new Size(100, 30);
            saveButton.TabIndex = 1;
            saveButton.Text = "Enregistrer";
            saveButton.Click += SaveButton_Click;
            // 
            // RegleRelanceEditForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(600, 600);
            Controls.Add(mainPanel);
            Name = "RegleRelanceEditForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Règle de relance";
            Load += RegleRelanceEditForm_Load;
            mainPanel.ResumeLayout(false);
            mainPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)montantMinimumNumericUpDown).EndInit();
            ((System.ComponentModel.ISupportInitialize)joursApresPremiere).EndInit();
            ((System.ComponentModel.ISupportInitialize)joursEntrePremiereDeuxieme).EndInit();
            ((System.ComponentModel.ISupportInitialize)joursEntreDeuxiemeTroisieme).EndInit();
            ((System.ComponentModel.ISupportInitialize)prioriteNumericUpDown).EndInit();
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel mainPanel;
        private Label nomLabel;
        private TextBox nomTextBox;
        private Label typeClientLabel;
        private ComboBox typeClientComboBox;
        private Label descriptionLabel;
        private TextBox descriptionTextBox;
        private Label montantMinimumLabel;
        private NumericUpDown montantMinimumNumericUpDown;
        private Label joursApresPremierLabel;
        private NumericUpDown joursApresPremiere;
        private Label joursEntrePremiereDeuxiemeLabel;
        private NumericUpDown joursEntrePremiereDeuxieme;
        private Label joursEntreDeuxiemeTroisiemeLabel;
        private NumericUpDown joursEntreDeuxiemeTroisieme;
        private Label modeleNiveau1Label;
        private ComboBox modeleNiveau1ComboBox;
        private Label modeleNiveau2Label;
        private ComboBox modeleNiveau2ComboBox;
        private Label modeleNiveau3Label;
        private ComboBox modeleNiveau3ComboBox;
        private Label prioriteLabel;
        private NumericUpDown prioriteNumericUpDown;
        private Label requiertValidationLabel;
        private CheckBox requiertValidationCheckBox;
        private Label estActiveLabel;
        private CheckBox estActiveCheckBox;
        private FlowLayoutPanel buttonsPanel;
        private Button cancelButton;
        private Button saveButton;
    }
}
