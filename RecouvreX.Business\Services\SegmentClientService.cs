using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des segments client
    /// </summary>
    public class SegmentClientService : ISegmentClientService
    {
        private readonly ISegmentClientRepository _segmentClientRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="segmentClientRepository">Repository de gestion des segments client</param>
        /// <param name="clientRepository">Repository de gestion des clients</param>
        /// <param name="journalAuditRepository">Repository de gestion du journal d'audit</param>
        /// <param name="utilisateurRepository">Repository de gestion des utilisateurs</param>
        public SegmentClientService(
            ISegmentClientRepository segmentClientRepository,
            IClientRepository clientRepository,
            IJournalAuditRepository journalAuditRepository,
            IUtilisateurRepository utilisateurRepository)
        {
            _segmentClientRepository = segmentClientRepository ?? throw new ArgumentNullException(nameof(segmentClientRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
        }

        /// <summary>
        /// Récupère les clients par segment
        /// </summary>
        /// <param name="segment">Segment de client</param>
        /// <returns>Liste des clients du segment spécifié</returns>
        public async Task<IEnumerable<Client>> GetClientsBySegmentAsync(SegmentClient segment)
        {
            return await _segmentClientRepository.GetClientsBySegmentAsync(segment);
        }

        /// <summary>
        /// Met à jour le segment d'un client manuellement
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="segment">Nouveau segment</param>
        /// <param name="commentaire">Commentaire sur la segmentation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Client mis à jour</returns>
        public async Task<Client> UpdateClientSegmentAsync(int clientId, SegmentClient segment, string commentaire, int modifiePar)
        {
            if (clientId <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la mise à jour du segment d'un client");

            // Récupérer le client existant
            var existingClient = await _clientRepository.GetByIdAsync(clientId);
            if (existingClient == null)
                throw new InvalidOperationException($"Le client avec l'ID {clientId} n'existe pas");

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Client",
                EntiteId = clientId,
                Description = $"Modification du segment du client {existingClient.RaisonSociale} : {existingClient.Segment} -> {segment}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingClient.Id,
                    existingClient.RaisonSociale,
                    Segment = existingClient.Segment,
                    existingClient.CommentaireSegmentation,
                    existingClient.DateSegmentation,
                    existingClient.SegmentationManuelle
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingClient.Id,
                    existingClient.RaisonSociale,
                    Segment = segment,
                    CommentaireSegmentation = commentaire,
                    DateSegmentation = DateTime.Now,
                    SegmentationManuelle = true
                })
            });

            // Mettre à jour le segment du client
            return await _segmentClientRepository.UpdateClientSegmentAsync(clientId, segment, commentaire, true, modifiePar);
        }

        /// <summary>
        /// Segmente automatiquement tous les clients
        /// </summary>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre de clients segmentés</returns>
        public async Task<int> SegmenterClientsAutomatiquementAsync(int modifiePar)
        {
            if (modifiePar <= 0)
                throw new ArgumentException("Paramètre utilisateurId invalide");

            // Récupérer tous les clients actifs
            var clients = await _clientRepository.GetAllAsync();
            clients = clients.Where(c => c.EstActif);

            int count = 0;

            foreach (var client in clients)
            {
                try
                {
                    // Ne pas modifier les clients qui ont été segmentés manuellement
                    if (client.SegmentationManuelle)
                        continue;

                    // Calculer le segment du client
                    var segment = await CalculerSegmentClientAsync(client.Id);

                    // Mettre à jour le segment du client
                    string commentaire = $"Segmentation automatique basée sur les critères du {DateTime.Now:dd/MM/yyyy}";
                    await _segmentClientRepository.UpdateClientSegmentAsync(client.Id, segment, commentaire, false, modifiePar);

                    count++;
                }
                catch (Exception ex)
                {
                    // Journaliser l'erreur mais continuer avec les autres clients
                    Serilog.Log.Error(ex, "Erreur lors de la segmentation automatique du client {ClientId}", client.Id);
                }
            }

            return count;
        }

        /// <summary>
        /// Récupère la répartition des clients par segment
        /// </summary>
        /// <returns>Dictionnaire avec le segment comme clé et le nombre de clients comme valeur</returns>
        public async Task<Dictionary<SegmentClient, int>> GetClientDistributionBySegmentAsync()
        {
            return await _segmentClientRepository.GetClientDistributionBySegmentAsync();
        }

        /// <summary>
        /// Calcule le segment d'un client en fonction de critères prédéfinis
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Segment calculé</returns>
        public async Task<SegmentClient> CalculerSegmentClientAsync(int clientId)
        {
            // Récupérer le client avec ses factures
            var client = await _clientRepository.GetWithFacturesAsync(clientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {clientId} n'existe pas");

            // Critères de segmentation
            int score = 0;

            // Critère 1: Montant total des factures (poids: 40%)
            decimal montantTotal = 0;
            if (client.Factures != null && client.Factures.Any())
            {
                montantTotal = client.Factures.Sum(f => f.MontantTotal);
            }

            // Segmentation par montant
            if (montantTotal > 50000) // Grand compte
            {
                score += 40;
            }
            else if (montantTotal > 10000) // Compte moyen
            {
                score += 20;
            }
            else if (montantTotal > 1000) // Petit compte
            {
                score += 10;
            }

            // Critère 2: Historique de paiement (poids: 30%)
            if (client.Factures != null && client.Factures.Any())
            {
                // Calculer le pourcentage de factures payées à temps
                int facturesTotal = client.Factures.Count();
                // Vérifier si la facture est payée (MontantRestant = 0) et n'a pas de relances
                int facturesPayeesATempsSansRelance = client.Factures.Count(f =>
                    f.MontantRestant == 0 && // Facture payée
                    f.MontantPaye > 0 && // Montant payé positif
                    (f.Relances == null || !f.Relances.Any()) && // Pas de relances
                    f.DateModification <= f.DateEcheance // Payée avant échéance (en utilisant DateModification comme approximation)
                );
                double pourcentagePayeesATemps = facturesTotal > 0 ? (double)facturesPayeesATempsSansRelance / facturesTotal * 100 : 0;

                // Segmentation par historique de paiement
                if (pourcentagePayeesATemps >= 90) // Excellent payeur
                {
                    score += 30;
                }
                else if (pourcentagePayeesATemps >= 70) // Bon payeur
                {
                    score += 20;
                }
                else if (pourcentagePayeesATemps >= 50) // Payeur moyen
                {
                    score += 10;
                }
            }

            // Critère 3: Ancienneté de la relation (poids: 20%)
            if (client.DateCreation != DateTime.MinValue)
            {
                var anciennete = (DateTime.Now - client.DateCreation).TotalDays / 365; // En années

                // Segmentation par ancienneté
                if (anciennete > 3) // Client fidèle
                {
                    score += 20;
                }
                else if (anciennete > 1) // Client établi
                {
                    score += 10;
                }
            }

            // Critère 4: Limite de crédit (poids: 10%)
            if (client.LimiteCredit > 10000) // Limite de crédit élevée
            {
                score += 10;
            }
            else if (client.LimiteCredit > 5000) // Limite de crédit moyenne
            {
                score += 5;
            }

            // Déterminer le segment en fonction du score
            if (score >= 70) // Clients stratégiques
            {
                return SegmentClient.A;
            }
            else if (score >= 40) // Clients importants
            {
                return SegmentClient.B;
            }
            else // Clients standards
            {
                return SegmentClient.C;
            }
        }
    }
}
