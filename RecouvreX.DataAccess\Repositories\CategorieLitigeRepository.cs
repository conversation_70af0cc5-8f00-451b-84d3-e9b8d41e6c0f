using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les catégories de litiges
    /// </summary>
    public class CategorieLitigeRepository : BaseRepository<CategorieLitige>, ICategorieLitigeRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public CategorieLitigeRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "CategoriesLitige")
        {
        }

        /// <summary>
        /// Récupère une catégorie de litige par son nom
        /// </summary>
        /// <param name="nom">Nom de la catégorie</param>
        /// <returns>Catégorie de litige trouvée ou null</returns>
        public async Task<CategorieLitige> GetByNomAsync(string nom)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE Nom = @Nom AND EstActif = 1";
                return await connection.QueryFirstOrDefaultAsync<CategorieLitige>(query, new { Nom = nom });
            }
        }

        /// <summary>
        /// Récupère toutes les catégories de litiges avec le nombre de litiges associés
        /// </summary>
        /// <returns>Liste des catégories de litiges avec le nombre de litiges</returns>
        public async Task<IEnumerable<CategorieLitige>> GetAllWithCountAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT c.*, COUNT(l.Id) AS NombreLitiges
                    FROM CategoriesLitige c
                    LEFT JOIN Litiges l ON c.Id = l.CategorieLitigeId AND l.EstActif = 1
                    WHERE c.EstActif = 1
                    GROUP BY c.Id, c.Nom, c.Description, c.Couleur, c.DelaiResolutionJours, c.DateCreation, c.CreePar, c.DateModification, c.ModifiePar, c.EstActif
                    ORDER BY c.Nom";

                var categories = await connection.QueryAsync<CategorieLitige>(query);
                return categories;
            }
        }
    }
}
