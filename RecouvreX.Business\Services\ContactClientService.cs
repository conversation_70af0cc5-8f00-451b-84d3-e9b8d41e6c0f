using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des contacts client
    /// </summary>
    public class ContactClientService : IContactClientService
    {
        private readonly IContactClientRepository _contactClientRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        public ContactClientService(
            IContactClientRepository contactClientRepository,
            IClientRepository clientRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _contactClientRepository = contactClientRepository ?? throw new ArgumentNullException(nameof(contactClientRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère tous les contacts client
        /// </summary>
        /// <returns>Liste des contacts client</returns>
        public async Task<IEnumerable<ContactClient>> GetAllAsync()
        {
            return await _contactClientRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère un contact client par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du contact client</param>
        /// <returns>Contact client trouvé ou null</returns>
        public async Task<ContactClient> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _contactClientRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère les contacts par client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des contacts pour le client spécifié</returns>
        public async Task<IEnumerable<ContactClient>> GetByClientIdAsync(int clientId)
        {
            if (clientId <= 0)
                return new List<ContactClient>();

            return await _contactClientRepository.GetByClientIdAsync(clientId);
        }

        /// <summary>
        /// Récupère le contact principal d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Contact principal du client ou null</returns>
        public async Task<ContactClient> GetPrincipalContactAsync(int clientId)
        {
            if (clientId <= 0)
                return null;

            return await _contactClientRepository.GetPrincipalContactAsync(clientId);
        }

        /// <summary>
        /// Récupère le contact responsable des paiements d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Contact responsable des paiements du client ou null</returns>
        public async Task<ContactClient> GetPaymentContactAsync(int clientId)
        {
            if (clientId <= 0)
                return null;

            return await _contactClientRepository.GetPaymentContactAsync(clientId);
        }

        /// <summary>
        /// Crée un nouveau contact client
        /// </summary>
        /// <param name="contactClient">Contact client à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact client créé avec son identifiant généré</returns>
        public async Task<ContactClient> CreateAsync(ContactClient contactClient, int creePar)
        {
            if (contactClient == null)
                throw new ArgumentNullException(nameof(contactClient));

            if (creePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que le client existe
            var client = await _clientRepository.GetByIdAsync(contactClient.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {contactClient.ClientId} n'existe pas");

            // Créer le contact client
            var createdContact = await _contactClientRepository.AddAsync(contactClient, creePar);

            // Si c'est le premier contact du client, le définir comme contact principal et responsable des paiements
            var contacts = await _contactClientRepository.GetByClientIdAsync(contactClient.ClientId);
            if (contacts.Count() == 1)
            {
                if (contactClient.EstPrincipal)
                    await _contactClientRepository.SetAsPrincipalAsync(createdContact.Id, creePar);

                if (contactClient.EstResponsablePaiements)
                    await _contactClientRepository.SetAsPaymentResponsibleAsync(createdContact.Id, creePar);
            }

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "ContactClient",
                EntiteId = createdContact.Id,
                TypeAction = "Création",
                UtilisateurId = creePar,
                DateAction = DateTime.Now,
                Description = $"Création du contact {createdContact.NomComplet:s} pour le client {client.RaisonSociale:s}",
                DonneesAvant = null,
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdContact.Id,
                    createdContact.ClientId,
                    ClientRaisonSociale = client.RaisonSociale,
                    createdContact.Nom,
                    createdContact.Prenom,
                    createdContact.Fonction,
                    createdContact.Service,
                    createdContact.Email,
                    createdContact.Telephone,
                    createdContact.Mobile,
                    createdContact.EstPrincipal,
                    createdContact.EstResponsablePaiements
                })
            });

            return createdContact;
        }

        /// <summary>
        /// Met à jour un contact client existant
        /// </summary>
        /// <param name="contactClient">Contact client à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact client mis à jour</returns>
        public async Task<ContactClient> UpdateAsync(ContactClient contactClient, int modifiePar)
        {
            if (contactClient == null)
                throw new ArgumentNullException(nameof(contactClient));

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que le contact client existe
            var existingContact = await _contactClientRepository.GetByIdAsync(contactClient.Id);
            if (existingContact == null)
                throw new InvalidOperationException($"Le contact client avec l'ID {contactClient.Id} n'existe pas");

            // Vérifier que le client existe
            var client = await _clientRepository.GetByIdAsync(contactClient.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {contactClient.ClientId} n'existe pas");

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "ContactClient",
                EntiteId = contactClient.Id,
                TypeAction = "Modification",
                UtilisateurId = modifiePar,
                DateAction = DateTime.Now,
                Description = $"Modification du contact {contactClient.NomComplet:s} pour le client {client.RaisonSociale:s}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingContact.Id,
                    existingContact.ClientId,
                    ClientRaisonSociale = client.RaisonSociale,
                    existingContact.Nom,
                    existingContact.Prenom,
                    existingContact.Fonction,
                    existingContact.Service,
                    existingContact.Email,
                    existingContact.Telephone,
                    existingContact.Mobile,
                    existingContact.EstPrincipal,
                    existingContact.EstResponsablePaiements
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    contactClient.Id,
                    contactClient.ClientId,
                    ClientRaisonSociale = client.RaisonSociale,
                    contactClient.Nom,
                    contactClient.Prenom,
                    contactClient.Fonction,
                    contactClient.Service,
                    contactClient.Email,
                    contactClient.Telephone,
                    contactClient.Mobile,
                    contactClient.EstPrincipal,
                    contactClient.EstResponsablePaiements
                })
            });

            // Mettre à jour le contact client
            var updatedContact = await _contactClientRepository.UpdateAsync(contactClient, modifiePar);

            // Mettre à jour le statut de contact principal si nécessaire
            if (contactClient.EstPrincipal && !existingContact.EstPrincipal)
                await _contactClientRepository.SetAsPrincipalAsync(contactClient.Id, modifiePar);

            // Mettre à jour le statut de responsable des paiements si nécessaire
            if (contactClient.EstResponsablePaiements && !existingContact.EstResponsablePaiements)
                await _contactClientRepository.SetAsPaymentResponsibleAsync(contactClient.Id, modifiePar);

            return updatedContact;
        }

        /// <summary>
        /// Supprime un contact client
        /// </summary>
        /// <param name="id">Identifiant du contact client à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant du contact client ne peut pas être négatif ou nul");

            if (supprimePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que le contact client existe
            var contact = await _contactClientRepository.GetByIdAsync(id);
            if (contact == null)
                return false;

            // Récupérer le client pour le journal d'audit
            var client = await _clientRepository.GetByIdAsync(contact.ClientId);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "ContactClient",
                EntiteId = id,
                TypeAction = "Suppression",
                UtilisateurId = supprimePar,
                DateAction = DateTime.Now,
                Description = $"Suppression du contact {contact.NomComplet:s} pour le client {client?.RaisonSociale ?? contact.ClientId.ToString()}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    contact.Id,
                    contact.ClientId,
                    ClientRaisonSociale = client?.RaisonSociale,
                    contact.Nom,
                    contact.Prenom,
                    contact.Fonction,
                    contact.Service,
                    contact.Email,
                    contact.Telephone,
                    contact.Mobile,
                    contact.EstPrincipal,
                    contact.EstResponsablePaiements
                }),
                DonneesApres = null
            });

            // Supprimer le contact client
            return await _contactClientRepository.DeleteAsync(id, supprimePar);
        }

        /// <summary>
        /// Définit un contact comme contact principal
        /// </summary>
        /// <param name="contactId">Identifiant du contact</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> SetAsPrincipalAsync(int contactId, int modifiePar)
        {
            if (contactId <= 0)
                throw new ArgumentException("L'identifiant du contact client ne peut pas être négatif ou nul");

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que le contact client existe
            var contact = await _contactClientRepository.GetByIdAsync(contactId);
            if (contact == null)
                return false;

            // Récupérer le client pour le journal d'audit
            var client = await _clientRepository.GetByIdAsync(contact.ClientId);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "ContactClient",
                EntiteId = contactId,
                TypeAction = "Définition comme contact principal",
                UtilisateurId = modifiePar,
                DateAction = DateTime.Now,
                Description = $"Définition de {contact.NomComplet:s} comme contact principal pour le client {client?.RaisonSociale ?? contact.ClientId.ToString()}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    EstPrincipal = contact.EstPrincipal
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    EstPrincipal = true
                })
            });

            // Définir le contact comme contact principal
            return await _contactClientRepository.SetAsPrincipalAsync(contactId, modifiePar);
        }

        /// <summary>
        /// Définit un contact comme responsable des paiements
        /// </summary>
        /// <param name="contactId">Identifiant du contact</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> SetAsPaymentResponsibleAsync(int contactId, int modifiePar)
        {
            if (contactId <= 0)
                throw new ArgumentException("L'identifiant du contact client ne peut pas être négatif ou nul");

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que le contact client existe
            var contact = await _contactClientRepository.GetByIdAsync(contactId);
            if (contact == null)
                return false;

            // Récupérer le client pour le journal d'audit
            var client = await _clientRepository.GetByIdAsync(contact.ClientId);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "ContactClient",
                EntiteId = contactId,
                TypeAction = "Définition comme responsable des paiements",
                UtilisateurId = modifiePar,
                DateAction = DateTime.Now,
                Description = $"Définition de {contact.NomComplet:s} comme responsable des paiements pour le client {client?.RaisonSociale ?? contact.ClientId.ToString()}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    EstResponsablePaiements = contact.EstResponsablePaiements
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    EstResponsablePaiements = true
                })
            });

            // Définir le contact comme responsable des paiements
            return await _contactClientRepository.SetAsPaymentResponsibleAsync(contactId, modifiePar);
        }
    }
}
