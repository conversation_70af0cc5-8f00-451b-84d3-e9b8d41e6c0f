using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un document dans le système
    /// </summary>
    public class Document : BaseEntity
    {
        /// <summary>
        /// Nom du document
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Type de document (Facture, <PERSON> de livraison, <PERSON><PERSON><PERSON>, etc.)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Type MIME du document
        /// </summary>
        public string MimeType { get; set; }

        /// <summary>
        /// Taille du document en octets
        /// </summary>
        public long Taille { get; set; }

        /// <summary>
        /// Contenu du document (stocké en base de données)
        /// </summary>
        public byte[] Contenu { get; set; }

        /// <summary>
        /// Description du document
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Type d'entité associée (Facture, Paiement, Relance, Client)
        /// </summary>
        public string TypeEntite { get; set; }

        /// <summary>
        /// Identifiant de l'entité associée
        /// </summary>
        public int EntiteId { get; set; }
    }
}
