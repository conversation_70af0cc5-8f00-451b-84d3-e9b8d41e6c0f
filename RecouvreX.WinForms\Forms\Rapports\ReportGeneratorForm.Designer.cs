namespace RecouvreX.WinForms.Forms.Rapports
{
    partial class ReportGeneratorForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // ReportGeneratorForm
            // 
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.Name = "ReportGeneratorForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Générateur de rapports";
            this.Load += new System.EventHandler(this.ReportGeneratorForm_Load);
            this.ResumeLayout(false);

            // Panneau principal
            System.Windows.Forms.TableLayoutPanel mainPanel = new System.Windows.Forms.TableLayoutPanel();
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Padding = new System.Windows.Forms.Padding(10);
            mainPanel.ColumnCount = 1;
            mainPanel.RowCount = 2;
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 150F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.Controls.Add(mainPanel);

            // Panneau des options de rapport
            System.Windows.Forms.GroupBox optionsGroupBox = new System.Windows.Forms.GroupBox();
            optionsGroupBox.Text = "Options du rapport";
            optionsGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(optionsGroupBox, 0, 0);

            System.Windows.Forms.TableLayoutPanel optionsPanel = new System.Windows.Forms.TableLayoutPanel();
            optionsPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            optionsPanel.Padding = new System.Windows.Forms.Padding(5);
            optionsPanel.ColumnCount = 4;
            optionsPanel.RowCount = 3;
            optionsPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            optionsPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            optionsPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            optionsPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            optionsPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            optionsPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            optionsPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            optionsGroupBox.Controls.Add(optionsPanel);

            // Ligne 1
            System.Windows.Forms.Label typeRapportLabel = new System.Windows.Forms.Label();
            typeRapportLabel.Text = "Type de rapport* :";
            typeRapportLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            optionsPanel.Controls.Add(typeRapportLabel, 0, 0);

            System.Windows.Forms.ComboBox typeRapportComboBox = new System.Windows.Forms.ComboBox();
            typeRapportComboBox.Name = "typeRapportComboBox";
            typeRapportComboBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            typeRapportComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            typeRapportComboBox.Items.Add("Factures en retard");
            typeRapportComboBox.Items.Add("Paiements par période");
            typeRapportComboBox.Items.Add("Factures par période");
            typeRapportComboBox.Items.Add("Clients avec factures en retard");
            typeRapportComboBox.Items.Add("Performances des commerciaux");
            typeRapportComboBox.Items.Add("Modes de paiement");
            typeRapportComboBox.Items.Add("Relances effectuées");
            typeRapportComboBox.Items.Add("Taux de recouvrement");
            typeRapportComboBox.SelectedIndex = 0;
            typeRapportComboBox.SelectedIndexChanged += new System.EventHandler(this.TypeRapportComboBox_SelectedIndexChanged);
            typeRapportComboBox.Validating += new System.ComponentModel.CancelEventHandler(this.TypeRapportComboBox_Validating);
            optionsPanel.Controls.Add(typeRapportComboBox, 1, 0);

            System.Windows.Forms.Label formatLabel = new System.Windows.Forms.Label();
            formatLabel.Text = "Format d'export* :";
            formatLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            optionsPanel.Controls.Add(formatLabel, 2, 0);

            System.Windows.Forms.ComboBox formatComboBox = new System.Windows.Forms.ComboBox();
            formatComboBox.Name = "formatComboBox";
            formatComboBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            formatComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            formatComboBox.Items.Add("JSON");
            formatComboBox.Items.Add("CSV");
            formatComboBox.Items.Add("Excel");
            formatComboBox.Items.Add("PDF");
            formatComboBox.SelectedIndex = 0;
            formatComboBox.Validating += new System.ComponentModel.CancelEventHandler(this.FormatComboBox_Validating);
            optionsPanel.Controls.Add(formatComboBox, 3, 0);

            // Ligne 2
            System.Windows.Forms.Label dateDebutLabel = new System.Windows.Forms.Label();
            dateDebutLabel.Name = "dateDebutLabel";
            dateDebutLabel.Text = "Date de début :";
            dateDebutLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            optionsPanel.Controls.Add(dateDebutLabel, 0, 1);

            System.Windows.Forms.DateTimePicker dateDebutPicker = new System.Windows.Forms.DateTimePicker();
            dateDebutPicker.Name = "dateDebutPicker";
            dateDebutPicker.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            dateDebutPicker.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            dateDebutPicker.Value = new System.DateTime(System.DateTime.Today.Year, System.DateTime.Today.Month, 1);
            optionsPanel.Controls.Add(dateDebutPicker, 1, 1);

            System.Windows.Forms.Label dateFinLabel = new System.Windows.Forms.Label();
            dateFinLabel.Name = "dateFinLabel";
            dateFinLabel.Text = "Date de fin :";
            dateFinLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            optionsPanel.Controls.Add(dateFinLabel, 2, 1);

            System.Windows.Forms.DateTimePicker dateFinPicker = new System.Windows.Forms.DateTimePicker();
            dateFinPicker.Name = "dateFinPicker";
            dateFinPicker.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            dateFinPicker.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            dateFinPicker.Value = System.DateTime.Today;
            optionsPanel.Controls.Add(dateFinPicker, 3, 1);

            // Ligne 3
            System.Windows.Forms.Label cheminLabel = new System.Windows.Forms.Label();
            cheminLabel.Text = "Chemin d'export :";
            cheminLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            optionsPanel.Controls.Add(cheminLabel, 0, 2);

            System.Windows.Forms.TextBox cheminTextBox = new System.Windows.Forms.TextBox();
            cheminTextBox.Name = "cheminTextBox";
            cheminTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            cheminTextBox.ReadOnly = true;
            optionsPanel.Controls.Add(cheminTextBox, 1, 2);

            System.Windows.Forms.Button browseButton = new System.Windows.Forms.Button();
            browseButton.Name = "browseButton";
            browseButton.Text = "Parcourir...";
            browseButton.Size = new System.Drawing.Size(100, 25);
            browseButton.Anchor = System.Windows.Forms.AnchorStyles.Left;
            browseButton.Click += new System.EventHandler(this.BrowseButton_Click);
            optionsPanel.Controls.Add(browseButton, 2, 2);

            System.Windows.Forms.Button generateButton = new System.Windows.Forms.Button();
            generateButton.Name = "generateButton";
            generateButton.Text = "Générer";
            generateButton.Size = new System.Drawing.Size(100, 25);
            generateButton.Anchor = System.Windows.Forms.AnchorStyles.Left;
            generateButton.Click += new System.EventHandler(this.GenerateButton_Click);
            optionsPanel.Controls.Add(generateButton, 3, 2);

            // Panneau d'aperçu du rapport
            System.Windows.Forms.GroupBox previewGroupBox = new System.Windows.Forms.GroupBox();
            previewGroupBox.Text = "Aperçu du rapport";
            previewGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(previewGroupBox, 0, 1);

            System.Windows.Forms.TextBox previewTextBox = new System.Windows.Forms.TextBox();
            previewTextBox.Name = "previewTextBox";
            previewTextBox.Dock = System.Windows.Forms.DockStyle.Fill;
            previewTextBox.Multiline = true;
            previewTextBox.ReadOnly = true;
            previewTextBox.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            previewTextBox.Font = new System.Drawing.Font("Consolas", 9F);
            previewGroupBox.Controls.Add(previewTextBox);
        }

        #endregion
    }
}
