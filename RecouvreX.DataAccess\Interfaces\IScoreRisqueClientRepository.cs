using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository de gestion des scores de risque client
    /// </summary>
    public interface IScoreRisqueClientRepository
    {
        /// <summary>
        /// Récupère tous les scores de risque client
        /// </summary>
        /// <returns>Liste des scores de risque client</returns>
        Task<IEnumerable<ScoreRisqueClient>> GetAllAsync();

        /// <summary>
        /// Récupère un score de risque client par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du score de risque client</param>
        /// <returns>Score de risque client trouvé ou null</returns>
        Task<ScoreRisqueClient> GetByIdAsync(int id);

        /// <summary>
        /// Récupère le score de risque d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Score de risque du client ou null si non trouvé</returns>
        Task<ScoreRisqueClient> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Ajoute un nouveau score de risque client
        /// </summary>
        /// <param name="scoreRisqueClient">Score de risque client à ajouter</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Score de risque client ajouté avec son identifiant généré</returns>
        Task<ScoreRisqueClient> AddAsync(ScoreRisqueClient scoreRisqueClient, int creePar);

        /// <summary>
        /// Met à jour un score de risque client existant
        /// </summary>
        /// <param name="scoreRisqueClient">Score de risque client à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Score de risque client mis à jour</returns>
        Task<ScoreRisqueClient> UpdateAsync(ScoreRisqueClient scoreRisqueClient, int modifiePar);

        /// <summary>
        /// Supprime un score de risque client
        /// </summary>
        /// <param name="id">Identifiant du score de risque client à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Récupère les clients par catégorie de risque
        /// </summary>
        /// <param name="categorie">Catégorie de risque (A, B, C, D, E)</param>
        /// <returns>Liste des clients dans la catégorie spécifiée</returns>
        Task<IEnumerable<Client>> GetClientsByCategorieAsync(string categorie);

        /// <summary>
        /// Récupère les statistiques de répartition des clients par catégorie de risque
        /// </summary>
        /// <returns>Dictionnaire avec la catégorie comme clé et le nombre de clients comme valeur</returns>
        Task<Dictionary<string, int>> GetClientDistributionByCategorieAsync();
    }
}
