using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une échéance de paiement dans un plan de paiement
    /// </summary>
    public class EcheancePaiement : BaseEntity
    {
        /// <summary>
        /// Identifiant du plan de paiement auquel appartient cette échéance
        /// </summary>
        public int PlanPaiementId { get; set; }

        /// <summary>
        /// Plan de paiement associé (navigation property)
        /// </summary>
        public PlanPaiement PlanPaiement { get; set; }

        /// <summary>
        /// Numéro d'ordre de l'échéance dans le plan (1, 2, 3, etc.)
        /// </summary>
        public int NumeroOrdre { get; set; }

        /// <summary>
        /// Date d'échéance prévue
        /// </summary>
        public DateTime DateEcheance { get; set; }

        /// <summary>
        /// Montant prévu pour cette échéance
        /// </summary>
        public decimal MontantPrevu { get; set; }

        /// <summary>
        /// Indique si l'échéance a été payée
        /// </summary>
        public bool EstPayee { get; set; }

        /// <summary>
        /// Date de paiement effective (si payée)
        /// </summary>
        public DateTime? DatePaiement { get; set; }

        /// <summary>
        /// Identifiant du paiement associé (si payée)
        /// </summary>
        public int? PaiementId { get; set; }

        /// <summary>
        /// Paiement associé (navigation property)
        /// </summary>
        public Paiement Paiement { get; set; }

        /// <summary>
        /// Montant effectivement payé (peut être différent du montant prévu)
        /// </summary>
        public decimal? MontantPaye { get; set; }

        /// <summary>
        /// Notes ou commentaires sur cette échéance
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Indique si une notification a été envoyée pour cette échéance
        /// </summary>
        public bool NotificationEnvoyee { get; set; }

        /// <summary>
        /// Date d'envoi de la notification
        /// </summary>
        public DateTime? DateNotification { get; set; }

        /// <summary>
        /// Indique si l'échéance est en retard
        /// </summary>
        public bool EstEnRetard
        {
            get
            {
                return !EstPayee && DateEcheance < DateTime.Now;
            }
        }

        /// <summary>
        /// Nombre de jours de retard (si en retard)
        /// </summary>
        public int JoursRetard
        {
            get
            {
                if (!EstEnRetard)
                    return 0;

                return (int)(DateTime.Now - DateEcheance).TotalDays;
            }
        }
    }
}
