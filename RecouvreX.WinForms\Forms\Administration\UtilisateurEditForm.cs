using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.ComponentModel;

namespace RecouvreX.WinForms.Forms.Administration
{
    public partial class UtilisateurEditForm : Form
    {
        private readonly IUtilisateurService _utilisateurService;
        private readonly IRoleService _roleService;
        private readonly int _currentUserId;
        private readonly Utilisateur? _utilisateur;
        private readonly bool _isEditMode;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();
        private List<Role> _roles = new List<Role>();
        private Label motDePasseLabel;
        private TextBox motDePasseTextBox;
        private Label confirmationMotDePasseLabel;
        private TextBox confirmationMotDePasseTextBox;

        public UtilisateurEditForm(IUtilisateurService utilisateurService, IRoleService roleService, int currentUserId, Utilisateur? utilisateur = null)
        {
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _roleService = roleService ?? throw new ArgumentNullException(nameof(roleService));
            _currentUserId = currentUserId;
            _utilisateur = utilisateur;
            _isEditMode = utilisateur != null;

            InitializeComponent();

            // Mettre à jour le titre du formulaire après l'initialisation des composants
            this.Text = _isEditMode ? "Modifier un utilisateur" : "Ajouter un utilisateur";

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;

            // Configurer la visibilité des champs de mot de passe en fonction du mode
            if (motDePasseLabel != null) motDePasseLabel.Visible = !_isEditMode;
            if (motDePasseTextBox != null) motDePasseTextBox.Visible = !_isEditMode;
            if (confirmationMotDePasseLabel != null) confirmationMotDePasseLabel.Visible = !_isEditMode;
            if (confirmationMotDePasseTextBox != null) confirmationMotDePasseTextBox.Visible = !_isEditMode;
        }

        private async void UtilisateurEditForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Charger les rôles
                await LoadRolesAsync();

                // Si en mode édition, remplir les champs avec les données de l'utilisateur
                if (_isEditMode && _utilisateur != null)
                {
                    FillFormWithUtilisateurData(_utilisateur);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'édition d'utilisateur");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadRolesAsync()
        {
            try
            {
                // Récupérer les rôles
                _roles = (await _roleService.GetAllAsync()).ToList();

                // Remplir la ComboBox
                var roleComboBox = this.Controls.Find("roleComboBox", true).FirstOrDefault() as ComboBox;
                if (roleComboBox != null)
                {
                    roleComboBox.Items.Clear();
                    roleComboBox.DisplayMember = "Text";
                    roleComboBox.ValueMember = "Value";

                    // Ajouter un élément vide
                    roleComboBox.Items.Add(new { Text = "-- Sélectionner un rôle --", Value = 0 });

                    // Ajouter les rôles
                    foreach (var role in _roles)
                    {
                        roleComboBox.Items.Add(new { Text = role.Nom, Value = role.Id });
                    }

                    roleComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des rôles");
                throw;
            }
        }

        private void FillFormWithUtilisateurData(Utilisateur utilisateur)
        {
            var nomUtilisateurTextBox = this.Controls.Find("nomUtilisateurTextBox", true).FirstOrDefault() as TextBox;
            var nomTextBox = this.Controls.Find("nomTextBox", true).FirstOrDefault() as TextBox;
            var prenomTextBox = this.Controls.Find("prenomTextBox", true).FirstOrDefault() as TextBox;
            var emailTextBox = this.Controls.Find("emailTextBox", true).FirstOrDefault() as TextBox;
            var telephoneTextBox = this.Controls.Find("telephoneTextBox", true).FirstOrDefault() as TextBox;
            var roleComboBox = this.Controls.Find("roleComboBox", true).FirstOrDefault() as ComboBox;
            var actifCheckBox = this.Controls.Find("actifCheckBox", true).FirstOrDefault() as CheckBox;

            if (nomUtilisateurTextBox != null) nomUtilisateurTextBox.Text = utilisateur.NomUtilisateur;

            // Diviser le nom complet en nom et prénom si possible
            string[] nomCompletParts = (utilisateur.NomComplet ?? "").Split(new char[] { ' ' }, 2);
            string nom = nomCompletParts.Length > 0 ? nomCompletParts[0] : "";
            string prenom = nomCompletParts.Length > 1 ? nomCompletParts[1] : "";

            if (nomTextBox != null) nomTextBox.Text = nom;
            if (prenomTextBox != null) prenomTextBox.Text = prenom;
            if (emailTextBox != null) emailTextBox.Text = utilisateur.Email;
            if (telephoneTextBox != null) telephoneTextBox.Text = utilisateur.Telephone;

            if (roleComboBox != null)
            {
                // Sélectionner le rôle
                for (int i = 0; i < roleComboBox.Items.Count; i++)
                {
                    dynamic item = roleComboBox.Items[i];
                    if (item.Value == utilisateur.RoleId)
                    {
                        roleComboBox.SelectedIndex = i;
                        break;
                    }
                }
            }

            if (actifCheckBox != null) actifCheckBox.Checked = utilisateur.EstActif;
        }

        private void NomUtilisateurTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "Le nom d'utilisateur est obligatoire.");
                    e.Cancel = true;
                }
                else if (textBox.Text.Length < 3)
                {
                    _errorProvider.SetError(textBox, "Le nom d'utilisateur doit contenir au moins 3 caractères.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private void MotDePasseTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null && !_isEditMode) // Validation uniquement en mode ajout
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "Le mot de passe est obligatoire.");
                    e.Cancel = true;
                }
                else if (textBox.Text.Length < 8)
                {
                    _errorProvider.SetError(textBox, "Le mot de passe doit contenir au moins 8 caractères.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private void ConfirmationMotDePasseTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null && !_isEditMode) // Validation uniquement en mode ajout
            {
                var motDePasseTextBox = this.Controls.Find("motDePasseTextBox", true).FirstOrDefault() as TextBox;
                if (motDePasseTextBox != null && textBox.Text != motDePasseTextBox.Text)
                {
                    _errorProvider.SetError(textBox, "Les mots de passe ne correspondent pas.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private void NomTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "Le nom est obligatoire.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private void PrenomTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "Le prénom est obligatoire.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private void EmailTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "L'email est obligatoire.");
                    e.Cancel = true;
                }
                else if (!IsValidEmail(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "L'email n'est pas valide.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void RoleComboBox_Validating(object sender, CancelEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                if (comboBox.SelectedIndex == 0) // L'élément vide
                {
                    _errorProvider.SetError(comboBox, "Veuillez sélectionner un rôle.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(comboBox, "");
                }
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant d'enregistrer.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer les valeurs du formulaire
                var nomUtilisateurTextBox = this.Controls.Find("nomUtilisateurTextBox", true).FirstOrDefault() as TextBox;
                var motDePasseTextBox = this.Controls.Find("motDePasseTextBox", true).FirstOrDefault() as TextBox;
                var nomTextBox = this.Controls.Find("nomTextBox", true).FirstOrDefault() as TextBox;
                var prenomTextBox = this.Controls.Find("prenomTextBox", true).FirstOrDefault() as TextBox;
                var emailTextBox = this.Controls.Find("emailTextBox", true).FirstOrDefault() as TextBox;
                var telephoneTextBox = this.Controls.Find("telephoneTextBox", true).FirstOrDefault() as TextBox;
                var roleComboBox = this.Controls.Find("roleComboBox", true).FirstOrDefault() as ComboBox;
                var actifCheckBox = this.Controls.Find("actifCheckBox", true).FirstOrDefault() as CheckBox;

                if (nomUtilisateurTextBox == null || nomTextBox == null || prenomTextBox == null ||
                    emailTextBox == null || telephoneTextBox == null || roleComboBox == null || actifCheckBox == null)
                {
                    throw new InvalidOperationException("Un ou plusieurs champs sont introuvables.");
                }

                // Créer ou mettre à jour l'utilisateur
                Utilisateur utilisateur;
                if (_isEditMode && _utilisateur != null)
                {
                    // Mode édition
                    utilisateur = _utilisateur;
                }
                else
                {
                    // Mode ajout
                    utilisateur = new Utilisateur();
                }

                // Remplir les propriétés de l'utilisateur
                utilisateur.NomUtilisateur = nomUtilisateurTextBox.Text.Trim();

                // Combiner le nom et le prénom pour former le nom complet
                string nom = nomTextBox.Text.Trim();
                string prenom = prenomTextBox.Text.Trim();
                utilisateur.NomComplet = $"{nom} {prenom}".Trim();

                utilisateur.Email = emailTextBox.Text.Trim();
                utilisateur.Telephone = telephoneTextBox.Text.Trim();
                utilisateur.RoleId = ((dynamic)roleComboBox.SelectedItem).Value;
                utilisateur.EstActif = actifCheckBox.Checked;

                // Enregistrer l'utilisateur
                if (_isEditMode)
                {
                    // Mettre à jour l'utilisateur existant
                    await _utilisateurService.UpdateAsync(utilisateur, _currentUserId);
                    MessageBox.Show("L'utilisateur a été mis à jour avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Créer un nouvel utilisateur
                    string motDePasse = motDePasseTextBox.Text;
                    await _utilisateurService.CreateAsync(utilisateur, motDePasse, _currentUserId);
                    MessageBox.Show("L'utilisateur a été créé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement de l'utilisateur");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement de l'utilisateur : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
