using Microsoft.Extensions.DependencyInjection;
using RecouvreX.Business.Interfaces;
using RecouvreX.Business.Services;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.DataAccess.Repositories;
using System;

namespace RecouvreX.Business.DependencyInjection
{
    public static class RecouvreXServiceCollectionExtensions
    {
        public static IServiceCollection AddRecouvreXServices(this IServiceCollection services, string connectionString)
        {
            if (services == null)
                throw new ArgumentNullException(nameof(services));

            if (string.IsNullOrEmpty(connectionString))
                throw new ArgumentException("La chaîne de connexion ne peut pas être nulle ou vide.", nameof(connectionString));

            // Repositories
            services.AddScoped<IUtilisateurRepository, UtilisateurRepository>();
            services.AddScoped<IRoleRepository, RoleRepository>();
            services.AddScoped<IClientRepository, ClientRepository>();
            services.AddScoped<IFactureRepository, FactureRepository>();
            services.AddScoped<IPaiementRepository, PaiementRepository>();
            services.AddScoped<IRelanceRepository, RelanceRepository>();
            services.AddScoped<IModeleRelanceRepository, ModeleRelanceRepository>();
            services.AddScoped<IRegleRelanceRepository, RegleRelanceRepository>();
            services.AddScoped<IPlanificationRelanceRepository, PlanificationRelanceRepository>();
            services.AddScoped<ICommunicationRepository, CommunicationRepository>();
            services.AddScoped<IContactClientRepository, ContactClientRepository>();
            services.AddScoped<IAlerteRepository, AlerteRepository>();
            services.AddScoped<IScoreRisqueClientRepository, ScoreRisqueClientRepository>();
            services.AddScoped<ISegmentClientRepository, SegmentClientRepository>();
            services.AddScoped<IActionPrioritaireRepository, ActionPrioritaireRepository>();
            services.AddScoped<ILitigeRepository, LitigeRepository>();
            services.AddScoped<IPlanPaiementRepository, PlanPaiementRepository>();
            services.AddScoped<IEcheancePaiementRepository, EcheancePaiementRepository>();
            services.AddScoped<IRapportPersonnaliseRepository, RapportPersonnaliseRepository>();

            // Services
            services.AddScoped<IAuthenticationService, AuthenticationService>();
            services.AddScoped<IUtilisateurService, UtilisateurService>();
            services.AddScoped<IRoleService, RoleService>();
            services.AddScoped<IClientService, ClientService>();
            services.AddScoped<IFactureService, FactureService>();
            services.AddScoped<IPaiementService, PaiementService>();
            services.AddScoped<IRelanceService, RelanceService>();
            services.AddScoped<IModeleRelanceService, ModeleRelanceService>();
            services.AddScoped<IRegleRelanceService, RegleRelanceService>();
            services.AddScoped<IPlanificationRelanceService, PlanificationRelanceService>();
            services.AddScoped<ICommunicationService, CommunicationService>();
            services.AddScoped<IContactClientService, ContactClientService>();
            services.AddScoped<IAlerteService, AlerteService>();
            services.AddScoped<IScoreRisqueClientService, ScoreRisqueClientService>();
            services.AddScoped<ISegmentClientService, SegmentClientService>();
            services.AddScoped<IActionPrioritaireService, ActionPrioritaireService>();
            services.AddScoped<ILitigeService, LitigeService>();
            services.AddScoped<IPlanPaiementService, PlanPaiementService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<IRapportPersonnaliseService, RapportPersonnaliseService>();
            services.AddScoped<IEmailService, EmailService>();

            return services;
        }
    }
}
