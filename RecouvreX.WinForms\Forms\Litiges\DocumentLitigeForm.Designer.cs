using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.IO;

namespace RecouvreX.WinForms.Forms.Litiges
{
    partial class DocumentLitigeForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private Label titleLabel;
        private Label descriptionLabel;
        private Label documentsLabel;
        private DataGridView documentsDataGridView;
        private Panel buttonsPanel;
        private Button addButton;
        private Button editButton;
        private Button deleteButton;
        private Button openButton;
        private Button closeButton;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();

            // Définir les propriétés du formulaire
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimizeBox = false;
            this.MaximizeBox = false;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;

            // Créer les contrôles
            this.mainPanel = new Panel();
            this.mainPanel.Dock = DockStyle.Fill;
            this.Controls.Add(this.mainPanel);

            // Titre
            this.titleLabel = new Label();
            this.titleLabel.Font = new Font(this.Font.FontFamily, 14, FontStyle.Bold);
            this.titleLabel.AutoSize = true;
            this.titleLabel.Location = new Point(20, 20);
            this.mainPanel.Controls.Add(this.titleLabel);

            // Description du litige
            this.descriptionLabel = new Label();
            this.descriptionLabel.AutoSize = true;
            this.descriptionLabel.Location = new Point(20, 50);
            this.mainPanel.Controls.Add(this.descriptionLabel);

            // Liste des documents
            this.documentsLabel = new Label();
            this.documentsLabel.Text = "Documents :";
            this.documentsLabel.Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold);
            this.documentsLabel.AutoSize = true;
            this.documentsLabel.Location = new Point(20, 80);
            this.mainPanel.Controls.Add(this.documentsLabel);

            this.documentsDataGridView = new DataGridView();
            this.documentsDataGridView.Name = "documentsDataGridView";
            this.documentsDataGridView.Location = new Point(20, 110);
            this.documentsDataGridView.Size = new Size(740, 350);
            this.documentsDataGridView.AllowUserToAddRows = false;
            this.documentsDataGridView.AllowUserToDeleteRows = false;
            this.documentsDataGridView.AllowUserToResizeRows = false;
            this.documentsDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.documentsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.documentsDataGridView.MultiSelect = false;
            this.documentsDataGridView.ReadOnly = true;
            this.documentsDataGridView.RowHeadersVisible = false;
            this.documentsDataGridView.CellDoubleClick += new DataGridViewCellEventHandler(this.DocumentsDataGridView_CellDoubleClick);
            this.mainPanel.Controls.Add(this.documentsDataGridView);

            // Boutons d'action
            this.buttonsPanel = new Panel();
            this.buttonsPanel.Size = new Size(740, 50);
            this.buttonsPanel.Location = new Point(20, 470);
            this.mainPanel.Controls.Add(this.buttonsPanel);

            this.addButton = new Button();
            this.addButton.Text = "Ajouter un document";
            this.addButton.Size = new Size(150, 30);
            this.addButton.Location = new Point(0, 10);
            this.addButton.Click += new EventHandler(this.AddButton_Click);
            this.buttonsPanel.Controls.Add(this.addButton);

            this.editButton = new Button();
            this.editButton.Text = "Modifier";
            this.editButton.Size = new Size(100, 30);
            this.editButton.Location = new Point(160, 10);
            this.editButton.Click += new EventHandler(this.EditButton_Click);
            this.buttonsPanel.Controls.Add(this.editButton);

            this.deleteButton = new Button();
            this.deleteButton.Text = "Supprimer";
            this.deleteButton.Size = new Size(100, 30);
            this.deleteButton.Location = new Point(270, 10);
            this.deleteButton.Click += new EventHandler(this.DeleteButton_Click);
            this.buttonsPanel.Controls.Add(this.deleteButton);

            this.openButton = new Button();
            this.openButton.Text = "Ouvrir";
            this.openButton.Size = new Size(100, 30);
            this.openButton.Location = new Point(380, 10);
            this.openButton.Click += new EventHandler(this.OpenButton_Click);
            this.buttonsPanel.Controls.Add(this.openButton);

            this.closeButton = new Button();
            this.closeButton.Text = "Fermer";
            this.closeButton.Size = new Size(100, 30);
            this.closeButton.Location = new Point(640, 10);
            this.closeButton.Click += new EventHandler(this.CloseButton_Click);
            this.buttonsPanel.Controls.Add(this.closeButton);

            // Charger le formulaire
            this.Load += new EventHandler(this.DocumentLitigeForm_Load);
        }

        #endregion
    }
}
