using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des actions prioritaires
    /// </summary>
    public interface IActionPrioritaireService
    {
        /// <summary>
        /// Récupère toutes les actions prioritaires
        /// </summary>
        /// <returns>Liste des actions prioritaires</returns>
        Task<IEnumerable<ActionPrioritaire>> GetAllAsync();

        /// <summary>
        /// Récupère une action prioritaire par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire</param>
        /// <returns>Action prioritaire trouvée ou null</returns>
        Task<ActionPrioritaire> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les actions prioritaires d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des actions prioritaires du client</returns>
        Task<IEnumerable<ActionPrioritaire>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère les actions prioritaires liées à une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des actions prioritaires liées à la facture</returns>
        Task<IEnumerable<ActionPrioritaire>> GetByFactureIdAsync(int factureId);

        /// <summary>
        /// Récupère les actions prioritaires assignées à un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des actions prioritaires assignées à l'utilisateur</returns>
        Task<IEnumerable<ActionPrioritaire>> GetByUtilisateurAssigneIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les actions prioritaires par niveau de priorité
        /// </summary>
        /// <param name="niveauPriorite">Niveau de priorité</param>
        /// <returns>Liste des actions prioritaires du niveau spécifié</returns>
        Task<IEnumerable<ActionPrioritaire>> GetByNiveauPrioriteAsync(NiveauPriorite niveauPriorite);

        /// <summary>
        /// Récupère les actions prioritaires par type d'action
        /// </summary>
        /// <param name="typeAction">Type d'action</param>
        /// <returns>Liste des actions prioritaires du type spécifié</returns>
        Task<IEnumerable<ActionPrioritaire>> GetByTypeActionAsync(TypeActionPrioritaire typeAction);

        /// <summary>
        /// Récupère les actions prioritaires à échéance dans un nombre de jours spécifié
        /// </summary>
        /// <param name="jours">Nombre de jours</param>
        /// <returns>Liste des actions prioritaires à échéance dans le nombre de jours spécifié</returns>
        Task<IEnumerable<ActionPrioritaire>> GetByEcheanceAsync(int jours);

        /// <summary>
        /// Récupère les actions prioritaires en retard (échéance dépassée et non complétées)
        /// </summary>
        /// <returns>Liste des actions prioritaires en retard</returns>
        Task<IEnumerable<ActionPrioritaire>> GetEnRetardAsync();

        /// <summary>
        /// Crée une nouvelle action prioritaire
        /// </summary>
        /// <param name="actionPrioritaire">Action prioritaire à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire créée avec son identifiant généré</returns>
        Task<ActionPrioritaire> CreateAsync(ActionPrioritaire actionPrioritaire, int creePar);

        /// <summary>
        /// Met à jour une action prioritaire existante
        /// </summary>
        /// <param name="actionPrioritaire">Action prioritaire à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire mise à jour</returns>
        Task<ActionPrioritaire> UpdateAsync(ActionPrioritaire actionPrioritaire, int modifiePar);

        /// <summary>
        /// Supprime une action prioritaire
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Marque une action prioritaire comme complétée
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire</param>
        /// <param name="commentaire">Commentaire sur la complétion</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire mise à jour</returns>
        Task<ActionPrioritaire> MarquerCompleteAsync(int id, string commentaire, int modifiePar);

        /// <summary>
        /// Assigne une action prioritaire à un utilisateur
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire mise à jour</returns>
        Task<ActionPrioritaire> AssignerAsync(int id, int utilisateurId, int modifiePar);

        /// <summary>
        /// Génère automatiquement des actions prioritaires pour tous les clients
        /// </summary>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre d'actions prioritaires créées</returns>
        Task<int> GenererActionsPrioritairesAsync(int creePar);

        /// <summary>
        /// Alias pour GenererActionsPrioritairesAsync
        /// </summary>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre d'actions prioritaires créées</returns>
        Task<int> GenerateActionsAsync(int creePar);

        /// <summary>
        /// Alias pour MarquerCompleteAsync
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire mise à jour</returns>
        Task<ActionPrioritaire> CompleteActionAsync(int id, int modifiePar);

        /// <summary>
        /// Récupère le nombre d'actions prioritaires par niveau de priorité
        /// </summary>
        /// <returns>Dictionnaire avec le niveau de priorité comme clé et le nombre d'actions comme valeur</returns>
        Task<Dictionary<NiveauPriorite, int>> GetCountByNiveauPrioriteAsync();

        /// <summary>
        /// Alias pour GetCountByNiveauPrioriteAsync
        /// </summary>
        /// <returns>Dictionnaire avec le niveau de priorité comme clé et le nombre d'actions comme valeur</returns>
        Task<Dictionary<NiveauPriorite, int>> GetDistributionByPrioriteAsync();
    }
}
