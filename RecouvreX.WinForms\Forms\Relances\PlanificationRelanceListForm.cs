using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using RecouvreX.WinForms.Helpers;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Relances
{
    public partial class PlanificationRelanceListForm : Form
    {
        private readonly IPlanificationRelanceService _planificationRelanceService;
        private readonly IModeleRelanceService _modeleRelanceService;
        private readonly IFactureService _factureService;
        private readonly IClientService _clientService;
        private readonly IAuthenticationService _authenticationService;
        private readonly int _currentUserId;
        private List<PlanificationRelance> _planifications = new List<PlanificationRelance>();
        private DataTable _dataTable = new DataTable();

        public PlanificationRelanceListForm(
            IPlanificationRelanceService planificationRelanceService,
            IModeleRelanceService modeleRelanceService,
            IFactureService factureService,
            IClientService clientService,
            IAuthenticationService authenticationService,
            int currentUserId)
        {
            _planificationRelanceService = planificationRelanceService ?? throw new ArgumentNullException(nameof(planificationRelanceService));
            _modeleRelanceService = modeleRelanceService ?? throw new ArgumentNullException(nameof(modeleRelanceService));
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }

        private async void PlanificationRelanceListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Vérifier les permissions
                bool canView = await _authenticationService.HasPermissionAsync(_currentUserId, "RELANCES_VIEW");
                bool canValidate = await _authenticationService.HasPermissionAsync(_currentUserId, "RELANCES_VALIDATE");
                bool canSend = await _authenticationService.HasPermissionAsync(_currentUserId, "RELANCES_SEND");
                bool canCancel = await _authenticationService.HasPermissionAsync(_currentUserId, "RELANCES_CANCEL");

                if (!canView)
                {
                    MessageBox.Show("Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.",
                        "Accès refusé", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return;
                }

                // Configurer les boutons en fonction des permissions
                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                var validateButton = toolStrip?.Items.Find("validateButton", false).FirstOrDefault() as ToolStripButton;
                var sendButton = toolStrip?.Items.Find("sendButton", false).FirstOrDefault() as ToolStripButton;
                var cancelButton = toolStrip?.Items.Find("cancelButton", false).FirstOrDefault() as ToolStripButton;

                if (validateButton != null) validateButton.Enabled = canValidate;
                if (sendButton != null) sendButton.Enabled = canSend;
                if (cancelButton != null) cancelButton.Enabled = canCancel;

                // Initialiser la table de données
                InitializeDataTable();

                // Initialiser les filtres
                var statutFilterComboBox = this.Controls.Find("statutFilterComboBox", true).FirstOrDefault() as ComboBox;
                if (statutFilterComboBox != null)
                {
                    statutFilterComboBox.Items.Clear();
                    statutFilterComboBox.Items.Add("Tous les statuts");
                    statutFilterComboBox.Items.Add(StatutPlanificationRelance.Planifiee);
                    statutFilterComboBox.Items.Add(StatutPlanificationRelance.EnAttenteValidation);
                    statutFilterComboBox.Items.Add(StatutPlanificationRelance.Validee);
                    statutFilterComboBox.Items.Add(StatutPlanificationRelance.Envoyee);
                    statutFilterComboBox.Items.Add(StatutPlanificationRelance.Annulee);
                    statutFilterComboBox.SelectedIndex = 0;
                }

                var niveauFilterComboBox = this.Controls.Find("niveauFilterComboBox", true).FirstOrDefault() as ComboBox;
                if (niveauFilterComboBox != null)
                {
                    niveauFilterComboBox.Items.Clear();
                    niveauFilterComboBox.Items.Add("Tous les niveaux");
                    niveauFilterComboBox.Items.Add("Niveau 1");
                    niveauFilterComboBox.Items.Add("Niveau 2");
                    niveauFilterComboBox.Items.Add("Niveau 3");
                    niveauFilterComboBox.SelectedIndex = 0;
                }

                // Charger les planifications de relance
                await LoadPlanificationsAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire de liste des planifications de relance");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Facture", typeof(string));
            _dataTable.Columns.Add("Client", typeof(string));
            _dataTable.Columns.Add("Niveau", typeof(string));
            _dataTable.Columns.Add("Date prévue", typeof(DateTime));
            _dataTable.Columns.Add("Statut", typeof(string));
            _dataTable.Columns.Add("Modèle", typeof(string));
            _dataTable.Columns.Add("Canal", typeof(string));
            _dataTable.Columns.Add("Date envoi", typeof(DateTime));

            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (dataGridView.Columns["Date prévue"] != null)
                    dataGridView.Columns["Date prévue"].DefaultCellStyle.Format = "dd/MM/yyyy";
                if (dataGridView.Columns["Date envoi"] != null)
                    dataGridView.Columns["Date envoi"].DefaultCellStyle.Format = "dd/MM/yyyy";

                // Ajouter un gestionnaire d'événements pour la mise en forme des cellules
                dataGridView.CellFormatting += DataGridView_CellFormatting;
            }
        }

        private void DataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            var dataGridView = sender as DataGridView;
            if (dataGridView != null && e.RowIndex >= 0)
            {
                // Colorer la cellule Statut en fonction du statut
                if (e.ColumnIndex == dataGridView.Columns["Statut"].Index && e.Value != null)
                {
                    string statut = e.Value.ToString();
                    e.CellStyle.ForeColor = PlanificationHelper.GetStatusColor(statut);
                    e.CellStyle.Font = new Font(dataGridView.Font, FontStyle.Bold);
                }

                // Colorer la cellule Niveau en fonction du niveau
                if (e.ColumnIndex == dataGridView.Columns["Niveau"].Index && e.Value != null)
                {
                    string niveau = e.Value.ToString();
                    int niveauValue = 1;
                    if (niveau.Contains("2"))
                        niveauValue = 2;
                    else if (niveau.Contains("3"))
                        niveauValue = 3;

                    e.CellStyle.ForeColor = PlanificationHelper.GetLevelColor(niveauValue);
                    e.CellStyle.Font = new Font(dataGridView.Font, FontStyle.Bold);
                }
            }
        }

        private async Task LoadPlanificationsAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer les planifications de relance
                _planifications = (await _planificationRelanceService.GetAllAsync()).ToList();

                // Pour chaque planification, récupérer la facture et le client
                foreach (var planification in _planifications)
                {
                    var facture = await _factureService.GetByIdAsync(planification.FactureId);
                    if (facture != null)
                    {
                        planification.Facture = facture;
                        var client = await _clientService.GetByIdAsync(facture.ClientId);
                        if (client != null)
                        {
                            facture.Client = client;
                        }
                    }

                    if (planification.ModeleRelanceId.HasValue)
                    {
                        var modele = await _modeleRelanceService.GetByIdAsync(planification.ModeleRelanceId.Value);
                        if (modele != null)
                        {
                            planification.ModeleRelance = modele;
                        }
                    }
                }

                // Appliquer les filtres
                ApplyFilters();

                // Mettre à jour le compteur
                var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
                var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
                if (countLabel != null)
                {
                    countLabel.Text = $"Nombre de planifications : {_planifications.Count}";
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des planifications de relance");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des planifications de relance : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void ApplyFilters()
        {
            try
            {
                // Récupérer les valeurs des filtres
                var statutFilterComboBox = this.Controls.Find("statutFilterComboBox", true).FirstOrDefault() as ComboBox;
                var niveauFilterComboBox = this.Controls.Find("niveauFilterComboBox", true).FirstOrDefault() as ComboBox;
                var searchTextBox = this.Controls.Find("searchTextBox", true).FirstOrDefault() as TextBox;
                var dateDebutPicker = this.Controls.Find("dateDebutPicker", true).FirstOrDefault() as DateTimePicker;
                var dateFinPicker = this.Controls.Find("dateFinPicker", true).FirstOrDefault() as DateTimePicker;

                string statutFilter = statutFilterComboBox?.SelectedIndex > 0 ? statutFilterComboBox.SelectedItem.ToString() : null;
                int niveauFilter = niveauFilterComboBox?.SelectedIndex > 0 ? niveauFilterComboBox.SelectedIndex : 0;
                string searchText = searchTextBox?.Text?.Trim().ToLower() ?? string.Empty;
                DateTime? dateDebut = dateDebutPicker?.Checked == true ? dateDebutPicker.Value.Date : (DateTime?)null;
                DateTime? dateFin = dateFinPicker?.Checked == true ? dateFinPicker.Value.Date.AddDays(1).AddSeconds(-1) : (DateTime?)null;

                // Filtrer les planifications
                var filteredPlanifications = _planifications.AsEnumerable();

                if (!string.IsNullOrEmpty(statutFilter))
                {
                    filteredPlanifications = filteredPlanifications.Where(p => p.Statut == statutFilter);
                }

                if (niveauFilter > 0)
                {
                    filteredPlanifications = filteredPlanifications.Where(p => p.NiveauRelance == niveauFilter);
                }

                if (!string.IsNullOrEmpty(searchText))
                {
                    filteredPlanifications = filteredPlanifications.Where(p =>
                        (p.Facture?.Numero?.ToLower().Contains(searchText) == true) ||
                        (p.Facture?.Client?.RaisonSociale?.ToLower().Contains(searchText) == true) ||
                        (p.ModeleRelance?.Nom?.ToLower().Contains(searchText) == true)
                    );
                }

                if (dateDebut.HasValue)
                {
                    filteredPlanifications = filteredPlanifications.Where(p => p.DatePrevue >= dateDebut.Value);
                }

                if (dateFin.HasValue)
                {
                    filteredPlanifications = filteredPlanifications.Where(p => p.DatePrevue <= dateFin.Value);
                }

                // Mettre à jour la table de données
                UpdateDataTable(filteredPlanifications.ToList());
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'application des filtres");
                MessageBox.Show($"Une erreur s'est produite lors de l'application des filtres : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateDataTable(List<PlanificationRelance> planifications)
        {
            _dataTable.Clear();

            foreach (var planification in planifications)
            {
                _dataTable.Rows.Add(
                    planification.Id,
                    planification.Facture?.Numero ?? $"Facture #{planification.FactureId}",
                    planification.Facture?.Client?.RaisonSociale ?? "Client inconnu",
                    PlanificationHelper.GetLevelDescription(planification.NiveauRelance),
                    planification.DatePrevue,
                    planification.Statut,
                    planification.ModeleRelance?.Nom ?? "Modèle inconnu",
                    planification.Canal,
                    planification.DateEnvoi
                );
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            // Recharger les planifications de relance
            await LoadPlanificationsAsync();
        }

        private void ApplyFilterButton_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                ApplyFilters();
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        private async void ValidateButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer la planification sélectionnée
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int planificationId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string statut = dataGridView.SelectedRows[0].Cells["Statut"].Value.ToString();

                    // Vérifier que la planification est en attente de validation
                    if (statut != StatutPlanificationRelance.EnAttenteValidation)
                    {
                        MessageBox.Show("Seules les planifications en attente de validation peuvent être validées.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // Demander confirmation
                    var result = MessageBox.Show("Êtes-vous sûr de vouloir valider cette planification de relance ?",
                        "Confirmation de validation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Valider la planification
                        bool success = await _planificationRelanceService.ValidateAsync(planificationId, _currentUserId);
                        if (success)
                        {
                            // Recharger les planifications
                            await LoadPlanificationsAsync();
                            MessageBox.Show("La planification a été validée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("Une erreur s'est produite lors de la validation de la planification.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une planification à valider.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la validation d'une planification de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SendButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer la planification sélectionnée
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int planificationId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string statut = dataGridView.SelectedRows[0].Cells["Statut"].Value.ToString();

                    // Vérifier que la planification est planifiée ou validée
                    if (statut != StatutPlanificationRelance.Planifiee && statut != StatutPlanificationRelance.Validee)
                    {
                        MessageBox.Show("Seules les planifications planifiées ou validées peuvent être envoyées.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // Récupérer la planification
                    var planification = await _planificationRelanceService.GetByIdAsync(planificationId);
                    if (planification == null)
                    {
                        MessageBox.Show("La planification demandée n'existe pas ou a été supprimée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // Récupérer la facture et le client
                    var facture = await _factureService.GetByIdAsync(planification.FactureId);
                    if (facture == null)
                    {
                        MessageBox.Show("La facture associée à cette planification n'existe pas ou a été supprimée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    var client = await _clientService.GetByIdAsync(facture.ClientId);
                    if (client == null)
                    {
                        MessageBox.Show("Le client associé à cette facture n'existe pas ou a été supprimé.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // Récupérer le modèle de relance
                    if (!planification.ModeleRelanceId.HasValue)
                    {
                        MessageBox.Show("Aucun modèle de relance n'est associé à cette planification.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    var modele = await _modeleRelanceService.GetByIdAsync(planification.ModeleRelanceId.Value);
                    if (modele == null)
                    {
                        MessageBox.Show("Le modèle de relance associé à cette planification n'existe pas ou a été supprimé.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // Appliquer les variables dynamiques au modèle
                    string contenu = await _modeleRelanceService.ApplyVariablesAsync(modele, facture.Id);

                    // Afficher un aperçu de la relance
                    PlanificationHelper.ShowPreview(planification, facture, client, modele, contenu);

                    // Demander confirmation
                    var result = MessageBox.Show("Êtes-vous sûr de vouloir envoyer cette relance ?",
                        "Confirmation d'envoi", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Marquer la planification comme envoyée
                        bool success = await _planificationRelanceService.MarkAsSentAsync(planificationId, _currentUserId);
                        if (success)
                        {
                            // Recharger les planifications
                            await LoadPlanificationsAsync();
                            MessageBox.Show("La relance a été envoyée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("Une erreur s'est produite lors de l'envoi de la relance.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une planification à envoyer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'envoi d'une relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void CancelButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer la planification sélectionnée
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int planificationId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string statut = dataGridView.SelectedRows[0].Cells["Statut"].Value.ToString();

                    // Vérifier que la planification n'est pas déjà envoyée ou annulée
                    if (statut == StatutPlanificationRelance.Envoyee || statut == StatutPlanificationRelance.Annulee)
                    {
                        MessageBox.Show("Les planifications déjà envoyées ou annulées ne peuvent pas être annulées.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // Demander confirmation
                    var result = MessageBox.Show("Êtes-vous sûr de vouloir annuler cette planification de relance ?",
                        "Confirmation d'annulation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Demander un commentaire
                        string commentaire = string.Empty;
                        using (var form = new CommentaireForm())
                        {
                            if (form.ShowDialog() == DialogResult.OK)
                            {
                                commentaire = form.Commentaire;
                            }
                            else
                            {
                                return;
                            }
                        }

                        // Annuler la planification
                        bool success = await _planificationRelanceService.CancelAsync(planificationId, _currentUserId, commentaire);
                        if (success)
                        {
                            // Recharger les planifications
                            await LoadPlanificationsAsync();
                            MessageBox.Show("La planification a été annulée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("Une erreur s'est produite lors de l'annulation de la planification.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une planification à annuler.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'annulation d'une planification de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int planificationId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    ShowDetails(planificationId);
                }
            }
        }

        private async void ShowDetails(int planificationId)
        {
            try
            {
                // Récupérer la planification
                var planification = await _planificationRelanceService.GetByIdAsync(planificationId);
                if (planification == null)
                {
                    MessageBox.Show("La planification demandée n'existe pas ou a été supprimée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Récupérer la facture et le client
                var facture = await _factureService.GetByIdAsync(planification.FactureId);
                if (facture == null)
                {
                    MessageBox.Show("La facture associée à cette planification n'existe pas ou a été supprimée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var client = await _clientService.GetByIdAsync(facture.ClientId);
                if (client == null)
                {
                    MessageBox.Show("Le client associé à cette facture n'existe pas ou a été supprimé.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Récupérer le modèle de relance
                if (!planification.ModeleRelanceId.HasValue)
                {
                    MessageBox.Show("Aucun modèle de relance n'est associé à cette planification.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var modele = await _modeleRelanceService.GetByIdAsync(planification.ModeleRelanceId.Value);
                if (modele == null)
                {
                    MessageBox.Show("Le modèle de relance associé à cette planification n'existe pas ou a été supprimé.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Appliquer les variables dynamiques au modèle
                string contenu = await _modeleRelanceService.ApplyVariablesAsync(modele, facture.Id);

                // Afficher un aperçu de la relance
                PlanificationHelper.ShowPreview(planification, facture, client, modele, contenu);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'affichage des détails d'une planification de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
