using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;

namespace RecouvreX.WinForms.Forms.Litiges
{
    /// <summary>
    /// Formulaire d'édition d'une étape de litige
    /// </summary>
    public partial class EtapeLitigeEditForm : Form
    {
        private readonly ILitigeService _litigeService;
        private readonly int _currentUserId;
        private EtapeLitige? _etape;
        private bool _isNewEtape = true;
        private List<EtapeLitige> _etapesExistantes = new List<EtapeLitige>();

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="litigeService">Service de gestion des litiges</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        /// <param name="etape">Étape à éditer (null pour une nouvelle étape)</param>
        public EtapeLitigeEditForm(ILitigeService litigeService, int currentUserId, EtapeLitige? etape = null)
        {
            _litigeService = litigeService ?? throw new ArgumentNullException(nameof(litigeService));
            _currentUserId = currentUserId;
            _etape = etape;
            _isNewEtape = etape == null;

            InitializeComponent();
        }

        // La méthode InitializeComponent() a été déplacée dans le fichier EtapeLitigeEditForm.Designer.cs

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void EtapeLitigeEditForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Définir le titre du formulaire et de l'étiquette de titre
                this.Text = _isNewEtape ? "Nouvelle étape de litige" : "Modifier une étape de litige";

                // Charger l'icône du formulaire
                try
                {
                    this.Icon = new Icon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "dispute.ico"));
                }
                catch
                {
                    // Ignorer les erreurs d'icône
                }

                // Mettre à jour le titre
                var titleLabelControl = this.Controls.Find("titleLabel", true).FirstOrDefault() as Label;
                if (titleLabelControl != null)
                {
                    titleLabelControl.Text = _isNewEtape ? "Nouvelle étape de litige" : "Modifier une étape de litige";
                }

                // Si c'est une modification, remplir les champs avec les données de l'étape
                if (!_isNewEtape && _etape != null)
                {
                    var nomTextBoxControl = this.Controls.Find("nomTextBox", true).FirstOrDefault() as TextBox;
                    if (nomTextBoxControl != null)
                        nomTextBoxControl.Text = _etape.Nom;

                    var descriptionTextBoxControl = this.Controls.Find("descriptionTextBox", true).FirstOrDefault() as TextBox;
                    if (descriptionTextBoxControl != null)
                        descriptionTextBoxControl.Text = _etape.Description;

                    var ordreNumericUpDownControl = this.Controls.Find("ordreNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                    if (ordreNumericUpDownControl != null)
                        ordreNumericUpDownControl.Value = _etape.Ordre;

                    var delaiNumericUpDownControl = this.Controls.Find("delaiNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                    if (delaiNumericUpDownControl != null)
                        delaiNumericUpDownControl.Value = _etape.DelaiJours;

                    var etapeFinaleCheckBoxControl = this.Controls.Find("etapeFinaleCheckBox", true).FirstOrDefault() as CheckBox;
                    if (etapeFinaleCheckBoxControl != null)
                        etapeFinaleCheckBoxControl.Checked = _etape.EstEtapeFinale;

                    var necessiteValidationCheckBoxControl = this.Controls.Find("necessiteValidationCheckBox", true).FirstOrDefault() as CheckBox;
                    if (necessiteValidationCheckBoxControl != null)
                        necessiteValidationCheckBoxControl.Checked = _etape.NecessiteValidation;
                }

                // Charger les étapes existantes pour vérifier les ordres
                _etapesExistantes = (await _litigeService.GetAllEtapesAsync()).ToList();

                // Remplir la liste des étapes d'escalade
                var etapeEscaladeComboBox = this.Controls.Find("etapeEscaladeComboBox", true).FirstOrDefault() as ComboBox;
                if (etapeEscaladeComboBox != null)
                {
                    etapeEscaladeComboBox.Items.Clear();
                    etapeEscaladeComboBox.DisplayMember = "Nom";
                    etapeEscaladeComboBox.ValueMember = "Id";

                    // Ajouter les étapes dans l'ordre
                    foreach (var etape in _etapesExistantes.OrderBy(e => e.Ordre))
                    {
                        // Ne pas ajouter l'étape courante ni les étapes finales
                        if ((_etape == null || etape.Id != _etape.Id) && !etape.EstEtapeFinale)
                        {
                            etapeEscaladeComboBox.Items.Add(etape);
                        }
                    }

                    // Sélectionner l'étape d'escalade si elle existe
                    if (!_isNewEtape && _etape != null && _etape.EtapeEscaladeId.HasValue)
                    {
                        for (int i = 0; i < etapeEscaladeComboBox.Items.Count; i++)
                        {
                            var item = etapeEscaladeComboBox.Items[i] as EtapeLitige;
                            if (item != null && item.Id == _etape.EtapeEscaladeId.Value)
                            {
                                etapeEscaladeComboBox.SelectedIndex = i;
                                break;
                            }
                        }
                    }
                }

                // Si c'est une modification, configurer l'escalade automatique
                if (!_isNewEtape && _etape != null)
                {
                    var escaladeAutomatiqueCheckBox = this.Controls.Find("escaladeAutomatiqueCheckBox", true).FirstOrDefault() as CheckBox;
                    if (escaladeAutomatiqueCheckBox != null)
                    {
                        escaladeAutomatiqueCheckBox.Checked = _etape.EscaladeAutomatique;
                    }
                }

                // Si c'est une nouvelle étape, proposer le prochain ordre disponible
                if (_isNewEtape)
                {
                    var ordreNumericUpDown = this.Controls.Find("ordreNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                    if (ordreNumericUpDown != null && _etapesExistantes.Any())
                    {
                        int maxOrdre = _etapesExistantes.Max(e => e.Ordre);
                        ordreNumericUpDown.Value = maxOrdre + 1;
                    }
                }

                // Mettre le focus sur le champ Nom
                var nomTextBox = this.Controls.Find("nomTextBox", true).FirstOrDefault() as TextBox;
                if (nomTextBox != null)
                {
                    nomTextBox.Focus();
                }

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors du chargement du formulaire d'édition d'étape de litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gère le changement d'état de la case à cocher d'escalade automatique
        /// </summary>
        private void EscaladeAutomatiqueCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            var escaladeAutomatiqueCheckBox = sender as CheckBox;
            if (escaladeAutomatiqueCheckBox != null)
            {
                var etapeEscaladeLabel = this.Controls.Find("etapeEscaladeLabel", true).FirstOrDefault() as Label;
                var etapeEscaladeComboBox = this.Controls.Find("etapeEscaladeComboBox", true).FirstOrDefault() as ComboBox;

                if (etapeEscaladeLabel != null)
                {
                    etapeEscaladeLabel.Enabled = escaladeAutomatiqueCheckBox.Checked;
                }

                if (etapeEscaladeComboBox != null)
                {
                    etapeEscaladeComboBox.Enabled = escaladeAutomatiqueCheckBox.Checked;
                }
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Enregistrer
        /// </summary>
        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer les valeurs des champs
                var nomTextBox = this.Controls.Find("nomTextBox", true).FirstOrDefault() as TextBox;
                var descriptionTextBox = this.Controls.Find("descriptionTextBox", true).FirstOrDefault() as TextBox;
                var ordreNumericUpDown = this.Controls.Find("ordreNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                var delaiNumericUpDown = this.Controls.Find("delaiNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                var etapeFinaleCheckBox = this.Controls.Find("etapeFinaleCheckBox", true).FirstOrDefault() as CheckBox;
                var necessiteValidationCheckBox = this.Controls.Find("necessiteValidationCheckBox", true).FirstOrDefault() as CheckBox;
                var escaladeAutomatiqueCheckBox = this.Controls.Find("escaladeAutomatiqueCheckBox", true).FirstOrDefault() as CheckBox;
                var etapeEscaladeComboBox = this.Controls.Find("etapeEscaladeComboBox", true).FirstOrDefault() as ComboBox;

                // Valider les champs obligatoires
                if (string.IsNullOrWhiteSpace(nomTextBox?.Text))
                {
                    MessageBox.Show("Le nom de l'étape est obligatoire.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Vérifier que l'ordre n'est pas déjà utilisé par une autre étape
                int ordre = (int)(ordreNumericUpDown?.Value ?? 1);
                if (_etapesExistantes.Any(e => e.Ordre == ordre && e.Id != (_etape?.Id ?? 0)))
                {
                    MessageBox.Show("Cet ordre est déjà utilisé par une autre étape. Veuillez choisir un ordre différent.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Créer ou mettre à jour l'étape
                if (_isNewEtape)
                {
                    // Créer une nouvelle étape
                    var etape = new EtapeLitige
                    {
                        Nom = nomTextBox.Text,
                        Description = descriptionTextBox?.Text ?? string.Empty,
                        Ordre = ordre,
                        DelaiJours = (int)(delaiNumericUpDown?.Value ?? 7),
                        EstEtapeFinale = etapeFinaleCheckBox?.Checked ?? false,
                        NecessiteValidation = necessiteValidationCheckBox?.Checked ?? false,
                        EscaladeAutomatique = escaladeAutomatiqueCheckBox?.Checked ?? false,
                        EtapeEscaladeId = escaladeAutomatiqueCheckBox?.Checked == true && etapeEscaladeComboBox?.SelectedItem != null
                            ? ((EtapeLitige)etapeEscaladeComboBox.SelectedItem).Id
                            : (int?)null
                    };

                    // Enregistrer l'étape
                    await _litigeService.CreateEtapeAsync(etape, _currentUserId);
                }
                else
                {
                    // Mettre à jour l'étape existante
                    _etape.Nom = nomTextBox.Text;
                    _etape.Description = descriptionTextBox?.Text ?? string.Empty;
                    _etape.Ordre = ordre;
                    _etape.DelaiJours = (int)(delaiNumericUpDown?.Value ?? 7);
                    _etape.EstEtapeFinale = etapeFinaleCheckBox?.Checked ?? false;
                    _etape.NecessiteValidation = necessiteValidationCheckBox?.Checked ?? false;
                    _etape.EscaladeAutomatique = escaladeAutomatiqueCheckBox?.Checked ?? false;
                    _etape.EtapeEscaladeId = escaladeAutomatiqueCheckBox?.Checked == true && etapeEscaladeComboBox?.SelectedItem != null
                        ? ((EtapeLitige)etapeEscaladeComboBox.SelectedItem).Id
                        : (int?)null;

                    // Enregistrer les modifications
                    await _litigeService.UpdateEtapeAsync(_etape, _currentUserId);
                }

                // Fermer le formulaire avec succès
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement de l'étape de litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Annuler
        /// </summary>
        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
