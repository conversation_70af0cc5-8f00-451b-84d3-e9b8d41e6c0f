using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;
using RecouvreX.Models.Enums;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace RecouvreX.WinForms.Forms.Factures
{
    public partial class FactureDetailsForm : Form
    {
        private readonly IFactureService _factureService;
        private readonly IClientService _clientService;
        private readonly IPaiementService _paiementService;
        private readonly IRelanceService _relanceService;
        private readonly ICommunicationService _communicationService;
        private readonly IContactClientService _contactClientService;
        private readonly IConfiguration _configuration;
        private readonly IServiceProvider _serviceProvider;
        private readonly int _currentUserId;
        private readonly int _factureId;
        private Facture? _facture;
        private Client? _client;
        private List<Paiement> _paiements = new List<Paiement>();
        private List<Relance> _relances = new List<Relance>();
        private DataTable _paiementsDataTable = new DataTable();
        private DataTable _relancesDataTable = new DataTable();

        public FactureDetailsForm(IFactureService factureService, IClientService clientService, int currentUserId, int factureId)
        {
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _paiementService = null!; // À injecter
            _relanceService = null!; // À injecter
            _currentUserId = currentUserId;
            _factureId = factureId;

            InitializeComponent();
        }

        public FactureDetailsForm(IFactureService factureService, IClientService clientService,
            IPaiementService paiementService, IRelanceService relanceService, int currentUserId, int factureId)
        {
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _paiementService = paiementService ?? throw new ArgumentNullException(nameof(paiementService));
            _relanceService = relanceService ?? throw new ArgumentNullException(nameof(relanceService));
            _communicationService = null!; // À injecter
            _contactClientService = null!; // À injecter
            _configuration = null!; // À injecter
            _serviceProvider = null!; // À injecter
            _currentUserId = currentUserId;
            _factureId = factureId;

            InitializeComponent();
        }

        public FactureDetailsForm(
            IFactureService factureService,
            IClientService clientService,
            IPaiementService paiementService,
            IRelanceService relanceService,
            ICommunicationService communicationService,
            IContactClientService contactClientService,
            IConfiguration configuration,
            IServiceProvider serviceProvider,
            int currentUserId,
            int factureId)
        {
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _paiementService = paiementService ?? throw new ArgumentNullException(nameof(paiementService));
            _relanceService = relanceService ?? throw new ArgumentNullException(nameof(relanceService));
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _contactClientService = contactClientService ?? throw new ArgumentNullException(nameof(contactClientService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _currentUserId = currentUserId;
            _factureId = factureId;

            InitializeComponent();
        }



        private async void FactureDetailsForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialiser les tables de données
                InitializePaiementsDataTable();
                InitializeRelancesDataTable();

                // Charger les données de la facture
                await LoadFactureDataAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des détails de la facture");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des détails de la facture : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializePaiementsDataTable()
        {
            _paiementsDataTable = new DataTable();
            _paiementsDataTable.Columns.Add("Id", typeof(int));
            _paiementsDataTable.Columns.Add("Référence", typeof(string));
            _paiementsDataTable.Columns.Add("Date", typeof(DateTime));
            _paiementsDataTable.Columns.Add("Montant", typeof(decimal));
            _paiementsDataTable.Columns.Add("Mode", typeof(string));
            _paiementsDataTable.Columns.Add("Référence Bancaire", typeof(string));

            var paiementsDataGridView = this.Controls.Find("paiementsDataGridView", true).FirstOrDefault() as DataGridView;
            if (paiementsDataGridView != null)
            {
                paiementsDataGridView.DataSource = _paiementsDataTable;

                // Masquer la colonne Id
                if (paiementsDataGridView.Columns["Id"] != null)
                    paiementsDataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (paiementsDataGridView.Columns["Date"] != null)
                    paiementsDataGridView.Columns["Date"].DefaultCellStyle.Format = "dd/MM/yyyy";
                if (paiementsDataGridView.Columns["Montant"] != null)
                    paiementsDataGridView.Columns["Montant"].DefaultCellStyle.Format = "C2";
            }
        }

        private void InitializeRelancesDataTable()
        {
            _relancesDataTable = new DataTable();
            _relancesDataTable.Columns.Add("Id", typeof(int));
            _relancesDataTable.Columns.Add("Type", typeof(string));
            _relancesDataTable.Columns.Add("Date", typeof(DateTime));
            _relancesDataTable.Columns.Add("Niveau", typeof(int));
            _relancesDataTable.Columns.Add("Statut", typeof(string));
            _relancesDataTable.Columns.Add("Utilisateur", typeof(string));
            _relancesDataTable.Columns.Add("Date Prochaine", typeof(DateTime));

            var relancesDataGridView = this.Controls.Find("relancesDataGridView", true).FirstOrDefault() as DataGridView;
            if (relancesDataGridView != null)
            {
                relancesDataGridView.DataSource = _relancesDataTable;

                // Masquer la colonne Id
                if (relancesDataGridView.Columns["Id"] != null)
                    relancesDataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (relancesDataGridView.Columns["Date"] != null)
                    relancesDataGridView.Columns["Date"].DefaultCellStyle.Format = "dd/MM/yyyy";
                if (relancesDataGridView.Columns["Date Prochaine"] != null)
                    relancesDataGridView.Columns["Date Prochaine"].DefaultCellStyle.Format = "dd/MM/yyyy";
            }
        }

        private async Task LoadFactureDataAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer la facture
                _facture = await _factureService.GetByIdAsync(_factureId);
                if (_facture == null)
                {
                    MessageBox.Show("La facture demandée n'existe pas.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Récupérer le client
                _client = await _clientService.GetByIdAsync(_facture.ClientId);
                if (_client == null)
                {
                    MessageBox.Show("Le client associé à cette facture n'existe pas.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Mettre à jour le titre du formulaire
                this.Text = $"Détails de la facture - {_facture.Numero}";

                // Remplir les informations de la facture
                FillFactureInfo(_facture, _client);

                // Récupérer les paiements si le service est disponible
                if (_paiementService != null)
                {
                    _paiements = (await _paiementService.GetByFactureIdAsync(_factureId)).ToList();
                    UpdatePaiementsDataTable(_paiements);
                }

                // Récupérer les relances si le service est disponible
                if (_relanceService != null)
                {
                    _relances = (await _relanceService.GetByFactureIdAsync(_factureId)).ToList();
                    UpdateRelancesDataTable(_relances);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données de la facture");
                throw;
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void FillFactureInfo(Facture facture, Client client)
        {
            var numeroValueLabel = this.Controls.Find("numeroValueLabel", true).FirstOrDefault() as Label;
            var clientValueLabel = this.Controls.Find("clientValueLabel", true).FirstOrDefault() as Label;
            var dateEmissionValueLabel = this.Controls.Find("dateEmissionValueLabel", true).FirstOrDefault() as Label;
            var dateEcheanceValueLabel = this.Controls.Find("dateEcheanceValueLabel", true).FirstOrDefault() as Label;
            var montantHTValueLabel = this.Controls.Find("montantHTValueLabel", true).FirstOrDefault() as Label;
            var montantTVAValueLabel = this.Controls.Find("montantTVAValueLabel", true).FirstOrDefault() as Label;
            var montantTTCValueLabel = this.Controls.Find("montantTTCValueLabel", true).FirstOrDefault() as Label;
            var statutValueLabel = this.Controls.Find("statutValueLabel", true).FirstOrDefault() as Label;

            if (numeroValueLabel != null) numeroValueLabel.Text = facture.Numero;
            if (clientValueLabel != null) clientValueLabel.Text = $"{client.Code} - {client.RaisonSociale}";
            if (dateEmissionValueLabel != null) dateEmissionValueLabel.Text = facture.DateEmission.ToString("dd/MM/yyyy");
            if (dateEcheanceValueLabel != null) dateEcheanceValueLabel.Text = facture.DateEcheance.ToString("dd/MM/yyyy");
            if (montantHTValueLabel != null) montantHTValueLabel.Text = facture.MontantHT.ToString("C2");
            if (montantTVAValueLabel != null) montantTVAValueLabel.Text = facture.MontantTVA.ToString("C2");
            if (montantTTCValueLabel != null) montantTTCValueLabel.Text = facture.MontantTTC.ToString("C2");
            if (statutValueLabel != null)
            {
                statutValueLabel.Text = facture.Statut;

                // Colorer le statut
                switch (facture.Statut)
                {
                    case "Payée":
                        statutValueLabel.ForeColor = Color.Green;
                        break;
                    case "En retard":
                        statutValueLabel.ForeColor = Color.Red;
                        break;
                    case "Payée partiellement":
                        statutValueLabel.ForeColor = Color.Orange;
                        break;
                    case "Litige":
                        statutValueLabel.ForeColor = Color.Red;
                        break;
                    case "Annulée":
                        statutValueLabel.ForeColor = Color.Gray;
                        break;
                    default:
                        statutValueLabel.ForeColor = SystemColors.ControlText;
                        break;
                }
            }
        }

        private void UpdatePaiementsDataTable(List<Paiement> paiements)
        {
            _paiementsDataTable.Clear();

            foreach (var paiement in paiements)
            {
                _paiementsDataTable.Rows.Add(
                    paiement.Id,
                    paiement.Reference,
                    paiement.DatePaiement,
                    paiement.Montant,
                    paiement.ModePaiement,
                    paiement.ReferenceBancaire
                );
            }
        }

        private void UpdateRelancesDataTable(List<Relance> relances)
        {
            _relancesDataTable.Clear();

            foreach (var relance in relances)
            {
                _relancesDataTable.Rows.Add(
                    relance.Id,
                    relance.Type,
                    relance.DateRelance,
                    relance.Niveau,
                    relance.Statut,
                    relance.UtilisateurId.HasValue ? "Utilisateur " + relance.UtilisateurId.Value : "Système",
                    relance.DateProchaineRelance
                );
            }
        }

        private async void AddPaiementButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_facture == null)
                {
                    MessageBox.Show("Veuillez d'abord charger une facture.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Vérifier si la facture est déjà entièrement payée
                if (_facture.Statut == StatutFacture.Payee)
                {
                    MessageBox.Show("Cette facture est déjà entièrement payée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Créer un nouveau paiement pour cette facture
                var nouveauPaiement = new Paiement
                {
                    ClientId = _facture.ClientId,
                    DatePaiement = DateTime.Today,
                    Montant = _facture.MontantRestant,
                    EstActif = true
                };

                // Ouvrir le formulaire d'ajout de paiement
                using (var form = new Paiements.PaiementEditForm(_paiementService, _clientService, _currentUserId, nouveauPaiement))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger la facture et les paiements
                        await LoadFactureDataAsync();
                        MessageBox.Show("Le paiement a été ajouté avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ajout d'un paiement");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewPaiementButton_Click(object sender, EventArgs e)
        {
            var paiementsDataGridView = this.Controls.Find("paiementsDataGridView", true).FirstOrDefault() as DataGridView;
            if (paiementsDataGridView != null && paiementsDataGridView.SelectedRows.Count > 0)
            {
                int paiementId = Convert.ToInt32(paiementsDataGridView.SelectedRows[0].Cells["Id"].Value);
                ViewPaiement(paiementId);
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un paiement à consulter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void PaiementsDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int paiementId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    ViewPaiement(paiementId);
                }
            }
        }

        private void ViewPaiement(int paiementId)
        {
            try
            {
                // Ouvrir le formulaire de détails de paiement
                using (var form = new Paiements.PaiementDetailsForm(_paiementService, _clientService, _factureService, _currentUserId, paiementId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de détails de paiement");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void AddRelanceButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_facture == null)
                {
                    MessageBox.Show("Veuillez d'abord charger une facture.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Vérifier si la facture est déjà entièrement payée
                if (_facture.Statut == StatutFacture.Payee)
                {
                    MessageBox.Show("Cette facture est déjà entièrement payée, il n'est pas nécessaire de faire une relance.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Créer une nouvelle relance
                var relance = new Relance
                {
                    FactureId = _facture.Id,
                    DateRelance = DateTime.Today,
                    UtilisateurId = _currentUserId,
                    Statut = "Planifiée",
                    EstActif = true
                };

                // Ouvrir le formulaire d'édition de relance
                using (var form = new Relances.RelanceEditForm(_relanceService, _factureService, _clientService, _currentUserId, relance))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les relances
                        await LoadFactureDataAsync();
                        MessageBox.Show("La relance a été ajoutée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ajout d'une relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewRelanceButton_Click(object sender, EventArgs e)
        {
            var relancesDataGridView = this.Controls.Find("relancesDataGridView", true).FirstOrDefault() as DataGridView;
            if (relancesDataGridView != null && relancesDataGridView.SelectedRows.Count > 0)
            {
                int relanceId = Convert.ToInt32(relancesDataGridView.SelectedRows[0].Cells["Id"].Value);
                ViewRelance(relanceId);
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une relance à consulter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void RelancesDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int relanceId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    ViewRelance(relanceId);
                }
            }
        }

        private void ViewRelance(int relanceId)
        {
            try
            {
                // Ouvrir le formulaire de détails de relance
                using (var form = new Relances.RelanceDetailsForm(_relanceService, _factureService, _clientService, _currentUserId, relanceId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de détails de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Générer lettre"
        /// </summary>
        private void GenerateLettreButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_facture == null || _client == null)
                {
                    MessageBox.Show("Veuillez d'abord charger une facture.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Vérifier si la facture est déjà entièrement payée
                if (_facture.Statut == StatutFacture.Payee)
                {
                    MessageBox.Show("Cette facture est déjà entièrement payée, il n'est pas nécessaire de générer une lettre de relance.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Vérifier si les services nécessaires sont disponibles
                if (_communicationService == null || _contactClientService == null || _configuration == null || _serviceProvider == null)
                {
                    MessageBox.Show("Les services nécessaires pour générer une lettre de relance ne sont pas disponibles.",
                        "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Ouvrir le formulaire de génération de lettre de relance
                var lettreRelanceForm = _serviceProvider.GetRequiredService<Relances.LettreRelanceForm>();

                // Créer une nouvelle instance avec les paramètres corrects
                using (var form = new Relances.LettreRelanceForm(
                    _factureService,
                    _clientService,
                    _communicationService,
                    _contactClientService,
                    _configuration,
                    _currentUserId,
                    _factureId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les données
                        LoadFactureDataAsync().Wait();
                        MessageBox.Show("La lettre de relance a été générée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération de la lettre de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Envoyer WhatsApp"
        /// </summary>
        private void SendWhatsAppButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_facture == null || _client == null)
                {
                    MessageBox.Show("Veuillez d'abord charger une facture.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Vérifier si la facture est déjà entièrement payée
                if (_facture.Statut == StatutFacture.Payee)
                {
                    MessageBox.Show("Cette facture est déjà entièrement payée, il n'est pas nécessaire d'envoyer un message de relance.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Vérifier si les services nécessaires sont disponibles
                if (_communicationService == null || _contactClientService == null || _serviceProvider == null)
                {
                    MessageBox.Show("Les services nécessaires pour envoyer un message WhatsApp ne sont pas disponibles.",
                        "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Ouvrir le formulaire d'envoi de message WhatsApp
                using (var form = new Communications.WhatsAppForm(
                    _communicationService,
                    _contactClientService,
                    _factureService,
                    _clientService,
                    _currentUserId,
                    _factureId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les données
                        LoadFactureDataAsync().Wait();
                        MessageBox.Show("Le message WhatsApp a été envoyé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'envoi du message WhatsApp");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
