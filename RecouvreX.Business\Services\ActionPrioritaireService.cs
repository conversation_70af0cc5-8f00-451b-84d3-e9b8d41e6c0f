using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des actions prioritaires
    /// </summary>
    public class ActionPrioritaireService : IActionPrioritaireService
    {
        private readonly IActionPrioritaireRepository _actionPrioritaireRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IFactureRepository _factureRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="actionPrioritaireRepository">Repository des actions prioritaires</param>
        /// <param name="clientRepository">Repository des clients</param>
        /// <param name="factureRepository">Repository des factures</param>
        /// <param name="utilisateurRepository">Repository des utilisateurs</param>
        /// <param name="journalAuditRepository">Repository du journal d'audit</param>
        public ActionPrioritaireService(
            IActionPrioritaireRepository actionPrioritaireRepository,
            IClientRepository clientRepository,
            IFactureRepository factureRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _actionPrioritaireRepository = actionPrioritaireRepository ?? throw new ArgumentNullException(nameof(actionPrioritaireRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère toutes les actions prioritaires
        /// </summary>
        /// <returns>Liste des actions prioritaires</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetAllAsync()
        {
            return await _actionPrioritaireRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère une action prioritaire par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire</param>
        /// <returns>Action prioritaire trouvée ou null</returns>
        public async Task<ActionPrioritaire> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _actionPrioritaireRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère les actions prioritaires d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des actions prioritaires du client</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetByClientIdAsync(int clientId)
        {
            if (clientId <= 0)
                return new List<ActionPrioritaire>();

            return await _actionPrioritaireRepository.GetByClientIdAsync(clientId);
        }

        /// <summary>
        /// Récupère les actions prioritaires liées à une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des actions prioritaires liées à la facture</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetByFactureIdAsync(int factureId)
        {
            if (factureId <= 0)
                return new List<ActionPrioritaire>();

            return await _actionPrioritaireRepository.GetByFactureIdAsync(factureId);
        }

        /// <summary>
        /// Récupère les actions prioritaires assignées à un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des actions prioritaires assignées à l'utilisateur</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetByUtilisateurAssigneIdAsync(int utilisateurId)
        {
            if (utilisateurId <= 0)
                return new List<ActionPrioritaire>();

            return await _actionPrioritaireRepository.GetByUtilisateurAssigneIdAsync(utilisateurId);
        }

        /// <summary>
        /// Récupère les actions prioritaires par niveau de priorité
        /// </summary>
        /// <param name="niveauPriorite">Niveau de priorité</param>
        /// <returns>Liste des actions prioritaires du niveau spécifié</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetByNiveauPrioriteAsync(NiveauPriorite niveauPriorite)
        {
            return await _actionPrioritaireRepository.GetByNiveauPrioriteAsync(niveauPriorite);
        }

        /// <summary>
        /// Récupère les actions prioritaires par type d'action
        /// </summary>
        /// <param name="typeAction">Type d'action</param>
        /// <returns>Liste des actions prioritaires du type spécifié</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetByTypeActionAsync(TypeActionPrioritaire typeAction)
        {
            return await _actionPrioritaireRepository.GetByTypeActionAsync(typeAction);
        }

        /// <summary>
        /// Récupère les actions prioritaires à échéance dans un nombre de jours spécifié
        /// </summary>
        /// <param name="jours">Nombre de jours</param>
        /// <returns>Liste des actions prioritaires à échéance dans le nombre de jours spécifié</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetByEcheanceAsync(int jours)
        {
            return await _actionPrioritaireRepository.GetByEcheanceAsync(jours);
        }

        /// <summary>
        /// Récupère les actions prioritaires en retard (échéance dépassée et non complétées)
        /// </summary>
        /// <returns>Liste des actions prioritaires en retard</returns>
        public async Task<IEnumerable<ActionPrioritaire>> GetEnRetardAsync()
        {
            return await _actionPrioritaireRepository.GetEnRetardAsync();
        }

        /// <summary>
        /// Crée une nouvelle action prioritaire
        /// </summary>
        /// <param name="actionPrioritaire">Action prioritaire à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire créée avec son identifiant généré</returns>
        public async Task<ActionPrioritaire> CreateAsync(ActionPrioritaire actionPrioritaire, int creePar)
        {
            if (actionPrioritaire == null || creePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la création d'une action prioritaire");

            // Vérifier si le client existe
            var client = await _clientRepository.GetByIdAsync(actionPrioritaire.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {actionPrioritaire.ClientId} n'existe pas");

            // Vérifier si la facture existe (si spécifiée)
            if (actionPrioritaire.FactureId.HasValue && actionPrioritaire.FactureId.Value > 0)
            {
                var facture = await _factureRepository.GetByIdAsync(actionPrioritaire.FactureId.Value);
                if (facture == null)
                    throw new InvalidOperationException($"La facture avec l'ID {actionPrioritaire.FactureId.Value} n'existe pas");
            }

            // Vérifier si l'utilisateur assigné existe (si spécifié)
            if (actionPrioritaire.UtilisateurAssigneId.HasValue && actionPrioritaire.UtilisateurAssigneId.Value > 0)
            {
                var utilisateur = await _utilisateurRepository.GetByIdAsync(actionPrioritaire.UtilisateurAssigneId.Value);
                if (utilisateur == null)
                    throw new InvalidOperationException($"L'utilisateur avec l'ID {actionPrioritaire.UtilisateurAssigneId.Value} n'existe pas");
            }

            // Créer l'action prioritaire
            var createdAction = await _actionPrioritaireRepository.AddAsync(actionPrioritaire, creePar);

            // Journaliser la création
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = creePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(creePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Creation,
                TypeEntite = "ActionPrioritaire",
                EntiteId = createdAction.Id,
                Description = $"Création d'une action prioritaire pour le client {client.RaisonSociale}",
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdAction.Id,
                    createdAction.ClientId,
                    createdAction.FactureId,
                    createdAction.TypeAction,
                    createdAction.NiveauPriorite,
                    createdAction.Description,
                    createdAction.DateEcheance,
                    createdAction.UtilisateurAssigneId,
                    createdAction.ScorePriorite,
                    createdAction.EstAutomatique
                })
            });

            return createdAction;
        }

        /// <summary>
        /// Met à jour une action prioritaire existante
        /// </summary>
        /// <param name="actionPrioritaire">Action prioritaire à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire mise à jour</returns>
        public async Task<ActionPrioritaire> UpdateAsync(ActionPrioritaire actionPrioritaire, int modifiePar)
        {
            if (actionPrioritaire == null || actionPrioritaire.Id <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la mise à jour d'une action prioritaire");

            // Récupérer l'action prioritaire existante
            var existingAction = await _actionPrioritaireRepository.GetByIdAsync(actionPrioritaire.Id);
            if (existingAction == null)
                throw new InvalidOperationException($"L'action prioritaire avec l'ID {actionPrioritaire.Id} n'existe pas");

            // Vérifier si le client existe
            var client = await _clientRepository.GetByIdAsync(actionPrioritaire.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {actionPrioritaire.ClientId} n'existe pas");

            // Vérifier si la facture existe (si spécifiée)
            if (actionPrioritaire.FactureId.HasValue && actionPrioritaire.FactureId.Value > 0)
            {
                var facture = await _factureRepository.GetByIdAsync(actionPrioritaire.FactureId.Value);
                if (facture == null)
                    throw new InvalidOperationException($"La facture avec l'ID {actionPrioritaire.FactureId.Value} n'existe pas");
            }

            // Vérifier si l'utilisateur assigné existe (si spécifié)
            if (actionPrioritaire.UtilisateurAssigneId.HasValue && actionPrioritaire.UtilisateurAssigneId.Value > 0)
            {
                var utilisateur = await _utilisateurRepository.GetByIdAsync(actionPrioritaire.UtilisateurAssigneId.Value);
                if (utilisateur == null)
                    throw new InvalidOperationException($"L'utilisateur avec l'ID {actionPrioritaire.UtilisateurAssigneId.Value} n'existe pas");
            }

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "ActionPrioritaire",
                EntiteId = actionPrioritaire.Id,
                Description = $"Modification d'une action prioritaire pour le client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingAction.Id,
                    existingAction.ClientId,
                    existingAction.FactureId,
                    existingAction.TypeAction,
                    existingAction.NiveauPriorite,
                    existingAction.Description,
                    existingAction.DateEcheance,
                    existingAction.UtilisateurAssigneId,
                    existingAction.EstCompletee,
                    existingAction.DateCompletion,
                    existingAction.CommentaireCompletion,
                    existingAction.ScorePriorite,
                    existingAction.EstAutomatique
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    actionPrioritaire.Id,
                    actionPrioritaire.ClientId,
                    actionPrioritaire.FactureId,
                    actionPrioritaire.TypeAction,
                    actionPrioritaire.NiveauPriorite,
                    actionPrioritaire.Description,
                    actionPrioritaire.DateEcheance,
                    actionPrioritaire.UtilisateurAssigneId,
                    actionPrioritaire.EstCompletee,
                    actionPrioritaire.DateCompletion,
                    actionPrioritaire.CommentaireCompletion,
                    actionPrioritaire.ScorePriorite,
                    actionPrioritaire.EstAutomatique
                })
            });

            // Mettre à jour l'action prioritaire
            return await _actionPrioritaireRepository.UpdateAsync(actionPrioritaire, modifiePar);
        }

        /// <summary>
        /// Supprime une action prioritaire
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0 || supprimePar <= 0)
                return false;

            // Récupérer l'action prioritaire à supprimer
            var actionPrioritaire = await _actionPrioritaireRepository.GetByIdAsync(id);
            if (actionPrioritaire == null)
                return false;

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(actionPrioritaire.ClientId);
            if (client == null)
                return false;

            // Journaliser la suppression
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = supprimePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(supprimePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Suppression,
                TypeEntite = "ActionPrioritaire",
                EntiteId = id,
                Description = $"Suppression d'une action prioritaire pour le client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    actionPrioritaire.Id,
                    actionPrioritaire.ClientId,
                    actionPrioritaire.FactureId,
                    actionPrioritaire.TypeAction,
                    actionPrioritaire.NiveauPriorite,
                    actionPrioritaire.Description,
                    actionPrioritaire.DateEcheance,
                    actionPrioritaire.UtilisateurAssigneId,
                    actionPrioritaire.EstCompletee,
                    actionPrioritaire.DateCompletion,
                    actionPrioritaire.CommentaireCompletion,
                    actionPrioritaire.ScorePriorite,
                    actionPrioritaire.EstAutomatique
                })
            });

            // Supprimer l'action prioritaire
            return await _actionPrioritaireRepository.DeleteAsync(id, supprimePar);
        }

        /// <summary>
        /// Marque une action prioritaire comme complétée
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire</param>
        /// <param name="commentaire">Commentaire sur la complétion</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire mise à jour</returns>
        public async Task<ActionPrioritaire> MarquerCompleteAsync(int id, string commentaire, int modifiePar)
        {
            if (id <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour marquer une action prioritaire comme complétée");

            // Récupérer l'action prioritaire existante
            var existingAction = await _actionPrioritaireRepository.GetByIdAsync(id);
            if (existingAction == null)
                throw new InvalidOperationException($"L'action prioritaire avec l'ID {id} n'existe pas");

            // Vérifier si l'action est déjà complétée
            if (existingAction.EstCompletee)
                throw new InvalidOperationException($"L'action prioritaire avec l'ID {id} est déjà marquée comme complétée");

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(existingAction.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {existingAction.ClientId} n'existe pas");

            // Journaliser la modification
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "ActionPrioritaire",
                EntiteId = id,
                Description = $"Action prioritaire marquée comme complétée pour le client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingAction.Id,
                    existingAction.EstCompletee,
                    existingAction.DateCompletion,
                    existingAction.CommentaireCompletion
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingAction.Id,
                    EstCompletee = true,
                    DateCompletion = DateTime.Now,
                    CommentaireCompletion = commentaire
                })
            });

            // Marquer l'action comme complétée
            return await _actionPrioritaireRepository.MarquerCompleteAsync(id, commentaire, modifiePar);
        }

        /// <summary>
        /// Assigne une action prioritaire à un utilisateur
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire mise à jour</returns>
        public async Task<ActionPrioritaire> AssignerAsync(int id, int utilisateurId, int modifiePar)
        {
            if (id <= 0 || utilisateurId <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour assigner une action prioritaire");

            // Récupérer l'action prioritaire existante
            var existingAction = await _actionPrioritaireRepository.GetByIdAsync(id);
            if (existingAction == null)
                throw new InvalidOperationException($"L'action prioritaire avec l'ID {id} n'existe pas");

            // Vérifier si l'utilisateur existe
            var utilisateur = await _utilisateurRepository.GetByIdAsync(utilisateurId);
            if (utilisateur == null)
                throw new InvalidOperationException($"L'utilisateur avec l'ID {utilisateurId} n'existe pas");

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(existingAction.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {existingAction.ClientId} n'existe pas");

            // Journaliser la modification
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "ActionPrioritaire",
                EntiteId = id,
                Description = $"Action prioritaire assignée à {utilisateur.NomComplet} pour le client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingAction.Id,
                    existingAction.UtilisateurAssigneId
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingAction.Id,
                    UtilisateurAssigneId = utilisateurId
                })
            });

            // Assigner l'action à l'utilisateur
            return await _actionPrioritaireRepository.AssignerAsync(id, utilisateurId, modifiePar);
        }

        /// <summary>
        /// Génère automatiquement des actions prioritaires pour tous les clients
        /// </summary>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre d'actions prioritaires créées</returns>
        public async Task<int> GenererActionsPrioritairesAsync(int creePar)
        {
            if (creePar <= 0)
                throw new ArgumentException("Paramètre utilisateurId invalide");

            int count = 0;

            try
            {
                // Récupérer tous les clients
                var clients = await _clientRepository.GetAllAsync();

                // Récupérer toutes les factures en retard
                var facturesEnRetard = await _factureRepository.GetOverdueInvoicesAsync();

                // Récupérer les actions prioritaires existantes non complétées
                var actionsExistantes = (await _actionPrioritaireRepository.GetAllAsync())
                    .Where(a => !a.EstCompletee)
                    .ToList();

                // Pour chaque client, générer des actions prioritaires en fonction de son segment et de ses factures en retard
                foreach (var client in clients)
                {
                    try
                    {
                        // Récupérer les factures en retard du client
                        var facturesClientEnRetard = facturesEnRetard.Where(f => f.ClientId == client.Id).ToList();

                        if (!facturesClientEnRetard.Any())
                            continue; // Passer au client suivant s'il n'a pas de factures en retard

                        // Calculer le montant total des factures en retard
                        decimal montantTotalEnRetard = facturesClientEnRetard.Sum(f => f.MontantRestantDu);

                        // Déterminer le niveau de priorité en fonction du segment du client et du montant en retard
                        NiveauPriorite niveauPriorite = DeterminerNiveauPriorite(client.Segment, montantTotalEnRetard, facturesClientEnRetard.Count);

                        // Déterminer le type d'action en fonction du segment et du niveau de priorité
                        TypeActionPrioritaire typeAction = DeterminerTypeAction(client.Segment, niveauPriorite, facturesClientEnRetard);

                        // Vérifier si une action similaire existe déjà pour ce client
                        bool actionSimilaireExiste = actionsExistantes.Any(a =>
                            a.ClientId == client.Id &&
                            a.TypeAction == typeAction &&
                            !a.EstCompletee);

                        if (actionSimilaireExiste)
                            continue; // Passer au client suivant si une action similaire existe déjà

                        // Créer une description pour l'action
                        string description = GenererDescriptionAction(client, typeAction, facturesClientEnRetard);

                        // Calculer le score de priorité
                        int scorePriorite = CalculerScorePriorite(client.Segment, niveauPriorite, montantTotalEnRetard, facturesClientEnRetard.Count);

                        // Déterminer la date d'échéance en fonction du niveau de priorité
                        DateTime dateEcheance = DeterminerDateEcheance(niveauPriorite);

                        // Créer l'action prioritaire
                        var actionPrioritaire = new ActionPrioritaire
                        {
                            ClientId = client.Id,
                            FactureId = facturesClientEnRetard.OrderByDescending(f => f.MontantRestantDu).FirstOrDefault()?.Id,
                            TypeAction = typeAction,
                            NiveauPriorite = niveauPriorite,
                            Description = description,
                            DateEcheance = dateEcheance,
                            ScorePriorite = scorePriorite,
                            EstAutomatique = true
                        };

                        // Enregistrer l'action prioritaire
                        await _actionPrioritaireRepository.AddAsync(actionPrioritaire, creePar);
                        count++;

                        // Journaliser la création
                        await _journalAuditRepository.AddAsync(new JournalAudit
                        {
                            DateAction = DateTime.Now,
                            UtilisateurId = creePar,
                            NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(creePar))?.NomUtilisateur ?? "Système",
                            TypeAction = TypeAudit.Creation,
                            TypeEntite = "ActionPrioritaire",
                            EntiteId = actionPrioritaire.Id,
                            Description = $"Création automatique d'une action prioritaire pour le client {client.RaisonSociale}",
                            DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                            {
                                actionPrioritaire.Id,
                                actionPrioritaire.ClientId,
                                actionPrioritaire.FactureId,
                                actionPrioritaire.TypeAction,
                                actionPrioritaire.NiveauPriorite,
                                actionPrioritaire.Description,
                                actionPrioritaire.DateEcheance,
                                actionPrioritaire.ScorePriorite,
                                actionPrioritaire.EstAutomatique
                            })
                        });
                    }
                    catch (Exception ex)
                    {
                        // Journaliser l'erreur mais continuer avec les autres clients
                        Log.Error(ex, "Erreur lors de la génération d'actions prioritaires pour le client {ClientId}", client.Id);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération d'actions prioritaires");
                throw;
            }

            return count;
        }

        /// <summary>
        /// Récupère le nombre d'actions prioritaires par niveau de priorité
        /// </summary>
        /// <returns>Dictionnaire avec le niveau de priorité comme clé et le nombre d'actions comme valeur</returns>
        public async Task<Dictionary<NiveauPriorite, int>> GetCountByNiveauPrioriteAsync()
        {
            return await _actionPrioritaireRepository.GetCountByNiveauPrioriteAsync();
        }

        /// <summary>
        /// Alias pour GenererActionsPrioritairesAsync
        /// </summary>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre d'actions prioritaires créées</returns>
        public async Task<int> GenerateActionsAsync(int creePar)
        {
            return await GenererActionsPrioritairesAsync(creePar);
        }

        /// <summary>
        /// Alias pour MarquerCompleteAsync
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire mise à jour</returns>
        public async Task<ActionPrioritaire> CompleteActionAsync(int id, int modifiePar)
        {
            return await MarquerCompleteAsync(id, "Marquée comme complétée", modifiePar);
        }

        /// <summary>
        /// Alias pour GetCountByNiveauPrioriteAsync
        /// </summary>
        /// <returns>Dictionnaire avec le niveau de priorité comme clé et le nombre d'actions comme valeur</returns>
        public async Task<Dictionary<NiveauPriorite, int>> GetDistributionByPrioriteAsync()
        {
            return await GetCountByNiveauPrioriteAsync();
        }

        #region Méthodes privées

        /// <summary>
        /// Détermine le niveau de priorité en fonction du segment du client et du montant en retard
        /// </summary>
        private NiveauPriorite DeterminerNiveauPriorite(SegmentClient segment, decimal montantTotalEnRetard, int nombreFacturesEnRetard)
        {
            // Déterminer le niveau de priorité en fonction du segment du client
            switch (segment)
            {
                case SegmentClient.A: // Clients stratégiques
                    if (montantTotalEnRetard > 10000)
                        return NiveauPriorite.Critique;
                    else if (montantTotalEnRetard > 5000)
                        return NiveauPriorite.Haute;
                    else if (montantTotalEnRetard > 1000)
                        return NiveauPriorite.Moyenne;
                    else
                        return NiveauPriorite.Basse;

                case SegmentClient.B: // Clients importants
                    if (montantTotalEnRetard > 20000)
                        return NiveauPriorite.Critique;
                    else if (montantTotalEnRetard > 10000)
                        return NiveauPriorite.Haute;
                    else if (montantTotalEnRetard > 2000)
                        return NiveauPriorite.Moyenne;
                    else
                        return NiveauPriorite.Basse;

                case SegmentClient.C: // Clients standards
                    if (montantTotalEnRetard > 30000)
                        return NiveauPriorite.Critique;
                    else if (montantTotalEnRetard > 15000)
                        return NiveauPriorite.Haute;
                    else if (montantTotalEnRetard > 5000)
                        return NiveauPriorite.Moyenne;
                    else
                        return NiveauPriorite.Basse;

                default: // Non segmenté ou autre
                    if (montantTotalEnRetard > 20000)
                        return NiveauPriorite.Haute;
                    else if (montantTotalEnRetard > 10000)
                        return NiveauPriorite.Moyenne;
                    else
                        return NiveauPriorite.Basse;
            }
        }


        /// <summary>
        /// Détermine le type d'action en fonction du segment client, du niveau de priorité et du retard des factures
        /// </summary>
        private TypeActionPrioritaire DeterminerTypeAction(SegmentClient segment, NiveauPriorite niveauPriorite, List<Facture> facturesEnRetard)
        {
            // Calculer le retard moyen des factures en jours
            int retardMoyenJours = 0;
            if (facturesEnRetard.Any())
            {
                retardMoyenJours = (int)facturesEnRetard.Average(f => (DateTime.Now - f.DateEcheance).TotalDays);
            }

            // Calculer le montant total en retard
            decimal montantTotalEnRetard = facturesEnRetard.Sum(f => f.MontantRestantDu);

            // Déterminer le type d'action en fonction du segment client, du niveau de priorité et du retard moyen
            switch (segment)
            {
                case SegmentClient.A: // Clients stratégiques - approche personnalisée et proactive
                    if (niveauPriorite == NiveauPriorite.Critique)
                    {
                        if (retardMoyenJours > 60)
                            return TypeActionPrioritaire.Escalade;
                        else
                            return TypeActionPrioritaire.RelanceTelephonique;
                    }
                    else if (niveauPriorite == NiveauPriorite.Haute)
                    {
                        if (retardMoyenJours > 30)
                            return TypeActionPrioritaire.RelanceTelephonique;
                        else
                            return TypeActionPrioritaire.RelanceEmail;
                    }
                    else
                    {
                        return TypeActionPrioritaire.RelanceEmail;
                    }

                case SegmentClient.B: // Clients importants - approche équilibrée
                    if (niveauPriorite == NiveauPriorite.Critique)
                    {
                        if (retardMoyenJours > 90)
                            return TypeActionPrioritaire.Escalade;
                        else if (retardMoyenJours > 45)
                            return TypeActionPrioritaire.RelanceTelephonique;
                        else
                            return TypeActionPrioritaire.RelanceEmail;
                    }
                    else if (niveauPriorite == NiveauPriorite.Haute)
                    {
                        if (retardMoyenJours > 45)
                            return TypeActionPrioritaire.RelanceTelephonique;
                        else
                            return TypeActionPrioritaire.RelanceEmail;
                    }
                    else
                    {
                        return TypeActionPrioritaire.RelanceEmail;
                    }

                case SegmentClient.C: // Clients standards - approche standardisée
                    if (niveauPriorite == NiveauPriorite.Critique)
                    {
                        if (retardMoyenJours > 90)
                            return TypeActionPrioritaire.RelanceCourrier;
                        else if (retardMoyenJours > 60)
                            return TypeActionPrioritaire.RelanceTelephonique;
                        else
                            return TypeActionPrioritaire.RelanceEmail;
                    }
                    else
                    {
                        return TypeActionPrioritaire.RelanceEmail;
                    }

                case SegmentClient.NonSegmente: // Clients non segmentés - approche automatisée
                    if (niveauPriorite == NiveauPriorite.Critique && montantTotalEnRetard > 5000)
                    {
                        if (retardMoyenJours > 90)
                            return TypeActionPrioritaire.RelanceCourrier;
                        else
                            return TypeActionPrioritaire.RelanceEmail;
                    }
                    else
                    {
                        return TypeActionPrioritaire.RelanceEmail;
                    }

                default: // Approche par défaut basée sur le niveau de priorité
                    switch (niveauPriorite)
                    {
                        case NiveauPriorite.Critique:
                            if (retardMoyenJours > 90)
                                return TypeActionPrioritaire.ProcedureJuridique;
                            else if (retardMoyenJours > 60)
                                return TypeActionPrioritaire.Escalade;
                            else
                                return TypeActionPrioritaire.RelanceTelephonique;

                        case NiveauPriorite.Haute:
                            if (retardMoyenJours > 60)
                                return TypeActionPrioritaire.RelanceCourrier;
                            else if (retardMoyenJours > 30)
                                return TypeActionPrioritaire.RelanceTelephonique;
                            else
                                return TypeActionPrioritaire.RelanceEmail;

                        case NiveauPriorite.Moyenne:
                            if (retardMoyenJours > 45)
                                return TypeActionPrioritaire.RelanceTelephonique;
                            else
                                return TypeActionPrioritaire.RelanceEmail;

                        case NiveauPriorite.Basse:
                            return TypeActionPrioritaire.RelanceEmail;

                        default:
                            return TypeActionPrioritaire.RelanceEmail;
                    }
            }
        }

        /// <summary>
        /// Génère une description pour l'action prioritaire
        /// </summary>
        private string GenererDescriptionAction(Client client, TypeActionPrioritaire typeAction, List<Facture> facturesEnRetard)
        {
            string description = string.Empty;
            decimal montantTotal = facturesEnRetard.Sum(f => f.MontantRestantDu);
            int nombreFactures = facturesEnRetard.Count;

            switch (typeAction)
            {
                case TypeActionPrioritaire.RelanceTelephonique:
                    description = $"Appeler le client {client.RaisonSociale} concernant {nombreFactures} facture(s) en retard pour un montant total de {montantTotal:C2}.";
                    break;

                case TypeActionPrioritaire.RelanceEmail:
                    description = $"Envoyer un email de relance au client {client.RaisonSociale} concernant {nombreFactures} facture(s) en retard pour un montant total de {montantTotal:C2}.";
                    break;

                case TypeActionPrioritaire.RelanceCourrier:
                    description = $"Envoyer une lettre de relance au client {client.RaisonSociale} concernant {nombreFactures} facture(s) en retard pour un montant total de {montantTotal:C2}.";
                    break;

                case TypeActionPrioritaire.NegociationPlanPaiement:
                    description = $"Négocier un plan de paiement avec le client {client.RaisonSociale} pour {nombreFactures} facture(s) en retard pour un montant total de {montantTotal:C2}.";
                    break;

                case TypeActionPrioritaire.Escalade:
                    description = $"Escalader le dossier du client {client.RaisonSociale} à un niveau supérieur. {nombreFactures} facture(s) en retard pour un montant total de {montantTotal:C2}.";
                    break;

                case TypeActionPrioritaire.ProcedureJuridique:
                    description = $"Initier une procédure juridique contre le client {client.RaisonSociale} pour {nombreFactures} facture(s) en retard pour un montant total de {montantTotal:C2}.";
                    break;

                default:
                    description = $"Action à effectuer pour le client {client.RaisonSociale} concernant {nombreFactures} facture(s) en retard pour un montant total de {montantTotal:C2}.";
                    break;
            }

            return description;
        }

        /// <summary>
        /// Calcule le score de priorité en utilisant un algorithme amélioré
        /// </summary>
        private int CalculerScorePriorite(SegmentClient segment, NiveauPriorite niveauPriorite, decimal montantTotalEnRetard, int nombreFacturesEnRetard)
        {
            int score = 0;

            // Points pour le segment (pondération: 30%)
            switch (segment)
            {
                case SegmentClient.A: // Clients stratégiques
                    score += 30;
                    break;
                case SegmentClient.B: // Clients importants
                    score += 20;
                    break;
                case SegmentClient.C: // Clients standards
                    score += 10;
                    break;
                case SegmentClient.NonSegmente: // Clients non segmentés
                    score += 5;
                    break;
                default:
                    score += 5;
                    break;
            }

            // Points pour le niveau de priorité (pondération: 40%)
            switch (niveauPriorite)
            {
                case NiveauPriorite.Critique:
                    score += 40;
                    break;
                case NiveauPriorite.Haute:
                    score += 30;
                    break;
                case NiveauPriorite.Moyenne:
                    score += 20;
                    break;
                case NiveauPriorite.Basse:
                    score += 10;
                    break;
            }

            // Points pour le montant total en retard (pondération: 20%)
            if (montantTotalEnRetard > 50000)
                score += 20;
            else if (montantTotalEnRetard > 20000)
                score += 15;
            else if (montantTotalEnRetard > 5000)
                score += 10;
            else if (montantTotalEnRetard > 1000)
                score += 5;
            else
                score += 2;

            // Points pour le nombre de factures en retard (pondération: 10%)
            if (nombreFacturesEnRetard > 10)
                score += 10;
            else if (nombreFacturesEnRetard > 5)
                score += 8;
            else if (nombreFacturesEnRetard > 2)
                score += 5;
            else if (nombreFacturesEnRetard > 1)
                score += 3;
            else
                score += 1;

            // Facteur d'urgence basé sur la date d'échéance (pondération: 10%)
            // Plus la date d'échéance est proche, plus le score est élevé
            DateTime now = DateTime.Now;
            DateTime echeance = DeterminerDateEcheance(niveauPriorite);
            int joursRestants = (int)(echeance - now).TotalDays;

            if (joursRestants <= 1)
                score += 10; // Très urgent (aujourd'hui ou demain)
            else if (joursRestants <= 3)
                score += 8; // Urgent (dans les 3 jours)
            else if (joursRestants <= 7)
                score += 5; // Moyennement urgent (dans la semaine)
            else
                score += 2; // Peu urgent (plus d'une semaine)

            // Normaliser le score sur une échelle de 0 à 100
            score = Math.Min(100, score);

            return score;
        }

        /// <summary>
        /// Détermine la date d'échéance en fonction du niveau de priorité
        /// </summary>
        private DateTime DeterminerDateEcheance(NiveauPriorite niveauPriorite)
        {
            DateTime now = DateTime.Now;

            switch (niveauPriorite)
            {
                case NiveauPriorite.Critique:
                    return now.AddDays(1); // Échéance à J+1
                case NiveauPriorite.Haute:
                    return now.AddDays(3); // Échéance à J+3
                case NiveauPriorite.Moyenne:
                    return now.AddDays(7); // Échéance à J+7
                case NiveauPriorite.Basse:
                    return now.AddDays(14); // Échéance à J+14
                default:
                    return now.AddDays(7);
            }
        }

        #endregion
    }
}
