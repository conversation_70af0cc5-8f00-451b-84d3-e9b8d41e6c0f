namespace RecouvreX.Models.Enums
{
    /// <summary>
    /// Niveaux de priorité
    /// </summary>
    public enum NiveauPriorite
    {
        /// <summary>
        /// Priorité basse
        /// </summary>
        Basse = 1,

        /// <summary>
        /// Priorité moyenne
        /// </summary>
        Moyenne = 2,

        /// <summary>
        /// Priorité haute
        /// </summary>
        Haute = 3,

        /// <summary>
        /// Priorité critique
        /// </summary>
        Critique = 4
    }
}
