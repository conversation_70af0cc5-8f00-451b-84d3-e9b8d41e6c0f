using RecouvreX.Models.Reporting;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des rapports
    /// </summary>
    public interface IRapportService
    {
        /// <summary>
        /// Récupère tous les rapports
        /// </summary>
        /// <returns>Liste des rapports</returns>
        Task<IEnumerable<Rapport>> GetAllAsync();

        /// <summary>
        /// Récupère un rapport par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du rapport</param>
        /// <returns>Rapport trouvé ou null</returns>
        Task<Rapport> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les rapports par type
        /// </summary>
        /// <param name="type">Type de rapport</param>
        /// <returns>Liste des rapports du type spécifié</returns>
        Task<IEnumerable<Rapport>> GetByTypeAsync(string type);

        /// <summary>
        /// Récupère les rapports par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des rapports créés par l'utilisateur spécifié</returns>
        Task<IEnumerable<Rapport>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les rapports par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des rapports pour la période spécifiée</returns>
        Task<IEnumerable<Rapport>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère les rapports programmés à générer
        /// </summary>
        /// <param name="dateReference">Date de référence (par défaut, la date actuelle)</param>
        /// <returns>Liste des rapports programmés à générer</returns>
        Task<IEnumerable<Rapport>> GetScheduledReportsAsync(DateTime? dateReference = null);

        /// <summary>
        /// Crée un nouveau rapport
        /// </summary>
        /// <param name="rapport">Rapport à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Rapport créé avec son identifiant généré</returns>
        Task<Rapport> CreateAsync(Rapport rapport, int creePar);

        /// <summary>
        /// Met à jour un rapport existant
        /// </summary>
        /// <param name="rapport">Rapport à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Rapport mis à jour</returns>
        Task<Rapport> UpdateAsync(Rapport rapport, int modifiePar);

        /// <summary>
        /// Supprime un rapport
        /// </summary>
        /// <param name="id">Identifiant du rapport à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Programme un rapport pour génération périodique
        /// </summary>
        /// <param name="id">Identifiant du rapport</param>
        /// <param name="estProgramme">Indique si le rapport est programmé</param>
        /// <param name="frequence">Fréquence de génération</param>
        /// <param name="dateProchainRapport">Date de prochaine génération</param>
        /// <param name="destinataires">Liste des destinataires (au format JSON)</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> ScheduleReportAsync(int id, bool estProgramme, string frequence, DateTime? dateProchainRapport, string destinataires, int modifiePar);

        /// <summary>
        /// Met à jour la date de prochaine génération d'un rapport programmé
        /// </summary>
        /// <param name="id">Identifiant du rapport</param>
        /// <param name="dateProchainRapport">Date de prochaine génération</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateNextGenerationDateAsync(int id, DateTime dateProchainRapport, int modifiePar);

        /// <summary>
        /// Génère un rapport
        /// </summary>
        /// <param name="id">Identifiant du rapport</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui génère le rapport</param>
        /// <returns>Chemin vers le fichier du rapport généré</returns>
        Task<string> GenerateReportAsync(int id, int utilisateurId);

        /// <summary>
        /// Exécute les rapports programmés
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui exécute les rapports</param>
        /// <returns>Nombre de rapports générés</returns>
        Task<int> ExecuteScheduledReportsAsync(int utilisateurId);

        /// <summary>
        /// Envoie un rapport par email
        /// </summary>
        /// <param name="id">Identifiant du rapport</param>
        /// <param name="destinataires">Liste des adresses email des destinataires</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui envoie le rapport</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        Task<bool> SendReportByEmailAsync(int id, List<string> destinataires, int utilisateurId);
    }
}
