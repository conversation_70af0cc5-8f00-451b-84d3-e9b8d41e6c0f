using System.Collections.Generic;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un rôle d'utilisateur dans le système
    /// </summary>
    public class Role : BaseEntity
    {
        /// <summary>
        /// Nom du rôle
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Description du rôle
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Liste des utilisateurs ayant ce rôle (navigation property)
        /// </summary>
        public ICollection<Utilisateur> Utilisateurs { get; set; }

        /// <summary>
        /// Liste des permissions associées à ce rôle (navigation property)
        /// </summary>
        public ICollection<RolePermission> RolePermissions { get; set; }
    }
}
