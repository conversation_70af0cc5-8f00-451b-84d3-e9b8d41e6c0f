namespace RecouvreX.WinForms.Forms.Alertes
{
    partial class AlerteEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            mainPanel = new TableLayoutPanel();
            nomLabel = new Label();
            nomTextBox = new TextBox();
            typeLabel = new Label();
            typeComboBox = new ComboBox();
            descriptionLabel = new Label();
            descriptionTextBox = new TextBox();
            conditionLabel = new Label();
            conditionComboBox = new ComboBox();
            seuilLabel = new Label();
            seuilNumericUpDown = new NumericUpDown();
            frequenceLabel = new Label();
            frequenceNumericUpDown = new NumericUpDown();
            estActiveLabel = new Label();
            estActiveCheckBox = new CheckBox();
            envoyerEmailLabel = new Label();
            envoyerEmailCheckBox = new CheckBox();
            buttonsPanel = new FlowLayoutPanel();
            cancelButton = new Button();
            saveButton = new Button();
            mainPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)seuilNumericUpDown).BeginInit();
            ((System.ComponentModel.ISupportInitialize)frequenceNumericUpDown).BeginInit();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.ColumnCount = 2;
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            mainPanel.Controls.Add(nomLabel, 0, 0);
            mainPanel.Controls.Add(nomTextBox, 1, 0);
            mainPanel.Controls.Add(typeLabel, 0, 1);
            mainPanel.Controls.Add(typeComboBox, 1, 1);
            mainPanel.Controls.Add(descriptionLabel, 0, 2);
            mainPanel.Controls.Add(descriptionTextBox, 1, 2);
            mainPanel.Controls.Add(conditionLabel, 0, 3);
            mainPanel.Controls.Add(conditionComboBox, 1, 3);
            mainPanel.Controls.Add(seuilLabel, 0, 4);
            mainPanel.Controls.Add(seuilNumericUpDown, 1, 4);
            mainPanel.Controls.Add(frequenceLabel, 0, 5);
            mainPanel.Controls.Add(frequenceNumericUpDown, 1, 5);
            mainPanel.Controls.Add(estActiveLabel, 0, 6);
            mainPanel.Controls.Add(estActiveCheckBox, 1, 6);
            mainPanel.Controls.Add(envoyerEmailLabel, 0, 7);
            mainPanel.Controls.Add(envoyerEmailCheckBox, 1, 7);
            mainPanel.Controls.Add(buttonsPanel, 1, 8);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Location = new Point(0, 0);
            mainPanel.Name = "mainPanel";
            mainPanel.Padding = new Padding(10);
            mainPanel.RowCount = 9;
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            mainPanel.Size = new Size(500, 400);
            mainPanel.TabIndex = 0;
            // 
            // nomLabel
            // 
            nomLabel.Dock = DockStyle.Fill;
            nomLabel.Location = new Point(13, 10);
            nomLabel.Name = "nomLabel";
            nomLabel.Size = new Size(138, 30);
            nomLabel.TabIndex = 0;
            nomLabel.Text = "Nom :";
            nomLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // nomTextBox
            // 
            nomTextBox.Dock = DockStyle.Fill;
            nomTextBox.Location = new Point(157, 13);
            nomTextBox.Name = "nomTextBox";
            nomTextBox.Size = new Size(330, 23);
            nomTextBox.TabIndex = 1;
            // 
            // typeLabel
            // 
            typeLabel.Dock = DockStyle.Fill;
            typeLabel.Location = new Point(13, 40);
            typeLabel.Name = "typeLabel";
            typeLabel.Size = new Size(138, 30);
            typeLabel.TabIndex = 2;
            typeLabel.Text = "Type :";
            typeLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // typeComboBox
            // 
            typeComboBox.Dock = DockStyle.Fill;
            typeComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            typeComboBox.Location = new Point(157, 43);
            typeComboBox.Name = "typeComboBox";
            typeComboBox.Size = new Size(330, 23);
            typeComboBox.TabIndex = 3;
            typeComboBox.SelectedIndexChanged += TypeComboBox_SelectedIndexChanged;
            // 
            // descriptionLabel
            // 
            descriptionLabel.Dock = DockStyle.Fill;
            descriptionLabel.Location = new Point(13, 70);
            descriptionLabel.Name = "descriptionLabel";
            descriptionLabel.Size = new Size(138, 60);
            descriptionLabel.TabIndex = 4;
            descriptionLabel.Text = "Description :";
            descriptionLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // descriptionTextBox
            // 
            descriptionTextBox.Dock = DockStyle.Fill;
            descriptionTextBox.Location = new Point(157, 73);
            descriptionTextBox.Multiline = true;
            descriptionTextBox.Name = "descriptionTextBox";
            descriptionTextBox.ScrollBars = ScrollBars.Vertical;
            descriptionTextBox.Size = new Size(330, 54);
            descriptionTextBox.TabIndex = 5;
            // 
            // conditionLabel
            // 
            conditionLabel.Dock = DockStyle.Fill;
            conditionLabel.Location = new Point(13, 130);
            conditionLabel.Name = "conditionLabel";
            conditionLabel.Size = new Size(138, 30);
            conditionLabel.TabIndex = 6;
            conditionLabel.Text = "Condition :";
            conditionLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // conditionComboBox
            // 
            conditionComboBox.Dock = DockStyle.Fill;
            conditionComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            conditionComboBox.Location = new Point(157, 133);
            conditionComboBox.Name = "conditionComboBox";
            conditionComboBox.Size = new Size(330, 23);
            conditionComboBox.TabIndex = 7;
            // 
            // seuilLabel
            // 
            seuilLabel.Dock = DockStyle.Fill;
            seuilLabel.Location = new Point(13, 160);
            seuilLabel.Name = "seuilLabel";
            seuilLabel.Size = new Size(138, 30);
            seuilLabel.TabIndex = 8;
            seuilLabel.Text = "Seuil :";
            seuilLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // seuilNumericUpDown
            // 
            seuilNumericUpDown.DecimalPlaces = 2;
            seuilNumericUpDown.Dock = DockStyle.Fill;
            seuilNumericUpDown.Increment = new decimal(new int[] { 100, 0, 0, 0 });
            seuilNumericUpDown.Location = new Point(157, 163);
            seuilNumericUpDown.Maximum = new decimal(new int[] { 1000000, 0, 0, 0 });
            seuilNumericUpDown.Name = "seuilNumericUpDown";
            seuilNumericUpDown.Size = new Size(330, 23);
            seuilNumericUpDown.TabIndex = 9;
            seuilNumericUpDown.ThousandsSeparator = true;
            seuilNumericUpDown.Value = new decimal(new int[] { 1000, 0, 0, 0 });
            // 
            // frequenceLabel
            // 
            frequenceLabel.Dock = DockStyle.Fill;
            frequenceLabel.Location = new Point(13, 190);
            frequenceLabel.Name = "frequenceLabel";
            frequenceLabel.Size = new Size(138, 30);
            frequenceLabel.TabIndex = 10;
            frequenceLabel.Text = "Fréquence (heures) :";
            frequenceLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // frequenceNumericUpDown
            // 
            frequenceNumericUpDown.Dock = DockStyle.Fill;
            frequenceNumericUpDown.Location = new Point(157, 193);
            frequenceNumericUpDown.Maximum = new decimal(new int[] { 168, 0, 0, 0 });
            frequenceNumericUpDown.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            frequenceNumericUpDown.Name = "frequenceNumericUpDown";
            frequenceNumericUpDown.Size = new Size(330, 23);
            frequenceNumericUpDown.TabIndex = 11;
            frequenceNumericUpDown.Value = new decimal(new int[] { 24, 0, 0, 0 });
            // 
            // estActiveLabel
            // 
            estActiveLabel.Dock = DockStyle.Fill;
            estActiveLabel.Location = new Point(13, 220);
            estActiveLabel.Name = "estActiveLabel";
            estActiveLabel.Size = new Size(138, 30);
            estActiveLabel.TabIndex = 12;
            estActiveLabel.Text = "Active :";
            estActiveLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // estActiveCheckBox
            // 
            estActiveCheckBox.Checked = true;
            estActiveCheckBox.CheckState = CheckState.Checked;
            estActiveCheckBox.Dock = DockStyle.Fill;
            estActiveCheckBox.Location = new Point(157, 223);
            estActiveCheckBox.Name = "estActiveCheckBox";
            estActiveCheckBox.Size = new Size(330, 24);
            estActiveCheckBox.TabIndex = 13;
            // 
            // envoyerEmailLabel
            // 
            envoyerEmailLabel.Dock = DockStyle.Fill;
            envoyerEmailLabel.Location = new Point(13, 250);
            envoyerEmailLabel.Name = "envoyerEmailLabel";
            envoyerEmailLabel.Size = new Size(138, 30);
            envoyerEmailLabel.TabIndex = 14;
            envoyerEmailLabel.Text = "Envoyer email :";
            envoyerEmailLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // envoyerEmailCheckBox
            // 
            envoyerEmailCheckBox.Dock = DockStyle.Fill;
            envoyerEmailCheckBox.Location = new Point(157, 253);
            envoyerEmailCheckBox.Name = "envoyerEmailCheckBox";
            envoyerEmailCheckBox.Size = new Size(330, 24);
            envoyerEmailCheckBox.TabIndex = 15;
            // 
            // buttonsPanel
            // 
            buttonsPanel.Controls.Add(cancelButton);
            buttonsPanel.Controls.Add(saveButton);
            buttonsPanel.Dock = DockStyle.Fill;
            buttonsPanel.FlowDirection = FlowDirection.RightToLeft;
            buttonsPanel.Location = new Point(157, 283);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.Size = new Size(330, 104);
            buttonsPanel.TabIndex = 16;
            // 
            // cancelButton
            // 
            cancelButton.Location = new Point(227, 3);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(100, 30);
            cancelButton.TabIndex = 0;
            cancelButton.Text = "Annuler";
            cancelButton.Click += CancelButton_Click;
            // 
            // saveButton
            // 
            saveButton.Location = new Point(121, 3);
            saveButton.Name = "saveButton";
            saveButton.Size = new Size(100, 30);
            saveButton.TabIndex = 1;
            saveButton.Text = "Enregistrer";
            saveButton.Click += SaveButton_Click;
            // 
            // AlerteEditForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(500, 400);
            Controls.Add(mainPanel);
            Name = "AlerteEditForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Alerte";
            Load += AlerteEditForm_Load;
            mainPanel.ResumeLayout(false);
            mainPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)seuilNumericUpDown).EndInit();
            ((System.ComponentModel.ISupportInitialize)frequenceNumericUpDown).EndInit();
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel mainPanel;
        private Label nomLabel;
        private TextBox nomTextBox;
        private Label typeLabel;
        private ComboBox typeComboBox;
        private Label descriptionLabel;
        private TextBox descriptionTextBox;
        private Label conditionLabel;
        private ComboBox conditionComboBox;
        private Label seuilLabel;
        private NumericUpDown seuilNumericUpDown;
        private Label frequenceLabel;
        private NumericUpDown frequenceNumericUpDown;
        private Label estActiveLabel;
        private CheckBox estActiveCheckBox;
        private Label envoyerEmailLabel;
        private CheckBox envoyerEmailCheckBox;
        private FlowLayoutPanel buttonsPanel;
        private Button cancelButton;
        private Button saveButton;
    }
}
