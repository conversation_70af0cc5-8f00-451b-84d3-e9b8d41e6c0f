using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une entrée dans le journal d'audit du système
    /// </summary>
    public class JournalAudit
    {
        /// <summary>
        /// Identifiant unique de l'entrée
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Date et heure de l'action
        /// </summary>
        public DateTime DateAction { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a effectué l'action
        /// </summary>
        public int UtilisateurId { get; set; }

        /// <summary>
        /// Nom d'utilisateur qui a effectué l'action
        /// </summary>
        public string NomUtilisateur { get; set; }

        /// <summary>
        /// Type d'action (Création, Modification, Suppression, Connexion, etc.)
        /// </summary>
        public string TypeAction { get; set; }

        /// <summary>
        /// Type d'entité concernée (Utilisateur, Client, Facture, etc.)
        /// </summary>
        public string TypeEntite { get; set; }

        /// <summary>
        /// Identifiant de l'entité concernée
        /// </summary>
        public int? EntiteId { get; set; }

        /// <summary>
        /// Description de l'action
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Données avant modification (format JSON)
        /// </summary>
        public string DonneesAvant { get; set; }

        /// <summary>
        /// Données après modification (format JSON)
        /// </summary>
        public string DonneesApres { get; set; }

        /// <summary>
        /// Adresse IP de l'utilisateur
        /// </summary>
        public string AdresseIP { get; set; }
    }
}
