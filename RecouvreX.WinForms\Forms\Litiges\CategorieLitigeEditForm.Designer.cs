using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.IO;

namespace RecouvreX.WinForms.Forms.Litiges
{
    partial class CategorieLitigeEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private Label titleLabel;
        private Label nomLabel;
        private TextBox nomTextBox;
        private Label descriptionLabel;
        private TextBox descriptionTextBox;
        private Label delaiLabel;
        private NumericUpDown delaiNumericUpDown;
        private Label couleurLabel;
        private Panel couleurPanel;
        private TextBox couleurTextBox;
        private Panel buttonsPanel;
        private Button saveButton;
        private Button cancelButton;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();

            // Définir les propriétés du formulaire
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimizeBox = false;
            this.MaximizeBox = false;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;

            try
            {
                this.Icon = new Icon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "dispute.ico"));
            }
            catch
            {
                // Ignorer les erreurs d'icône
            }

            // Créer les contrôles
            this.mainPanel = new Panel();
            this.mainPanel.Dock = DockStyle.Fill;
            this.Controls.Add(this.mainPanel);

            // Titre
            this.titleLabel = new Label();
            this.titleLabel.Font = new Font(this.Font.FontFamily, 14, FontStyle.Bold);
            this.titleLabel.AutoSize = true;
            this.titleLabel.Location = new Point(20, 20);
            this.mainPanel.Controls.Add(this.titleLabel);

            // Nom
            this.nomLabel = new Label();
            this.nomLabel.Text = "Nom :";
            this.nomLabel.AutoSize = true;
            this.nomLabel.Location = new Point(20, 70);
            this.mainPanel.Controls.Add(this.nomLabel);

            this.nomTextBox = new TextBox();
            this.nomTextBox.Name = "nomTextBox";
            this.nomTextBox.Location = new Point(150, 67);
            this.nomTextBox.Size = new Size(300, 25);
            this.mainPanel.Controls.Add(this.nomTextBox);

            // Description
            this.descriptionLabel = new Label();
            this.descriptionLabel.Text = "Description :";
            this.descriptionLabel.AutoSize = true;
            this.descriptionLabel.Location = new Point(20, 110);
            this.mainPanel.Controls.Add(this.descriptionLabel);

            this.descriptionTextBox = new TextBox();
            this.descriptionTextBox.Name = "descriptionTextBox";
            this.descriptionTextBox.Location = new Point(150, 107);
            this.descriptionTextBox.Size = new Size(300, 100);
            this.descriptionTextBox.Multiline = true;
            this.descriptionTextBox.ScrollBars = ScrollBars.Vertical;
            this.mainPanel.Controls.Add(this.descriptionTextBox);

            // Délai de résolution
            this.delaiLabel = new Label();
            this.delaiLabel.Text = "Délai (jours) :";
            this.delaiLabel.AutoSize = true;
            this.delaiLabel.Location = new Point(20, 220);
            this.mainPanel.Controls.Add(this.delaiLabel);

            this.delaiNumericUpDown = new NumericUpDown();
            this.delaiNumericUpDown.Name = "delaiNumericUpDown";
            this.delaiNumericUpDown.Location = new Point(150, 217);
            this.delaiNumericUpDown.Size = new Size(100, 25);
            this.delaiNumericUpDown.Minimum = 1;
            this.delaiNumericUpDown.Maximum = 365;
            this.delaiNumericUpDown.Value = 7;
            this.mainPanel.Controls.Add(this.delaiNumericUpDown);

            // Couleur
            this.couleurLabel = new Label();
            this.couleurLabel.Text = "Couleur :";
            this.couleurLabel.AutoSize = true;
            this.couleurLabel.Location = new Point(20, 260);
            this.mainPanel.Controls.Add(this.couleurLabel);

            this.couleurPanel = new Panel();
            this.couleurPanel.Name = "couleurPanel";
            this.couleurPanel.Location = new Point(150, 257);
            this.couleurPanel.Size = new Size(30, 30);
            this.couleurPanel.BackColor = Color.LightGray;
            this.couleurPanel.BorderStyle = BorderStyle.FixedSingle;
            this.couleurPanel.Cursor = Cursors.Hand;
            this.couleurPanel.Click += new EventHandler(this.CouleurPanel_Click);
            this.mainPanel.Controls.Add(this.couleurPanel);

            this.couleurTextBox = new TextBox();
            this.couleurTextBox.Name = "couleurTextBox";
            this.couleurTextBox.Location = new Point(190, 257);
            this.couleurTextBox.Size = new Size(100, 25);
            this.couleurTextBox.Text = "#CCCCCC";
            this.couleurTextBox.ReadOnly = true;
            this.mainPanel.Controls.Add(this.couleurTextBox);

            // Boutons
            this.buttonsPanel = new Panel();
            this.buttonsPanel.Dock = DockStyle.Bottom;
            this.buttonsPanel.Height = 50;
            this.mainPanel.Controls.Add(this.buttonsPanel);

            this.saveButton = new Button();
            this.saveButton.Text = "Enregistrer";
            this.saveButton.Size = new Size(100, 30);
            this.saveButton.Location = new Point(250, 10);
            this.saveButton.Click += new EventHandler(this.SaveButton_Click);
            this.buttonsPanel.Controls.Add(this.saveButton);

            this.cancelButton = new Button();
            this.cancelButton.Text = "Annuler";
            this.cancelButton.Size = new Size(100, 30);
            this.cancelButton.Location = new Point(360, 10);
            this.cancelButton.Click += new EventHandler(this.CancelButton_Click);
            this.buttonsPanel.Controls.Add(this.cancelButton);

            // Charger le formulaire
            this.Load += new EventHandler(this.CategorieLitigeEditForm_Load);
        }

        #endregion
    }
}
