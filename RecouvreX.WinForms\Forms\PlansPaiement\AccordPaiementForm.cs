using Microsoft.Extensions.Configuration;
using RecouvreX.Business.Helpers;
using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.PlansPaiement
{
    public partial class AccordPaiementForm : Form
    {
        private readonly IPlanPaiementService _planPaiementService;
        private readonly IClientService _clientService;
        private readonly IUtilisateurService _utilisateurService;
        private readonly IContactClientService _contactClientService;
        private readonly ICommunicationService _communicationService;
        private readonly IConfiguration _configuration;
        private readonly int _planPaiementId;
        private readonly int _currentUserId;
        private PlanPaiement _planPaiement;
        private Client _client;
        private Utilisateur _responsable;
        private List<ContactClient> _contacts = new List<ContactClient>();
        private TableLayoutPanel mainPanel;
        private Label titleLabel;
        private CheckBox envoyerEmailCheckBox;
        private TableLayoutPanel buttonsPanel;
        private Button generateButton;
        private Button cancelButton;
        private ComboBox contactComboBox;
        private string _pdfPath;

        public AccordPaiementForm(
            IPlanPaiementService planPaiementService,
            IClientService clientService,
            IUtilisateurService utilisateurService,
            IContactClientService contactClientService,
            ICommunicationService communicationService,
            IConfiguration configuration,
            int planPaiementId,
            int currentUserId)
        {
            _planPaiementService = planPaiementService ?? throw new ArgumentNullException(nameof(planPaiementService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _contactClientService = contactClientService ?? throw new ArgumentNullException(nameof(contactClientService));
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _planPaiementId = planPaiementId;
            _currentUserId = currentUserId;

            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            mainPanel = new TableLayoutPanel();
            titleLabel = new Label();
            contactComboBox = new ComboBox();
            envoyerEmailCheckBox = new CheckBox();
            buttonsPanel = new TableLayoutPanel();
            generateButton = new Button();
            cancelButton = new Button();
            mainPanel.SuspendLayout();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 110F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 58F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            mainPanel.Controls.Add(titleLabel, 0, 0);
            mainPanel.Controls.Add(envoyerEmailCheckBox, 2, 1);
            mainPanel.Controls.Add(buttonsPanel, 1, 5);
            mainPanel.Controls.Add(contactComboBox, 2, 0);
            mainPanel.Location = new Point(64, 87);
            mainPanel.Name = "mainPanel";
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 22F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 10F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 18F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 45F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 8F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            mainPanel.Size = new Size(370, 243);
            mainPanel.TabIndex = 0;
            // 
            // titleLabel
            // 
            mainPanel.SetColumnSpan(titleLabel, 2);
            titleLabel.Location = new Point(3, 0);
            titleLabel.Name = "titleLabel";
            titleLabel.Size = new Size(100, 23);
            titleLabel.TabIndex = 0;
            // 
            // contactComboBox
            // 
            contactComboBox.Location = new Point(171, 3);
            contactComboBox.Name = "contactComboBox";
            contactComboBox.Size = new Size(196, 23);
            contactComboBox.TabIndex = 8;
            // 
            // envoyerEmailCheckBox
            // 
            envoyerEmailCheckBox.Location = new Point(171, 33);
            envoyerEmailCheckBox.Name = "envoyerEmailCheckBox";
            envoyerEmailCheckBox.Size = new Size(104, 16);
            envoyerEmailCheckBox.TabIndex = 10;
            // 
            // buttonsPanel
            // 
            mainPanel.SetColumnSpan(buttonsPanel, 2);
            buttonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 97F));
            buttonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 8F));
            buttonsPanel.Controls.Add(generateButton, 1, 0);
            buttonsPanel.Controls.Add(cancelButton, 0, 0);
            buttonsPanel.Location = new Point(113, 128);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            buttonsPanel.Size = new Size(200, 30);
            buttonsPanel.TabIndex = 11;
            // 
            // generateButton
            // 
            generateButton.Location = new Point(100, 3);
            generateButton.Name = "generateButton";
            generateButton.Size = new Size(75, 23);
            generateButton.TabIndex = 0;
            generateButton.Click += GenerateButton_Click;
            // 
            // cancelButton
            // 
            cancelButton.Location = new Point(3, 3);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(75, 23);
            cancelButton.TabIndex = 1;
            cancelButton.Click += CancelButton_Click;
            // 
            // AccordPaiementForm
            // 
            ClientSize = new Size(484, 361);
            Controls.Add(mainPanel);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "AccordPaiementForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "Génération d'accord de paiement";
            mainPanel.ResumeLayout(false);
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
        }

        private async void LoadData()
        {
            try
            {
                // Charger le plan de paiement
                _planPaiement = await _planPaiementService.GetPlanCompleteAsync(_planPaiementId);
                if (_planPaiement == null)
                {
                    MessageBox.Show("Le plan de paiement demandé n'existe pas ou a été supprimé.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Charger le client
                _client = await _clientService.GetByIdAsync(_planPaiement.ClientId);
                if (_client == null)
                {
                    MessageBox.Show("Le client associé au plan de paiement n'existe pas.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Charger le responsable
                _responsable = await _utilisateurService.GetByIdAsync(_planPaiement.ResponsableId);
                if (_responsable == null)
                {
                    MessageBox.Show("Le responsable associé au plan de paiement n'existe pas.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Charger les contacts du client
                _contacts = (await _contactClientService.GetByClientIdAsync(_client.Id)).ToList();
                if (_contacts.Count == 0)
                {
                    MessageBox.Show("Aucun contact n'est associé à ce client. Veuillez d'abord ajouter un contact.", "Avertissement", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // Mettre à jour les contrôles
                UpdateControls();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données pour l'accord de paiement");
                MessageBox.Show($"Erreur lors du chargement des données : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
            }
        }

        private void UpdateControls()
        {
            // Mettre à jour les informations du plan de paiement
            var planPaiementLabel = Controls.Find("planPaiementLabel", true).FirstOrDefault() as Label;
            if (planPaiementLabel != null)
            {
                planPaiementLabel.Text = $"{_planPaiement.Reference} - {_planPaiement.DateDebut:dd/MM/yyyy} à {_planPaiement.DateFinPrevue:dd/MM/yyyy}";
            }

            var clientLabel = Controls.Find("clientLabel", true).FirstOrDefault() as Label;
            if (clientLabel != null)
            {
                clientLabel.Text = _client.RaisonSociale;
            }

            var montantTotalLabel = Controls.Find("montantTotalLabel", true).FirstOrDefault() as Label;
            if (montantTotalLabel != null)
            {
                montantTotalLabel.Text = $"{_planPaiement.MontantTotal:C2} ({_planPaiement.Echeances?.Count ?? 0} échéances)";
            }

            // Remplir la liste des contacts
            var contactComboBox = Controls.Find("contactComboBox", true).FirstOrDefault() as ComboBox;
            if (contactComboBox != null)
            {
                contactComboBox.Items.Clear();
                foreach (var contact in _contacts)
                {
                    contactComboBox.Items.Add($"{contact.Prenom} {contact.Nom} - {contact.Email}");
                }
                if (contactComboBox.Items.Count > 0)
                {
                    contactComboBox.SelectedIndex = 0;
                }
            }
        }

        private async void GenerateButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Vérifier si un contact est sélectionné
                var contactComboBox = Controls.Find("contactComboBox", true).FirstOrDefault() as ComboBox;
                var envoyerEmailCheckBox = Controls.Find("envoyerEmailCheckBox", true).FirstOrDefault() as CheckBox;

                if (contactComboBox == null || contactComboBox.SelectedIndex < 0)
                {
                    MessageBox.Show("Veuillez sélectionner un contact.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Récupérer le contact sélectionné
                var contact = _contacts[contactComboBox.SelectedIndex];
                bool envoyerEmail = envoyerEmailCheckBox != null && envoyerEmailCheckBox.Checked;

                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Générer le PDF
                string documentsPath = _configuration.GetSection("AppSettings")["DocumentsPath"] ?? "Documents";
                string plansPaiementPath = Path.Combine(documentsPath, "PlansPaiement");
                string fileName = $"Accord_{_planPaiement.Reference}_{DateTime.Now:yyyyMMdd}.pdf";
                _pdfPath = Path.Combine(plansPaiementPath, fileName);

                bool pdfGenere = AccordPaiementHelper.GenererAccordPaiement(
                    _planPaiement,
                    _client,
                    _planPaiement.Factures?.ToList() ?? new List<Facture>(),
                    _planPaiement.Echeances?.ToList() ?? new List<EcheancePaiement>(),
                    _responsable,
                    _pdfPath);

                if (!pdfGenere)
                {
                    MessageBox.Show("Une erreur s'est produite lors de la génération du PDF.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    Cursor.Current = Cursors.Default;
                    return;
                }

                // Envoyer par email si demandé
                bool emailEnvoye = false;
                if (envoyerEmail)
                {
                    emailEnvoye = await AccordPaiementHelper.EnvoyerAccordPaiementAsync(
                        _planPaiement,
                        _client,
                        contact,
                        _pdfPath,
                        _currentUserId,
                        _configuration,
                        _communicationService);
                }

                // Mettre à jour le plan de paiement
                if (pdfGenere)
                {
                    _planPaiement.CheminAccord = _pdfPath;
                    await _planPaiementService.UpdatePlanAsync(_planPaiement, _currentUserId);
                }

                // Restaurer le curseur
                Cursor.Current = Cursors.Default;

                // Afficher un message de confirmation
                if (envoyerEmail && emailEnvoye)
                {
                    MessageBox.Show("L'accord de paiement a été généré et envoyé par email avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else if (pdfGenere)
                {
                    MessageBox.Show("L'accord de paiement a été généré avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Demander si l'utilisateur souhaite ouvrir le PDF
                    if (MessageBox.Show("Souhaitez-vous ouvrir le document PDF ?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = _pdfPath,
                            UseShellExecute = true
                        });
                    }
                }

                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération de l'accord de paiement");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Cursor.Current = Cursors.Default;
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
}
