using Moq;
using RecouvreX.Business.Interfaces;
using RecouvreX.Business.Services;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Xunit;

namespace RecouvreX.Tests.Business
{
    public class AuditServiceTests
    {
        private readonly Mock<IAuditRepository> _mockAuditRepository;
        private readonly Mock<IUtilisateurRepository> _mockUtilisateurRepository;
        private readonly IAuditService _auditService;

        public AuditServiceTests()
        {
            _mockAuditRepository = new Mock<IAuditRepository>();
            _mockUtilisateurRepository = new Mock<IUtilisateurRepository>();
            _auditService = new AuditService(_mockAuditRepository.Object, _mockUtilisateurRepository.Object);
        }

        [Fact]
        public async Task LogActivityAsync_ShouldCreateAuditLogAndReturnTrue()
        {
            // Arrange
            string entityType = "Client";
            string action = "Création";
            int entityId = 1;
            int userId = 1;
            var user = new Utilisateur { Id = userId, NomUtilisateur = "testuser", Nom = "Test", Prenom = "User" };

            _mockUtilisateurRepository.Setup(repo => repo.GetByIdAsync(userId))
                .ReturnsAsync(user);

            _mockAuditRepository.Setup(repo => repo.CreateAsync(It.IsAny<AuditLog>()))
                .ReturnsAsync(1);

            // Act
            await _auditService.LogActivityAsync(entityType, action, entityId, userId);

            // Assert
            _mockUtilisateurRepository.Verify(repo => repo.GetByIdAsync(userId), Times.Once);
            _mockAuditRepository.Verify(repo => repo.CreateAsync(It.Is<AuditLog>(log =>
                log.EntityType == entityType &&
                log.Action == action &&
                log.EntityId == entityId &&
                log.UtilisateurId == userId &&
                log.UtilisateurNom == $"{user.Prenom} {user.Nom}" &&
                log.DateAction.Date == DateTime.Today
            )), Times.Once);
        }

        [Fact]
        public async Task LogActivityAsync_WithInvalidUser_ShouldStillCreateAuditLog()
        {
            // Arrange
            string entityType = "Client";
            string action = "Création";
            int entityId = 1;
            int userId = 999; // Utilisateur inexistant

            _mockUtilisateurRepository.Setup(repo => repo.GetByIdAsync(userId))
                .ReturnsAsync((Utilisateur)null);

            _mockAuditRepository.Setup(repo => repo.CreateAsync(It.IsAny<AuditLog>()))
                .ReturnsAsync(1);

            // Act
            await _auditService.LogActivityAsync(entityType, action, entityId, userId);

            // Assert
            _mockUtilisateurRepository.Verify(repo => repo.GetByIdAsync(userId), Times.Once);
            _mockAuditRepository.Verify(repo => repo.CreateAsync(It.Is<AuditLog>(log =>
                log.EntityType == entityType &&
                log.Action == action &&
                log.EntityId == entityId &&
                log.UtilisateurId == userId &&
                log.UtilisateurNom == "Utilisateur inconnu" &&
                log.DateAction.Date == DateTime.Today
            )), Times.Once);
        }

        [Fact]
        public async Task GetAllAsync_ShouldReturnAllAuditLogs()
        {
            // Arrange
            var expectedLogs = new List<AuditLog>
            {
                new AuditLog { Id = 1, EntityType = "Client", Action = "Création", EntityId = 1, UtilisateurId = 1, DateAction = DateTime.Today.AddDays(-2) },
                new AuditLog { Id = 2, EntityType = "Facture", Action = "Modification", EntityId = 1, UtilisateurId = 1, DateAction = DateTime.Today.AddDays(-1) },
                new AuditLog { Id = 3, EntityType = "Paiement", Action = "Création", EntityId = 1, UtilisateurId = 2, DateAction = DateTime.Today }
            };

            _mockAuditRepository.Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(expectedLogs);

            // Act
            var result = await _auditService.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedLogs.Count, result.Count());
            Assert.Equal(expectedLogs, result);
            _mockAuditRepository.Verify(repo => repo.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetByEntityTypeAsync_ShouldReturnFilteredAuditLogs()
        {
            // Arrange
            string entityType = "Client";
            var allLogs = new List<AuditLog>
            {
                new AuditLog { Id = 1, EntityType = "Client", Action = "Création", EntityId = 1, UtilisateurId = 1, DateAction = DateTime.Today.AddDays(-2) },
                new AuditLog { Id = 2, EntityType = "Facture", Action = "Modification", EntityId = 1, UtilisateurId = 1, DateAction = DateTime.Today.AddDays(-1) },
                new AuditLog { Id = 3, EntityType = "Client", Action = "Modification", EntityId = 2, UtilisateurId = 2, DateAction = DateTime.Today }
            };
            var expectedLogs = allLogs.Where(log => log.EntityType == entityType).ToList();

            _mockAuditRepository.Setup(repo => repo.GetByEntityTypeAsync(entityType))
                .ReturnsAsync(expectedLogs);

            // Act
            var result = await _auditService.GetByEntityTypeAsync(entityType);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedLogs.Count, result.Count());
            Assert.Equal(expectedLogs, result);
            Assert.All(result, log => Assert.Equal(entityType, log.EntityType));
            _mockAuditRepository.Verify(repo => repo.GetByEntityTypeAsync(entityType), Times.Once);
        }

        [Fact]
        public async Task GetByEntityIdAsync_ShouldReturnFilteredAuditLogs()
        {
            // Arrange
            string entityType = "Client";
            int entityId = 1;
            var allLogs = new List<AuditLog>
            {
                new AuditLog { Id = 1, EntityType = "Client", Action = "Création", EntityId = 1, UtilisateurId = 1, DateAction = DateTime.Today.AddDays(-2) },
                new AuditLog { Id = 2, EntityType = "Facture", Action = "Modification", EntityId = 1, UtilisateurId = 1, DateAction = DateTime.Today.AddDays(-1) },
                new AuditLog { Id = 3, EntityType = "Client", Action = "Modification", EntityId = 2, UtilisateurId = 2, DateAction = DateTime.Today }
            };
            var expectedLogs = allLogs.Where(log => log.EntityType == entityType && log.EntityId == entityId).ToList();

            _mockAuditRepository.Setup(repo => repo.GetByEntityIdAsync(entityType, entityId))
                .ReturnsAsync(expectedLogs);

            // Act
            var result = await _auditService.GetByEntityIdAsync(entityType, entityId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedLogs.Count, result.Count());
            Assert.Equal(expectedLogs, result);
            Assert.All(result, log => Assert.Equal(entityType, log.EntityType));
            Assert.All(result, log => Assert.Equal(entityId, log.EntityId));
            _mockAuditRepository.Verify(repo => repo.GetByEntityIdAsync(entityType, entityId), Times.Once);
        }

        [Fact]
        public async Task GetByUserIdAsync_ShouldReturnFilteredAuditLogs()
        {
            // Arrange
            int userId = 1;
            var allLogs = new List<AuditLog>
            {
                new AuditLog { Id = 1, EntityType = "Client", Action = "Création", EntityId = 1, UtilisateurId = 1, DateAction = DateTime.Today.AddDays(-2) },
                new AuditLog { Id = 2, EntityType = "Facture", Action = "Modification", EntityId = 1, UtilisateurId = 1, DateAction = DateTime.Today.AddDays(-1) },
                new AuditLog { Id = 3, EntityType = "Client", Action = "Modification", EntityId = 2, UtilisateurId = 2, DateAction = DateTime.Today }
            };
            var expectedLogs = allLogs.Where(log => log.UtilisateurId == userId).ToList();

            _mockAuditRepository.Setup(repo => repo.GetByUserIdAsync(userId))
                .ReturnsAsync(expectedLogs);

            // Act
            var result = await _auditService.GetByUserIdAsync(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedLogs.Count, result.Count());
            Assert.Equal(expectedLogs, result);
            Assert.All(result, log => Assert.Equal(userId, log.UtilisateurId));
            _mockAuditRepository.Verify(repo => repo.GetByUserIdAsync(userId), Times.Once);
        }

        [Fact]
        public async Task GetByDateRangeAsync_ShouldReturnFilteredAuditLogs()
        {
            // Arrange
            DateTime startDate = DateTime.Today.AddDays(-7);
            DateTime endDate = DateTime.Today;
            var allLogs = new List<AuditLog>
            {
                new AuditLog { Id = 1, EntityType = "Client", Action = "Création", EntityId = 1, UtilisateurId = 1, DateAction = DateTime.Today.AddDays(-10) },
                new AuditLog { Id = 2, EntityType = "Facture", Action = "Modification", EntityId = 1, UtilisateurId = 1, DateAction = DateTime.Today.AddDays(-5) },
                new AuditLog { Id = 3, EntityType = "Client", Action = "Modification", EntityId = 2, UtilisateurId = 2, DateAction = DateTime.Today }
            };
            var expectedLogs = allLogs.Where(log => log.DateAction >= startDate && log.DateAction <= endDate).ToList();

            _mockAuditRepository.Setup(repo => repo.GetByDateRangeAsync(startDate, endDate))
                .ReturnsAsync(expectedLogs);

            // Act
            var result = await _auditService.GetByDateRangeAsync(startDate, endDate);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedLogs.Count, result.Count());
            Assert.Equal(expectedLogs, result);
            Assert.All(result, log => Assert.True(log.DateAction >= startDate && log.DateAction <= endDate));
            _mockAuditRepository.Verify(repo => repo.GetByDateRangeAsync(startDate, endDate), Times.Once);
        }
    }
}
