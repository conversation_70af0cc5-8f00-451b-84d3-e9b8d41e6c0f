using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une alerte personnalisable dans le système
    /// </summary>
    public class Alerte : BaseEntity
    {
        /// <summary>
        /// Nom de l'alerte
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Description de l'alerte
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Type d'alerte (Montant, Ancienneté, Taux, etc.)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Condition de l'alerte (Supérieur, Inférieur, Égal, etc.)
        /// </summary>
        public string Condition { get; set; }

        /// <summary>
        /// Valeur seuil de l'alerte
        /// </summary>
        public decimal Seuil { get; set; }

        /// <summary>
        /// Indique si l'alerte est active
        /// </summary>
        public bool EstActive { get; set; }

        /// <summary>
        /// Indique si l'alerte doit être envoyée par email
        /// </summary>
        public bool EnvoyerEmail { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a créé l'alerte
        /// </summary>
        public int UtilisateurId { get; set; }

        /// <summary>
        /// Utilisateur qui a créé l'alerte (navigation property)
        /// </summary>
        public Utilisateur Utilisateur { get; set; }

        /// <summary>
        /// Date de la dernière notification
        /// </summary>
        public DateTime? DerniereNotification { get; set; }

        /// <summary>
        /// Fréquence minimale entre deux notifications (en heures)
        /// </summary>
        public int FrequenceNotificationHeures { get; set; }
    }
}
