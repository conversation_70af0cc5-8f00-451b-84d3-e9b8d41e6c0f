using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;

namespace RecouvreX.WinForms.Forms.Relances
{
    public partial class RelanceDetailsForm : Form
    {
        private readonly IRelanceService _relanceService;
        private readonly IFactureService _factureService;
        private readonly IClientService _clientService;
        private readonly int _currentUserId;
        private readonly int _relanceId;
        private Relance? _relance;
        private Facture? _facture;
        private Client? _client;

        public RelanceDetailsForm(IRelanceService relanceService, IFactureService factureService, IClientService clientService,
            int currentUserId, int relanceId)
        {
            _relanceService = relanceService ?? throw new ArgumentNullException(nameof(relanceService));
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _currentUserId = currentUserId;
            _relanceId = relanceId;

            InitializeComponent();
        }



        private async void RelanceDetailsForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Charger les données de la relance
                await LoadRelanceDataAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des détails de la relance");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des détails de la relance : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadRelanceDataAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer la relance
                _relance = await _relanceService.GetByIdAsync(_relanceId);
                if (_relance == null)
                {
                    MessageBox.Show("La relance demandée n'existe pas.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Récupérer la facture associée
                _facture = await _factureService.GetByIdAsync(_relance.FactureId);
                if (_facture == null)
                {
                    MessageBox.Show("La facture associée à cette relance n'existe pas.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Récupérer le client
                _client = await _clientService.GetByIdAsync(_facture.ClientId);
                if (_client == null)
                {
                    MessageBox.Show("Le client associé à cette facture n'existe pas.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Mettre à jour le titre du formulaire
                this.Text = $"Détails de la relance - {_relance.Type} niveau {_relance.Niveau}";

                // Remplir les informations de la relance
                FillRelanceInfo(_relance);

                // Remplir les informations de la facture
                FillFactureInfo(_facture, _client);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données de la relance");
                throw;
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void FillRelanceInfo(Relance relance)
        {
            var typeValueLabel = this.Controls.Find("typeValueLabel", true).FirstOrDefault() as Label;
            var dateRelanceValueLabel = this.Controls.Find("dateRelanceValueLabel", true).FirstOrDefault() as Label;
            var niveauValueLabel = this.Controls.Find("niveauValueLabel", true).FirstOrDefault() as Label;
            var statutValueLabel = this.Controls.Find("statutValueLabel", true).FirstOrDefault() as Label;
            var utilisateurValueLabel = this.Controls.Find("utilisateurValueLabel", true).FirstOrDefault() as Label;
            var dateProchaineRelanceValueLabel = this.Controls.Find("dateProchaineRelanceValueLabel", true).FirstOrDefault() as Label;
            var contenuTextBox = this.Controls.Find("contenuTextBox", true).FirstOrDefault() as TextBox;

            if (typeValueLabel != null) typeValueLabel.Text = relance.Type;
            if (dateRelanceValueLabel != null) dateRelanceValueLabel.Text = relance.DateRelance.ToString("dd/MM/yyyy");
            if (niveauValueLabel != null) niveauValueLabel.Text = relance.Niveau.ToString();

            if (statutValueLabel != null)
            {
                statutValueLabel.Text = relance.Statut;

                // Colorer le statut
                switch (relance.Statut)
                {
                    case "Terminée":
                        statutValueLabel.ForeColor = Color.Green;
                        break;
                    case "En cours":
                        statutValueLabel.ForeColor = Color.Blue;
                        break;
                    case "Planifiée":
                        statutValueLabel.ForeColor = Color.Orange;
                        break;
                    case "Annulée":
                        statutValueLabel.ForeColor = Color.Red;
                        break;
                    default:
                        statutValueLabel.ForeColor = SystemColors.ControlText;
                        break;
                }
            }

            if (utilisateurValueLabel != null)
                utilisateurValueLabel.Text = relance.UtilisateurId.HasValue ? $"Utilisateur {relance.UtilisateurId.Value}" : "Système";

            if (dateProchaineRelanceValueLabel != null)
                dateProchaineRelanceValueLabel.Text = relance.DateProchaineRelance.HasValue
                    ? relance.DateProchaineRelance.Value.ToString("dd/MM/yyyy")
                    : "Non planifiée";

            if (contenuTextBox != null) contenuTextBox.Text = relance.Contenu;
        }

        private void FillFactureInfo(Facture facture, Client client)
        {
            var numeroFactureValueLabel = this.Controls.Find("numeroFactureValueLabel", true).FirstOrDefault() as Label;
            var clientValueLabel = this.Controls.Find("clientValueLabel", true).FirstOrDefault() as Label;
            var dateEmissionValueLabel = this.Controls.Find("dateEmissionValueLabel", true).FirstOrDefault() as Label;
            var dateEcheanceValueLabel = this.Controls.Find("dateEcheanceValueLabel", true).FirstOrDefault() as Label;
            var montantTTCValueLabel = this.Controls.Find("montantTTCValueLabel", true).FirstOrDefault() as Label;
            var montantRestantValueLabel = this.Controls.Find("montantRestantValueLabel", true).FirstOrDefault() as Label;

            if (numeroFactureValueLabel != null) numeroFactureValueLabel.Text = facture.Numero;
            if (clientValueLabel != null) clientValueLabel.Text = $"{client.Code} - {client.RaisonSociale}";
            if (dateEmissionValueLabel != null) dateEmissionValueLabel.Text = facture.DateEmission.ToString("dd/MM/yyyy");
            if (dateEcheanceValueLabel != null) dateEcheanceValueLabel.Text = facture.DateEcheance.ToString("dd/MM/yyyy");
            if (montantTTCValueLabel != null) montantTTCValueLabel.Text = facture.MontantTTC.ToString("C2");

            if (montantRestantValueLabel != null)
            {
                montantRestantValueLabel.Text = facture.MontantRestant.ToString("C2");

                // Colorer le montant restant
                if (facture.MontantRestant > 0)
                {
                    montantRestantValueLabel.ForeColor = Color.Red;
                }
                else
                {
                    montantRestantValueLabel.ForeColor = Color.Green;
                }
            }
        }

        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_relance != null)
                {
                    // Ouvrir le formulaire d'édition de relance
                    using (var form = new RelanceEditForm(_relanceService, _factureService, _clientService, _currentUserId, _relance))
                    {
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            // Recharger les données
                            LoadRelanceDataAsync().Wait();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'édition de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewFactureButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_facture != null)
                {
                    // Ouvrir le formulaire de détails de facture
                    using (var form = new Factures.FactureDetailsForm(_factureService, _clientService, _currentUserId, _facture.Id))
                    {
                        form.ShowDialog();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de détails de facture");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
