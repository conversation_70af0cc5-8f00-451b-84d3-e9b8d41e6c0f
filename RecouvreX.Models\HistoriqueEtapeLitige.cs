using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente l'historique des étapes d'un litige
    /// </summary>
    public class HistoriqueEtapeLitige : BaseEntity
    {
        /// <summary>
        /// Identifiant du litige associé
        /// </summary>
        public int LitigeId { get; set; }

        /// <summary>
        /// Litige associé (navigation property)
        /// </summary>
        public Litige Litige { get; set; }

        /// <summary>
        /// Identifiant de l'étape
        /// </summary>
        public int EtapeLitigeId { get; set; }

        /// <summary>
        /// Étape associée (navigation property)
        /// </summary>
        public EtapeLitige EtapeLitige { get; set; }

        /// <summary>
        /// Date de début de l'étape
        /// </summary>
        public DateTime DateDebut { get; set; }

        /// <summary>
        /// Date de fin de l'étape (null si l'étape est en cours)
        /// </summary>
        public DateTime? DateFin { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a initié l'étape
        /// </summary>
        public int UtilisateurId { get; set; }

        /// <summary>
        /// Utilisateur qui a initié l'étape (navigation property)
        /// </summary>
        public Utilisateur Utilisateur { get; set; }

        /// <summary>
        /// Commentaire associé à cette étape
        /// </summary>
        public string Commentaire { get; set; }

        /// <summary>
        /// Date du changement d'étape
        /// </summary>
        public DateTime DateChangement { get; set; }
    }
}
