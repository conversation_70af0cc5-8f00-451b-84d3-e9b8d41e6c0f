using System;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Relances
{
    /// <summary>
    /// Formulaire pour saisir un commentaire
    /// </summary>
    public partial class CommentaireForm : Form
    {
        /// <summary>
        /// Commentaire saisi
        /// </summary>
        public string Commentaire { get; private set; }

        /// <summary>
        /// Constructeur
        /// </summary>
        public CommentaireForm()
        {
            InitializeComponent();
        }

        private void OkButton_Click(object sender, EventArgs e)
        {
            var commentaireTextBox = this.Controls.Find("commentaireTextBox", true)[0] as TextBox;
            if (commentaireTextBox != null)
            {
                Commentaire = commentaireTextBox.Text;
            }

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
