using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.IO;
using System.Data;

namespace RecouvreX.WinForms.Forms.Litiges
{
    partial class LitigeListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private Label titleLabel;
        private Panel filtersPanel;
        private Label clientLabel;
        private TextBox clientTextBox;
        private Label factureLabel;
        private TextBox factureTextBox;
        private Label categorieLabel;
        private ComboBox categorieComboBox;
        private Label etapeLabel;
        private ComboBox etapeComboBox;
        private Label statutLabel;
        private ComboBox statutComboBox;
        private Label responsableLabel;
        private ComboBox responsableComboBox;
        private Button searchButton;
        private DataGridView litigesDataGridView;
        private Panel buttonsPanel;
        private Button newButton;
        private Button editButton;
        private Button deleteButton;
        private Button refreshButton;
        private Button exportButton;
        private Button closeButton;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();

            // Définir les propriétés du formulaire
            this.Text = "Liste des litiges";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimizeBox = false;
            this.MaximizeBox = true;
            this.FormBorderStyle = FormBorderStyle.Sizable;

            // Créer les contrôles
            this.mainPanel = new Panel();
            this.mainPanel.Dock = DockStyle.Fill;
            this.Controls.Add(this.mainPanel);

            // Titre
            this.titleLabel = new Label();
            this.titleLabel.Text = "Liste des litiges";
            this.titleLabel.Font = new Font(this.Font.FontFamily, 16, FontStyle.Bold);
            this.titleLabel.AutoSize = true;
            this.titleLabel.Location = new Point(20, 20);
            this.mainPanel.Controls.Add(this.titleLabel);

            // Panneau de filtres
            this.filtersPanel = new Panel();
            this.filtersPanel.Size = new Size(1150, 100);
            this.filtersPanel.Location = new Point(20, 50);
            this.filtersPanel.BorderStyle = BorderStyle.FixedSingle;
            this.mainPanel.Controls.Add(this.filtersPanel);

            // Client
            this.clientLabel = new Label();
            this.clientLabel.Text = "Client :";
            this.clientLabel.AutoSize = true;
            this.clientLabel.Location = new Point(10, 15);
            this.filtersPanel.Controls.Add(this.clientLabel);

            this.clientTextBox = new TextBox();
            this.clientTextBox.Name = "clientTextBox";
            this.clientTextBox.Location = new Point(100, 12);
            this.clientTextBox.Size = new Size(200, 25);
            this.filtersPanel.Controls.Add(this.clientTextBox);

            // Facture
            this.factureLabel = new Label();
            this.factureLabel.Text = "Facture :";
            this.factureLabel.AutoSize = true;
            this.factureLabel.Location = new Point(320, 15);
            this.filtersPanel.Controls.Add(this.factureLabel);

            this.factureTextBox = new TextBox();
            this.factureTextBox.Name = "factureTextBox";
            this.factureTextBox.Location = new Point(400, 12);
            this.factureTextBox.Size = new Size(150, 25);
            this.filtersPanel.Controls.Add(this.factureTextBox);

            // Catégorie
            this.categorieLabel = new Label();
            this.categorieLabel.Text = "Catégorie :";
            this.categorieLabel.AutoSize = true;
            this.categorieLabel.Location = new Point(570, 15);
            this.filtersPanel.Controls.Add(this.categorieLabel);

            this.categorieComboBox = new ComboBox();
            this.categorieComboBox.Name = "categorieComboBox";
            this.categorieComboBox.Location = new Point(650, 12);
            this.categorieComboBox.Size = new Size(200, 25);
            this.categorieComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.filtersPanel.Controls.Add(this.categorieComboBox);

            // Étape
            this.etapeLabel = new Label();
            this.etapeLabel.Text = "Étape :";
            this.etapeLabel.AutoSize = true;
            this.etapeLabel.Location = new Point(10, 55);
            this.filtersPanel.Controls.Add(this.etapeLabel);

            this.etapeComboBox = new ComboBox();
            this.etapeComboBox.Name = "etapeComboBox";
            this.etapeComboBox.Location = new Point(100, 52);
            this.etapeComboBox.Size = new Size(200, 25);
            this.etapeComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.filtersPanel.Controls.Add(this.etapeComboBox);

            // Statut
            this.statutLabel = new Label();
            this.statutLabel.Text = "Statut :";
            this.statutLabel.AutoSize = true;
            this.statutLabel.Location = new Point(320, 55);
            this.filtersPanel.Controls.Add(this.statutLabel);

            this.statutComboBox = new ComboBox();
            this.statutComboBox.Name = "statutComboBox";
            this.statutComboBox.Location = new Point(400, 52);
            this.statutComboBox.Size = new Size(150, 25);
            this.statutComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.filtersPanel.Controls.Add(this.statutComboBox);

            // Responsable
            this.responsableLabel = new Label();
            this.responsableLabel.Text = "Responsable :";
            this.responsableLabel.AutoSize = true;
            this.responsableLabel.Location = new Point(570, 55);
            this.filtersPanel.Controls.Add(this.responsableLabel);

            this.responsableComboBox = new ComboBox();
            this.responsableComboBox.Name = "responsableComboBox";
            this.responsableComboBox.Location = new Point(650, 52);
            this.responsableComboBox.Size = new Size(200, 25);
            this.responsableComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.filtersPanel.Controls.Add(this.responsableComboBox);

            // Bouton de recherche
            this.searchButton = new Button();
            this.searchButton.Text = "Rechercher";
            this.searchButton.Size = new Size(100, 30);
            this.searchButton.Location = new Point(1030, 35);
            this.searchButton.Click += new EventHandler(this.SearchButton_Click);
            this.filtersPanel.Controls.Add(this.searchButton);

            // DataGridView pour afficher les litiges
            this.litigesDataGridView = new DataGridView();
            this.litigesDataGridView.Name = "litigesDataGridView";
            this.litigesDataGridView.Location = new Point(20, 150);
            this.litigesDataGridView.Size = new Size(1150, 450);
            this.litigesDataGridView.AllowUserToAddRows = false;
            this.litigesDataGridView.AllowUserToDeleteRows = false;
            this.litigesDataGridView.AllowUserToResizeRows = false;
            this.litigesDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.litigesDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.litigesDataGridView.MultiSelect = false;
            this.litigesDataGridView.ReadOnly = true;
            this.litigesDataGridView.RowHeadersVisible = false;
            this.litigesDataGridView.BackgroundColor = Color.White;
            this.litigesDataGridView.BorderStyle = BorderStyle.None;
            this.litigesDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.litigesDataGridView.CellDoubleClick += new DataGridViewCellEventHandler(this.LitigesDataGridView_CellDoubleClick);
            this.mainPanel.Controls.Add(this.litigesDataGridView);

            // Configurer les colonnes du DataGridView
            ConfigureDataGridView(this.litigesDataGridView);

            // Boutons d'action
            this.buttonsPanel = new Panel();
            this.buttonsPanel.Size = new Size(1150, 50);
            this.buttonsPanel.Location = new Point(20, 610);
            this.mainPanel.Controls.Add(this.buttonsPanel);

            this.newButton = new Button();
            this.newButton.Text = "Nouveau litige";
            this.newButton.Size = new Size(150, 30);
            this.newButton.Location = new Point(0, 10);
            this.newButton.Click += new EventHandler(this.NewButton_Click);
            this.buttonsPanel.Controls.Add(this.newButton);

            this.editButton = new Button();
            this.editButton.Text = "Modifier";
            this.editButton.Size = new Size(100, 30);
            this.editButton.Location = new Point(160, 10);
            this.editButton.Click += new EventHandler(this.EditButton_Click);
            this.buttonsPanel.Controls.Add(this.editButton);

            this.deleteButton = new Button();
            this.deleteButton.Text = "Supprimer";
            this.deleteButton.Size = new Size(100, 30);
            this.deleteButton.Location = new Point(270, 10);
            this.deleteButton.Click += new EventHandler(this.DeleteButton_Click);
            this.buttonsPanel.Controls.Add(this.deleteButton);

            this.refreshButton = new Button();
            this.refreshButton.Text = "Rafraîchir";
            this.refreshButton.Size = new Size(100, 30);
            this.refreshButton.Location = new Point(380, 10);
            this.refreshButton.Click += new EventHandler(this.RefreshButton_Click);
            this.buttonsPanel.Controls.Add(this.refreshButton);

            this.exportButton = new Button();
            this.exportButton.Text = "Exporter";
            this.exportButton.Size = new Size(100, 30);
            this.exportButton.Location = new Point(490, 10);
            this.exportButton.Click += new EventHandler(this.ExportButton_Click);
            this.buttonsPanel.Controls.Add(this.exportButton);

            this.closeButton = new Button();
            this.closeButton.Text = "Fermer";
            this.closeButton.Size = new Size(100, 30);
            this.closeButton.Location = new Point(1050, 10);
            this.closeButton.Click += new EventHandler(this.CloseButton_Click);
            this.buttonsPanel.Controls.Add(this.closeButton);

            // Charger le formulaire
            this.Load += new EventHandler(this.LitigeListForm_Load);
        }

        /// <summary>
        /// Configure les colonnes du DataGridView
        /// </summary>
        private void ConfigureDataGridView(DataGridView dataGridView)
        {
            // Ajouter les colonnes
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "ID",
                DataPropertyName = "Id",
                Visible = false
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "NumeroFacture",
                HeaderText = "N° Facture",
                DataPropertyName = "NumeroFacture",
                Width = 100
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Client",
                HeaderText = "Client",
                DataPropertyName = "Client",
                Width = 150
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Categorie",
                HeaderText = "Catégorie",
                DataPropertyName = "Categorie",
                Width = 120
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "MontantConteste",
                HeaderText = "Montant contesté",
                DataPropertyName = "MontantConteste",
                Width = 100
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DateOuverture",
                HeaderText = "Date d'ouverture",
                DataPropertyName = "DateOuverture",
                Width = 100
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DateEcheance",
                HeaderText = "Date d'échéance",
                DataPropertyName = "DateEcheance",
                Width = 100
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Etape",
                HeaderText = "Étape",
                DataPropertyName = "Etape",
                Width = 120
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Responsable",
                HeaderText = "Responsable",
                DataPropertyName = "Responsable",
                Width = 120
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Statut",
                HeaderText = "Statut",
                DataPropertyName = "Statut",
                Width = 80
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Priorite",
                HeaderText = "Priorité",
                DataPropertyName = "Priorite",
                Width = 80
            });
        }

        #endregion
    }
}
