using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour la gestion des règles de relance
    /// </summary>
    public class RegleRelanceRepository : BaseRepository<RegleRelance>, IRegleRelanceRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public RegleRelanceRepository(DatabaseConnection dbConnection) : base(dbConnection, "ReglesRelance")
        {
        }

        /// <summary>
        /// Récupère les règles de relance actives
        /// </summary>
        /// <returns>Liste des règles de relance actives</returns>
        public async Task<IEnumerable<RegleRelance>> GetActiveAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE EstActive = 1 AND EstActif = 1 ORDER BY Priorite DESC";
                return await connection.QueryAsync<RegleRelance>(query);
            }
        }

        /// <summary>
        /// Récupère les règles de relance par type de client
        /// </summary>
        /// <param name="typeClient">Type de client</param>
        /// <returns>Liste des règles de relance pour le type de client spécifié</returns>
        public async Task<IEnumerable<RegleRelance>> GetByTypeClientAsync(string typeClient)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE (TypeClient = @TypeClient OR TypeClient IS NULL) AND EstActive = 1 AND EstActif = 1 ORDER BY Priorite DESC";
                return await connection.QueryAsync<RegleRelance>(query, new { TypeClient = typeClient });
            }
        }

        /// <summary>
        /// Récupère les règles de relance avec leurs modèles associés
        /// </summary>
        /// <returns>Liste des règles de relance avec leurs modèles</returns>
        public async Task<IEnumerable<RegleRelance>> GetWithModelesAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT r.*,
                           m1.* as ModeleRelanceNiveau1,
                           m2.* as ModeleRelanceNiveau2,
                           m3.* as ModeleRelanceNiveau3
                    FROM ReglesRelance r
                    LEFT JOIN ModelesRelance m1 ON r.ModeleRelanceNiveau1Id = m1.Id
                    LEFT JOIN ModelesRelance m2 ON r.ModeleRelanceNiveau2Id = m2.Id
                    LEFT JOIN ModelesRelance m3 ON r.ModeleRelanceNiveau3Id = m3.Id
                    WHERE r.EstActif = 1
                    ORDER BY r.Priorite DESC";

                var regleDict = new Dictionary<int, RegleRelance>();

                await connection.QueryAsync<RegleRelance, ModeleRelance, ModeleRelance, ModeleRelance, RegleRelance>(
                    query,
                    (regle, modele1, modele2, modele3) =>
                    {
                        if (!regleDict.TryGetValue(regle.Id, out var regleEntry))
                        {
                            regleEntry = regle;
                            regleDict.Add(regle.Id, regleEntry);
                        }

                        if (modele1 != null)
                            regleEntry.ModeleRelanceNiveau1 = modele1;

                        if (modele2 != null)
                            regleEntry.ModeleRelanceNiveau2 = modele2;

                        if (modele3 != null)
                            regleEntry.ModeleRelanceNiveau3 = modele3;

                        return regleEntry;
                    },
                    splitOn: "Id,Id,Id");

                return regleDict.Values;
            }
        }
    }
}
