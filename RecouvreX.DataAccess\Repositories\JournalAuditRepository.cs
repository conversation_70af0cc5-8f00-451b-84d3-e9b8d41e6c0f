using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour le journal d'audit
    /// </summary>
    public class JournalAuditRepository : IJournalAuditRepository
    {
        private readonly DatabaseConnection _dbConnection;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public JournalAuditRepository(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection ?? throw new ArgumentNullException(nameof(dbConnection));
        }

        /// <summary>
        /// Ajoute une entrée dans le journal d'audit
        /// </summary>
        /// <param name="journalAudit">Entrée à ajouter</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        public async Task<JournalAudit> AddAsync(JournalAudit journalAudit)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    INSERT INTO JournalAudit (
                        DateAction, UtilisateurId, NomUtilisateur, TypeAction,
                        TypeEntite, EntiteId, Description, DonneesAvant,
                        DonneesApres, AdresseIP
                    )
                    VALUES (
                        @DateAction, @UtilisateurId, @NomUtilisateur, @TypeAction,
                        @TypeEntite, @EntiteId, @Description, @DonneesAvant,
                        @DonneesApres, @AdresseIP
                    );
                    SELECT CAST(SCOPE_IDENTITY() as int)";

                var id = await connection.QuerySingleAsync<int>(query, journalAudit);
                journalAudit.Id = id;

                return journalAudit;
            }
        }

        /// <summary>
        /// Récupère toutes les entrées du journal d'audit
        /// </summary>
        /// <returns>Liste des entrées du journal</returns>
        public async Task<IEnumerable<JournalAudit>> GetAllAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM JournalAudit ORDER BY DateAction DESC";
                return await connection.QueryAsync<JournalAudit>(query);
            }
        }

        /// <summary>
        /// Récupère une entrée du journal par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'entrée</param>
        /// <returns>Entrée trouvée ou null</returns>
        public async Task<JournalAudit> GetByIdAsync(int id)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM JournalAudit WHERE Id = @Id";
                return await connection.QueryFirstOrDefaultAsync<JournalAudit>(query, new { Id = id });
            }
        }

        /// <summary>
        /// Récupère les entrées du journal par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des entrées de l'utilisateur</returns>
        public async Task<IEnumerable<JournalAudit>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM JournalAudit WHERE UtilisateurId = @UtilisateurId ORDER BY DateAction DESC";
                return await connection.QueryAsync<JournalAudit>(query, new { UtilisateurId = utilisateurId });
            }
        }

        /// <summary>
        /// Récupère les entrées du journal par type d'action
        /// </summary>
        /// <param name="typeAction">Type d'action</param>
        /// <returns>Liste des entrées du type spécifié</returns>
        public async Task<IEnumerable<JournalAudit>> GetByTypeActionAsync(string typeAction)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM JournalAudit WHERE TypeAction = @TypeAction ORDER BY DateAction DESC";
                return await connection.QueryAsync<JournalAudit>(query, new { TypeAction = typeAction });
            }
        }

        /// <summary>
        /// Récupère les entrées du journal par entité
        /// </summary>
        /// <param name="typeEntite">Type d'entité</param>
        /// <param name="entiteId">Identifiant de l'entité</param>
        /// <returns>Liste des entrées concernant l'entité spécifiée</returns>
        public async Task<IEnumerable<JournalAudit>> GetByEntityAsync(string typeEntite, int entiteId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT *
                    FROM JournalAudit
                    WHERE TypeEntite = @TypeEntite AND EntiteId = @EntiteId
                    ORDER BY DateAction DESC";

                return await connection.QueryAsync<JournalAudit>(query, new { TypeEntite = typeEntite, EntiteId = entiteId });
            }
        }

        /// <summary>
        /// Récupère les entrées du journal par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des entrées dans la période spécifiée</returns>
        public async Task<IEnumerable<JournalAudit>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT *
                    FROM JournalAudit
                    WHERE DateAction BETWEEN @DateDebut AND @DateFin
                    ORDER BY DateAction DESC";

                return await connection.QueryAsync<JournalAudit>(query, new { DateDebut = dateDebut, DateFin = dateFin });
            }
        }

        /// <summary>
        /// Recherche dans le journal d'audit
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des entrées correspondant au terme de recherche</returns>
        public async Task<IEnumerable<JournalAudit>> SearchAsync(string searchTerm)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT *
                    FROM JournalAudit
                    WHERE NomUtilisateur LIKE @SearchTerm
                        OR TypeAction LIKE @SearchTerm
                        OR TypeEntite LIKE @SearchTerm
                        OR Description LIKE @SearchTerm
                    ORDER BY DateAction DESC";

                return await connection.QueryAsync<JournalAudit>(query, new { SearchTerm = $"%{searchTerm}%" });
            }
        }
    }
}
