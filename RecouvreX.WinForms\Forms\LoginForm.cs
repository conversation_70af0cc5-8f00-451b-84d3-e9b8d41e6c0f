using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.ComponentModel;
using Microsoft.Extensions.Configuration;

namespace RecouvreX.WinForms.Forms
{
    public partial class LoginForm : Form
    {
        private readonly IAuthenticationService _authenticationService;
        private readonly IConfiguration _configuration;
        private int _loginAttempts = 0;
        private readonly int _maxLoginAttempts;
        private Label labelVersion;

        public Utilisateur? LoggedInUser { get; private set; }

        public LoginForm(IAuthenticationService authenticationService, IConfiguration configuration)
        {
            InitializeComponent();
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _maxLoginAttempts = _configuration.GetValue<int>("AppSettings:MaxLoginAttempts", 5);

            // Mettre à jour le texte de la version après l'initialisation des composants
            if (labelVersion != null)
            {
                labelVersion.Text = $"Version {_configuration.GetValue<string>("AppSettings:ApplicationVersion") ?? "1.0.0"}";
            }
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // Mettre le focus sur le champ nom d'utilisateur
            var textBoxUsername = this.Controls.Find("textBoxUsername", true).FirstOrDefault() as TextBox;
            textBoxUsername?.Focus();
        }

        private async void ButtonLogin_Click(object sender, EventArgs e)
        {
            var textBoxUsername = this.Controls.Find("textBoxUsername", true).FirstOrDefault() as TextBox;
            var textBoxPassword = this.Controls.Find("textBoxPassword", true).FirstOrDefault() as TextBox;
            var labelError = this.Controls.Find("labelError", true).FirstOrDefault() as Label;
            var buttonLogin = this.Controls.Find("buttonLogin", true).FirstOrDefault() as Button;

            if (textBoxUsername == null || textBoxPassword == null || labelError == null || buttonLogin == null)
                return;

            string username = textBoxUsername.Text.Trim();
            string password = textBoxPassword.Text;

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                labelError.Text = "Veuillez saisir un nom d'utilisateur et un mot de passe.";
                labelError.Visible = true;
                return;
            }

            // Désactiver le bouton de connexion pendant l'authentification
            buttonLogin.Enabled = false;
            buttonLogin.Text = "Connexion...";
            Application.DoEvents();

            try
            {
                // Authentifier l'utilisateur
                var user = await _authenticationService.AuthenticateAsync(username, password);

                if (user != null)
                {
                    // Authentification réussie
                    Log.Information("Utilisateur {Username} connecté avec succès", username);
                    LoggedInUser = user;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    // Authentification échouée
                    _loginAttempts++;
                    Log.Warning("Échec de connexion pour l'utilisateur {Username} (tentative {Attempt}/{MaxAttempts})",
                        username, _loginAttempts, _maxLoginAttempts);

                    if (_loginAttempts >= _maxLoginAttempts)
                    {
                        // Nombre maximal de tentatives atteint
                        MessageBox.Show(
                            "Nombre maximal de tentatives de connexion atteint. L'application va se fermer.",
                            "Erreur de connexion",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Error);

                        this.DialogResult = DialogResult.Cancel;
                        this.Close();
                    }
                    else
                    {
                        // Afficher le message d'erreur
                        labelError.Text = $"Nom d'utilisateur ou mot de passe incorrect. Tentative {_loginAttempts}/{_maxLoginAttempts}";
                        labelError.Visible = true;
                        textBoxPassword.Clear();
                        textBoxPassword.Focus();
                    }
                }
            }
            catch (Exception ex)
            {
                // Erreur lors de l'authentification
                Log.Error(ex, "Erreur lors de l'authentification de l'utilisateur {Username}", username);
                labelError.Text = "Une erreur s'est produite lors de la connexion. Veuillez réessayer.";
                labelError.Visible = true;
            }
            finally
            {
                // Réactiver le bouton de connexion
                buttonLogin.Enabled = true;
                buttonLogin.Text = "Se connecter";
            }
        }
    }
}
