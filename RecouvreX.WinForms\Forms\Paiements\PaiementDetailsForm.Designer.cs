namespace RecouvreX.WinForms.Forms.Paiements
{
    partial class PaiementDetailsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // PaiementDetailsForm
            //
            this.ClientSize = new System.Drawing.Size(800, 500);
            this.Name = "PaiementDetailsForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Détails du paiement";
            this.Load += new System.EventHandler(this.PaiementDetailsForm_Load);
            this.ResumeLayout(false);

            // Panneau principal
            System.Windows.Forms.TableLayoutPanel mainPanel = new System.Windows.Forms.TableLayoutPanel();
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Padding = new System.Windows.Forms.Padding(10);
            mainPanel.ColumnCount = 1;
            mainPanel.RowCount = 2;
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 200F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.Controls.Add(mainPanel);

            // Panneau d'informations du paiement
            System.Windows.Forms.GroupBox paiementInfoGroupBox = new System.Windows.Forms.GroupBox();
            paiementInfoGroupBox.Text = "Informations du paiement";
            paiementInfoGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(paiementInfoGroupBox, 0, 0);

            System.Windows.Forms.TableLayoutPanel paiementInfoPanel = new System.Windows.Forms.TableLayoutPanel();
            paiementInfoPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            paiementInfoPanel.Padding = new System.Windows.Forms.Padding(5);
            paiementInfoPanel.ColumnCount = 4;
            paiementInfoPanel.RowCount = 3;
            paiementInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            paiementInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            paiementInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            paiementInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            paiementInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            paiementInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            paiementInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            paiementInfoGroupBox.Controls.Add(paiementInfoPanel);

            // Ligne 1
            System.Windows.Forms.Label referenceLabel = new System.Windows.Forms.Label();
            referenceLabel.Text = "Référence :";
            referenceLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            paiementInfoPanel.Controls.Add(referenceLabel, 0, 0);

            System.Windows.Forms.Label referenceValueLabel = new System.Windows.Forms.Label();
            referenceValueLabel.Name = "referenceValueLabel";
            referenceValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            referenceValueLabel.Font = new System.Drawing.Font(referenceValueLabel.Font, System.Drawing.FontStyle.Bold);
            paiementInfoPanel.Controls.Add(referenceValueLabel, 1, 0);

            System.Windows.Forms.Label clientLabel = new System.Windows.Forms.Label();
            clientLabel.Text = "Client :";
            clientLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            paiementInfoPanel.Controls.Add(clientLabel, 2, 0);

            System.Windows.Forms.Label clientValueLabel = new System.Windows.Forms.Label();
            clientValueLabel.Name = "clientValueLabel";
            clientValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientValueLabel.Font = new System.Drawing.Font(clientValueLabel.Font, System.Drawing.FontStyle.Bold);
            paiementInfoPanel.Controls.Add(clientValueLabel, 3, 0);

            // Ligne 2
            System.Windows.Forms.Label datePaiementLabel = new System.Windows.Forms.Label();
            datePaiementLabel.Text = "Date de paiement :";
            datePaiementLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            paiementInfoPanel.Controls.Add(datePaiementLabel, 0, 1);

            System.Windows.Forms.Label datePaiementValueLabel = new System.Windows.Forms.Label();
            datePaiementValueLabel.Name = "datePaiementValueLabel";
            datePaiementValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            paiementInfoPanel.Controls.Add(datePaiementValueLabel, 1, 1);

            System.Windows.Forms.Label montantLabel = new System.Windows.Forms.Label();
            montantLabel.Text = "Montant :";
            montantLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            paiementInfoPanel.Controls.Add(montantLabel, 2, 1);

            System.Windows.Forms.Label montantValueLabel = new System.Windows.Forms.Label();
            montantValueLabel.Name = "montantValueLabel";
            montantValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            montantValueLabel.Font = new System.Drawing.Font(montantValueLabel.Font, System.Drawing.FontStyle.Bold);
            paiementInfoPanel.Controls.Add(montantValueLabel, 3, 1);

            // Ligne 3
            System.Windows.Forms.Label modePaiementLabel = new System.Windows.Forms.Label();
            modePaiementLabel.Text = "Mode de paiement :";
            modePaiementLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            paiementInfoPanel.Controls.Add(modePaiementLabel, 0, 2);

            System.Windows.Forms.Label modePaiementValueLabel = new System.Windows.Forms.Label();
            modePaiementValueLabel.Name = "modePaiementValueLabel";
            modePaiementValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            paiementInfoPanel.Controls.Add(modePaiementValueLabel, 1, 2);

            System.Windows.Forms.Label referenceBancaireLabel = new System.Windows.Forms.Label();
            referenceBancaireLabel.Text = "Référence bancaire :";
            referenceBancaireLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            paiementInfoPanel.Controls.Add(referenceBancaireLabel, 2, 2);

            System.Windows.Forms.Label referenceBancaireValueLabel = new System.Windows.Forms.Label();
            referenceBancaireValueLabel.Name = "referenceBancaireValueLabel";
            referenceBancaireValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            paiementInfoPanel.Controls.Add(referenceBancaireValueLabel, 3, 2);

            // Panneau des factures associées
            System.Windows.Forms.GroupBox facturesGroupBox = new System.Windows.Forms.GroupBox();
            facturesGroupBox.Text = "Factures associées";
            facturesGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(facturesGroupBox, 0, 1);

            System.Windows.Forms.TableLayoutPanel facturesPanel = new System.Windows.Forms.TableLayoutPanel();
            facturesPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            facturesPanel.Padding = new System.Windows.Forms.Padding(5);
            facturesPanel.ColumnCount = 1;
            facturesPanel.RowCount = 2;
            facturesPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            facturesPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            facturesPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            facturesGroupBox.Controls.Add(facturesPanel);

            // Barre d'outils des factures
            System.Windows.Forms.ToolStrip facturesToolStrip = new System.Windows.Forms.ToolStrip();
            facturesToolStrip.Name = "facturesToolStrip";
            facturesToolStrip.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
            facturesPanel.Controls.Add(facturesToolStrip, 0, 0);

            System.Windows.Forms.ToolStripButton associateFactureButton = new System.Windows.Forms.ToolStripButton();
            associateFactureButton.Name = "associateFactureButton";
            associateFactureButton.Text = "Associer une facture";
            associateFactureButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            associateFactureButton.Click += new System.EventHandler(this.AssociateFactureButton_Click);
            facturesToolStrip.Items.Add(associateFactureButton);

            System.Windows.Forms.ToolStripButton dissociateFactureButton = new System.Windows.Forms.ToolStripButton();
            dissociateFactureButton.Name = "dissociateFactureButton";
            dissociateFactureButton.Text = "Dissocier";
            dissociateFactureButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            dissociateFactureButton.Click += new System.EventHandler(this.DissociateFactureButton_Click);
            facturesToolStrip.Items.Add(dissociateFactureButton);

            System.Windows.Forms.ToolStripButton viewFactureButton = new System.Windows.Forms.ToolStripButton();
            viewFactureButton.Name = "viewFactureButton";
            viewFactureButton.Text = "Voir la facture";
            viewFactureButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            viewFactureButton.Click += new System.EventHandler(this.ViewFactureButton_Click);
            facturesToolStrip.Items.Add(viewFactureButton);

            // DataGridView des factures
            System.Windows.Forms.DataGridView facturesDataGridView = new System.Windows.Forms.DataGridView();
            facturesDataGridView.Name = "facturesDataGridView";
            facturesDataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            facturesDataGridView.AllowUserToAddRows = false;
            facturesDataGridView.AllowUserToDeleteRows = false;
            facturesDataGridView.ReadOnly = true;
            facturesDataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            facturesDataGridView.MultiSelect = false;
            facturesDataGridView.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            facturesDataGridView.RowHeadersVisible = false;
            facturesDataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.FacturesDataGridView_CellDoubleClick);
            facturesPanel.Controls.Add(facturesDataGridView, 0, 1);
        }

        #endregion
    }
}
