using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using RecouvreX.Business.Helpers;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des communications
    /// </summary>
    public class CommunicationService : ICommunicationService
    {
        private readonly ICommunicationRepository _communicationRepository;
        private readonly IFactureRepository _factureRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IContactClientRepository _contactClientRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        public CommunicationService(
            ICommunicationRepository communicationRepository,
            IFactureRepository factureRepository,
            IClientRepository clientRepository,
            IContactClientRepository contactClientRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _communicationRepository = communicationRepository ?? throw new ArgumentNullException(nameof(communicationRepository));
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _contactClientRepository = contactClientRepository ?? throw new ArgumentNullException(nameof(contactClientRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère toutes les communications
        /// </summary>
        /// <returns>Liste des communications</returns>
        public async Task<IEnumerable<Communication>> GetAllAsync()
        {
            return await _communicationRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère une communication par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la communication</param>
        /// <returns>Communication trouvée ou null</returns>
        public async Task<Communication> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _communicationRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère les communications par facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des communications pour la facture spécifiée</returns>
        public async Task<IEnumerable<Communication>> GetByFactureIdAsync(int factureId)
        {
            if (factureId <= 0)
                return new List<Communication>();

            return await _communicationRepository.GetByFactureIdAsync(factureId);
        }

        /// <summary>
        /// Récupère les communications par client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des communications pour le client spécifié</returns>
        public async Task<IEnumerable<Communication>> GetByClientIdAsync(int clientId)
        {
            if (clientId <= 0)
                return new List<Communication>();

            return await _communicationRepository.GetByClientIdAsync(clientId);
        }

        /// <summary>
        /// Récupère les communications par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des communications pour l'utilisateur spécifié</returns>
        public async Task<IEnumerable<Communication>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            if (utilisateurId <= 0)
                return new List<Communication>();

            return await _communicationRepository.GetByUtilisateurIdAsync(utilisateurId);
        }

        /// <summary>
        /// Récupère les communications par type
        /// </summary>
        /// <param name="type">Type de communication</param>
        /// <returns>Liste des communications du type spécifié</returns>
        public async Task<IEnumerable<Communication>> GetByTypeAsync(string type)
        {
            if (string.IsNullOrEmpty(type))
                return new List<Communication>();

            return await _communicationRepository.GetByTypeAsync(type);
        }

        /// <summary>
        /// Récupère les communications par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des communications pour la période spécifiée</returns>
        public async Task<IEnumerable<Communication>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            return await _communicationRepository.GetByPeriodAsync(dateDebut, dateFin);
        }

        /// <summary>
        /// Récupère les communications nécessitant un suivi
        /// </summary>
        /// <returns>Liste des communications nécessitant un suivi</returns>
        public async Task<IEnumerable<Communication>> GetRequiringSuiviAsync()
        {
            return await _communicationRepository.GetRequiringSuiviAsync();
        }

        /// <summary>
        /// Crée une nouvelle communication
        /// </summary>
        /// <param name="communication">Communication à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Communication créée avec son identifiant généré</returns>
        public async Task<Communication> CreateAsync(Communication communication, int creePar)
        {
            if (communication == null)
                throw new ArgumentNullException(nameof(communication));

            if (creePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que la facture existe
            var facture = await _factureRepository.GetByIdAsync(communication.FactureId);
            if (facture == null)
                throw new InvalidOperationException($"La facture avec l'ID {communication.FactureId} n'existe pas");

            // Vérifier que l'utilisateur existe
            var utilisateur = await _utilisateurRepository.GetByIdAsync(creePar);
            if (utilisateur == null)
                throw new InvalidOperationException($"L'utilisateur avec l'ID {creePar} n'existe pas");

            // Vérifier que le contact client existe (si spécifié)
            if (communication.ContactClientId.HasValue)
            {
                var contactClient = await _contactClientRepository.GetByIdAsync(communication.ContactClientId.Value);
                if (contactClient == null)
                    throw new InvalidOperationException($"Le contact client avec l'ID {communication.ContactClientId.Value} n'existe pas");
            }

            // Définir les valeurs par défaut
            communication.UtilisateurId = creePar;
            communication.DateCommunication = communication.DateCommunication == default ? DateTime.Now : communication.DateCommunication;

            // Créer la communication
            var createdCommunication = await _communicationRepository.AddAsync(communication, creePar);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "Communication",
                EntiteId = createdCommunication.Id,
                TypeAction = "Création",
                UtilisateurId = creePar,
                DateAction = DateTime.Now,
                Description = $"Création d'une communication de type {createdCommunication.Type:s} pour la facture {facture.Numero:s}",
                DonneesAvant = null,
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdCommunication.Id,
                    createdCommunication.FactureId,
                    FactureNumero = facture.Numero,
                    createdCommunication.Type,
                    createdCommunication.Direction,
                    createdCommunication.DateCommunication,
                    createdCommunication.Objet,
                    createdCommunication.Contenu,
                    createdCommunication.Resultat,
                    createdCommunication.ContactClientId,
                    createdCommunication.UtilisateurId
                })
            });

            return createdCommunication;
        }

        /// <summary>
        /// Met à jour une communication existante
        /// </summary>
        /// <param name="communication">Communication à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Communication mise à jour</returns>
        public async Task<Communication> UpdateAsync(Communication communication, int modifiePar)
        {
            if (communication == null)
                throw new ArgumentNullException(nameof(communication));

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que la communication existe
            var existingCommunication = await _communicationRepository.GetByIdAsync(communication.Id);
            if (existingCommunication == null)
                throw new InvalidOperationException($"La communication avec l'ID {communication.Id} n'existe pas");

            // Vérifier que la facture existe
            var facture = await _factureRepository.GetByIdAsync(communication.FactureId);
            if (facture == null)
                throw new InvalidOperationException($"La facture avec l'ID {communication.FactureId} n'existe pas");

            // Vérifier que l'utilisateur existe
            var utilisateur = await _utilisateurRepository.GetByIdAsync(modifiePar);
            if (utilisateur == null)
                throw new InvalidOperationException($"L'utilisateur avec l'ID {modifiePar} n'existe pas");

            // Vérifier que le contact client existe (si spécifié)
            if (communication.ContactClientId.HasValue)
            {
                var contactClient = await _contactClientRepository.GetByIdAsync(communication.ContactClientId.Value);
                if (contactClient == null)
                    throw new InvalidOperationException($"Le contact client avec l'ID {communication.ContactClientId.Value} n'existe pas");
            }

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "Communication",
                EntiteId = communication.Id,
                TypeAction = "Modification",
                UtilisateurId = modifiePar,
                DateAction = DateTime.Now,
                Description = $"Modification d'une communication de type {communication.Type:s} pour la facture {facture.Numero:s}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingCommunication.Id,
                    existingCommunication.FactureId,
                    FactureNumero = facture.Numero,
                    existingCommunication.Type,
                    existingCommunication.Direction,
                    existingCommunication.DateCommunication,
                    existingCommunication.Objet,
                    existingCommunication.Contenu,
                    existingCommunication.Resultat,
                    existingCommunication.ContactClientId,
                    existingCommunication.UtilisateurId
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    communication.Id,
                    communication.FactureId,
                    FactureNumero = facture.Numero,
                    communication.Type,
                    communication.Direction,
                    communication.DateCommunication,
                    communication.Objet,
                    communication.Contenu,
                    communication.Resultat,
                    communication.ContactClientId,
                    communication.UtilisateurId
                })
            });

            // Mettre à jour la communication
            return await _communicationRepository.UpdateAsync(communication, modifiePar);
        }

        /// <summary>
        /// Supprime une communication
        /// </summary>
        /// <param name="id">Identifiant de la communication à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant de la communication ne peut pas être négatif ou nul");

            if (supprimePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que la communication existe
            var communication = await _communicationRepository.GetByIdAsync(id);
            if (communication == null)
                return false;

            // Récupérer la facture pour le journal d'audit
            var facture = await _factureRepository.GetByIdAsync(communication.FactureId);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "Communication",
                EntiteId = id,
                TypeAction = "Suppression",
                UtilisateurId = supprimePar,
                DateAction = DateTime.Now,
                Description = $"Suppression d'une communication de type {communication.Type:s} pour la facture {facture?.Numero ?? communication.FactureId.ToString()}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    communication.Id,
                    communication.FactureId,
                    FactureNumero = facture?.Numero,
                    communication.Type,
                    communication.Direction,
                    communication.DateCommunication,
                    communication.Objet,
                    communication.Contenu,
                    communication.Resultat,
                    communication.ContactClientId,
                    communication.UtilisateurId
                }),
                DonneesApres = null
            });

            // Supprimer la communication
            return await _communicationRepository.DeleteAsync(id, supprimePar);
        }

        /// <summary>
        /// Marque une communication comme lue
        /// </summary>
        /// <param name="id">Identifiant de la communication</param>
        /// <param name="dateLecture">Date de lecture (par défaut, la date actuelle)</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> MarkAsReadAsync(int id, DateTime? dateLecture = null)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant de la communication ne peut pas être négatif ou nul");

            return await _communicationRepository.MarkAsReadAsync(id, dateLecture ?? DateTime.Now);
        }

        /// <summary>
        /// Met à jour le statut de suivi d'une communication
        /// </summary>
        /// <param name="id">Identifiant de la communication</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="notesSuivi">Notes de suivi</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateSuiviAsync(int id, bool suiviNecessaire, DateTime? dateSuivi, string notesSuivi, int modifiePar)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant de la communication ne peut pas être négatif ou nul");

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            return await _communicationRepository.UpdateSuiviAsync(id, suiviNecessaire, dateSuivi, notesSuivi, modifiePar);
        }

        /// <summary>
        /// Enregistre un appel téléphonique
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="contactClientId">Identifiant du contact client (optionnel)</param>
        /// <param name="direction">Direction de l'appel (Entrant/Sortant)</param>
        /// <param name="duree">Durée de l'appel en secondes</param>
        /// <param name="resultat">Résultat de l'appel</param>
        /// <param name="contenu">Contenu de l'appel</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Communication créée avec son identifiant généré</returns>
        public async Task<Communication> EnregistrerAppelAsync(int factureId, int? contactClientId, string direction, int duree, string resultat, string contenu, bool suiviNecessaire, DateTime? dateSuivi, int utilisateurId)
        {
            var communication = new Communication
            {
                FactureId = factureId,
                ContactClientId = contactClientId,
                Type = TypeCommunication.Appel,
                Direction = direction,
                DateCommunication = DateTime.Now,
                Duree = duree,
                Resultat = resultat,
                Contenu = contenu,
                SuiviNecessaire = suiviNecessaire,
                DateSuivi = dateSuivi,
                UtilisateurId = utilisateurId
            };

            return await CreateAsync(communication, utilisateurId);
        }

        /// <summary>
        /// Enregistre un email
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="contactClientId">Identifiant du contact client (optionnel)</param>
        /// <param name="direction">Direction de l'email (Entrant/Sortant)</param>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="contenu">Contenu de l'email</param>
        /// <param name="cheminFichier">Chemin vers le fichier attaché (optionnel)</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Communication créée avec son identifiant généré</returns>
        public async Task<Communication> EnregistrerEmailAsync(int factureId, int? contactClientId, string direction, string objet, string contenu, string cheminFichier, bool suiviNecessaire, DateTime? dateSuivi, int utilisateurId)
        {
            var communication = new Communication
            {
                FactureId = factureId,
                ContactClientId = contactClientId,
                Type = TypeCommunication.Email,
                Direction = direction,
                DateCommunication = DateTime.Now,
                Objet = objet,
                Contenu = contenu,
                CheminFichier = cheminFichier,
                SuiviNecessaire = suiviNecessaire,
                DateSuivi = dateSuivi,
                UtilisateurId = utilisateurId
            };

            return await CreateAsync(communication, utilisateurId);
        }

        /// <summary>
        /// Ajoute une note
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="contenu">Contenu de la note</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Communication créée avec son identifiant généré</returns>
        public async Task<Communication> AjouterNoteAsync(int factureId, string contenu, bool suiviNecessaire, DateTime? dateSuivi, int utilisateurId)
        {
            var communication = new Communication
            {
                FactureId = factureId,
                Type = TypeCommunication.Note,
                Direction = DirectionCommunication.Interne,
                DateCommunication = DateTime.Now,
                Contenu = contenu,
                SuiviNecessaire = suiviNecessaire,
                DateSuivi = dateSuivi,
                UtilisateurId = utilisateurId
            };

            return await CreateAsync(communication, utilisateurId);
        }

        /// <summary>
        /// Enregistre un SMS ou un message WhatsApp
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="contactClientId">Identifiant du contact client (optionnel)</param>
        /// <param name="direction">Direction du message (Entrant/Sortant)</param>
        /// <param name="contenu">Contenu du message</param>
        /// <param name="resultat">Résultat de l'envoi</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Communication créée avec son identifiant généré</returns>
        public async Task<Communication> EnregistrerSMSAsync(int factureId, int? contactClientId, string direction, string contenu, string resultat, bool suiviNecessaire, DateTime? dateSuivi, int utilisateurId)
        {
            var communication = new Communication
            {
                FactureId = factureId,
                ContactClientId = contactClientId,
                Type = TypeCommunication.SMS,
                Direction = direction,
                DateCommunication = DateTime.Now,
                Contenu = contenu,
                Resultat = resultat,
                SuiviNecessaire = suiviNecessaire,
                DateSuivi = dateSuivi,
                UtilisateurId = utilisateurId
            };

            return await CreateAsync(communication, utilisateurId);
        }

        /// <summary>
        /// Envoie un email et l'enregistre dans les communications
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="contactClientId">Identifiant du contact client</param>
        /// <param name="destinataire">Adresse email du destinataire</param>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="contenu">Contenu de l'email</param>
        /// <param name="piecesJointes">Liste des pièces jointes (optionnel)</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <param name="configuration">Configuration de l'application</param>
        /// <returns>Tuple contenant la communication créée et un booléen indiquant si l'envoi a réussi</returns>
        public async Task<(Communication Communication, bool EmailEnvoye)> EnvoyerEtEnregistrerEmailAsync(
            int factureId,
            int? contactClientId,
            string destinataire,
            string objet,
            string contenu,
            List<string> piecesJointes,
            bool suiviNecessaire,
            DateTime? dateSuivi,
            int utilisateurId,
            IConfiguration configuration)
        {
            // Vérifier les paramètres
            if (factureId <= 0)
                throw new ArgumentException("L'identifiant de la facture ne peut pas être négatif ou nul");

            if (string.IsNullOrEmpty(destinataire))
                throw new ArgumentException("L'adresse email du destinataire est obligatoire");

            if (string.IsNullOrEmpty(objet))
                throw new ArgumentException("L'objet de l'email est obligatoire");

            if (string.IsNullOrEmpty(contenu))
                throw new ArgumentException("Le contenu de l'email est obligatoire");

            if (utilisateurId <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            // Envoyer l'email
            bool emailEnvoye = await EmailHelper.SendEmailAsync(objet, contenu, destinataire, configuration, piecesJointes);

            // Enregistrer l'email dans les communications
            var communication = await EnregistrerEmailAsync(
                factureId,
                contactClientId,
                DirectionCommunication.Sortant,
                objet,
                contenu,
                piecesJointes != null && piecesJointes.Count > 0 ? string.Join(";", piecesJointes) : null,
                suiviNecessaire,
                dateSuivi,
                utilisateurId);

            // Mettre à jour le statut d'envoi de l'email
            if (emailEnvoye)
            {
                communication.Resultat = "Email envoyé avec succès";
            }
            else
            {
                communication.Resultat = "Échec de l'envoi de l'email";
            }

            // Mettre à jour la communication
            await UpdateAsync(communication, utilisateurId);

            return (communication, emailEnvoye);
        }
    }
}
