using RecouvreX.Business.Helpers;
using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.Configuration;

namespace RecouvreX.WinForms.Forms.Relances
{
    /// <summary>
    /// Formulaire pour la génération et l'envoi de lettres de relance
    /// </summary>
    public partial class LettreRelanceForm : Form
    {
        private readonly IFactureService _factureService;
        private readonly IClientService _clientService;
        private readonly ICommunicationService _communicationService;
        private readonly IContactClientService _contactClientService;
        private readonly IConfiguration _configuration;
        private readonly int _currentUserId;
        private readonly int _factureId;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();
        private Facture _facture;
        private Client _client;
        private string _pdfPath;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="factureService">Service de gestion des factures</param>
        /// <param name="clientService">Service de gestion des clients</param>
        /// <param name="communicationService">Service de gestion des communications</param>
        /// <param name="contactClientService">Service de gestion des contacts client</param>
        /// <param name="configuration">Configuration de l'application</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        /// <param name="factureId">Identifiant de la facture</param>
        public LettreRelanceForm(
            IFactureService factureService,
            IClientService clientService,
            ICommunicationService communicationService,
            IContactClientService contactClientService,
            IConfiguration configuration,
            int currentUserId,
            int factureId)
        {
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _contactClientService = contactClientService ?? throw new ArgumentNullException(nameof(contactClientService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _currentUserId = currentUserId;
            _factureId = factureId;

            InitializeComponent();

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;
        }

        // Les méthodes InitializeComponent() et CreateControls() ont été déplacées dans le fichier LettreRelanceForm.Designer.cs

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void LettreRelanceForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Charger l'icône du formulaire
                try
                {
                    this.Icon = new Icon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "relance.ico"));
                }
                catch
                {
                    // Ignorer les erreurs d'icône
                }

                // Charger les données de la facture
                _facture = await _factureService.GetByIdAsync(_factureId);
                if (_facture == null)
                {
                    MessageBox.Show("La facture demandée n'existe pas ou a été supprimée.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Charger les données du client
                _client = await _clientService.GetByIdAsync(_facture.ClientId);
                if (_client == null)
                {
                    MessageBox.Show("Le client associé à cette facture n'existe pas ou a été supprimé.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Afficher les informations de la facture
                var factureValueLabel = this.Controls.Find("factureValueLabel", true).FirstOrDefault() as Label;
                if (factureValueLabel != null)
                {
                    factureValueLabel.Text = $"{_facture.Numero} - {_facture.MontantTotal:C2} - Échéance : {_facture.DateEcheance:dd/MM/yyyy}";
                }

                // Afficher les informations du client
                var clientValueLabel = this.Controls.Find("clientValueLabel", true).FirstOrDefault() as Label;
                if (clientValueLabel != null)
                {
                    clientValueLabel.Text = $"{_client.RaisonSociale} - {_client.Email}";
                }

                // Charger les contacts du client
                await LoadContactsAsync();

                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire de génération de lettre de relance");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Cursor.Current = Cursors.Default;
            }
        }

        /// <summary>
        /// Chargement des contacts du client
        /// </summary>
        private async Task LoadContactsAsync()
        {
            try
            {
                // Récupérer les contacts du client
                var contacts = await _contactClientService.GetByClientIdAsync(_client.Id);

                // Remplir la liste déroulante des destinataires
                var destinataireComboBox = this.Controls.Find("destinataireComboBox", true).FirstOrDefault() as ComboBox;
                if (destinataireComboBox != null)
                {
                    destinataireComboBox.Items.Clear();
                    destinataireComboBox.DisplayMember = "NomComplet";
                    destinataireComboBox.ValueMember = "Id";

                    foreach (var contact in contacts)
                    {
                        if (!string.IsNullOrEmpty(contact.Email))
                        {
                            destinataireComboBox.Items.Add(contact);
                        }
                    }

                    // Sélectionner le contact principal s'il existe
                    var contactPrincipal = contacts.FirstOrDefault(c => c.EstPrincipal);
                    if (contactPrincipal != null && !string.IsNullOrEmpty(contactPrincipal.Email))
                    {
                        destinataireComboBox.SelectedItem = contactPrincipal;
                    }
                    else if (destinataireComboBox.Items.Count > 0)
                    {
                        destinataireComboBox.SelectedIndex = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des contacts du client");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des contacts : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Aperçu"
        /// </summary>
        private void PreviewButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer le niveau de relance
                var niveauComboBox = this.Controls.Find("niveauComboBox", true).FirstOrDefault() as ComboBox;
                int niveau = niveauComboBox?.SelectedIndex + 1 ?? 1;

                // Générer le contenu en fonction du niveau de relance
                string contenu = string.Empty;
                string objet = string.Empty;

                switch (niveau)
                {
                    case 1:
                        objet = $"Rappel de paiement - Facture {_facture.Numero}";
                        contenu = $"Nous constatons que votre facture n°{_facture.Numero} d'un montant de {_facture.MontantTotal:C2} émise le {_facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {_facture.DateEcheance:dd/MM/yyyy} n'a pas encore été réglée.\n\n" +
                                 $"Nous vous prions de bien vouloir procéder au règlement de cette facture dans les plus brefs délais.\n\n" +
                                 $"Si votre paiement est en cours, nous vous prions de ne pas tenir compte de ce rappel.";
                        break;

                    case 2:
                        objet = $"Deuxième rappel de paiement - Facture {_facture.Numero}";
                        contenu = $"Malgré notre précédent rappel, nous constatons que votre facture n°{_facture.Numero} d'un montant de {_facture.MontantTotal:C2} émise le {_facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {_facture.DateEcheance:dd/MM/yyyy} n'a toujours pas été réglée.\n\n" +
                                 $"Nous vous prions de bien vouloir procéder au règlement de cette facture sous 8 jours.\n\n" +
                                 $"Si votre paiement est en cours, nous vous prions de ne pas tenir compte de ce rappel.";
                        break;

                    case 3:
                        objet = $"Mise en demeure - Facture {_facture.Numero}";
                        contenu = $"Malgré nos précédents rappels, nous constatons que votre facture n°{_facture.Numero} d'un montant de {_facture.MontantTotal:C2} émise le {_facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {_facture.DateEcheance:dd/MM/yyyy} n'a toujours pas été réglée.\n\n" +
                                 $"Nous vous mettons en demeure de régler cette facture sous 48 heures. À défaut, nous nous verrons dans l'obligation d'engager une procédure de recouvrement judiciaire, ce qui entraînera des frais supplémentaires à votre charge.\n\n" +
                                 $"Si votre paiement est en cours, nous vous prions de nous en informer immédiatement.";
                        break;
                }

                // Afficher l'aperçu
                MessageBox.Show($"Objet : {objet}\n\n{contenu}", "Aperçu de la lettre de relance", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'affichage de l'aperçu de la lettre de relance");
                MessageBox.Show($"Une erreur s'est produite lors de l'affichage de l'aperçu : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Annuler"
        /// </summary>
        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Générer et envoyer"
        /// </summary>
        private async void GenerateButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer les valeurs du formulaire
                var niveauComboBox = this.Controls.Find("niveauComboBox", true).FirstOrDefault() as ComboBox;
                var emailCheckBox = this.Controls.Find("emailCheckBox", true).FirstOrDefault() as CheckBox;
                var saveCheckBox = this.Controls.Find("saveCheckBox", true).FirstOrDefault() as CheckBox;
                var destinataireComboBox = this.Controls.Find("destinataireComboBox", true).FirstOrDefault() as ComboBox;

                int niveau = niveauComboBox?.SelectedIndex + 1 ?? 1;
                bool envoyerEmail = emailCheckBox?.Checked ?? false;
                bool enregistrerDisque = saveCheckBox?.Checked ?? true;
                var contact = destinataireComboBox?.SelectedItem as ContactClient;

                // Vérifier si l'envoi par email est sélectionné et qu'un destinataire est choisi
                if (envoyerEmail && contact == null)
                {
                    MessageBox.Show("Veuillez sélectionner un destinataire pour l'envoi par email.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Générer le PDF
                string documentsPath = _configuration.GetSection("AppSettings")["DocumentsPath"] ?? "Documents";
                string relancesPath = Path.Combine(documentsPath, "Relances");
                string fileName = $"Relance_{_facture.Numero}_Niveau{niveau}_{DateTime.Now:yyyyMMdd}.pdf";
                _pdfPath = Path.Combine(relancesPath, fileName);

                bool pdfGenere = PdfHelper.GenererLettreRelance(_facture, _client, niveau, _pdfPath);

                if (!pdfGenere)
                {
                    MessageBox.Show("Une erreur s'est produite lors de la génération du PDF.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    Cursor.Current = Cursors.Default;
                    return;
                }

                // Envoyer par email si demandé
                bool emailEnvoye = false;
                if (envoyerEmail && contact != null)
                {
                    // Générer l'objet et le contenu de l'email
                    string objet = string.Empty;
                    string contenu = string.Empty;

                    switch (niveau)
                    {
                        case 1:
                            objet = $"Rappel de paiement - Facture {_facture.Numero}";
                            contenu = $"<p>Bonjour,</p>" +
                                     $"<p>Nous constatons que votre facture n°{_facture.Numero} d'un montant de {_facture.MontantTotal:C2} émise le {_facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {_facture.DateEcheance:dd/MM/yyyy} n'a pas encore été réglée.</p>" +
                                     $"<p>Nous vous prions de bien vouloir procéder au règlement de cette facture dans les plus brefs délais.</p>" +
                                     $"<p>Si votre paiement est en cours, nous vous prions de ne pas tenir compte de ce rappel.</p>" +
                                     $"<p>Cordialement,<br>Service Recouvrement</p>";
                            break;

                        case 2:
                            objet = $"Deuxième rappel de paiement - Facture {_facture.Numero}";
                            contenu = $"<p>Bonjour,</p>" +
                                     $"<p>Malgré notre précédent rappel, nous constatons que votre facture n°{_facture.Numero} d'un montant de {_facture.MontantTotal:C2} émise le {_facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {_facture.DateEcheance:dd/MM/yyyy} n'a toujours pas été réglée.</p>" +
                                     $"<p>Nous vous prions de bien vouloir procéder au règlement de cette facture sous 8 jours.</p>" +
                                     $"<p>Si votre paiement est en cours, nous vous prions de ne pas tenir compte de ce rappel.</p>" +
                                     $"<p>Cordialement,<br>Service Recouvrement</p>";
                            break;

                        case 3:
                            objet = $"Mise en demeure - Facture {_facture.Numero}";
                            contenu = $"<p>Bonjour,</p>" +
                                     $"<p>Malgré nos précédents rappels, nous constatons que votre facture n°{_facture.Numero} d'un montant de {_facture.MontantTotal:C2} émise le {_facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {_facture.DateEcheance:dd/MM/yyyy} n'a toujours pas été réglée.</p>" +
                                     $"<p>Nous vous mettons en demeure de régler cette facture sous 48 heures. À défaut, nous nous verrons dans l'obligation d'engager une procédure de recouvrement judiciaire, ce qui entraînera des frais supplémentaires à votre charge.</p>" +
                                     $"<p>Si votre paiement est en cours, nous vous prions de nous en informer immédiatement.</p>" +
                                     $"<p>Cordialement,<br>Service Recouvrement</p>";
                            break;
                    }

                    // Envoyer l'email avec la pièce jointe
                    var result = await _communicationService.EnvoyerEtEnregistrerEmailAsync(
                        _factureId,
                        contact.Id,
                        contact.Email,
                        objet,
                        contenu,
                        new List<string> { _pdfPath },
                        true,
                        DateTime.Now.AddDays(7),
                        _currentUserId,
                        _configuration);

                    emailEnvoye = result.EmailEnvoye;
                }

                // Restaurer le curseur
                Cursor.Current = Cursors.Default;

                // Afficher un message de confirmation
                if (envoyerEmail && emailEnvoye)
                {
                    MessageBox.Show("La lettre de relance a été générée et envoyée par email avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else if (envoyerEmail && !emailEnvoye)
                {
                    MessageBox.Show("La lettre de relance a été générée mais n'a pas pu être envoyée par email. Vérifiez la configuration SMTP.", "Avertissement", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                else
                {
                    MessageBox.Show($"La lettre de relance a été générée avec succès et enregistrée dans {_pdfPath}.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Fermer le formulaire
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération et de l'envoi de la lettre de relance");
                MessageBox.Show($"Une erreur s'est produite lors de la génération et de l'envoi de la lettre de relance : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Cursor.Current = Cursors.Default;
            }
        }
    }
}
