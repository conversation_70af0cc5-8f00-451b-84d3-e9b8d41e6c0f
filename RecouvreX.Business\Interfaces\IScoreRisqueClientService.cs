using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des scores de risque client
    /// </summary>
    public interface IScoreRisqueClientService
    {
        /// <summary>
        /// Récupère tous les scores de risque client
        /// </summary>
        /// <returns>Liste des scores de risque client</returns>
        Task<IEnumerable<ScoreRisqueClient>> GetAllAsync();

        /// <summary>
        /// Récupère un score de risque client par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du score de risque client</param>
        /// <returns>Score de risque client trouvé ou null</returns>
        Task<ScoreRisqueClient> GetByIdAsync(int id);

        /// <summary>
        /// Récupère le score de risque d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Score de risque du client ou null si non trouvé</returns>
        Task<ScoreRisqueClient> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Calcule et met à jour le score de risque d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Score de risque mis à jour</returns>
        Task<ScoreRisqueClient> CalculateAndUpdateScoreAsync(int clientId, int utilisateurId);

        /// <summary>
        /// Calcule et met à jour les scores de risque de tous les clients
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre de scores mis à jour</returns>
        Task<int> CalculateAndUpdateAllScoresAsync(int utilisateurId);

        /// <summary>
        /// Récupère les clients par catégorie de risque
        /// </summary>
        /// <param name="categorie">Catégorie de risque (A, B, C, D, E)</param>
        /// <returns>Liste des clients dans la catégorie spécifiée</returns>
        Task<IEnumerable<Client>> GetClientsByCategorieAsync(string categorie);

        /// <summary>
        /// Récupère les statistiques de répartition des clients par catégorie de risque
        /// </summary>
        /// <returns>Dictionnaire avec la catégorie comme clé et le nombre de clients comme valeur</returns>
        Task<Dictionary<string, int>> GetClientDistributionByCategorieAsync();
    }
}
