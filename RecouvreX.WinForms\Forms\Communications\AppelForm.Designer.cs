namespace RecouvreX.WinForms.Forms.Communications
{
    partial class AppelForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            mainPanel = new TableLayoutPanel();
            directionLabel = new Label();
            directionComboBox = new ComboBox();
            contactLabel = new Label();
            contactComboBox = new ComboBox();
            dureeLabel = new Label();
            dureePanel = new TableLayoutPanel();
            dureeMinutesNumericUpDown = new NumericUpDown();
            minutesLabel = new Label();
            dureeSecondesNumericUpDown = new NumericUpDown();
            secondesLabel = new Label();
            resultatLabel = new Label();
            resultatComboBox = new ComboBox();
            contenuLabel = new Label();
            contenuTextBox = new TextBox();
            suiviNecessaireLabel = new Label();
            suiviNecessaireCheckBox = new CheckBox();
            dateSuiviLabel = new Label();
            dateSuiviPicker = new DateTimePicker();
            buttonsPanel = new FlowLayoutPanel();
            cancelButton = new Button();
            saveButton = new Button();
            mainPanel.SuspendLayout();
            dureePanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dureeMinutesNumericUpDown).BeginInit();
            ((System.ComponentModel.ISupportInitialize)dureeSecondesNumericUpDown).BeginInit();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.ColumnCount = 2;
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            mainPanel.Controls.Add(directionLabel, 0, 0);
            mainPanel.Controls.Add(directionComboBox, 1, 0);
            mainPanel.Controls.Add(contactLabel, 0, 1);
            mainPanel.Controls.Add(contactComboBox, 1, 1);
            mainPanel.Controls.Add(dureeLabel, 0, 2);
            mainPanel.Controls.Add(dureePanel, 1, 2);
            mainPanel.Controls.Add(resultatLabel, 0, 3);
            mainPanel.Controls.Add(resultatComboBox, 1, 3);
            mainPanel.Controls.Add(contenuLabel, 0, 4);
            mainPanel.Controls.Add(contenuTextBox, 1, 4);
            mainPanel.Controls.Add(suiviNecessaireLabel, 0, 5);
            mainPanel.Controls.Add(suiviNecessaireCheckBox, 1, 5);
            mainPanel.Controls.Add(dateSuiviLabel, 0, 6);
            mainPanel.Controls.Add(dateSuiviPicker, 1, 6);
            mainPanel.Controls.Add(buttonsPanel, 1, 7);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Location = new Point(0, 0);
            mainPanel.Name = "mainPanel";
            mainPanel.Padding = new Padding(10);
            mainPanel.RowCount = 8;
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 150F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            mainPanel.Size = new Size(500, 500);
            mainPanel.TabIndex = 0;
            // 
            // directionLabel
            // 
            directionLabel.Dock = DockStyle.Fill;
            directionLabel.Location = new Point(13, 10);
            directionLabel.Name = "directionLabel";
            directionLabel.Size = new Size(138, 30);
            directionLabel.TabIndex = 0;
            directionLabel.Text = "Direction :";
            directionLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // directionComboBox
            // 
            directionComboBox.Dock = DockStyle.Fill;
            directionComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            directionComboBox.Location = new Point(157, 13);
            directionComboBox.Name = "directionComboBox";
            directionComboBox.Size = new Size(330, 23);
            directionComboBox.TabIndex = 1;
            // 
            // contactLabel
            // 
            contactLabel.Dock = DockStyle.Fill;
            contactLabel.Location = new Point(13, 40);
            contactLabel.Name = "contactLabel";
            contactLabel.Size = new Size(138, 30);
            contactLabel.TabIndex = 2;
            contactLabel.Text = "Contact :";
            contactLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // contactComboBox
            // 
            contactComboBox.Dock = DockStyle.Fill;
            contactComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            contactComboBox.Location = new Point(157, 43);
            contactComboBox.Name = "contactComboBox";
            contactComboBox.Size = new Size(330, 23);
            contactComboBox.TabIndex = 3;
            // 
            // dureeLabel
            // 
            dureeLabel.Dock = DockStyle.Fill;
            dureeLabel.Location = new Point(13, 70);
            dureeLabel.Name = "dureeLabel";
            dureeLabel.Size = new Size(138, 30);
            dureeLabel.TabIndex = 4;
            dureeLabel.Text = "Durée :";
            dureeLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // dureePanel
            // 
            dureePanel.ColumnCount = 4;
            dureePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40F));
            dureePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20F));
            dureePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20F));
            dureePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20F));
            dureePanel.Controls.Add(dureeMinutesNumericUpDown, 0, 0);
            dureePanel.Controls.Add(minutesLabel, 1, 0);
            dureePanel.Controls.Add(dureeSecondesNumericUpDown, 2, 0);
            dureePanel.Controls.Add(secondesLabel, 3, 0);
            dureePanel.Dock = DockStyle.Fill;
            dureePanel.Location = new Point(157, 73);
            dureePanel.Name = "dureePanel";
            dureePanel.RowCount = 1;
            dureePanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            dureePanel.Size = new Size(330, 24);
            dureePanel.TabIndex = 5;
            // 
            // dureeMinutesNumericUpDown
            // 
            dureeMinutesNumericUpDown.Dock = DockStyle.Fill;
            dureeMinutesNumericUpDown.Location = new Point(3, 3);
            dureeMinutesNumericUpDown.Maximum = new decimal(new int[] { 999, 0, 0, 0 });
            dureeMinutesNumericUpDown.Name = "dureeMinutesNumericUpDown";
            dureeMinutesNumericUpDown.Size = new Size(126, 23);
            dureeMinutesNumericUpDown.TabIndex = 0;
            // 
            // minutesLabel
            // 
            minutesLabel.Dock = DockStyle.Fill;
            minutesLabel.Location = new Point(135, 0);
            minutesLabel.Name = "minutesLabel";
            minutesLabel.Size = new Size(60, 24);
            minutesLabel.TabIndex = 1;
            minutesLabel.Text = "minutes";
            minutesLabel.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // dureeSecondesNumericUpDown
            // 
            dureeSecondesNumericUpDown.Dock = DockStyle.Fill;
            dureeSecondesNumericUpDown.Location = new Point(201, 3);
            dureeSecondesNumericUpDown.Maximum = new decimal(new int[] { 59, 0, 0, 0 });
            dureeSecondesNumericUpDown.Name = "dureeSecondesNumericUpDown";
            dureeSecondesNumericUpDown.Size = new Size(60, 23);
            dureeSecondesNumericUpDown.TabIndex = 2;
            // 
            // secondesLabel
            // 
            secondesLabel.Dock = DockStyle.Fill;
            secondesLabel.Location = new Point(267, 0);
            secondesLabel.Name = "secondesLabel";
            secondesLabel.Size = new Size(60, 24);
            secondesLabel.TabIndex = 3;
            secondesLabel.Text = "secondes";
            secondesLabel.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // resultatLabel
            // 
            resultatLabel.Dock = DockStyle.Fill;
            resultatLabel.Location = new Point(13, 100);
            resultatLabel.Name = "resultatLabel";
            resultatLabel.Size = new Size(138, 30);
            resultatLabel.TabIndex = 6;
            resultatLabel.Text = "Résultat :";
            resultatLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // resultatComboBox
            // 
            resultatComboBox.Dock = DockStyle.Fill;
            resultatComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            resultatComboBox.Location = new Point(157, 103);
            resultatComboBox.Name = "resultatComboBox";
            resultatComboBox.Size = new Size(330, 23);
            resultatComboBox.TabIndex = 7;
            // 
            // contenuLabel
            // 
            contenuLabel.Dock = DockStyle.Fill;
            contenuLabel.Location = new Point(13, 130);
            contenuLabel.Name = "contenuLabel";
            contenuLabel.Size = new Size(138, 150);
            contenuLabel.TabIndex = 8;
            contenuLabel.Text = "Contenu :";
            contenuLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // contenuTextBox
            // 
            contenuTextBox.Dock = DockStyle.Fill;
            contenuTextBox.Location = new Point(157, 133);
            contenuTextBox.Multiline = true;
            contenuTextBox.Name = "contenuTextBox";
            contenuTextBox.ScrollBars = ScrollBars.Vertical;
            contenuTextBox.Size = new Size(330, 144);
            contenuTextBox.TabIndex = 9;
            // 
            // suiviNecessaireLabel
            // 
            suiviNecessaireLabel.Dock = DockStyle.Fill;
            suiviNecessaireLabel.Location = new Point(13, 280);
            suiviNecessaireLabel.Name = "suiviNecessaireLabel";
            suiviNecessaireLabel.Size = new Size(138, 30);
            suiviNecessaireLabel.TabIndex = 10;
            suiviNecessaireLabel.Text = "Suivi nécessaire :";
            suiviNecessaireLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // suiviNecessaireCheckBox
            // 
            suiviNecessaireCheckBox.Dock = DockStyle.Fill;
            suiviNecessaireCheckBox.Location = new Point(157, 283);
            suiviNecessaireCheckBox.Name = "suiviNecessaireCheckBox";
            suiviNecessaireCheckBox.Size = new Size(330, 24);
            suiviNecessaireCheckBox.TabIndex = 11;
            suiviNecessaireCheckBox.Text = "Oui";
            suiviNecessaireCheckBox.CheckedChanged += SuiviNecessaireCheckBox_CheckedChanged;
            // 
            // dateSuiviLabel
            // 
            dateSuiviLabel.Dock = DockStyle.Fill;
            dateSuiviLabel.Enabled = false;
            dateSuiviLabel.Location = new Point(13, 310);
            dateSuiviLabel.Name = "dateSuiviLabel";
            dateSuiviLabel.Size = new Size(138, 30);
            dateSuiviLabel.TabIndex = 12;
            dateSuiviLabel.Text = "Date de suivi :";
            dateSuiviLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // dateSuiviPicker
            // 
            dateSuiviPicker.Checked = false;
            dateSuiviPicker.Dock = DockStyle.Fill;
            dateSuiviPicker.Enabled = false;
            dateSuiviPicker.Format = DateTimePickerFormat.Short;
            dateSuiviPicker.Location = new Point(157, 313);
            dateSuiviPicker.Name = "dateSuiviPicker";
            dateSuiviPicker.ShowCheckBox = true;
            dateSuiviPicker.Size = new Size(330, 23);
            dateSuiviPicker.TabIndex = 13;
            // 
            // buttonsPanel
            // 
            buttonsPanel.Controls.Add(cancelButton);
            buttonsPanel.Controls.Add(saveButton);
            buttonsPanel.Dock = DockStyle.Fill;
            buttonsPanel.FlowDirection = FlowDirection.RightToLeft;
            buttonsPanel.Location = new Point(157, 343);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.Size = new Size(330, 144);
            buttonsPanel.TabIndex = 14;
            // 
            // cancelButton
            // 
            cancelButton.Location = new Point(227, 3);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(100, 30);
            cancelButton.TabIndex = 0;
            cancelButton.Text = "Annuler";
            cancelButton.Click += CancelButton_Click;
            // 
            // saveButton
            // 
            saveButton.Location = new Point(121, 3);
            saveButton.Name = "saveButton";
            saveButton.Size = new Size(100, 30);
            saveButton.TabIndex = 1;
            saveButton.Text = "Enregistrer";
            saveButton.Click += SaveButton_Click;
            // 
            // AppelForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(500, 500);
            Controls.Add(mainPanel);
            Name = "AppelForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Nouvel appel";
            Load += AppelForm_Load;
            mainPanel.ResumeLayout(false);
            mainPanel.PerformLayout();
            dureePanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dureeMinutesNumericUpDown).EndInit();
            ((System.ComponentModel.ISupportInitialize)dureeSecondesNumericUpDown).EndInit();
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel mainPanel;
        private Label directionLabel;
        private ComboBox directionComboBox;
        private Label contactLabel;
        private ComboBox contactComboBox;
        private Label dureeLabel;
        private TableLayoutPanel dureePanel;
        private NumericUpDown dureeMinutesNumericUpDown;
        private Label minutesLabel;
        private NumericUpDown dureeSecondesNumericUpDown;
        private Label secondesLabel;
        private Label resultatLabel;
        private ComboBox resultatComboBox;
        private Label contenuLabel;
        private TextBox contenuTextBox;
        private Label suiviNecessaireLabel;
        private CheckBox suiviNecessaireCheckBox;
        private Label dateSuiviLabel;
        private DateTimePicker dateSuiviPicker;
        private FlowLayoutPanel buttonsPanel;
        private Button cancelButton;
        private Button saveButton;
    }
}
