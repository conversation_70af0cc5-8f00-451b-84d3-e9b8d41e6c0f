using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.WinForms.Helpers;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.Litiges
{
    /// <summary>
    /// Formulaire de liste des litiges
    /// </summary>
    public partial class LitigeListForm : Form
    {
        private readonly ILitigeService _litigeService;
        private readonly IClientService _clientService;
        private readonly IFactureService _factureService;
        private readonly IUtilisateurService _utilisateurService;
        private readonly int _currentUserId;
        private List<Litige> _litiges = new List<Litige>();
        private List<CategorieLitige> _categories = new List<CategorieLitige>();
        private List<EtapeLitige> _etapes = new List<EtapeLitige>();
        private int? _clientId;
        private int? _factureId;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="litigeService">Service de gestion des litiges</param>
        /// <param name="clientService">Service de gestion des clients</param>
        /// <param name="factureService">Service de gestion des factures</param>
        /// <param name="utilisateurService">Service de gestion des utilisateurs</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        /// <param name="clientId">Identifiant du client (optionnel)</param>
        /// <param name="factureId">Identifiant de la facture (optionnel)</param>
        public LitigeListForm(
            ILitigeService litigeService,
            IClientService clientService,
            IFactureService factureService,
            IUtilisateurService utilisateurService,
            int currentUserId,
            int? clientId = null,
            int? factureId = null)
        {
            _litigeService = litigeService ?? throw new ArgumentNullException(nameof(litigeService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _currentUserId = currentUserId;
            _clientId = clientId;
            _factureId = factureId;

            InitializeComponent();
        }

        // Les méthodes InitializeComponent() et ConfigureDataGridView() ont été déplacées dans le fichier LitigeListForm.Designer.cs

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void LitigeListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Charger l'icône du formulaire
                try
                {
                    this.Icon = new Icon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "dispute.ico"));
                }
                catch
                {
                    // Ignorer les erreurs d'icône
                }

                // Charger les catégories de litiges
                _categories = (await _litigeService.GetAllCategoriesAsync()).ToList();
                var categorieComboBox = this.Controls.Find("categorieComboBox", true).FirstOrDefault() as ComboBox;
                if (categorieComboBox != null)
                {
                    categorieComboBox.Items.Clear();
                    categorieComboBox.Items.Add("Toutes");
                    foreach (var categorie in _categories)
                    {
                        categorieComboBox.Items.Add(categorie.Nom);
                    }
                    categorieComboBox.SelectedIndex = 0;
                }

                // Charger les étapes de litiges
                _etapes = (await _litigeService.GetAllEtapesAsync()).ToList();
                var etapeComboBox = this.Controls.Find("etapeComboBox", true).FirstOrDefault() as ComboBox;
                if (etapeComboBox != null)
                {
                    etapeComboBox.Items.Clear();
                    etapeComboBox.Items.Add("Toutes");
                    foreach (var etape in _etapes)
                    {
                        etapeComboBox.Items.Add(etape.Nom);
                    }
                    etapeComboBox.SelectedIndex = 0;
                }

                // Charger les utilisateurs
                var utilisateurs = await _utilisateurService.GetAllAsync();
                var responsableComboBox = this.Controls.Find("responsableComboBox", true).FirstOrDefault() as ComboBox;
                if (responsableComboBox != null)
                {
                    responsableComboBox.Items.Clear();
                    responsableComboBox.Items.Add("Tous");
                    responsableComboBox.Items.Add("Mes litiges");
                    foreach (var utilisateur in utilisateurs)
                    {
                        responsableComboBox.Items.Add(utilisateur.NomComplet);
                    }
                    responsableComboBox.SelectedIndex = 0;
                }

                // Charger les litiges
                await LoadLitigesAsync();

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors du chargement de la liste des litiges");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Charge les litiges en fonction des filtres
        /// </summary>
        private async Task LoadLitigesAsync()
        {
            try
            {
                // Récupérer les litiges en fonction des filtres
                IEnumerable<Litige> litiges;

                if (_factureId.HasValue)
                {
                    // Si un ID de facture est spécifié, récupérer les litiges de cette facture
                    litiges = await _litigeService.GetByFactureIdAsync(_factureId.Value);
                }
                else if (_clientId.HasValue)
                {
                    // Si un ID de client est spécifié, récupérer les litiges de ce client
                    litiges = await _litigeService.GetByClientIdAsync(_clientId.Value);
                }
                else
                {
                    // Sinon, récupérer tous les litiges
                    litiges = await _litigeService.GetAllAsync();
                }

                // Appliquer les filtres
                var statutComboBox = this.Controls.Find("statutComboBox", true).FirstOrDefault() as ComboBox;
                var categorieComboBox = this.Controls.Find("categorieComboBox", true).FirstOrDefault() as ComboBox;
                var etapeComboBox = this.Controls.Find("etapeComboBox", true).FirstOrDefault() as ComboBox;
                var responsableComboBox = this.Controls.Find("responsableComboBox", true).FirstOrDefault() as ComboBox;
                var periodeComboBox = this.Controls.Find("periodeComboBox", true).FirstOrDefault() as ComboBox;

                // Filtre par statut
                if (statutComboBox != null && statutComboBox.SelectedIndex > 0)
                {
                    string statut = statutComboBox.SelectedItem.ToString();
                    litiges = litiges.Where(l => l.Statut == statut);
                }

                // Filtre par catégorie
                if (categorieComboBox != null && categorieComboBox.SelectedIndex > 0)
                {
                    string categorie = categorieComboBox.SelectedItem.ToString();
                    litiges = litiges.Where(l => l.CategorieLitige?.Nom == categorie);
                }

                // Filtre par étape
                if (etapeComboBox != null && etapeComboBox.SelectedIndex > 0)
                {
                    string etape = etapeComboBox.SelectedItem.ToString();
                    litiges = litiges.Where(l => l.EtapeLitige?.Nom == etape);
                }

                // Filtre par responsable
                if (responsableComboBox != null && responsableComboBox.SelectedIndex > 0)
                {
                    if (responsableComboBox.SelectedIndex == 1) // "Mes litiges"
                    {
                        litiges = litiges.Where(l => l.ResponsableId == _currentUserId);
                    }
                    else if (responsableComboBox.SelectedIndex > 1)
                    {
                        string responsable = responsableComboBox.SelectedItem.ToString();
                        litiges = litiges.Where(l => l.Responsable?.NomComplet == responsable);
                    }
                }

                // Filtre par période
                if (periodeComboBox != null && periodeComboBox.SelectedIndex > 0)
                {
                    DateTime dateDebut = DateTime.Today;
                    DateTime dateFin = DateTime.Today.AddDays(1).AddSeconds(-1);

                    switch (periodeComboBox.SelectedIndex)
                    {
                        case 1: // Aujourd'hui
                            break;
                        case 2: // Cette semaine
                            dateDebut = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
                            dateFin = dateDebut.AddDays(7).AddSeconds(-1);
                            break;
                        case 3: // Ce mois
                            dateDebut = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                            dateFin = dateDebut.AddMonths(1).AddSeconds(-1);
                            break;
                        case 4: // Ce trimestre
                            int currentQuarter = (DateTime.Today.Month - 1) / 3 + 1;
                            dateDebut = new DateTime(DateTime.Today.Year, (currentQuarter - 1) * 3 + 1, 1);
                            dateFin = dateDebut.AddMonths(3).AddSeconds(-1);
                            break;
                        case 5: // Cette année
                            dateDebut = new DateTime(DateTime.Today.Year, 1, 1);
                            dateFin = new DateTime(DateTime.Today.Year, 12, 31, 23, 59, 59);
                            break;
                    }

                    litiges = litiges.Where(l => l.DateOuverture >= dateDebut && l.DateOuverture <= dateFin);
                }

                // Stocker les litiges filtrés
                _litiges = litiges.ToList();

                // Afficher les litiges dans le DataGridView
                DisplayLitiges();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des litiges");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Affiche les litiges dans le DataGridView
        /// </summary>
        private void DisplayLitiges()
        {
            var dataGridView = this.Controls.Find("litigesDataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                // Créer une table de données
                var dataTable = new DataTable();
                dataTable.Columns.Add("Id", typeof(int));
                dataTable.Columns.Add("NumeroFacture", typeof(string));
                dataTable.Columns.Add("Client", typeof(string));
                dataTable.Columns.Add("Categorie", typeof(string));
                dataTable.Columns.Add("MontantConteste", typeof(string));
                dataTable.Columns.Add("DateOuverture", typeof(string));
                dataTable.Columns.Add("DateEcheance", typeof(string));
                dataTable.Columns.Add("Etape", typeof(string));
                dataTable.Columns.Add("Responsable", typeof(string));
                dataTable.Columns.Add("Statut", typeof(string));
                dataTable.Columns.Add("Priorite", typeof(string));

                // Remplir la table avec les données des litiges
                foreach (var litige in _litiges)
                {
                    string prioriteText = litige.Priorite switch
                    {
                        1 => "Basse",
                        2 => "Moyenne",
                        3 => "Haute",
                        4 => "Critique",
                        _ => "Moyenne"
                    };

                    dataTable.Rows.Add(
                        litige.Id,
                        litige.Facture?.Numero ?? "N/A",
                        litige.Facture?.Client?.RaisonSociale ?? "N/A",
                        litige.CategorieLitige?.Nom ?? "N/A",
                        litige.MontantConteste.ToString("C"),
                        litige.DateOuverture.ToString("dd/MM/yyyy"),
                        litige.DateEcheance.ToString("dd/MM/yyyy"),
                        litige.EtapeLitige?.Nom ?? "N/A",
                        litige.Responsable?.NomComplet ?? "N/A",
                        litige.Statut,
                        prioriteText
                    );
                }

                // Assigner la table au DataGridView
                dataGridView.DataSource = dataTable;

                // Appliquer la coloration conditionnelle
                ApplyConditionalFormatting(dataGridView);
            }
        }

        /// <summary>
        /// Applique une coloration conditionnelle aux lignes du DataGridView
        /// </summary>
        /// <param name="dataGridView">DataGridView à formater</param>
        private void ApplyConditionalFormatting(DataGridView dataGridView)
        {
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                // Récupérer les valeurs
                string statut = row.Cells["Statut"].Value?.ToString() ?? "";
                string priorite = row.Cells["Priorite"].Value?.ToString() ?? "";
                DateTime dateEcheance = DateTime.MinValue;
                DateTime.TryParse(row.Cells["DateEcheance"].Value?.ToString(), out dateEcheance);

                // Colorer en fonction du statut
                if (statut == "Résolu")
                {
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                }
                else if (dateEcheance < DateTime.Today)
                {
                    // En retard
                    row.DefaultCellStyle.BackColor = Color.LightPink;
                }
                else if (priorite == "Critique")
                {
                    row.DefaultCellStyle.BackColor = Color.LightCoral;
                }
                else if (priorite == "Haute")
                {
                    row.DefaultCellStyle.BackColor = Color.LightSalmon;
                }
            }
        }

        /// <summary>
        /// Événement de changement de sélection dans la liste déroulante des statuts
        /// </summary>
        private async void StatutComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadLitigesAsync();
        }

        /// <summary>
        /// Événement de changement de sélection dans la liste déroulante des catégories
        /// </summary>
        private async void CategorieComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadLitigesAsync();
        }

        /// <summary>
        /// Événement de changement de sélection dans la liste déroulante des étapes
        /// </summary>
        private async void EtapeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadLitigesAsync();
        }

        /// <summary>
        /// Événement de changement de sélection dans la liste déroulante des responsables
        /// </summary>
        private async void ResponsableComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadLitigesAsync();
        }

        /// <summary>
        /// Événement de changement de sélection dans la liste déroulante des périodes
        /// </summary>
        private async void PeriodeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadLitigesAsync();
        }

        /// <summary>
        /// Événement de clic sur le bouton de recherche
        /// </summary>
        private async void SearchButton_Click(object sender, EventArgs e)
        {
            await LoadLitigesAsync();
        }

        /// <summary>
        /// Événement de double-clic sur une cellule du DataGridView
        /// </summary>
        private void LitigesDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditSelectedLitige();
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Nouveau
        /// </summary>
        private void NewButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Créer un formulaire d'édition de litige
                using (var form = new LitigeEditForm(
                    _litigeService,
                    _clientService,
                    _factureService,
                    _utilisateurService,
                    _currentUserId,
                    null,
                    _clientId,
                    _factureId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les litiges
                        _ = LoadLitigesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la création d'un nouveau litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Modifier
        /// </summary>
        private void EditButton_Click(object sender, EventArgs e)
        {
            EditSelectedLitige();
        }

        /// <summary>
        /// Édite le litige sélectionné
        /// </summary>
        private void EditSelectedLitige()
        {
            try
            {
                var dataGridView = this.Controls.Find("litigesDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    // Récupérer l'ID du litige sélectionné
                    int litigeId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);

                    // Récupérer le litige correspondant
                    var litige = _litiges.FirstOrDefault(l => l.Id == litigeId);
                    if (litige != null)
                    {
                        // Créer un formulaire d'édition de litige
                        using (var form = new LitigeEditForm(
                            _litigeService,
                            _clientService,
                            _factureService,
                            _utilisateurService,
                            _currentUserId,
                            litige))
                        {
                            if (form.ShowDialog() == DialogResult.OK)
                            {
                                // Recharger les litiges
                                _ = LoadLitigesAsync();
                            }
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un litige à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la modification d'un litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Supprimer
        /// </summary>
        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = this.Controls.Find("litigesDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    // Récupérer l'ID du litige sélectionné
                    int litigeId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);

                    // Demander confirmation
                    var result = MessageBox.Show(
                        "Êtes-vous sûr de vouloir supprimer ce litige ?",
                        "Confirmation",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Supprimer le litige
                        await _litigeService.DeleteAsync(litigeId, _currentUserId);

                        // Recharger les litiges
                        await LoadLitigesAsync();

                        MessageBox.Show("Le litige a été supprimé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un litige à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression d'un litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Rafraîchir
        /// </summary>
        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            await LoadLitigesAsync();
        }

        /// <summary>
        /// Événement de clic sur le bouton Exporter
        /// </summary>
        private void ExportButton_Click(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = this.Controls.Find("litigesDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.Rows.Count > 0)
                {
                    // Créer une boîte de dialogue pour sélectionner le fichier de destination
                    using (var saveFileDialog = new SaveFileDialog())
                    {
                        saveFileDialog.Filter = "Fichier Excel (*.xlsx)|*.xlsx|Fichier CSV (*.csv)|*.csv";
                        saveFileDialog.Title = "Exporter les litiges";
                        saveFileDialog.FileName = $"Litiges_{DateTime.Now:yyyyMMdd}";

                        if (saveFileDialog.ShowDialog() == DialogResult.OK)
                        {
                            // Afficher un indicateur de chargement
                            this.Cursor = Cursors.WaitCursor;

                            // Exporter les données
                            if (saveFileDialog.FileName.EndsWith(".xlsx"))
                            {
                                // TODO: Implémenter l'export Excel
                                MessageBox.Show("L'export au format Excel sera implémenté dans une version future.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                            else if (saveFileDialog.FileName.EndsWith(".csv"))
                            {
                                // Exporter au format CSV
                                using (var writer = new StreamWriter(saveFileDialog.FileName))
                                {
                                    // Écrire l'en-tête
                                    var headers = new List<string>();
                                    foreach (DataGridViewColumn column in dataGridView.Columns)
                                    {
                                        if (column.Visible)
                                        {
                                            headers.Add(column.HeaderText);
                                        }
                                    }
                                    writer.WriteLine(string.Join(";", headers));

                                    // Écrire les données
                                    foreach (DataGridViewRow row in dataGridView.Rows)
                                    {
                                        var cells = new List<string>();
                                        foreach (DataGridViewCell cell in row.Cells)
                                        {
                                            if (dataGridView.Columns[cell.ColumnIndex].Visible)
                                            {
                                                cells.Add(cell.Value?.ToString() ?? "");
                                            }
                                        }
                                        writer.WriteLine(string.Join(";", cells));
                                    }
                                }

                                MessageBox.Show($"Les données ont été exportées avec succès dans le fichier {saveFileDialog.FileName}.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }

                            // Restaurer le curseur
                            this.Cursor = Cursors.Default;
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Aucune donnée à exporter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors de l'exportation des litiges");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Fermer
        /// </summary>
        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
