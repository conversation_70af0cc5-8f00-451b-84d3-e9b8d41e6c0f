using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les contacts
    /// </summary>
    public class ContactRepository : BaseRepository<Contact>, IContactRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public ContactRepository(DatabaseConnection dbConnection) : base(dbConnection, "Contacts")
        {
        }

        /// <summary>
        /// Récupère tous les contacts d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des contacts du client</returns>
        public async Task<IEnumerable<Contact>> GetByClientIdAsync(int clientId)
        {
            using (var connection = _dbConnection.CreateConnection())
            {
                return await connection.QueryAsync<Contact>(
                    "SELECT * FROM Contacts WHERE ClientId = @ClientId ORDER BY EstPrincipal DESC, Nom, Prenom",
                    new { ClientId = clientId });
            }
        }

        /// <summary>
        /// Récupère le contact principal d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Contact principal du client ou null si aucun contact principal n'est défini</returns>
        public async Task<Contact> GetPrincipalContactAsync(int clientId)
        {
            using (var connection = _dbConnection.CreateConnection())
            {
                return await connection.QueryFirstOrDefaultAsync<Contact>(
                    "SELECT TOP 1 * FROM Contacts WHERE ClientId = @ClientId AND EstPrincipal = 1",
                    new { ClientId = clientId });
            }
        }

        /// <summary>
        /// Récupère un contact par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du contact</param>
        /// <returns>Contact correspondant à l'identifiant ou null si aucun contact n'est trouvé</returns>
        public override async Task<Contact> GetByIdAsync(int id)
        {
            using (var connection = _dbConnection.CreateConnection())
            {
                return await connection.QueryFirstOrDefaultAsync<Contact>(
                    "SELECT * FROM Contacts WHERE Id = @Id",
                    new { Id = id });
            }
        }

        /// <summary>
        /// Récupère tous les contacts
        /// </summary>
        /// <returns>Liste de tous les contacts</returns>
        public override async Task<IEnumerable<Contact>> GetAllAsync()
        {
            using (var connection = _dbConnection.CreateConnection())
            {
                return await connection.QueryAsync<Contact>(
                    "SELECT * FROM Contacts ORDER BY ClientId, EstPrincipal DESC, Nom, Prenom");
            }
        }

        /// <summary>
        /// Ajoute un nouveau contact
        /// </summary>
        /// <param name="entity">Contact à ajouter</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact ajouté avec son identifiant généré</returns>
        public override async Task<Contact> AddAsync(Contact entity, int userId)
        {
            using (var connection = _dbConnection.CreateConnection())
            {
                // Si le contact est défini comme principal, mettre à jour les autres contacts du client
                if (entity.EstPrincipal)
                {
                    await connection.ExecuteAsync(
                        "UPDATE Contacts SET EstPrincipal = 0 WHERE ClientId = @ClientId",
                        new { ClientId = entity.ClientId });
                }

                // Définir les propriétés de base
                entity.DateCreation = System.DateTime.Now;
                entity.CreePar = userId;
                entity.EstActif = true;

                // Insérer le contact
                var id = await connection.QuerySingleAsync<int>(
                    @"INSERT INTO Contacts (ClientId, Nom, Prenom, Fonction, Email, Telephone, Mobile, EstPrincipal, Notes, DateCreation, CreePar, EstActif)
                    VALUES (@ClientId, @Nom, @Prenom, @Fonction, @Email, @Telephone, @Mobile, @EstPrincipal, @Notes, @DateCreation, @CreePar, @EstActif);
                    SELECT CAST(SCOPE_IDENTITY() as int)",
                    entity);

                entity.Id = id;
                return entity;
            }
        }

        /// <summary>
        /// Met à jour un contact existant
        /// </summary>
        /// <param name="entity">Contact à mettre à jour</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateAsync(Contact entity)
        {
            using (var connection = _dbConnection.CreateConnection())
            {
                // Si le contact est défini comme principal, mettre à jour les autres contacts du client
                if (entity.EstPrincipal)
                {
                    await connection.ExecuteAsync(
                        "UPDATE Contacts SET EstPrincipal = 0 WHERE ClientId = @ClientId AND Id != @Id",
                        new { ClientId = entity.ClientId, Id = entity.Id });
                }

                var result = await connection.ExecuteAsync(
                    @"UPDATE Contacts
                    SET ClientId = @ClientId,
                        Nom = @Nom,
                        Prenom = @Prenom,
                        Fonction = @Fonction,
                        Email = @Email,
                        Telephone = @Telephone,
                        Mobile = @Mobile,
                        EstPrincipal = @EstPrincipal,
                        Notes = @Notes
                    WHERE Id = @Id",
                    entity);

                return result > 0;
            }
        }

        /// <summary>
        /// Supprime un contact
        /// </summary>
        /// <param name="id">Identifiant du contact à supprimer</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            using (var connection = _dbConnection.CreateConnection())
            {
                var result = await connection.ExecuteAsync(
                    "DELETE FROM Contacts WHERE Id = @Id",
                    new { Id = id });

                return result > 0;
            }
        }
    }
}
