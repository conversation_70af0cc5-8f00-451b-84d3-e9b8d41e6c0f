using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de génération de rapports
    /// </summary>
    public interface IReportService
    {
        /// <summary>
        /// Génère un rapport sur les factures en retard
        /// </summary>
        /// <returns>Données du rapport au format JSON</returns>
        Task<string> GenerateOverdueInvoicesReportAsync();

        /// <summary>
        /// Génère un rapport sur les paiements par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Données du rapport au format JSON</returns>
        Task<string> GeneratePaymentsByPeriodReportAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Génère un rapport sur les factures par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Données du rapport au format JSON</returns>
        Task<string> GenerateInvoicesByPeriodReportAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Génère un rapport sur les clients avec des factures en retard
        /// </summary>
        /// <returns>Données du rapport au format JSON</returns>
        Task<string> GenerateClientsWithOverdueInvoicesReportAsync();

        /// <summary>
        /// Génère un rapport sur les performances des commerciaux
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Données du rapport au format JSON</returns>
        Task<string> GenerateSalesPerformanceReportAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Génère un rapport sur les modes de paiement
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Données du rapport au format JSON</returns>
        Task<string> GeneratePaymentMethodsReportAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Génère un rapport sur les relances effectuées
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Données du rapport au format JSON</returns>
        Task<string> GenerateRemindersReportAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Génère un rapport sur le taux de recouvrement
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Données du rapport au format JSON</returns>
        Task<string> GenerateRecoveryRateReportAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Exporte un rapport au format CSV
        /// </summary>
        /// <param name="reportData">Données du rapport au format JSON</param>
        /// <param name="filePath">Chemin du fichier de destination</param>
        /// <returns>True si l'exportation a réussi, sinon False</returns>
        Task<bool> ExportReportToCsvAsync(string reportData, string filePath);

        /// <summary>
        /// Exporte un rapport au format Excel
        /// </summary>
        /// <param name="reportData">Données du rapport au format JSON</param>
        /// <param name="filePath">Chemin du fichier de destination</param>
        /// <returns>True si l'exportation a réussi, sinon False</returns>
        Task<bool> ExportReportToExcelAsync(string reportData, string filePath);

        /// <summary>
        /// Exporte un rapport au format PDF
        /// </summary>
        /// <param name="reportData">Données du rapport au format JSON</param>
        /// <param name="filePath">Chemin du fichier de destination</param>
        /// <param name="title">Titre du rapport</param>
        /// <returns>True si l'exportation a réussi, sinon False</returns>
        Task<bool> ExportReportToPdfAsync(string reportData, string filePath, string title);
    }
}
