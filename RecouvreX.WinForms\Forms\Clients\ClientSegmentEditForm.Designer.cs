using RecouvreX.Models.Enums;
using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Clients
{
    partial class ClientSegmentEditForm
    {
        /// <summary>
        /// Variable nécessaire au concepteur.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private Label titleLabel;
        private Label currentSegmentLabel;
        private Label currentSegmentValueLabel;
        private Label newSegmentLabel;
        private ComboBox segmentComboBox;
        private Label commentaireLabel;
        private TextBox commentaireTextBox;
        private Button cancelButton;
        private Button saveButton;

        /// <summary>
        /// Nettoyage des ressources utilisées.
        /// </summary>
        /// <param name="disposing">true si les ressources managées doivent être supprimées ; sinon, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Code généré par le Concepteur Windows Form

        /// <summary>
        /// Méthode requise pour la prise en charge du concepteur - ne modifiez pas
        /// le contenu de cette méthode avec l'éditeur de code.
        /// </summary>
        private void InitializeComponent()
        {
            this.mainPanel = new Panel();
            this.titleLabel = new Label();
            this.currentSegmentLabel = new Label();
            this.currentSegmentValueLabel = new Label();
            this.newSegmentLabel = new Label();
            this.segmentComboBox = new ComboBox();
            this.commentaireLabel = new Label();
            this.commentaireTextBox = new TextBox();
            this.cancelButton = new Button();
            this.saveButton = new Button();

            this.mainPanel.SuspendLayout();
            this.SuspendLayout();

            //
            // mainPanel
            //
            this.mainPanel.Dock = DockStyle.Fill;
            this.mainPanel.Padding = new Padding(10);
            this.mainPanel.Controls.Add(this.titleLabel);
            this.mainPanel.Controls.Add(this.currentSegmentLabel);
            this.mainPanel.Controls.Add(this.currentSegmentValueLabel);
            this.mainPanel.Controls.Add(this.newSegmentLabel);
            this.mainPanel.Controls.Add(this.segmentComboBox);
            this.mainPanel.Controls.Add(this.commentaireLabel);
            this.mainPanel.Controls.Add(this.commentaireTextBox);
            this.mainPanel.Controls.Add(this.cancelButton);
            this.mainPanel.Controls.Add(this.saveButton);

            //
            // titleLabel
            //
            this.titleLabel.Text = "Modifier le segment du client";
            this.titleLabel.Font = new Font(this.titleLabel.Font.FontFamily, 10, FontStyle.Bold);
            this.titleLabel.AutoSize = true;
            this.titleLabel.Location = new Point(10, 10);

            //
            // currentSegmentLabel
            //
            this.currentSegmentLabel.Text = "Segment actuel :";
            this.currentSegmentLabel.AutoSize = true;
            this.currentSegmentLabel.Location = new Point(10, 50);

            //
            // currentSegmentValueLabel
            //
            this.currentSegmentValueLabel.Name = "currentSegmentValueLabel";
            this.currentSegmentValueLabel.Text = ""; // Sera défini dans le code
            this.currentSegmentValueLabel.Font = new Font(this.currentSegmentValueLabel.Font.FontFamily, 9, FontStyle.Bold);
            this.currentSegmentValueLabel.AutoSize = true;
            this.currentSegmentValueLabel.Location = new Point(120, 50);

            //
            // newSegmentLabel
            //
            this.newSegmentLabel.Text = "Nouveau segment :";
            this.newSegmentLabel.AutoSize = true;
            this.newSegmentLabel.Location = new Point(10, 80);

            //
            // segmentComboBox
            //
            this.segmentComboBox.Name = "segmentComboBox";
            this.segmentComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.segmentComboBox.Location = new Point(120, 77);
            this.segmentComboBox.Width = 200;
            this.segmentComboBox.Items.AddRange(new object[] {
                "A - Stratégique",
                "B - Important",
                "C - Standard",
                "Non segmenté"
            });

            // Le segment actuel sera sélectionné dans le code

            //
            // commentaireLabel
            //
            this.commentaireLabel.Text = "Commentaire :";
            this.commentaireLabel.AutoSize = true;
            this.commentaireLabel.Location = new Point(10, 110);

            //
            // commentaireTextBox
            //
            this.commentaireTextBox.Name = "commentaireTextBox";
            this.commentaireTextBox.Location = new Point(10, 130);
            this.commentaireTextBox.Width = 370;
            this.commentaireTextBox.Height = 100;
            this.commentaireTextBox.Multiline = true;
            this.commentaireTextBox.ScrollBars = ScrollBars.Vertical;

            //
            // cancelButton
            //
            this.cancelButton.Text = "Annuler";
            this.cancelButton.DialogResult = DialogResult.Cancel;
            this.cancelButton.Location = new Point(200, 250);
            this.cancelButton.Width = 80;
            this.cancelButton.Height = 30;

            //
            // saveButton
            //
            this.saveButton.Text = "Enregistrer";
            this.saveButton.DialogResult = DialogResult.OK;
            this.saveButton.Location = new Point(300, 250);
            this.saveButton.Width = 80;
            this.saveButton.Height = 30;
            this.saveButton.Click += new EventHandler(this.SaveButton_Click);

            //
            // ClientSegmentEditForm
            //
            this.ClientSize = new System.Drawing.Size(400, 300);
            this.Controls.Add(this.mainPanel);
            this.Name = "ClientSegmentEditForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Modifier le segment du client";
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.Load += new System.EventHandler(this.ClientSegmentEditForm_Load);

            this.mainPanel.ResumeLayout(false);
            this.mainPanel.PerformLayout();
            this.ResumeLayout(false);
        }

        #endregion
    }
}
