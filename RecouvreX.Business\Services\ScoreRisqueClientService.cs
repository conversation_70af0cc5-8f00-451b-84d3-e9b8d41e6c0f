using Newtonsoft.Json;
using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des scores de risque client
    /// </summary>
    public class ScoreRisqueClientService : IScoreRisqueClientService
    {
        private readonly IScoreRisqueClientRepository _scoreRisqueClientRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IFactureRepository _factureRepository;
        private readonly IPaiementRepository _paiementRepository;
        private readonly IRelanceRepository _relanceRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="scoreRisqueClientRepository">Repository pour les scores de risque client</param>
        /// <param name="clientRepository">Repository pour les clients</param>
        /// <param name="factureRepository">Repository pour les factures</param>
        /// <param name="paiementRepository">Repository pour les paiements</param>
        /// <param name="relanceRepository">Repository pour les relances</param>
        /// <param name="utilisateurRepository">Repository pour les utilisateurs</param>
        /// <param name="journalAuditRepository">Repository pour le journal d'audit</param>
        public ScoreRisqueClientService(
            IScoreRisqueClientRepository scoreRisqueClientRepository,
            IClientRepository clientRepository,
            IFactureRepository factureRepository,
            IPaiementRepository paiementRepository,
            IRelanceRepository relanceRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _scoreRisqueClientRepository = scoreRisqueClientRepository ?? throw new ArgumentNullException(nameof(scoreRisqueClientRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _paiementRepository = paiementRepository ?? throw new ArgumentNullException(nameof(paiementRepository));
            _relanceRepository = relanceRepository ?? throw new ArgumentNullException(nameof(relanceRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère tous les scores de risque client
        /// </summary>
        /// <returns>Liste des scores de risque client</returns>
        public async Task<IEnumerable<ScoreRisqueClient>> GetAllAsync()
        {
            return await _scoreRisqueClientRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère un score de risque client par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du score de risque client</param>
        /// <returns>Score de risque client trouvé ou null</returns>
        public async Task<ScoreRisqueClient> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _scoreRisqueClientRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère le score de risque d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Score de risque du client ou null si non trouvé</returns>
        public async Task<ScoreRisqueClient> GetByClientIdAsync(int clientId)
        {
            if (clientId <= 0)
                return null;

            return await _scoreRisqueClientRepository.GetByClientIdAsync(clientId);
        }

        /// <summary>
        /// Calcule et met à jour le score de risque d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Score de risque mis à jour</returns>
        public async Task<ScoreRisqueClient> CalculateAndUpdateScoreAsync(int clientId, int utilisateurId)
        {
            if (clientId <= 0 || utilisateurId <= 0)
                throw new ArgumentException("Paramètres invalides pour le calcul du score de risque");

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(clientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {clientId} n'existe pas");

            // Récupérer les factures du client
            var factures = await _factureRepository.GetByClientIdAsync(clientId);
            if (factures == null || !factures.Any())
                throw new InvalidOperationException($"Le client avec l'ID {clientId} n'a pas de factures");

            // Récupérer les paiements du client
            var paiements = await _paiementRepository.GetByClientIdAsync(clientId);

            // Récupérer les relances du client
            var relances = await _relanceRepository.GetByClientIdAsync(clientId);

            // Calculer les métriques de risque
            var metriques = CalculerMetriquesRisque(factures, paiements, relances);

            // Calculer le score de risque
            int score = CalculerScoreRisque(metriques);
            string categorie = ScoreRisqueClient.GetCategorie(score);

            // Créer ou mettre à jour le score de risque
            var scoreRisqueClient = await _scoreRisqueClientRepository.GetByClientIdAsync(clientId);
            if (scoreRisqueClient == null)
            {
                // Créer un nouveau score de risque
                scoreRisqueClient = new ScoreRisqueClient
                {
                    ClientId = clientId,
                    Score = score,
                    Categorie = categorie,
                    DateMiseAJour = DateTime.Now,
                    FacteursRisque = JsonConvert.SerializeObject(metriques),
                    DelaiMoyenPaiement = metriques.DelaiMoyenPaiement,
                    PourcentageFacturesRetard = metriques.PourcentageFacturesRetard,
                    MontantTotalRetard = metriques.MontantTotalRetard,
                    NombreMoyenRelances = metriques.NombreMoyenRelances
                };

                scoreRisqueClient = await _scoreRisqueClientRepository.AddAsync(scoreRisqueClient, utilisateurId);

                // Journaliser la création
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    DateAction = DateTime.Now,
                    UtilisateurId = utilisateurId,
                    NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(utilisateurId))?.NomUtilisateur ?? "Système",
                    TypeAction = TypeAudit.Creation,
                    TypeEntite = "ScoreRisqueClient",
                    EntiteId = scoreRisqueClient.Id,
                    Description = $"Création du score de risque pour le client {client.RaisonSociale} (Score: {score}, Catégorie: {categorie})",
                    DonneesApres = JsonConvert.SerializeObject(new
                    {
                        scoreRisqueClient.Id,
                        scoreRisqueClient.ClientId,
                        ClientNom = client.RaisonSociale,
                        scoreRisqueClient.Score,
                        scoreRisqueClient.Categorie,
                        scoreRisqueClient.DateMiseAJour,
                        scoreRisqueClient.DelaiMoyenPaiement,
                        scoreRisqueClient.PourcentageFacturesRetard,
                        scoreRisqueClient.MontantTotalRetard,
                        scoreRisqueClient.NombreMoyenRelances
                    })
                });
            }
            else
            {
                // Mettre à jour le score de risque existant
                var ancienScore = scoreRisqueClient.Score;
                var ancienneCategorie = scoreRisqueClient.Categorie;

                scoreRisqueClient.Score = score;
                scoreRisqueClient.Categorie = categorie;
                scoreRisqueClient.DateMiseAJour = DateTime.Now;
                scoreRisqueClient.FacteursRisque = JsonConvert.SerializeObject(metriques);
                scoreRisqueClient.DelaiMoyenPaiement = metriques.DelaiMoyenPaiement;
                scoreRisqueClient.PourcentageFacturesRetard = metriques.PourcentageFacturesRetard;
                scoreRisqueClient.MontantTotalRetard = metriques.MontantTotalRetard;
                scoreRisqueClient.NombreMoyenRelances = metriques.NombreMoyenRelances;

                scoreRisqueClient = await _scoreRisqueClientRepository.UpdateAsync(scoreRisqueClient, utilisateurId);

                // Journaliser la mise à jour
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    DateAction = DateTime.Now,
                    UtilisateurId = utilisateurId,
                    NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(utilisateurId))?.NomUtilisateur ?? "Système",
                    TypeAction = TypeAudit.Modification,
                    TypeEntite = "ScoreRisqueClient",
                    EntiteId = scoreRisqueClient.Id,
                    Description = $"Mise à jour du score de risque pour le client {client.RaisonSociale} (Score: {ancienScore} -> {score}, Catégorie: {ancienneCategorie} -> {categorie})",
                    DonneesAvant = JsonConvert.SerializeObject(new
                    {
                        scoreRisqueClient.Id,
                        scoreRisqueClient.ClientId,
                        ClientNom = client.RaisonSociale,
                        Score = ancienScore,
                        Categorie = ancienneCategorie
                    }),
                    DonneesApres = JsonConvert.SerializeObject(new
                    {
                        scoreRisqueClient.Id,
                        scoreRisqueClient.ClientId,
                        ClientNom = client.RaisonSociale,
                        scoreRisqueClient.Score,
                        scoreRisqueClient.Categorie,
                        scoreRisqueClient.DateMiseAJour,
                        scoreRisqueClient.DelaiMoyenPaiement,
                        scoreRisqueClient.PourcentageFacturesRetard,
                        scoreRisqueClient.MontantTotalRetard,
                        scoreRisqueClient.NombreMoyenRelances
                    })
                });
            }

            return scoreRisqueClient;
        }

        /// <summary>
        /// Calcule et met à jour les scores de risque de tous les clients
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre de scores mis à jour</returns>
        public async Task<int> CalculateAndUpdateAllScoresAsync(int utilisateurId)
        {
            if (utilisateurId <= 0)
                throw new ArgumentException("Paramètre utilisateurId invalide");

            // Récupérer tous les clients
            var clients = await _clientRepository.GetAllAsync();
            int count = 0;

            foreach (var client in clients)
            {
                try
                {
                    await CalculateAndUpdateScoreAsync(client.Id, utilisateurId);
                    count++;
                }
                catch (Exception ex)
                {
                    // Journaliser l'erreur mais continuer avec les autres clients
                    Serilog.Log.Error(ex, "Erreur lors du calcul du score de risque pour le client {ClientId}", client.Id);
                }
            }

            return count;
        }

        /// <summary>
        /// Récupère les clients par catégorie de risque
        /// </summary>
        /// <param name="categorie">Catégorie de risque (A, B, C, D, E)</param>
        /// <returns>Liste des clients dans la catégorie spécifiée</returns>
        public async Task<IEnumerable<Client>> GetClientsByCategorieAsync(string categorie)
        {
            if (string.IsNullOrEmpty(categorie))
                throw new ArgumentException("La catégorie ne peut pas être vide");

            return await _scoreRisqueClientRepository.GetClientsByCategorieAsync(categorie);
        }

        /// <summary>
        /// Récupère les statistiques de répartition des clients par catégorie de risque
        /// </summary>
        /// <returns>Dictionnaire avec la catégorie comme clé et le nombre de clients comme valeur</returns>
        public async Task<Dictionary<string, int>> GetClientDistributionByCategorieAsync()
        {
            return await _scoreRisqueClientRepository.GetClientDistributionByCategorieAsync();
        }

        /// <summary>
        /// Calcule les métriques de risque pour un client
        /// </summary>
        /// <param name="factures">Liste des factures du client</param>
        /// <param name="paiements">Liste des paiements du client</param>
        /// <param name="relances">Liste des relances du client</param>
        /// <returns>Métriques de risque calculées</returns>
        private MetriquesRisque CalculerMetriquesRisque(IEnumerable<Facture> factures, IEnumerable<Paiement> paiements, IEnumerable<Relance> relances)
        {
            var metriques = new MetriquesRisque();

            // Nombre total de factures
            metriques.NombreFactures = factures.Count();

            // Nombre de factures payées
            metriques.NombreFacturesPayees = factures.Count(f => f.Statut == StatutFacture.Payee);

            // Nombre de factures en retard
            metriques.NombreFacturesEnRetard = factures.Count(f => f.Statut == StatutFacture.EnRetard);

            // Pourcentage de factures en retard
            metriques.PourcentageFacturesRetard = metriques.NombreFactures > 0
                ? (double)metriques.NombreFacturesEnRetard / metriques.NombreFactures * 100
                : 0;

            // Montant total des factures en retard
            metriques.MontantTotalRetard = factures
                .Where(f => f.Statut == StatutFacture.EnRetard)
                .Sum(f => f.MontantRestant);

            // Délai moyen de paiement
            var facturesPayees = factures.Where(f => f.Statut == StatutFacture.Payee).ToList();
            if (facturesPayees.Any())
            {
                double totalJours = 0;
                int count = 0;

                foreach (var facture in facturesPayees)
                {
                    // Trouver le dernier paiement pour cette facture
                    var dernierPaiement = paiements
                        .Where(p => p.FacturePaiements != null && p.FacturePaiements.Any(fp => fp.FactureId == facture.Id))
                        .OrderByDescending(p => p.DatePaiement)
                        .FirstOrDefault();

                    if (dernierPaiement != null)
                    {
                        // Calculer le nombre de jours entre la date d'échéance et la date de paiement
                        var jours = (dernierPaiement.DatePaiement - facture.DateEcheance).TotalDays;
                        totalJours += jours;
                        count++;
                    }
                }

                metriques.DelaiMoyenPaiement = count > 0 ? totalJours / count : 0;
            }

            // Nombre moyen de relances par facture
            if (metriques.NombreFactures > 0 && relances.Any())
            {
                metriques.NombreMoyenRelances = (double)relances.Count() / metriques.NombreFactures;
            }

            return metriques;
        }

        /// <summary>
        /// Calcule le score de risque à partir des métriques
        /// </summary>
        /// <param name="metriques">Métriques de risque</param>
        /// <returns>Score de risque (0-100)</returns>
        private int CalculerScoreRisque(MetriquesRisque metriques)
        {
            // Initialiser le score à 0 (risque minimal)
            double score = 0;

            // Facteur 1: Pourcentage de factures en retard (poids: 30%)
            // 0% = 0 points, 100% = 30 points
            score += metriques.PourcentageFacturesRetard * 0.3;

            // Facteur 2: Délai moyen de paiement (poids: 25%)
            // < 0 jours (paiement avant échéance) = 0 points
            // 0-30 jours de retard = 5-10 points
            // 30-60 jours de retard = 10-15 points
            // 60-90 jours de retard = 15-20 points
            // > 90 jours de retard = 25 points
            if (metriques.DelaiMoyenPaiement <= 0)
            {
                // Pas de points pour les paiements avant échéance
            }
            else if (metriques.DelaiMoyenPaiement <= 30)
            {
                score += 5 + (metriques.DelaiMoyenPaiement / 30) * 5;
            }
            else if (metriques.DelaiMoyenPaiement <= 60)
            {
                score += 10 + ((metriques.DelaiMoyenPaiement - 30) / 30) * 5;
            }
            else if (metriques.DelaiMoyenPaiement <= 90)
            {
                score += 15 + ((metriques.DelaiMoyenPaiement - 60) / 30) * 5;
            }
            else
            {
                score += 25;
            }

            // Facteur 3: Montant total en retard (poids: 25%)
            // Échelle logarithmique pour éviter que les gros montants ne dominent trop le score
            if (metriques.MontantTotalRetard > 0)
            {
                // Utiliser log10 pour une échelle logarithmique
                // 100€ = ~5 points, 1000€ = ~10 points, 10000€ = ~15 points, 100000€ = ~20 points, 1000000€ = 25 points
                double logScore = Math.Log10((double)metriques.MontantTotalRetard) * 5;
                score += Math.Min(logScore, 25); // Plafonner à 25 points
            }

            // Facteur 4: Nombre moyen de relances (poids: 20%)
            // 0 relances = 0 points, 1 relance = 5 points, 2 relances = 10 points, 3 relances = 15 points, 4+ relances = 20 points
            score += Math.Min(metriques.NombreMoyenRelances * 5, 20);

            // Arrondir et s'assurer que le score est entre 0 et 100
            return (int)Math.Max(0, Math.Min(100, Math.Round(score)));
        }
    }

    /// <summary>
    /// Classe pour stocker les métriques de risque calculées
    /// </summary>
    public class MetriquesRisque
    {
        /// <summary>
        /// Nombre total de factures
        /// </summary>
        public int NombreFactures { get; set; }

        /// <summary>
        /// Nombre de factures payées
        /// </summary>
        public int NombreFacturesPayees { get; set; }

        /// <summary>
        /// Nombre de factures en retard
        /// </summary>
        public int NombreFacturesEnRetard { get; set; }

        /// <summary>
        /// Pourcentage de factures en retard
        /// </summary>
        public double PourcentageFacturesRetard { get; set; }

        /// <summary>
        /// Montant total des factures en retard
        /// </summary>
        public decimal MontantTotalRetard { get; set; }

        /// <summary>
        /// Délai moyen de paiement (en jours)
        /// </summary>
        public double DelaiMoyenPaiement { get; set; }

        /// <summary>
        /// Nombre moyen de relances par facture
        /// </summary>
        public double NombreMoyenRelances { get; set; }
    }
}
