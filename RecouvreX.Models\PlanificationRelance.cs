using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une planification de relance
    /// </summary>
    public class PlanificationRelance : BaseEntity
    {
        /// <summary>
        /// Identifiant de la facture concernée
        /// </summary>
        public int FactureId { get; set; }

        /// <summary>
        /// Facture concernée (navigation property)
        /// </summary>
        public Facture Facture { get; set; }

        /// <summary>
        /// Identifiant de la règle de relance appliquée
        /// </summary>
        public int RegleRelanceId { get; set; }

        /// <summary>
        /// Règle de relance appliquée (navigation property)
        /// </summary>
        public RegleRelance RegleRelance { get; set; }

        /// <summary>
        /// Niveau de relance (1, 2, 3)
        /// </summary>
        public int NiveauRelance { get; set; }

        /// <summary>
        /// Date prévue pour l'envoi de la relance
        /// </summary>
        public DateTime DatePrevue { get; set; }

        /// <summary>
        /// Date d'envoi effective de la relance (null si non envoyée)
        /// </summary>
        public DateTime? DateEnvoi { get; set; }

        /// <summary>
        /// Statut de la planification (Planifiée, En attente de validation, Validée, Envoyée, Annulée)
        /// </summary>
        public string Statut { get; set; }

        /// <summary>
        /// Identifiant du modèle de relance utilisé
        /// </summary>
        public int? ModeleRelanceId { get; set; }

        /// <summary>
        /// Modèle de relance utilisé (navigation property)
        /// </summary>
        public ModeleRelance ModeleRelance { get; set; }

        /// <summary>
        /// Canal d'envoi (Email, Courrier, SMS)
        /// </summary>
        public string Canal { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a validé la relance (null si non validée)
        /// </summary>
        public int? ValidePar { get; set; }

        /// <summary>
        /// Utilisateur qui a validé la relance (navigation property)
        /// </summary>
        public Utilisateur UtilisateurValidation { get; set; }

        /// <summary>
        /// Date de validation de la relance (null si non validée)
        /// </summary>
        public DateTime? DateValidation { get; set; }

        /// <summary>
        /// Commentaire sur la planification
        /// </summary>
        public string Commentaire { get; set; }
    }
}
