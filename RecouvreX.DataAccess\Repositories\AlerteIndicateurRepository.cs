using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models.Reporting;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour la gestion des alertes sur indicateurs
    /// </summary>
    public class AlerteIndicateurRepository : BaseRepository<AlerteIndicateur>, IAlerteIndicateurRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public AlerteIndicateurRepository(DatabaseConnection dbConnection) : base(dbConnection, "AlertesIndicateurs")
        {
        }

        /// <summary>
        /// Récupère les alertes par indicateur
        /// </summary>
        /// <param name="indicateur">Nom de l'indicateur</param>
        /// <returns>Liste des alertes pour l'indicateur spécifié</returns>
        public async Task<IEnumerable<AlerteIndicateur>> GetByIndicateurAsync(string indicateur)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE Indicateur = @Indicateur AND EstActif = 1 ORDER BY Severite DESC";
                return await connection.QueryAsync<AlerteIndicateur>(query, new { Indicateur = indicateur });
            }
        }

        /// <summary>
        /// Récupère les alertes par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des alertes pour l'utilisateur spécifié</returns>
        public async Task<IEnumerable<AlerteIndicateur>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE (UtilisateurId = @UtilisateurId OR UtilisateurId = 0) AND EstActif = 1 ORDER BY Severite DESC";
                return await connection.QueryAsync<AlerteIndicateur>(query, new { UtilisateurId = utilisateurId });
            }
        }

        /// <summary>
        /// Récupère les alertes actives
        /// </summary>
        /// <returns>Liste des alertes actives</returns>
        public async Task<IEnumerable<AlerteIndicateur>> GetActiveAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE EstActive = 1 AND EstActif = 1 ORDER BY Severite DESC";
                return await connection.QueryAsync<AlerteIndicateur>(query);
            }
        }

        /// <summary>
        /// Récupère les alertes par sévérité
        /// </summary>
        /// <param name="severite">Niveau de sévérité</param>
        /// <returns>Liste des alertes avec le niveau de sévérité spécifié</returns>
        public async Task<IEnumerable<AlerteIndicateur>> GetBySeveriteAsync(string severite)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE Severite = @Severite AND EstActif = 1 ORDER BY DateCreation DESC";
                return await connection.QueryAsync<AlerteIndicateur>(query, new { Severite = severite });
            }
        }

        /// <summary>
        /// Met à jour le statut de déclenchement d'une alerte
        /// </summary>
        /// <param name="id">Identifiant de l'alerte</param>
        /// <param name="dateDeclenchement">Date de déclenchement</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateTriggerStatusAsync(int id, DateTime dateDeclenchement, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $@"
                    UPDATE {_tableName}
                    SET DateDernierDeclenchement = @DateDeclenchement,
                        NombreDeclenchements = NombreDeclenchements + 1,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id AND EstActif = 1";

                var parameters = new
                {
                    Id = id,
                    DateDeclenchement = dateDeclenchement,
                    DateModification = DateTime.Now,
                    ModifiePar = modifiePar
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }

        /// <summary>
        /// Active ou désactive une alerte
        /// </summary>
        /// <param name="id">Identifiant de l'alerte</param>
        /// <param name="estActive">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> SetActiveStatusAsync(int id, bool estActive, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $@"
                    UPDATE {_tableName}
                    SET EstActive = @EstActive,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id AND EstActif = 1";

                var parameters = new
                {
                    Id = id,
                    EstActive = estActive,
                    DateModification = DateTime.Now,
                    ModifiePar = modifiePar
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }
    }
}
