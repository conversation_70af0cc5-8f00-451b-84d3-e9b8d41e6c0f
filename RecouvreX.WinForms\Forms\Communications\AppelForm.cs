using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Communications
{
    public partial class AppelForm : Form
    {
        private readonly ICommunicationService _communicationService;
        private readonly IContactClientService _contactClientService;
        private readonly int _currentUserId;
        private readonly int _factureId;
        private List<ContactClient> _contacts = new List<ContactClient>();

        public AppelForm(ICommunicationService communicationService, IContactClientService contactClientService, int currentUserId, int factureId)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _contactClientService = contactClientService ?? throw new ArgumentNullException(nameof(contactClientService));
            _currentUserId = currentUserId;
            _factureId = factureId;

            InitializeComponent();
        }

        private async void AppelForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialiser les combobox
                var directionComboBox = this.Controls.Find("directionComboBox", true)[0] as ComboBox;
                var resultatComboBox = this.Controls.Find("resultatComboBox", true)[0] as ComboBox;
                var contactComboBox = this.Controls.Find("contactComboBox", true)[0] as ComboBox;

                if (directionComboBox != null)
                {
                    directionComboBox.Items.Clear();
                    directionComboBox.Items.Add(DirectionCommunication.Entrant);
                    directionComboBox.Items.Add(DirectionCommunication.Sortant);
                    directionComboBox.SelectedIndex = 1; // Sortant par défaut
                }

                if (resultatComboBox != null)
                {
                    resultatComboBox.Items.Clear();
                    resultatComboBox.Items.Add(ResultatCommunication.Reussi);
                    resultatComboBox.Items.Add(ResultatCommunication.Echec);
                    resultatComboBox.Items.Add(ResultatCommunication.RappelDemande);
                    resultatComboBox.Items.Add(ResultatCommunication.MessageLaisse);
                    resultatComboBox.Items.Add(ResultatCommunication.PasDeReponse);
                    resultatComboBox.Items.Add(ResultatCommunication.Occupe);
                    resultatComboBox.Items.Add(ResultatCommunication.Refuse);
                    resultatComboBox.SelectedIndex = 0; // Réussi par défaut
                }

                // Charger les contacts du client
                await LoadContactsAsync();

                // Initialiser les valeurs par défaut
                var dureeMinutesNumericUpDown = this.Controls.Find("dureeMinutesNumericUpDown", true)[0] as NumericUpDown;
                var dureeSecondesNumericUpDown = this.Controls.Find("dureeSecondesNumericUpDown", true)[0] as NumericUpDown;
                var dateSuiviPicker = this.Controls.Find("dateSuiviPicker", true)[0] as DateTimePicker;

                if (dureeMinutesNumericUpDown != null)
                    dureeMinutesNumericUpDown.Value = 0;

                if (dureeSecondesNumericUpDown != null)
                    dureeSecondesNumericUpDown.Value = 0;

                if (dateSuiviPicker != null)
                {
                    dateSuiviPicker.Value = DateTime.Now.AddDays(1);
                    dateSuiviPicker.Checked = false;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'appel");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadContactsAsync()
        {
            try
            {
                // Récupérer les contacts du client associé à la facture
                _contacts = (await _contactClientService.GetByClientIdAsync(_factureId)).ToList();

                // Remplir la combobox des contacts
                var contactComboBox = this.Controls.Find("contactComboBox", true)[0] as ComboBox;
                if (contactComboBox != null)
                {
                    contactComboBox.Items.Clear();
                    contactComboBox.Items.Add(new ComboBoxItem { Text = "-- Sélectionner un contact --", Value = 0 });
                    foreach (var contact in _contacts)
                    {
                        contactComboBox.Items.Add(new ComboBoxItem { Text = $"{contact.NomComplet} ({contact.Fonction})", Value = contact.Id });
                    }
                    contactComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des contacts");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des contacts : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer les valeurs des champs
                var directionComboBox = this.Controls.Find("directionComboBox", true)[0] as ComboBox;
                var resultatComboBox = this.Controls.Find("resultatComboBox", true)[0] as ComboBox;
                var contactComboBox = this.Controls.Find("contactComboBox", true)[0] as ComboBox;
                var contenuTextBox = this.Controls.Find("contenuTextBox", true)[0] as TextBox;
                var dureeMinutesNumericUpDown = this.Controls.Find("dureeMinutesNumericUpDown", true)[0] as NumericUpDown;
                var dureeSecondesNumericUpDown = this.Controls.Find("dureeSecondesNumericUpDown", true)[0] as NumericUpDown;
                var suiviNecessaireCheckBox = this.Controls.Find("suiviNecessaireCheckBox", true)[0] as CheckBox;
                var dateSuiviPicker = this.Controls.Find("dateSuiviPicker", true)[0] as DateTimePicker;

                // Valider les champs obligatoires
                if (directionComboBox?.SelectedIndex < 0)
                {
                    MessageBox.Show("Veuillez sélectionner une direction.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    directionComboBox?.Focus();
                    return;
                }

                if (resultatComboBox?.SelectedIndex < 0)
                {
                    MessageBox.Show("Veuillez sélectionner un résultat.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    resultatComboBox?.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(contenuTextBox?.Text))
                {
                    MessageBox.Show("Veuillez saisir le contenu de l'appel.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    contenuTextBox?.Focus();
                    return;
                }

                // Calculer la durée en secondes
                int dureeMinutes = (int)(dureeMinutesNumericUpDown?.Value ?? 0);
                int dureeSecondes = (int)(dureeSecondesNumericUpDown?.Value ?? 0);
                int dureeTotal = dureeMinutes * 60 + dureeSecondes;

                // Récupérer l'identifiant du contact
                int? contactId = null;
                if (contactComboBox?.SelectedIndex > 0)
                {
                    var selectedItem = contactComboBox.SelectedItem as ComboBoxItem;
                    if (selectedItem != null && selectedItem.Value > 0)
                    {
                        contactId = selectedItem.Value;
                    }
                }

                // Récupérer la date de suivi
                DateTime? dateSuivi = null;
                if (suiviNecessaireCheckBox?.Checked == true && dateSuiviPicker?.Checked == true)
                {
                    dateSuivi = dateSuiviPicker.Value;
                }

                // Enregistrer l'appel
                var communication = await _communicationService.EnregistrerAppelAsync(
                    _factureId,
                    contactId,
                    directionComboBox?.SelectedItem.ToString(),
                    dureeTotal,
                    resultatComboBox?.SelectedItem.ToString(),
                    contenuTextBox?.Text,
                    suiviNecessaireCheckBox?.Checked ?? false,
                    dateSuivi,
                    _currentUserId);

                // Fermer le formulaire
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement de l'appel");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement de l'appel : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void SuiviNecessaireCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            var suiviNecessaireCheckBox = sender as CheckBox;
            var dateSuiviPicker = this.Controls.Find("dateSuiviPicker", true)[0] as DateTimePicker;
            var dateSuiviLabel = this.Controls.Find("dateSuiviLabel", true)[0] as Label;

            if (dateSuiviPicker != null && dateSuiviLabel != null)
            {
                dateSuiviPicker.Enabled = suiviNecessaireCheckBox?.Checked ?? false;
                dateSuiviLabel.Enabled = suiviNecessaireCheckBox?.Checked ?? false;
                
                if (suiviNecessaireCheckBox?.Checked == true)
                {
                    dateSuiviPicker.Checked = true;
                }
                else
                {
                    dateSuiviPicker.Checked = false;
                }
            }
        }

        /// <summary>
        /// Classe pour les éléments de la combobox
        /// </summary>
        private class ComboBoxItem
        {
            public string Text { get; set; }
            public int Value { get; set; }

            public override string ToString()
            {
                return Text;
            }
        }
    }
}
