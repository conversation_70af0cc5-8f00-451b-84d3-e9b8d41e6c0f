using Microsoft.Extensions.DependencyInjection;
using RecouvreX.Business.Interfaces;
using RecouvreX.Business.Services;
using System;
using RecouvreX.DataAccess;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.DataAccess.Repositories;

namespace RecouvreX.Business.DependencyInjection
{
    /// <summary>
    /// Extensions pour la configuration des services
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Ajoute les services de l'application au conteneur d'injection de dépendances
        /// </summary>
        /// <param name="services">Collection de services</param>
        /// <param name="connectionString">Chaîne de connexion à la base de données</param>
        /// <returns>Collection de services mise à jour</returns>
        public static IServiceCollection AddRecouvreXServices(this IServiceCollection services, string connectionString)
        {
            if (services == null)
                throw new ArgumentNullException(nameof(services));

            if (string.IsNullOrEmpty(connectionString))
                throw new ArgumentException("La chaîne de connexion ne peut pas être vide", nameof(connectionString));

            // Ajouter la connexion à la base de données
            services.AddSingleton(new DatabaseConnection(connectionString));

            // Ajouter les repositories
            services.AddRepositories();

            // Ajouter les services métier
            services.AddBusinessServices();

            return services;
        }

        /// <summary>
        /// Ajoute les repositories au conteneur d'injection de dépendances
        /// </summary>
        /// <param name="services">Collection de services</param>
        /// <returns>Collection de services mise à jour</returns>
        private static IServiceCollection AddRepositories(this IServiceCollection services)
        {
            // Repositories
            services.AddScoped<IUtilisateurRepository, UtilisateurRepository>();
            services.AddScoped<IRoleRepository, RoleRepository>();
            services.AddScoped<IPermissionRepository, PermissionRepository>();
            services.AddScoped<IClientRepository, ClientRepository>();
            services.AddScoped<IFactureRepository, FactureRepository>();
            services.AddScoped<IPaiementRepository, PaiementRepository>();
            services.AddScoped<IRelanceRepository, RelanceRepository>();
            services.AddScoped<IDocumentRepository, DocumentRepository>();
            services.AddScoped<IJournalAuditRepository, JournalAuditRepository>();
            services.AddScoped<IAlerteRepository, AlerteRepository>();
            services.AddScoped<IModeleRelanceRepository, ModeleRelanceRepository>();
            services.AddScoped<IVariableDynamiqueRepository, VariableDynamiqueRepository>();
            services.AddScoped<IRegleRelanceRepository, RegleRelanceRepository>();
            services.AddScoped<IPlanificationRelanceRepository, PlanificationRelanceRepository>();
            services.AddScoped<ICommunicationRepository, CommunicationRepository>();
            services.AddScoped<IContactClientRepository, ContactClientRepository>();
            services.AddScoped<IScoreRisqueClientRepository, ScoreRisqueClientRepository>();
            services.AddScoped<IActionPrioritaireRepository, ActionPrioritaireRepository>();
            services.AddScoped<ILitigeRepository, LitigeRepository>();
            services.AddScoped<ICategorieLitigeRepository, CategorieLitigeRepository>();
            services.AddScoped<IEtapeLitigeRepository, EtapeLitigeRepository>();
            services.AddScoped<IHistoriqueEtapeLitigeRepository, HistoriqueEtapeLitigeRepository>();
            services.AddScoped<ICommentaireLitigeRepository, CommentaireLitigeRepository>();
            services.AddScoped<INotificationLitigeRepository, NotificationLitigeRepository>();
            services.AddScoped<IDocumentLitigeRepository, DocumentLitigeRepository>();
            services.AddScoped<IPlanPaiementRepository, PlanPaiementRepository>();
            services.AddScoped<IEcheancePaiementRepository, EcheancePaiementRepository>();
            services.AddScoped<IRapportPersonnaliseRepository, RapportPersonnaliseRepository>();

            return services;
        }

        /// <summary>
        /// Ajoute les services métier au conteneur d'injection de dépendances
        /// </summary>
        /// <param name="services">Collection de services</param>
        /// <returns>Collection de services mise à jour</returns>
        private static IServiceCollection AddBusinessServices(this IServiceCollection services)
        {
            // Services métier
            services.AddScoped<IAuthenticationService, AuthenticationService>();
            services.AddScoped<IUtilisateurService, UtilisateurService>();
            services.AddScoped<IRoleService, RoleService>();
            services.AddScoped<IClientService, ClientService>();
            services.AddScoped<IFactureService, FactureService>();
            services.AddScoped<IPaiementService, PaiementService>();
            services.AddScoped<IRelanceService, RelanceService>();
            services.AddScoped<IDocumentService, DocumentService>();
            services.AddScoped<IJournalAuditService, JournalAuditService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<IAlerteService, AlerteService>();
            services.AddScoped<IModeleRelanceService, ModeleRelanceService>();
            services.AddScoped<IRegleRelanceService, RegleRelanceService>();
            services.AddScoped<IPlanificationRelanceService, PlanificationRelanceService>();
            services.AddScoped<ICommunicationService, CommunicationService>();
            services.AddScoped<IContactClientService, ContactClientService>();
            services.AddScoped<IScoreRisqueClientService, ScoreRisqueClientService>();
            services.AddScoped<IActionPrioritaireService, ActionPrioritaireService>();
            services.AddScoped<IPlanPaiementService, PlanPaiementService>();
            services.AddScoped<ILitigeService, LitigeService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<IRapportPersonnaliseService, RapportPersonnaliseService>();
            services.AddScoped<IEmailService, EmailService>();
            services.AddScoped<IDatabaseAdminService, DatabaseAdminService>();

            return services;
        }
    }
}
