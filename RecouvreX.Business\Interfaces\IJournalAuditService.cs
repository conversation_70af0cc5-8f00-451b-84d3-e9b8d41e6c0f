using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion du journal d'audit
    /// </summary>
    public interface IJournalAuditService
    {
        /// <summary>
        /// Récupère toutes les entrées du journal d'audit
        /// </summary>
        /// <returns>Liste des entrées du journal</returns>
        Task<IEnumerable<JournalAudit>> GetAllAsync();

        /// <summary>
        /// Récupère une entrée du journal par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'entrée</param>
        /// <returns>Entrée trouvée ou null</returns>
        Task<JournalAudit> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les entrées du journal par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des entrées de l'utilisateur</returns>
        Task<IEnumerable<JournalAudit>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les entrées du journal par type d'action
        /// </summary>
        /// <param name="typeAction">Type d'action</param>
        /// <returns>Liste des entrées du type spécifié</returns>
        Task<IEnumerable<JournalAudit>> GetByTypeActionAsync(string typeAction);

        /// <summary>
        /// Récupère les entrées du journal par entité
        /// </summary>
        /// <param name="typeEntite">Type d'entité</param>
        /// <param name="entiteId">Identifiant de l'entité</param>
        /// <returns>Liste des entrées concernant l'entité spécifiée</returns>
        Task<IEnumerable<JournalAudit>> GetByEntityAsync(string typeEntite, int entiteId);

        /// <summary>
        /// Récupère les entrées du journal par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des entrées dans la période spécifiée</returns>
        Task<IEnumerable<JournalAudit>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Recherche dans le journal d'audit
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des entrées correspondant au terme de recherche</returns>
        Task<IEnumerable<JournalAudit>> SearchAsync(string searchTerm);

        /// <summary>
        /// Ajoute une entrée dans le journal d'audit
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <param name="nomUtilisateur">Nom d'utilisateur qui effectue l'action</param>
        /// <param name="typeAction">Type d'action (Création, Modification, Suppression, etc.)</param>
        /// <param name="typeEntite">Type d'entité concernée</param>
        /// <param name="entiteId">Identifiant de l'entité concernée</param>
        /// <param name="description">Description de l'action</param>
        /// <param name="donneesAvant">Données avant modification (format JSON)</param>
        /// <param name="donneesApres">Données après modification (format JSON)</param>
        /// <param name="adresseIP">Adresse IP de l'utilisateur</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        Task<JournalAudit> AddEntryAsync(
            int utilisateurId,
            string nomUtilisateur,
            string typeAction,
            string typeEntite,
            int? entiteId,
            string description,
            string donneesAvant = null,
            string donneesApres = null,
            string adresseIP = null);

        /// <summary>
        /// Journalise une action de connexion
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="adresseIP">Adresse IP de l'utilisateur</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        Task<JournalAudit> LogLoginAsync(int utilisateurId, string nomUtilisateur, string adresseIP);

        /// <summary>
        /// Journalise une action de déconnexion
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="adresseIP">Adresse IP de l'utilisateur</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        Task<JournalAudit> LogLogoutAsync(int utilisateurId, string nomUtilisateur, string adresseIP);

        /// <summary>
        /// Journalise une action de création d'entité
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="typeEntite">Type d'entité créée</param>
        /// <param name="entiteId">Identifiant de l'entité créée</param>
        /// <param name="description">Description de l'entité créée</param>
        /// <param name="donneesEntite">Données de l'entité créée (format JSON)</param>
        /// <param name="adresseIP">Adresse IP de l'utilisateur</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        Task<JournalAudit> LogCreationAsync(
            int utilisateurId,
            string nomUtilisateur,
            string typeEntite,
            int entiteId,
            string description,
            string donneesEntite,
            string adresseIP = null);

        /// <summary>
        /// Journalise une action de modification d'entité
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="typeEntite">Type d'entité modifiée</param>
        /// <param name="entiteId">Identifiant de l'entité modifiée</param>
        /// <param name="description">Description de la modification</param>
        /// <param name="donneesAvant">Données avant modification (format JSON)</param>
        /// <param name="donneesApres">Données après modification (format JSON)</param>
        /// <param name="adresseIP">Adresse IP de l'utilisateur</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        Task<JournalAudit> LogModificationAsync(
            int utilisateurId,
            string nomUtilisateur,
            string typeEntite,
            int entiteId,
            string description,
            string donneesAvant,
            string donneesApres,
            string adresseIP = null);

        /// <summary>
        /// Journalise une action de suppression d'entité
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="typeEntite">Type d'entité supprimée</param>
        /// <param name="entiteId">Identifiant de l'entité supprimée</param>
        /// <param name="description">Description de l'entité supprimée</param>
        /// <param name="donneesEntite">Données de l'entité supprimée (format JSON)</param>
        /// <param name="adresseIP">Adresse IP de l'utilisateur</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        Task<JournalAudit> LogSuppressionAsync(
            int utilisateurId,
            string nomUtilisateur,
            string typeEntite,
            int entiteId,
            string description,
            string donneesEntite,
            string adresseIP = null);
    }
}
