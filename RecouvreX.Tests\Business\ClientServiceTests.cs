using Moq;
using RecouvreX.Business.Interfaces;
using RecouvreX.Business.Services;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Xunit;

namespace RecouvreX.Tests.Business
{
    public class ClientServiceTests
    {
        private readonly Mock<IClientRepository> _mockClientRepository;
        private readonly Mock<IAuditService> _mockAuditService;
        private readonly IClientService _clientService;

        public ClientServiceTests()
        {
            _mockClientRepository = new Mock<IClientRepository>();
            _mockAuditService = new Mock<IAuditService>();
            _clientService = new ClientService(_mockClientRepository.Object, _mockAuditService.Object);
        }

        [Fact]
        public async Task GetAllAsync_ShouldReturnAllClients()
        {
            // Arrange
            var expectedClients = new List<Client>
            {
                new Client { Id = 1, Code = "CLI001", RaisonSociale = "Client 1" },
                new Client { Id = 2, Code = "CLI002", RaisonSociale = "Client 2" },
                new Client { Id = 3, Code = "CLI003", RaisonSociale = "Client 3" }
            };

            _mockClientRepository.Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(expectedClients);

            // Act
            var result = await _clientService.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedClients.Count, result.Count());
            Assert.Equal(expectedClients, result);
            _mockClientRepository.Verify(repo => repo.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithValidId_ShouldReturnClient()
        {
            // Arrange
            int clientId = 1;
            var expectedClient = new Client { Id = clientId, Code = "CLI001", RaisonSociale = "Client 1" };

            _mockClientRepository.Setup(repo => repo.GetByIdAsync(clientId))
                .ReturnsAsync(expectedClient);

            // Act
            var result = await _clientService.GetByIdAsync(clientId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedClient, result);
            _mockClientRepository.Verify(repo => repo.GetByIdAsync(clientId), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            int clientId = 999;

            _mockClientRepository.Setup(repo => repo.GetByIdAsync(clientId))
                .ReturnsAsync((Client)null);

            // Act
            var result = await _clientService.GetByIdAsync(clientId);

            // Assert
            Assert.Null(result);
            _mockClientRepository.Verify(repo => repo.GetByIdAsync(clientId), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_ShouldCreateClientAndReturnId()
        {
            // Arrange
            int userId = 1;
            int expectedClientId = 1;
            var client = new Client { RaisonSociale = "Nouveau Client", Adresse = "123 Rue Test", Ville = "Ville Test" };

            _mockClientRepository.Setup(repo => repo.CreateAsync(It.IsAny<Client>()))
                .ReturnsAsync(expectedClientId);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _clientService.CreateAsync(client, userId);

            // Assert
            Assert.Equal(expectedClientId, result);
            Assert.Equal(expectedClientId, client.Id);
            Assert.NotNull(client.Code);
            Assert.True(!string.IsNullOrEmpty(client.Code));
            _mockClientRepository.Verify(repo => repo.CreateAsync(client), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Client",
                It.IsAny<string>(),
                expectedClientId,
                userId), Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_ShouldUpdateClientAndReturnTrue()
        {
            // Arrange
            int userId = 1;
            var client = new Client { Id = 1, Code = "CLI001", RaisonSociale = "Client Modifié", Adresse = "456 Rue Test", Ville = "Ville Test" };

            _mockClientRepository.Setup(repo => repo.UpdateAsync(client))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _clientService.UpdateAsync(client, userId);

            // Assert
            Assert.True(result);
            _mockClientRepository.Verify(repo => repo.UpdateAsync(client), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Client",
                It.IsAny<string>(),
                client.Id,
                userId), Times.Once);
        }

        [Fact]
        public async Task DeleteAsync_ShouldDeleteClientAndReturnTrue()
        {
            // Arrange
            int clientId = 1;
            int userId = 1;

            _mockClientRepository.Setup(repo => repo.DeleteAsync(clientId))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _clientService.DeleteAsync(clientId, userId);

            // Assert
            Assert.True(result);
            _mockClientRepository.Verify(repo => repo.DeleteAsync(clientId), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Client",
                It.IsAny<string>(),
                clientId,
                userId), Times.Once);
        }

        [Fact]
        public async Task SearchAsync_ShouldReturnMatchingClients()
        {
            // Arrange
            string searchTerm = "Test";
            var expectedClients = new List<Client>
            {
                new Client { Id = 1, Code = "CLI001", RaisonSociale = "Client Test 1" },
                new Client { Id = 2, Code = "CLI002", RaisonSociale = "Test Client 2" }
            };

            _mockClientRepository.Setup(repo => repo.SearchAsync(searchTerm))
                .ReturnsAsync(expectedClients);

            // Act
            var result = await _clientService.SearchAsync(searchTerm);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedClients.Count, result.Count());
            Assert.Equal(expectedClients, result);
            _mockClientRepository.Verify(repo => repo.SearchAsync(searchTerm), Times.Once);
        }

        [Fact]
        public async Task GetWithContactsAsync_ShouldReturnClientWithContacts()
        {
            // Arrange
            int clientId = 1;
            var expectedClient = new Client
            {
                Id = clientId,
                Code = "CLI001",
                RaisonSociale = "Client 1",
                Contacts = new List<Contact>
                {
                    new Contact { Id = 1, ClientId = clientId, Nom = "Dupont", Prenom = "Jean" },
                    new Contact { Id = 2, ClientId = clientId, Nom = "Martin", Prenom = "Sophie" }
                }
            };

            _mockClientRepository.Setup(repo => repo.GetWithContactsAsync(clientId))
                .ReturnsAsync(expectedClient);

            // Act
            var result = await _clientService.GetWithContactsAsync(clientId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedClient, result);
            Assert.NotNull(result.Contacts);
            Assert.Equal(2, result.Contacts.Count());
            _mockClientRepository.Verify(repo => repo.GetWithContactsAsync(clientId), Times.Once);
        }

        [Fact]
        public async Task DeleteContactAsync_ShouldDeleteContactAndReturnTrue()
        {
            // Arrange
            int contactId = 1;
            int userId = 1;

            _mockClientRepository.Setup(repo => repo.DeleteContactAsync(contactId))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _clientService.DeleteContactAsync(contactId, userId);

            // Assert
            Assert.True(result);
            _mockClientRepository.Verify(repo => repo.DeleteContactAsync(contactId), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Contact",
                It.IsAny<string>(),
                contactId,
                userId), Times.Once);
        }
    }
}
