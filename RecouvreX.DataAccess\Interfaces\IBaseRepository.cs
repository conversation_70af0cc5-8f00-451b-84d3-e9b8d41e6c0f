using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface de base pour les repositories
    /// </summary>
    /// <typeparam name="T">Type d'entité</typeparam>
    public interface IBaseRepository<T> : IRepository<T> where T : BaseEntity
    {
        // Cette interface hérite de IRepository<T> et n'ajoute pas de méthodes supplémentaires
        // Elle est utilisée pour la compatibilité avec les interfaces existantes
    }
}
