# RecouvreX - Application de Gestion du Recouvrement de Créances

RecouvreX est une application Windows Forms développée en .NET 6+ pour la gestion du recouvrement de créances clients. Elle permet de suivre les factures impayées, de gérer les relances et de faciliter le processus de recouvrement.

## Fonctionnalités

### Gestion des Clients
- Création, modification et suppression de clients
- Gestion des contacts associés aux clients
- Recherche et filtrage des clients

### Gestion des Factures
- Création, modification et suivi des factures
- Suivi des statuts de paiement
- Identification des factures en retard
- Génération de rapports sur les factures

### Gestion des Paiements
- Enregistrement des paiements reçus
- Mise à jour automatique du statut des factures
- Suivi des montants restants à payer

### Gestion des Relances
- Planification et suivi des relances
- Différents types de relances (email, téléphone, courrier)
- Génération automatique de relances pour les factures en retard
- Historique des relances effectuées

### Génération de Rapports
- Rapports sur les factures en retard
- Rapports sur les paiements par période
- Rapports sur les clients avec factures en retard
- Exportation des rapports en différents formats (JSON, CSV, Excel, PDF)

### Administration
- Gestion des utilisateurs
- Gestion des rôles et permissions
- Audit des actions des utilisateurs

## Architecture

L'application est construite selon une architecture multi-couches :

### Couche Présentation (RecouvreX.WinForms)
- Interface utilisateur Windows Forms
- Formulaires pour l'interaction avec l'utilisateur
- Validation des entrées utilisateur

### Couche Métier (RecouvreX.Business)
- Services métier implémentant la logique de l'application
- Validation des règles métier
- Coordination entre la couche présentation et la couche d'accès aux données

### Couche d'Accès aux Données (RecouvreX.DataAccess)
- Repositories pour l'accès à la base de données
- Utilisation de Dapper ORM pour les requêtes SQL
- Gestion des transactions

### Couche Modèles (RecouvreX.Models)
- Classes de modèles représentant les entités de l'application
- DTOs (Data Transfer Objects) pour le transfert de données entre les couches

## Technologies Utilisées

- **Langage de programmation** : C#
- **Framework** : .NET 6+
- **Interface utilisateur** : Windows Forms
- **ORM** : Dapper
- **Base de données** : SQL Server Express
- **Journalisation** : Serilog
- **Tests unitaires** : xUnit, Moq

## Installation

### Prérequis
- .NET 6 SDK ou supérieur
- SQL Server Express 2019 ou supérieur
- Visual Studio 2022 ou supérieur (recommandé)

### Étapes d'installation
1. Clonez le dépôt Git
2. Ouvrez la solution dans Visual Studio
3. Restaurez les packages NuGet
4. Configurez la chaîne de connexion à la base de données dans le fichier `appsettings.json`
5. Exécutez les scripts SQL pour créer la base de données et les tables
6. Compilez et exécutez l'application

## Configuration

La configuration de l'application se fait via le fichier `appsettings.json` :

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost\\SQLEXPRESS;Database=RecouvreX;Trusted_Connection=True;MultipleActiveResultSets=true"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning"
    }
  },
  "AppSettings": {
    "CompanyName": "Votre Entreprise",
    "CompanyLogo": "logo.png",
    "DefaultCurrency": "EUR",
    "DefaultPaymentDelay": 30,
    "AutomaticReminders": true
  }
}
```

## Utilisation

### Connexion
- Lancez l'application
- Connectez-vous avec vos identifiants
- L'interface principale s'affiche avec les différentes fonctionnalités accessibles selon vos permissions

### Gestion des Clients
- Ajoutez de nouveaux clients avec leurs informations de contact
- Modifiez les informations des clients existants
- Consultez l'historique des factures et des paiements par client

### Gestion des Factures
- Créez de nouvelles factures pour les clients
- Suivez le statut des factures (en attente, payée partiellement, payée, en retard)
- Identifiez rapidement les factures en retard

### Gestion des Paiements
- Enregistrez les paiements reçus des clients
- Associez les paiements aux factures correspondantes
- Suivez les montants restants à payer

### Gestion des Relances
- Planifiez des relances pour les factures en retard
- Suivez l'historique des relances effectuées
- Générez automatiquement des relances pour les factures en retard

### Génération de Rapports
- Générez des rapports sur les factures en retard
- Analysez les paiements par période
- Identifiez les clients avec des factures en retard
- Exportez les rapports en différents formats

## Maintenance

### Sauvegarde de la Base de Données
Il est recommandé de sauvegarder régulièrement la base de données pour éviter la perte de données. Utilisez les outils de sauvegarde de SQL Server pour planifier des sauvegardes automatiques.

### Mise à Jour
Les mises à jour de l'application seront fournies sous forme de nouvelles versions. Pour mettre à jour l'application :
1. Sauvegardez la base de données
2. Installez la nouvelle version
3. Exécutez les scripts de migration si nécessaire

## Support

Pour toute question ou problème, veuillez contacter le support technique à l'adresse <EMAIL>.

## Licence

Ce logiciel est protégé par le droit d'auteur. Tous droits réservés.
