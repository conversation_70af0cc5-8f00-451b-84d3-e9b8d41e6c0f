namespace RecouvreX.Models.Enums
{
    /// <summary>
    /// Statuts possibles pour un plan de paiement
    /// </summary>
    public enum StatutPlanPaiement
    {
        /// <summary>
        /// Plan de paiement en attente de validation
        /// </summary>
        EnAttente = 1,

        /// <summary>
        /// Plan de paiement actif et en cours
        /// </summary>
        EnCours = 2,

        /// <summary>
        /// Plan de paiement terminé (toutes les échéances sont payées)
        /// </summary>
        Termine = 3,

        /// <summary>
        /// Plan de paiement en retard (au moins une échéance est en retard)
        /// </summary>
        EnRetard = 4,

        /// <summary>
        /// Plan de paiement annulé
        /// </summary>
        Annule = 5,

        /// <summary>
        /// Plan de paiement rompu (non-respect des conditions)
        /// </summary>
        Rompu = 6
    }
}
