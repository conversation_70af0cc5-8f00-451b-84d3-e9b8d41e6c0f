namespace RecouvreX.WinForms.Forms.Communications
{
    partial class CommunicationListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            toolStrip = new ToolStrip();
            addAppelButton = new ToolStripButton();
            addEmailButton = new ToolStripButton();
            addNoteButton = new ToolStripButton();
            refreshButton = new ToolStripButton();
            filterPanel = new Panel();
            filterTable = new TableLayoutPanel();
            typeLabel = new Label();
            typeFilterComboBox = new ComboBox();
            dateDebutLabel = new Label();
            dateDebutPicker = new DateTimePicker();
            dateFinLabel = new Label();
            dateFinPicker = new DateTimePicker();
            searchLabel = new Label();
            searchTextBox = new TextBox();
            suiviLabel = new Label();
            suiviCheckBox = new CheckBox();
            applyFilterButton = new Button();
            dataGridView = new DataGridView();
            statusStrip = new StatusStrip();
            countLabel = new ToolStripStatusLabel();
            toolStrip.SuspendLayout();
            filterPanel.SuspendLayout();
            filterTable.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView).BeginInit();
            statusStrip.SuspendLayout();
            SuspendLayout();
            // 
            // toolStrip
            // 
            toolStrip.GripStyle = ToolStripGripStyle.Hidden;
            toolStrip.Items.AddRange(new ToolStripItem[] { addAppelButton, addEmailButton, addNoteButton, refreshButton });
            toolStrip.Location = new Point(0, 80);
            toolStrip.Name = "toolStrip";
            toolStrip.Size = new Size(1000, 25);
            toolStrip.TabIndex = 0;
            // 
            // addAppelButton
            // 
            addAppelButton.Name = "addAppelButton";
            addAppelButton.Size = new Size(81, 22);
            addAppelButton.Text = "Nouvel appel";
            addAppelButton.Click += AddAppelButton_Click;
            // 
            // addEmailButton
            // 
            addEmailButton.Name = "addEmailButton";
            addEmailButton.Size = new Size(81, 22);
            addEmailButton.Text = "Nouvel email";
            addEmailButton.Click += AddEmailButton_Click;
            // 
            // addNoteButton
            // 
            addNoteButton.Name = "addNoteButton";
            addNoteButton.Size = new Size(85, 22);
            addNoteButton.Text = "Nouvelle note";
            addNoteButton.Click += AddNoteButton_Click;
            // 
            // refreshButton
            // 
            refreshButton.Name = "refreshButton";
            refreshButton.Size = new Size(61, 22);
            refreshButton.Text = "Rafraîchir";
            refreshButton.Click += RefreshButton_Click;
            // 
            // filterPanel
            // 
            filterPanel.Controls.Add(filterTable);
            filterPanel.Dock = DockStyle.Top;
            filterPanel.Location = new Point(0, 0);
            filterPanel.Name = "filterPanel";
            filterPanel.Padding = new Padding(10);
            filterPanel.Size = new Size(1000, 80);
            filterPanel.TabIndex = 1;
            // 
            // filterTable
            // 
            filterTable.ColumnCount = 6;
            filterTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.66F));
            filterTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.66F));
            filterTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.66F));
            filterTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.66F));
            filterTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.66F));
            filterTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.66F));
            filterTable.Controls.Add(typeLabel, 0, 0);
            filterTable.Controls.Add(typeFilterComboBox, 1, 0);
            filterTable.Controls.Add(dateDebutLabel, 2, 0);
            filterTable.Controls.Add(dateDebutPicker, 3, 0);
            filterTable.Controls.Add(dateFinLabel, 4, 0);
            filterTable.Controls.Add(dateFinPicker, 5, 0);
            filterTable.Controls.Add(searchLabel, 0, 1);
            filterTable.Controls.Add(searchTextBox, 1, 1);
            filterTable.Controls.Add(suiviLabel, 2, 1);
            filterTable.Controls.Add(suiviCheckBox, 3, 1);
            filterTable.Controls.Add(applyFilterButton, 5, 1);
            filterTable.Dock = DockStyle.Fill;
            filterTable.Location = new Point(10, 10);
            filterTable.Name = "filterTable";
            filterTable.RowCount = 2;
            filterTable.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            filterTable.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            filterTable.Size = new Size(980, 60);
            filterTable.TabIndex = 0;
            // 
            // typeLabel
            // 
            typeLabel.Dock = DockStyle.Fill;
            typeLabel.Location = new Point(3, 0);
            typeLabel.Name = "typeLabel";
            typeLabel.Size = new Size(157, 30);
            typeLabel.TabIndex = 0;
            typeLabel.Text = "Type :";
            typeLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // typeFilterComboBox
            // 
            typeFilterComboBox.Dock = DockStyle.Fill;
            typeFilterComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            typeFilterComboBox.Location = new Point(166, 3);
            typeFilterComboBox.Name = "typeFilterComboBox";
            typeFilterComboBox.Size = new Size(157, 23);
            typeFilterComboBox.TabIndex = 1;
            // 
            // dateDebutLabel
            // 
            dateDebutLabel.Dock = DockStyle.Fill;
            dateDebutLabel.Location = new Point(329, 0);
            dateDebutLabel.Name = "dateDebutLabel";
            dateDebutLabel.Size = new Size(157, 30);
            dateDebutLabel.TabIndex = 2;
            dateDebutLabel.Text = "Date début :";
            dateDebutLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // dateDebutPicker
            // 
            dateDebutPicker.Checked = false;
            dateDebutPicker.Dock = DockStyle.Fill;
            dateDebutPicker.Format = DateTimePickerFormat.Short;
            dateDebutPicker.Location = new Point(492, 3);
            dateDebutPicker.Name = "dateDebutPicker";
            dateDebutPicker.ShowCheckBox = true;
            dateDebutPicker.Size = new Size(157, 23);
            dateDebutPicker.TabIndex = 3;
            // 
            // dateFinLabel
            // 
            dateFinLabel.Dock = DockStyle.Fill;
            dateFinLabel.Location = new Point(655, 0);
            dateFinLabel.Name = "dateFinLabel";
            dateFinLabel.Size = new Size(157, 30);
            dateFinLabel.TabIndex = 4;
            dateFinLabel.Text = "Date fin :";
            dateFinLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // dateFinPicker
            // 
            dateFinPicker.Checked = false;
            dateFinPicker.Dock = DockStyle.Fill;
            dateFinPicker.Format = DateTimePickerFormat.Short;
            dateFinPicker.Location = new Point(818, 3);
            dateFinPicker.Name = "dateFinPicker";
            dateFinPicker.ShowCheckBox = true;
            dateFinPicker.Size = new Size(159, 23);
            dateFinPicker.TabIndex = 5;
            // 
            // searchLabel
            // 
            searchLabel.Dock = DockStyle.Fill;
            searchLabel.Location = new Point(3, 30);
            searchLabel.Name = "searchLabel";
            searchLabel.Size = new Size(157, 30);
            searchLabel.TabIndex = 6;
            searchLabel.Text = "Recherche :";
            searchLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // searchTextBox
            // 
            searchTextBox.Dock = DockStyle.Fill;
            searchTextBox.Location = new Point(166, 33);
            searchTextBox.Name = "searchTextBox";
            searchTextBox.Size = new Size(157, 23);
            searchTextBox.TabIndex = 7;
            searchTextBox.KeyDown += SearchTextBox_KeyDown;
            // 
            // suiviLabel
            // 
            suiviLabel.Dock = DockStyle.Fill;
            suiviLabel.Location = new Point(329, 30);
            suiviLabel.Name = "suiviLabel";
            suiviLabel.Size = new Size(157, 30);
            suiviLabel.TabIndex = 8;
            suiviLabel.Text = "Suivi nécessaire :";
            suiviLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // suiviCheckBox
            // 
            suiviCheckBox.Dock = DockStyle.Fill;
            suiviCheckBox.Location = new Point(492, 33);
            suiviCheckBox.Name = "suiviCheckBox";
            suiviCheckBox.Size = new Size(157, 24);
            suiviCheckBox.TabIndex = 9;
            suiviCheckBox.Text = "Oui";
            // 
            // applyFilterButton
            // 
            applyFilterButton.Dock = DockStyle.Fill;
            applyFilterButton.Location = new Point(818, 33);
            applyFilterButton.Name = "applyFilterButton";
            applyFilterButton.Size = new Size(159, 24);
            applyFilterButton.TabIndex = 10;
            applyFilterButton.Text = "Appliquer les filtres";
            applyFilterButton.Click += ApplyFilterButton_Click;
            // 
            // dataGridView
            // 
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView.Dock = DockStyle.Fill;
            dataGridView.Location = new Point(0, 0);
            dataGridView.MultiSelect = false;
            dataGridView.Name = "dataGridView";
            dataGridView.ReadOnly = true;
            dataGridView.RowHeadersVisible = false;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.Size = new Size(1000, 578);
            dataGridView.TabIndex = 2;
            dataGridView.CellDoubleClick += DataGridView_CellDoubleClick;
            // 
            // statusStrip
            // 
            statusStrip.Items.AddRange(new ToolStripItem[] { countLabel });
            statusStrip.Location = new Point(0, 578);
            statusStrip.Name = "statusStrip";
            statusStrip.Size = new Size(1000, 22);
            statusStrip.TabIndex = 3;
            // 
            // countLabel
            // 
            countLabel.Name = "countLabel";
            countLabel.Size = new Size(175, 17);
            countLabel.Text = "Nombre de communications : 0";
            // 
            // CommunicationListForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1000, 600);
            Controls.Add(toolStrip);
            Controls.Add(filterPanel);
            Controls.Add(dataGridView);
            Controls.Add(statusStrip);
            Name = "CommunicationListForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Historique des communications";
            Load += CommunicationListForm_Load;
            toolStrip.ResumeLayout(false);
            toolStrip.PerformLayout();
            filterPanel.ResumeLayout(false);
            filterTable.ResumeLayout(false);
            filterTable.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView).EndInit();
            statusStrip.ResumeLayout(false);
            statusStrip.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private ToolStrip toolStrip;
        private ToolStripButton addAppelButton;
        private ToolStripButton addEmailButton;
        private ToolStripButton addNoteButton;
        private ToolStripButton refreshButton;
        private Panel filterPanel;
        private TableLayoutPanel filterTable;
        private Label typeLabel;
        private ComboBox typeFilterComboBox;
        private Label dateDebutLabel;
        private DateTimePicker dateDebutPicker;
        private Label dateFinLabel;
        private DateTimePicker dateFinPicker;
        private Label searchLabel;
        private TextBox searchTextBox;
        private Label suiviLabel;
        private CheckBox suiviCheckBox;
        private Button applyFilterButton;
        private DataGridView dataGridView;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel countLabel;
    }
}
