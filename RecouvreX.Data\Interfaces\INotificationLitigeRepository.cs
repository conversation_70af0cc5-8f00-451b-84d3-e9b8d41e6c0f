using RecouvreX.Models;

namespace RecouvreX.Data.Interfaces
{
    /// <summary>
    /// Interface pour le repository des notifications de litiges
    /// </summary>
    public interface INotificationLitigeRepository
    {
        /// <summary>
        /// Récupère toutes les notifications
        /// </summary>
        /// <returns>Liste de toutes les notifications</returns>
        Task<IEnumerable<NotificationLitige>> GetAllAsync();

        /// <summary>
        /// Récupère une notification par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la notification</param>
        /// <returns>Notification trouvée ou null</returns>
        Task<NotificationLitige> GetByIdAsync(int id);

        /// <summary>
        /// Récupère toutes les notifications pour un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des notifications de l'utilisateur</returns>
        Task<IEnumerable<NotificationLitige>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les notifications non lues pour un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des notifications non lues de l'utilisateur</returns>
        Task<IEnumerable<NotificationLitige>> GetUnreadByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère toutes les notifications pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des notifications du litige</returns>
        Task<IEnumerable<NotificationLitige>> GetByLitigeIdAsync(int litigeId);

        /// <summary>
        /// Ajoute une notification
        /// </summary>
        /// <param name="notification">Notification à ajouter</param>
        /// <returns>Notification ajoutée</returns>
        Task<NotificationLitige> AddAsync(NotificationLitige notification);

        /// <summary>
        /// Marque une notification comme lue
        /// </summary>
        /// <param name="id">Identifiant de la notification</param>
        /// <returns>True si la notification a été marquée comme lue</returns>
        Task<bool> MarkAsReadAsync(int id);

        /// <summary>
        /// Marque toutes les notifications d'un utilisateur comme lues
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Nombre de notifications marquées comme lues</returns>
        Task<int> MarkAllAsReadAsync(int utilisateurId);
    }
}
