using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using RecouvreX.WinForms.Helpers;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace RecouvreX.WinForms.Forms.Communications
{
    public partial class CommunicationListForm : Form
    {
        private readonly ICommunicationService _communicationService;
        private readonly IFactureService _factureService;
        private readonly IClientService _clientService;
        private readonly IContactClientService _contactClientService;
        private readonly IAuthenticationService _authenticationService;
        private readonly IConfiguration _configuration;
        private readonly IServiceProvider _serviceProvider;
        private readonly int _currentUserId;
        private readonly int? _factureId;
        private readonly int? _clientId;
        private List<Communication> _communications = new List<Communication>();
        private DataTable _dataTable = new DataTable();

        public CommunicationListForm(
            ICommunicationService communicationService,
            IFactureService factureService,
            IClientService clientService,
            IContactClientService contactClientService,
            IAuthenticationService authenticationService,
            IConfiguration configuration,
            IServiceProvider serviceProvider,
            int currentUserId,
            int? factureId = null,
            int? clientId = null)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _contactClientService = contactClientService ?? throw new ArgumentNullException(nameof(contactClientService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _currentUserId = currentUserId;
            _factureId = factureId;
            _clientId = clientId;

            InitializeComponent();
        }

        private async void CommunicationListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Vérifier les permissions
                bool canView = await _authenticationService.HasPermissionAsync(_currentUserId, "COMMUNICATIONS_VIEW");
                bool canAdd = await _authenticationService.HasPermissionAsync(_currentUserId, "COMMUNICATIONS_ADD");
                bool canEdit = await _authenticationService.HasPermissionAsync(_currentUserId, "COMMUNICATIONS_EDIT");
                bool canDelete = await _authenticationService.HasPermissionAsync(_currentUserId, "COMMUNICATIONS_DELETE");

                if (!canView)
                {
                    MessageBox.Show("Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.",
                        "Accès refusé", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return;
                }

                // Configurer les boutons en fonction des permissions
                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                var addAppelButton = toolStrip?.Items.Find("addAppelButton", false).FirstOrDefault() as ToolStripButton;
                var addEmailButton = toolStrip?.Items.Find("addEmailButton", false).FirstOrDefault() as ToolStripButton;
                var addNoteButton = toolStrip?.Items.Find("addNoteButton", false).FirstOrDefault() as ToolStripButton;
                var editButton = toolStrip?.Items.Find("editButton", false).FirstOrDefault() as ToolStripButton;
                var deleteButton = toolStrip?.Items.Find("deleteButton", false).FirstOrDefault() as ToolStripButton;

                if (addAppelButton != null) addAppelButton.Enabled = canAdd;
                if (addEmailButton != null) addEmailButton.Enabled = canAdd;
                if (addNoteButton != null) addNoteButton.Enabled = canAdd;
                if (editButton != null) editButton.Enabled = canEdit;
                if (deleteButton != null) deleteButton.Enabled = canDelete;

                // Initialiser la table de données
                InitializeDataTable();

                // Initialiser les filtres
                var typeFilterComboBox = this.Controls.Find("typeFilterComboBox", true).FirstOrDefault() as ComboBox;
                if (typeFilterComboBox != null)
                {
                    typeFilterComboBox.Items.Clear();
                    typeFilterComboBox.Items.Add("Tous les types");
                    typeFilterComboBox.Items.Add(TypeCommunication.Appel);
                    typeFilterComboBox.Items.Add(TypeCommunication.Email);
                    typeFilterComboBox.Items.Add(TypeCommunication.Courrier);
                    typeFilterComboBox.Items.Add(TypeCommunication.SMS);
                    typeFilterComboBox.Items.Add(TypeCommunication.Note);
                    typeFilterComboBox.Items.Add(TypeCommunication.Relance);
                    typeFilterComboBox.SelectedIndex = 0;
                }

                // Configurer le titre du formulaire
                if (_factureId.HasValue)
                {
                    var facture = await _factureService.GetByIdAsync(_factureId.Value);
                    if (facture != null)
                    {
                        this.Text = $"Communications - Facture {facture.Numero}";
                    }
                }
                else if (_clientId.HasValue)
                {
                    var client = await _clientService.GetByIdAsync(_clientId.Value);
                    if (client != null)
                    {
                        this.Text = $"Communications - Client {client.RaisonSociale}";
                    }
                }

                // Charger les communications
                await LoadCommunicationsAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire de liste des communications");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Type", typeof(string));
            _dataTable.Columns.Add("Direction", typeof(string));
            _dataTable.Columns.Add("Date", typeof(DateTime));
            _dataTable.Columns.Add("Utilisateur", typeof(string));
            _dataTable.Columns.Add("Contact", typeof(string));
            _dataTable.Columns.Add("Objet", typeof(string));
            _dataTable.Columns.Add("Résultat", typeof(string));
            _dataTable.Columns.Add("Suivi", typeof(bool));
            _dataTable.Columns.Add("Date suivi", typeof(DateTime));

            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (dataGridView.Columns["Date"] != null)
                    dataGridView.Columns["Date"].DefaultCellStyle.Format = "dd/MM/yyyy HH:mm";
                if (dataGridView.Columns["Date suivi"] != null)
                    dataGridView.Columns["Date suivi"].DefaultCellStyle.Format = "dd/MM/yyyy";

                // Ajouter un gestionnaire d'événements pour la mise en forme des cellules
                dataGridView.CellFormatting += DataGridView_CellFormatting;
            }
        }

        private void DataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            var dataGridView = sender as DataGridView;
            if (dataGridView != null && e.RowIndex >= 0)
            {
                // Colorer la cellule Type en fonction du type
                if (e.ColumnIndex == dataGridView.Columns["Type"].Index && e.Value != null)
                {
                    string type = e.Value.ToString();
                    e.CellStyle.ForeColor = CommunicationHelper.GetTypeColor(type);
                    e.CellStyle.Font = new Font(dataGridView.Font, FontStyle.Bold);
                }

                // Colorer la cellule Direction en fonction de la direction
                if (e.ColumnIndex == dataGridView.Columns["Direction"].Index && e.Value != null)
                {
                    string direction = e.Value.ToString();
                    e.CellStyle.ForeColor = CommunicationHelper.GetDirectionColor(direction);
                }

                // Colorer la cellule Résultat en fonction du résultat
                if (e.ColumnIndex == dataGridView.Columns["Résultat"].Index && e.Value != null)
                {
                    string resultat = e.Value.ToString();
                    e.CellStyle.ForeColor = CommunicationHelper.GetResultatColor(resultat);
                }

                // Mettre en évidence les communications nécessitant un suivi
                if (e.ColumnIndex == dataGridView.Columns["Suivi"].Index && e.Value != null)
                {
                    bool suiviNecessaire = (bool)e.Value;
                    if (suiviNecessaire)
                    {
                        e.CellStyle.BackColor = Color.LightYellow;
                        e.CellStyle.ForeColor = Color.Red;
                        e.Value = "Oui";
                    }
                    else
                    {
                        e.Value = "Non";
                    }
                }
            }
        }

        private async Task LoadCommunicationsAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer les communications
                if (_factureId.HasValue)
                {
                    _communications = (await _communicationService.GetByFactureIdAsync(_factureId.Value)).ToList();
                }
                else if (_clientId.HasValue)
                {
                    _communications = (await _communicationService.GetByClientIdAsync(_clientId.Value)).ToList();
                }
                else
                {
                    _communications = (await _communicationService.GetAllAsync()).ToList();
                }

                // Appliquer les filtres
                ApplyFilters();

                // Mettre à jour le compteur
                var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
                var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
                if (countLabel != null)
                {
                    countLabel.Text = $"Nombre de communications : {_communications.Count}";
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des communications");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des communications : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void ApplyFilters()
        {
            try
            {
                // Récupérer les valeurs des filtres
                var typeFilterComboBox = this.Controls.Find("typeFilterComboBox", true).FirstOrDefault() as ComboBox;
                var searchTextBox = this.Controls.Find("searchTextBox", true).FirstOrDefault() as TextBox;
                var dateDebutPicker = this.Controls.Find("dateDebutPicker", true).FirstOrDefault() as DateTimePicker;
                var dateFinPicker = this.Controls.Find("dateFinPicker", true).FirstOrDefault() as DateTimePicker;
                var suiviCheckBox = this.Controls.Find("suiviCheckBox", true).FirstOrDefault() as CheckBox;

                string typeFilter = typeFilterComboBox?.SelectedIndex > 0 ? typeFilterComboBox.SelectedItem.ToString() : null;
                string searchText = searchTextBox?.Text?.Trim().ToLower() ?? string.Empty;
                DateTime? dateDebut = dateDebutPicker?.Checked == true ? dateDebutPicker.Value.Date : (DateTime?)null;
                DateTime? dateFin = dateFinPicker?.Checked == true ? dateFinPicker.Value.Date.AddDays(1).AddSeconds(-1) : (DateTime?)null;
                bool? suiviFilter = suiviCheckBox?.Checked == true ? true : (bool?)null;

                // Filtrer les communications
                var filteredCommunications = _communications.AsEnumerable();

                if (!string.IsNullOrEmpty(typeFilter))
                {
                    filteredCommunications = filteredCommunications.Where(c => c.Type == typeFilter);
                }

                if (!string.IsNullOrEmpty(searchText))
                {
                    filteredCommunications = filteredCommunications.Where(c =>
                        (c.Objet?.ToLower().Contains(searchText) == true) ||
                        (c.Contenu?.ToLower().Contains(searchText) == true) ||
                        (c.Utilisateur?.NomComplet?.ToLower().Contains(searchText) == true) ||
                        (c.ContactClient?.NomComplet?.ToLower().Contains(searchText) == true)
                    );
                }

                if (dateDebut.HasValue)
                {
                    filteredCommunications = filteredCommunications.Where(c => c.DateCommunication >= dateDebut.Value);
                }

                if (dateFin.HasValue)
                {
                    filteredCommunications = filteredCommunications.Where(c => c.DateCommunication <= dateFin.Value);
                }

                if (suiviFilter.HasValue)
                {
                    filteredCommunications = filteredCommunications.Where(c => c.SuiviNecessaire == suiviFilter.Value);
                }

                // Mettre à jour la table de données
                UpdateDataTable(filteredCommunications.ToList());
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'application des filtres");
                MessageBox.Show($"Une erreur s'est produite lors de l'application des filtres : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateDataTable(List<Communication> communications)
        {
            _dataTable.Clear();

            foreach (var communication in communications)
            {
                _dataTable.Rows.Add(
                    communication.Id,
                    communication.Type,
                    communication.Direction,
                    communication.DateCommunication,
                    communication.Utilisateur?.NomComplet ?? $"Utilisateur #{communication.UtilisateurId}",
                    communication.ContactClient?.NomComplet ?? string.Empty,
                    communication.Objet ?? string.Empty,
                    communication.Resultat ?? string.Empty,
                    communication.SuiviNecessaire,
                    communication.DateSuivi
                );
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            // Recharger les communications
            await LoadCommunicationsAsync();
        }

        private void ApplyFilterButton_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                ApplyFilters();
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        private async void AddAppelButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Vérifier qu'une facture est sélectionnée
                if (!_factureId.HasValue && !_clientId.HasValue)
                {
                    MessageBox.Show("Veuillez d'abord sélectionner une facture ou un client.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                int factureId = _factureId ?? 0;

                // Si on est dans le contexte d'un client, demander de sélectionner une facture
                if (_clientId.HasValue && !_factureId.HasValue)
                {
                    // TODO: Implémenter la sélection d'une facture
                    MessageBox.Show("La sélection d'une facture n'est pas encore implémentée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Ouvrir le formulaire d'ajout d'appel
                using (var form = new AppelForm(_communicationService, _contactClientService, _currentUserId, factureId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les communications
                        await LoadCommunicationsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout d'appel");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void AddEmailButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Vérifier qu'une facture est sélectionnée
                if (!_factureId.HasValue && !_clientId.HasValue)
                {
                    MessageBox.Show("Veuillez d'abord sélectionner une facture ou un client.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                int factureId = _factureId ?? 0;

                // Si on est dans le contexte d'un client, demander de sélectionner une facture
                if (_clientId.HasValue && !_factureId.HasValue)
                {
                    // TODO: Implémenter la sélection d'une facture
                    MessageBox.Show("La sélection d'une facture n'est pas encore implémentée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Ouvrir le formulaire d'ajout d'email
                using (var form = new EmailForm(_communicationService, _contactClientService, _configuration, _currentUserId, factureId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les communications
                        await LoadCommunicationsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout d'email");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void AddNoteButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Vérifier qu'une facture est sélectionnée
                if (!_factureId.HasValue && !_clientId.HasValue)
                {
                    MessageBox.Show("Veuillez d'abord sélectionner une facture ou un client.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                int factureId = _factureId ?? 0;

                // Si on est dans le contexte d'un client, demander de sélectionner une facture
                if (_clientId.HasValue && !_factureId.HasValue)
                {
                    // TODO: Implémenter la sélection d'une facture
                    MessageBox.Show("La sélection d'une facture n'est pas encore implémentée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Ouvrir le formulaire d'ajout de note
                using (var form = new NoteForm(_communicationService, _currentUserId, factureId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les communications
                        await LoadCommunicationsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout de note");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int communicationId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    ShowDetails(communicationId);
                }
            }
        }

        private async void ShowDetails(int communicationId)
        {
            try
            {
                // Récupérer la communication
                var communication = await _communicationService.GetByIdAsync(communicationId);
                if (communication == null)
                {
                    MessageBox.Show("La communication demandée n'existe pas ou a été supprimée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Afficher les détails de la communication
                CommunicationHelper.ShowDetails(communication);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'affichage des détails d'une communication");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
