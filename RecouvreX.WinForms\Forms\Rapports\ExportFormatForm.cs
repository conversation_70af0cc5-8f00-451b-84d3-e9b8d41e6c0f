using System;
using System.Drawing;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Rapports
{
    public partial class ExportFormatForm : Form
    {
        public string SelectedFormat { get; private set; } = "PDF";

        public ExportFormatForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form
            this.Text = "Format d'exportation";
            this.Size = new Size(300, 200);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimizeBox = false;
            this.MaximizeBox = false;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.AcceptButton = new Button(); // Sera défini plus tard
            this.CancelButton = new Button(); // Sera défini plus tard

            // Main panel
            TableLayoutPanel mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 1,
                RowCount = 2,
                RowStyles = {
                    new RowStyle(SizeType.Percent, 100),
                    new RowStyle(SizeType.Absolute, 40)
                }
            };
            this.Controls.Add(mainPanel);

            // Format panel
            GroupBox formatGroupBox = new GroupBox
            {
                Text = "Format d'exportation",
                Dock = DockStyle.Fill
            };
            mainPanel.Controls.Add(formatGroupBox, 0, 0);

            TableLayoutPanel formatPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 1,
                RowCount = 4
            };
            formatGroupBox.Controls.Add(formatPanel);

            // PDF radio button
            RadioButton pdfRadioButton = new RadioButton
            {
                Name = "pdfRadioButton",
                Text = "PDF",
                Checked = true,
                Dock = DockStyle.Fill
            };
            pdfRadioButton.CheckedChanged += FormatRadioButton_CheckedChanged;
            formatPanel.Controls.Add(pdfRadioButton, 0, 0);

            // Excel radio button
            RadioButton excelRadioButton = new RadioButton
            {
                Name = "excelRadioButton",
                Text = "Excel",
                Dock = DockStyle.Fill
            };
            excelRadioButton.CheckedChanged += FormatRadioButton_CheckedChanged;
            formatPanel.Controls.Add(excelRadioButton, 0, 1);

            // CSV radio button
            RadioButton csvRadioButton = new RadioButton
            {
                Name = "csvRadioButton",
                Text = "CSV",
                Dock = DockStyle.Fill
            };
            csvRadioButton.CheckedChanged += FormatRadioButton_CheckedChanged;
            formatPanel.Controls.Add(csvRadioButton, 0, 2);

            // JSON radio button
            RadioButton jsonRadioButton = new RadioButton
            {
                Name = "jsonRadioButton",
                Text = "JSON",
                Dock = DockStyle.Fill
            };
            jsonRadioButton.CheckedChanged += FormatRadioButton_CheckedChanged;
            formatPanel.Controls.Add(jsonRadioButton, 0, 3);

            // Buttons panel
            TableLayoutPanel buttonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 100),
                    new ColumnStyle(SizeType.Absolute, 100),
                    new ColumnStyle(SizeType.Absolute, 100)
                }
            };
            mainPanel.Controls.Add(buttonsPanel, 0, 1);

            Button okButton = new Button
            {
                Name = "okButton",
                Text = "OK",
                Dock = DockStyle.Fill,
                Margin = new Padding(3),
                DialogResult = DialogResult.OK
            };
            buttonsPanel.Controls.Add(okButton, 1, 0);
            this.AcceptButton = okButton;

            Button cancelButton = new Button
            {
                Name = "cancelButton",
                Text = "Annuler",
                Dock = DockStyle.Fill,
                Margin = new Padding(3),
                DialogResult = DialogResult.Cancel
            };
            buttonsPanel.Controls.Add(cancelButton, 2, 0);
            this.CancelButton = cancelButton;

            this.ResumeLayout(false);
        }

        private void FormatRadioButton_CheckedChanged(object sender, EventArgs e)
        {
            RadioButton radioButton = sender as RadioButton;
            if (radioButton != null && radioButton.Checked)
            {
                switch (radioButton.Name)
                {
                    case "pdfRadioButton":
                        SelectedFormat = "PDF";
                        break;
                    case "excelRadioButton":
                        SelectedFormat = "Excel";
                        break;
                    case "csvRadioButton":
                        SelectedFormat = "CSV";
                        break;
                    case "jsonRadioButton":
                        SelectedFormat = "JSON";
                        break;
                }
            }
        }
    }
}
