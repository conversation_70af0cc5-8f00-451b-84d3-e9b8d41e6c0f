using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des modèles de relance
    /// </summary>
    public class ModeleRelanceService : IModeleRelanceService
    {
        private readonly IModeleRelanceRepository _modeleRelanceRepository;
        private readonly IVariableDynamiqueRepository _variableDynamiqueRepository;
        private readonly IFactureRepository _factureRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        public ModeleRelanceService(
            IModeleRelanceRepository modeleRelanceRepository,
            IVariableDynamiqueRepository variableDynamiqueRepository,
            IFactureRepository factureRepository,
            IClientRepository clientRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _modeleRelanceRepository = modeleRelanceRepository ?? throw new ArgumentNullException(nameof(modeleRelanceRepository));
            _variableDynamiqueRepository = variableDynamiqueRepository ?? throw new ArgumentNullException(nameof(variableDynamiqueRepository));
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère tous les modèles de relance
        /// </summary>
        /// <returns>Liste des modèles de relance</returns>
        public async Task<IEnumerable<ModeleRelance>> GetAllAsync()
        {
            return await _modeleRelanceRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère un modèle de relance par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du modèle de relance</param>
        /// <returns>Modèle de relance trouvé ou null</returns>
        public async Task<ModeleRelance> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _modeleRelanceRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère les modèles de relance par type
        /// </summary>
        /// <param name="type">Type de modèle de relance</param>
        /// <returns>Liste des modèles de relance du type spécifié</returns>
        public async Task<IEnumerable<ModeleRelance>> GetByTypeAsync(string type)
        {
            if (string.IsNullOrEmpty(type))
                return Enumerable.Empty<ModeleRelance>();

            return await _modeleRelanceRepository.GetByTypeAsync(type);
        }

        /// <summary>
        /// Récupère les modèles de relance par niveau de fermeté
        /// </summary>
        /// <param name="niveauFermete">Niveau de fermeté</param>
        /// <returns>Liste des modèles de relance du niveau spécifié</returns>
        public async Task<IEnumerable<ModeleRelance>> GetByNiveauFermeteAsync(int niveauFermete)
        {
            if (niveauFermete <= 0 || niveauFermete > 3)
                return Enumerable.Empty<ModeleRelance>();

            return await _modeleRelanceRepository.GetByNiveauFermeteAsync(niveauFermete);
        }

        /// <summary>
        /// Récupère le modèle de relance par défaut pour un type et un niveau de fermeté
        /// </summary>
        /// <param name="type">Type de modèle de relance</param>
        /// <param name="niveauFermete">Niveau de fermeté</param>
        /// <returns>Modèle de relance par défaut ou null</returns>
        public async Task<ModeleRelance> GetDefaultAsync(string type, int niveauFermete)
        {
            if (string.IsNullOrEmpty(type) || niveauFermete <= 0 || niveauFermete > 3)
                return null;

            return await _modeleRelanceRepository.GetDefaultAsync(type, niveauFermete);
        }

        /// <summary>
        /// Crée un nouveau modèle de relance
        /// </summary>
        /// <param name="modeleRelance">Modèle de relance à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Modèle de relance créé avec son identifiant généré</returns>
        public async Task<ModeleRelance> CreateAsync(ModeleRelance modeleRelance, int creePar)
        {
            if (modeleRelance == null)
                throw new ArgumentNullException(nameof(modeleRelance));

            if (creePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que l'utilisateur existe
            var utilisateur = await _utilisateurRepository.GetByIdAsync(modeleRelance.UtilisateurId);
            if (utilisateur == null)
                throw new InvalidOperationException($"L'utilisateur avec l'ID {modeleRelance.UtilisateurId} n'existe pas");

            // Créer le modèle de relance
            var createdModele = await _modeleRelanceRepository.AddAsync(modeleRelance, creePar);

            // Si le modèle est défini comme modèle par défaut, mettre à jour les autres modèles
            if (modeleRelance.EstParDefaut)
            {
                await _modeleRelanceRepository.SetAsDefaultAsync(createdModele.Id, creePar);
            }

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "ModeleRelance",
                EntiteId = createdModele.Id,
                TypeAction = "Création",
                UtilisateurId = creePar,
                DateAction = DateTime.Now,
                Description = $"Création du modèle de relance '{createdModele.Nom:s}'",
                DonneesAvant = null,
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdModele.Id,
                    createdModele.Nom,
                    createdModele.Description,
                    createdModele.Type,
                    createdModele.NiveauFermete,
                    createdModele.Objet,
                    createdModele.EstParDefaut,
                    createdModele.EstActif,
                    createdModele.UtilisateurId,
                    UtilisateurNom = utilisateur.NomUtilisateur
                })
            });

            return createdModele;
        }

        /// <summary>
        /// Met à jour un modèle de relance existant
        /// </summary>
        /// <param name="modeleRelance">Modèle de relance à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Modèle de relance mis à jour</returns>
        public async Task<ModeleRelance> UpdateAsync(ModeleRelance modeleRelance, int modifiePar)
        {
            if (modeleRelance == null)
                throw new ArgumentNullException(nameof(modeleRelance));

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que le modèle de relance existe
            var existingModele = await _modeleRelanceRepository.GetByIdAsync(modeleRelance.Id);
            if (existingModele == null)
                throw new InvalidOperationException($"Le modèle de relance avec l'ID {modeleRelance.Id} n'existe pas");

            // Vérifier que l'utilisateur existe
            var utilisateur = await _utilisateurRepository.GetByIdAsync(modeleRelance.UtilisateurId);
            if (utilisateur == null)
                throw new InvalidOperationException($"L'utilisateur avec l'ID {modeleRelance.UtilisateurId} n'existe pas");

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "ModeleRelance",
                EntiteId = modeleRelance.Id,
                TypeAction = "Modification",
                UtilisateurId = modifiePar,
                DateAction = DateTime.Now,
                Description = $"Modification du modèle de relance '{modeleRelance.Nom:s}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingModele.Id,
                    existingModele.Nom,
                    existingModele.Description,
                    existingModele.Type,
                    existingModele.NiveauFermete,
                    existingModele.Objet,
                    existingModele.EstParDefaut,
                    existingModele.EstActif,
                    existingModele.UtilisateurId,
                    UtilisateurNom = utilisateur.NomUtilisateur
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    modeleRelance.Id,
                    modeleRelance.Nom,
                    modeleRelance.Description,
                    modeleRelance.Type,
                    modeleRelance.NiveauFermete,
                    modeleRelance.Objet,
                    modeleRelance.EstParDefaut,
                    modeleRelance.EstActif,
                    modeleRelance.UtilisateurId,
                    UtilisateurNom = utilisateur.NomUtilisateur
                })
            });

            // Mettre à jour le modèle de relance
            var updatedModele = await _modeleRelanceRepository.UpdateAsync(modeleRelance, modifiePar);

            // Si le modèle est défini comme modèle par défaut, mettre à jour les autres modèles
            if (modeleRelance.EstParDefaut && !existingModele.EstParDefaut)
            {
                await _modeleRelanceRepository.SetAsDefaultAsync(updatedModele.Id, modifiePar);
            }

            return updatedModele;
        }

        /// <summary>
        /// Supprime un modèle de relance
        /// </summary>
        /// <param name="id">Identifiant du modèle de relance à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant du modèle de relance ne peut pas être négatif ou nul");

            if (supprimePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que le modèle de relance existe
            var modele = await _modeleRelanceRepository.GetByIdAsync(id);
            if (modele == null)
                return false;

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "ModeleRelance",
                EntiteId = id,
                TypeAction = "Suppression",
                UtilisateurId = supprimePar,
                DateAction = DateTime.Now,
                Description = $"Suppression du modèle de relance '{modele.Nom:s}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    modele.Id,
                    modele.Nom,
                    modele.Description,
                    modele.Type,
                    modele.NiveauFermete,
                    modele.Objet,
                    modele.EstParDefaut,
                    modele.EstActif,
                    modele.UtilisateurId
                }),
                DonneesApres = null
            });

            // Supprimer le modèle de relance
            return await _modeleRelanceRepository.DeleteAsync(id, supprimePar);
        }

        /// <summary>
        /// Définit un modèle de relance comme modèle par défaut pour son type et niveau de fermeté
        /// </summary>
        /// <param name="id">Identifiant du modèle de relance</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> SetAsDefaultAsync(int id, int modifiePar)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant du modèle de relance ne peut pas être négatif ou nul");

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que le modèle de relance existe
            var modele = await _modeleRelanceRepository.GetByIdAsync(id);
            if (modele == null)
                return false;

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "ModeleRelance",
                EntiteId = id,
                TypeAction = "Définition comme modèle par défaut",
                UtilisateurId = modifiePar,
                DateAction = DateTime.Now,
                Description = $"Définition du modèle de relance '{modele.Nom:s}' comme modèle par défaut pour le type {modele.Type:s} et le niveau de fermeté {modele.NiveauFermete}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new { EstParDefaut = modele.EstParDefaut }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new { EstParDefaut = true })
            });

            // Définir le modèle de relance comme modèle par défaut
            return await _modeleRelanceRepository.SetAsDefaultAsync(id, modifiePar);
        }

        /// <summary>
        /// Active ou désactive un modèle de relance
        /// </summary>
        /// <param name="id">Identifiant du modèle de relance</param>
        /// <param name="estActif">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> SetActiveStatusAsync(int id, bool estActif, int modifiePar)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant du modèle de relance ne peut pas être négatif ou nul");

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que le modèle de relance existe
            var modele = await _modeleRelanceRepository.GetByIdAsync(id);
            if (modele == null)
                return false;

            // Mettre à jour l'état d'activation
            modele.EstActif = estActif;

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "ModeleRelance",
                EntiteId = id,
                TypeAction = estActif ? "Activation" : "Désactivation",
                UtilisateurId = modifiePar,
                DateAction = DateTime.Now,
                Description = $"{(estActif ? "Activation" : "Désactivation")} du modèle de relance '{modele.Nom:s}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new { EstActif = !estActif }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new { EstActif = estActif })
            });

            // Mettre à jour le modèle de relance
            await _modeleRelanceRepository.UpdateAsync(modele, modifiePar);
            return true;
        }

        /// <summary>
        /// Applique les variables dynamiques à un modèle de relance
        /// </summary>
        /// <param name="modeleRelance">Modèle de relance</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Contenu du modèle avec les variables remplacées</returns>
        public async Task<string> ApplyVariablesAsync(ModeleRelance modeleRelance, int factureId)
        {
            if (modeleRelance == null)
                throw new ArgumentNullException(nameof(modeleRelance));

            if (factureId <= 0)
                throw new ArgumentException("L'identifiant de la facture ne peut pas être négatif ou nul");

            // Récupérer la facture
            var facture = await _factureRepository.GetByIdAsync(factureId);
            if (facture == null)
                throw new InvalidOperationException($"La facture avec l'ID {factureId} n'existe pas");

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(facture.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {facture.ClientId} n'existe pas");

            // Récupérer toutes les variables dynamiques
            var variables = await _variableDynamiqueRepository.GetAllAsync();

            // Contenu à traiter
            string contenu = modeleRelance.Contenu;

            // Rechercher les variables dans le contenu
            var regex = new Regex(@"\{([^}]+)\}");
            var matches = regex.Matches(contenu);

            foreach (Match match in matches)
            {
                string variableName = match.Groups[1].Value;
                string replacement = string.Empty;

                // Rechercher la variable dans la liste des variables dynamiques
                var variable = variables.FirstOrDefault(v => v.Nom.Equals(variableName, StringComparison.OrdinalIgnoreCase));
                if (variable != null)
                {
                    // Évaluer la variable en fonction de sa catégorie
                    switch (variable.Categorie)
                    {
                        case "Client":
                            replacement = EvaluateClientVariable(variable, client);
                            break;
                        case "Facture":
                            replacement = EvaluateFactureVariable(variable, facture);
                            break;
                        case "Système":
                            replacement = EvaluateSystemVariable(variable);
                            break;
                        default:
                            replacement = variable.Exemple ?? string.Empty;
                            break;
                    }
                }

                // Remplacer la variable dans le contenu
                contenu = contenu.Replace(match.Value, replacement);
            }

            return contenu;
        }

        /// <summary>
        /// Récupère toutes les variables dynamiques disponibles
        /// </summary>
        /// <returns>Liste des variables dynamiques</returns>
        public async Task<IEnumerable<VariableDynamique>> GetAllVariablesAsync()
        {
            return await _variableDynamiqueRepository.GetAllAsync();
        }

        /// <summary>
        /// Évalue une variable dynamique de type Client
        /// </summary>
        /// <param name="variable">Variable dynamique</param>
        /// <param name="client">Client</param>
        /// <returns>Valeur de la variable</returns>
        private string EvaluateClientVariable(VariableDynamique variable, Client client)
        {
            if (client == null)
                return string.Empty;

            switch (variable.Nom.ToLower())
            {
                case "client.raisonsociale":
                    return client.RaisonSociale;
                case "client.adresse":
                    return client.Adresse;
                case "client.codepostal":
                    return client.CodePostal;
                case "client.ville":
                    return client.Ville;
                case "client.pays":
                    return client.Pays;
                case "client.telephone":
                    return client.Telephone;
                case "client.email":
                    return client.Email;
                case "client.siret":
                    return client.NumeroFiscal; // Utilisation de NumeroFiscal au lieu de Siret
                case "client.contactnom":
                    // Récupérer le contact principal si disponible
                    var contactPrincipal = client.Contacts?.FirstOrDefault(c => c.EstPrincipal);
                    return contactPrincipal?.Nom ?? string.Empty;
                case "client.contactprenom":
                    // Récupérer le contact principal si disponible
                    var contactPrincipalPrenom = client.Contacts?.FirstOrDefault(c => c.EstPrincipal);
                    return contactPrincipalPrenom?.Prenom ?? string.Empty;
                default:
                    return string.Empty;
            }
        }

        /// <summary>
        /// Évalue une variable dynamique de type Facture
        /// </summary>
        /// <param name="variable">Variable dynamique</param>
        /// <param name="facture">Facture</param>
        /// <returns>Valeur de la variable</returns>
        private string EvaluateFactureVariable(VariableDynamique variable, Facture facture)
        {
            if (facture == null)
                return string.Empty;

            switch (variable.Nom.ToLower())
            {
                case "facture.numero":
                    return facture.Numero;
                case "facture.date":
                    return facture.DateEmission.ToString("dd/MM/yyyy");
                case "facture.dateecheance":
                    return facture.DateEcheance.ToString("dd/MM/yyyy");
                case "facture.montantht":
                    return facture.MontantHT.ToString("C");
                case "facture.montantttc":
                    return facture.MontantTTC.ToString("C");
                case "facture.montantrestant":
                    return facture.MontantRestant.ToString("C");
                case "facture.retardjours":
                    return (DateTime.Now - facture.DateEcheance).Days.ToString();
                default:
                    return string.Empty;
            }
        }

        /// <summary>
        /// Évalue une variable dynamique de type Système
        /// </summary>
        /// <param name="variable">Variable dynamique</param>
        /// <returns>Valeur de la variable</returns>
        private string EvaluateSystemVariable(VariableDynamique variable)
        {
            switch (variable.Nom.ToLower())
            {
                case "date.aujourdhui":
                    return DateTime.Now.ToString("dd/MM/yyyy");
                case "date.demain":
                    return DateTime.Now.AddDays(1).ToString("dd/MM/yyyy");
                case "date.mois":
                    return DateTime.Now.ToString("MMMM yyyy");
                case "entreprise.nom":
                    return "Votre Entreprise"; // À remplacer par une configuration
                case "entreprise.adresse":
                    return "Adresse de l'entreprise"; // À remplacer par une configuration
                case "entreprise.telephone":
                    return "Téléphone de l'entreprise"; // À remplacer par une configuration
                case "entreprise.email":
                    return "Email de l'entreprise"; // À remplacer par une configuration
                default:
                    return string.Empty;
            }
        }
    }
}
