using Moq;
using RecouvreX.Business.Interfaces;
using RecouvreX.Business.Services;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Xunit;

namespace RecouvreX.Tests.Business
{
    public class AuthenticationServiceTests
    {
        private readonly Mock<IUtilisateurRepository> _mockUtilisateurRepository;
        private readonly Mock<IRoleRepository> _mockRoleRepository;
        private readonly Mock<IAuditService> _mockAuditService;
        private readonly IAuthenticationService _authenticationService;

        public AuthenticationServiceTests()
        {
            _mockUtilisateurRepository = new Mock<IUtilisateurRepository>();
            _mockRoleRepository = new Mock<IRoleRepository>();
            _mockAuditService = new Mock<IAuditService>();
            _authenticationService = new AuthenticationService(
                _mockUtilisateurRepository.Object,
                _mockRoleRepository.Object,
                _mockAuditService.Object);
        }

        [Fact]
        public async Task AuthenticateAsync_WithValidCredentials_ShouldReturnUser()
        {
            // Arrange
            string username = "testuser";
            string password = "password123";
            var expectedUser = new Utilisateur
            {
                Id = 1,
                NomUtilisateur = username,
                MotDePasse = BCrypt.Net.BCrypt.HashPassword(password),
                Nom = "Test",
                Prenom = "User",
                Email = "<EMAIL>",
                EstActif = true,
                RoleId = 1
            };

            _mockUtilisateurRepository.Setup(repo => repo.GetByUsernameAsync(username))
                .ReturnsAsync(expectedUser);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            _mockUtilisateurRepository.Setup(repo => repo.UpdateLastLoginAsync(expectedUser.Id))
                .ReturnsAsync(true);

            // Act
            var result = await _authenticationService.AuthenticateAsync(username, password);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedUser.Id, result.Id);
            Assert.Equal(expectedUser.NomUtilisateur, result.NomUtilisateur);
            _mockUtilisateurRepository.Verify(repo => repo.GetByUsernameAsync(username), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Authentification",
                It.IsAny<string>(),
                expectedUser.Id,
                expectedUser.Id), Times.Once);
            _mockUtilisateurRepository.Verify(repo => repo.UpdateLastLoginAsync(expectedUser.Id), Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_WithInvalidUsername_ShouldReturnNull()
        {
            // Arrange
            string username = "nonexistentuser";
            string password = "password123";

            _mockUtilisateurRepository.Setup(repo => repo.GetByUsernameAsync(username))
                .ReturnsAsync((Utilisateur)null);

            // Act
            var result = await _authenticationService.AuthenticateAsync(username, password);

            // Assert
            Assert.Null(result);
            _mockUtilisateurRepository.Verify(repo => repo.GetByUsernameAsync(username), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()), Times.Never);
            _mockUtilisateurRepository.Verify(repo => repo.UpdateLastLoginAsync(It.IsAny<int>()), Times.Never);
        }

        [Fact]
        public async Task AuthenticateAsync_WithInvalidPassword_ShouldReturnNull()
        {
            // Arrange
            string username = "testuser";
            string password = "wrongpassword";
            var user = new Utilisateur
            {
                Id = 1,
                NomUtilisateur = username,
                MotDePasse = BCrypt.Net.BCrypt.HashPassword("password123"),
                Nom = "Test",
                Prenom = "User",
                Email = "<EMAIL>",
                EstActif = true,
                RoleId = 1
            };

            _mockUtilisateurRepository.Setup(repo => repo.GetByUsernameAsync(username))
                .ReturnsAsync(user);

            // Act
            var result = await _authenticationService.AuthenticateAsync(username, password);

            // Assert
            Assert.Null(result);
            _mockUtilisateurRepository.Verify(repo => repo.GetByUsernameAsync(username), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()), Times.Never);
            _mockUtilisateurRepository.Verify(repo => repo.UpdateLastLoginAsync(It.IsAny<int>()), Times.Never);
        }

        [Fact]
        public async Task AuthenticateAsync_WithInactiveUser_ShouldReturnNull()
        {
            // Arrange
            string username = "inactiveuser";
            string password = "password123";
            var user = new Utilisateur
            {
                Id = 1,
                NomUtilisateur = username,
                MotDePasse = BCrypt.Net.BCrypt.HashPassword(password),
                Nom = "Inactive",
                Prenom = "User",
                Email = "<EMAIL>",
                EstActif = false,
                RoleId = 1
            };

            _mockUtilisateurRepository.Setup(repo => repo.GetByUsernameAsync(username))
                .ReturnsAsync(user);

            // Act
            var result = await _authenticationService.AuthenticateAsync(username, password);

            // Assert
            Assert.Null(result);
            _mockUtilisateurRepository.Verify(repo => repo.GetByUsernameAsync(username), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()), Times.Never);
            _mockUtilisateurRepository.Verify(repo => repo.UpdateLastLoginAsync(It.IsAny<int>()), Times.Never);
        }

        [Fact]
        public async Task HasPermissionAsync_WithValidPermission_ShouldReturnTrue()
        {
            // Arrange
            int userId = 1;
            string permissionCode = "CLIENTS_VIEW";
            int roleId = 2;
            var user = new Utilisateur { Id = userId, RoleId = roleId };
            var permissions = new List<Permission>
            {
                new Permission { Id = 1, Code = "CLIENTS_VIEW" },
                new Permission { Id = 2, Code = "CLIENTS_EDIT" }
            };

            _mockUtilisateurRepository.Setup(repo => repo.GetByIdAsync(userId))
                .ReturnsAsync(user);

            _mockRoleRepository.Setup(repo => repo.GetPermissionsByRoleIdAsync(roleId))
                .ReturnsAsync(permissions);

            // Act
            var result = await _authenticationService.HasPermissionAsync(userId, permissionCode);

            // Assert
            Assert.True(result);
            _mockUtilisateurRepository.Verify(repo => repo.GetByIdAsync(userId), Times.Once);
            _mockRoleRepository.Verify(repo => repo.GetPermissionsByRoleIdAsync(roleId), Times.Once);
        }

        [Fact]
        public async Task HasPermissionAsync_WithInvalidPermission_ShouldReturnFalse()
        {
            // Arrange
            int userId = 1;
            string permissionCode = "ADMIN_ACCESS";
            int roleId = 2;
            var user = new Utilisateur { Id = userId, RoleId = roleId };
            var permissions = new List<Permission>
            {
                new Permission { Id = 1, Code = "CLIENTS_VIEW" },
                new Permission { Id = 2, Code = "CLIENTS_EDIT" }
            };

            _mockUtilisateurRepository.Setup(repo => repo.GetByIdAsync(userId))
                .ReturnsAsync(user);

            _mockRoleRepository.Setup(repo => repo.GetPermissionsByRoleIdAsync(roleId))
                .ReturnsAsync(permissions);

            // Act
            var result = await _authenticationService.HasPermissionAsync(userId, permissionCode);

            // Assert
            Assert.False(result);
            _mockUtilisateurRepository.Verify(repo => repo.GetByIdAsync(userId), Times.Once);
            _mockRoleRepository.Verify(repo => repo.GetPermissionsByRoleIdAsync(roleId), Times.Once);
        }

        [Fact]
        public async Task HasPermissionAsync_WithInvalidUser_ShouldReturnFalse()
        {
            // Arrange
            int userId = 999;
            string permissionCode = "CLIENTS_VIEW";

            _mockUtilisateurRepository.Setup(repo => repo.GetByIdAsync(userId))
                .ReturnsAsync((Utilisateur)null);

            // Act
            var result = await _authenticationService.HasPermissionAsync(userId, permissionCode);

            // Assert
            Assert.False(result);
            _mockUtilisateurRepository.Verify(repo => repo.GetByIdAsync(userId), Times.Once);
            _mockRoleRepository.Verify(repo => repo.GetPermissionsByRoleIdAsync(It.IsAny<int>()), Times.Never);
        }

        [Fact]
        public async Task HasPermissionAsync_WithUserWithoutRole_ShouldReturnFalse()
        {
            // Arrange
            int userId = 1;
            string permissionCode = "CLIENTS_VIEW";
            var user = new Utilisateur { Id = userId, RoleId = null };

            _mockUtilisateurRepository.Setup(repo => repo.GetByIdAsync(userId))
                .ReturnsAsync(user);

            // Act
            var result = await _authenticationService.HasPermissionAsync(userId, permissionCode);

            // Assert
            Assert.False(result);
            _mockUtilisateurRepository.Verify(repo => repo.GetByIdAsync(userId), Times.Once);
            _mockRoleRepository.Verify(repo => repo.GetPermissionsByRoleIdAsync(It.IsAny<int>()), Times.Never);
        }
    }
}
