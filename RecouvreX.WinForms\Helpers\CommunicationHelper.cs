using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Helpers
{
    /// <summary>
    /// Classe utilitaire pour la gestion des communications
    /// </summary>
    public static class CommunicationHelper
    {
        /// <summary>
        /// Retourne l'icône associée à un type de communication
        /// </summary>
        /// <param name="type">Type de communication</param>
        /// <returns>Icône associée au type</returns>
        public static Icon GetTypeIcon(string type)
        {
            switch (type)
            {
                case TypeCommunication.Appel:
                    return SystemIcons.Information;
                case TypeCommunication.Email:
                    return SystemIcons.Shield;
                case TypeCommunication.Courrier:
                    return SystemIcons.Application;
                case TypeCommunication.SMS:
                    return SystemIcons.Exclamation;
                case TypeCommunication.Note:
                    return SystemIcons.Question;
                case TypeCommunication.Relance:
                    return SystemIcons.Warning;
                default:
                    return SystemIcons.WinLogo;
            }
        }

        /// <summary>
        /// Retourne la couleur associée à un type de communication
        /// </summary>
        /// <param name="type">Type de communication</param>
        /// <returns>Couleur associée au type</returns>
        public static Color GetTypeColor(string type)
        {
            switch (type)
            {
                case TypeCommunication.Appel:
                    return Color.DodgerBlue;
                case TypeCommunication.Email:
                    return Color.Green;
                case TypeCommunication.Courrier:
                    return Color.DarkOrange;
                case TypeCommunication.SMS:
                    return Color.Purple;
                case TypeCommunication.Note:
                    return Color.Gray;
                case TypeCommunication.Relance:
                    return Color.Red;
                default:
                    return Color.Black;
            }
        }

        /// <summary>
        /// Retourne la couleur associée à une direction de communication
        /// </summary>
        /// <param name="direction">Direction de communication</param>
        /// <returns>Couleur associée à la direction</returns>
        public static Color GetDirectionColor(string direction)
        {
            switch (direction)
            {
                case DirectionCommunication.Entrant:
                    return Color.Green;
                case DirectionCommunication.Sortant:
                    return Color.Blue;
                case DirectionCommunication.Interne:
                    return Color.Gray;
                default:
                    return Color.Black;
            }
        }

        /// <summary>
        /// Retourne la couleur associée à un résultat de communication
        /// </summary>
        /// <param name="resultat">Résultat de communication</param>
        /// <returns>Couleur associée au résultat</returns>
        public static Color GetResultatColor(string resultat)
        {
            switch (resultat)
            {
                case ResultatCommunication.Reussi:
                    return Color.Green;
                case ResultatCommunication.Echec:
                    return Color.Red;
                case ResultatCommunication.RappelDemande:
                    return Color.Orange;
                case ResultatCommunication.MessageLaisse:
                    return Color.DodgerBlue;
                case ResultatCommunication.PasDeReponse:
                    return Color.Gray;
                case ResultatCommunication.Occupe:
                    return Color.DarkOrange;
                case ResultatCommunication.Refuse:
                    return Color.DarkRed;
                case ResultatCommunication.EnAttente:
                    return Color.Purple;
                default:
                    return Color.Black;
            }
        }

        /// <summary>
        /// Affiche les détails d'une communication
        /// </summary>
        /// <param name="communication">Communication à afficher</param>
        public static void ShowDetails(Communication communication)
        {
            if (communication == null)
                return;

            // Créer un formulaire pour l'affichage des détails
            var form = new Form
            {
                Text = $"Détails de la communication - {communication.Type}",
                Size = new Size(700, 500),
                StartPosition = FormStartPosition.CenterScreen,
                MinimizeBox = false,
                MaximizeBox = true,
                ShowIcon = false,
                ShowInTaskbar = false
            };

            // Créer un TableLayoutPanel pour organiser les contrôles
            var tableLayoutPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(10)
            };
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 120));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            form.Controls.Add(tableLayoutPanel);

            // Panneau d'informations
            var infoPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle
            };
            tableLayoutPanel.Controls.Add(infoPanel, 0, 0);

            // Ajouter les informations
            var infoTable = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 3,
                Padding = new Padding(5)
            };
            infoTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20));
            infoTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30));
            infoTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20));
            infoTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30));
            infoPanel.Controls.Add(infoTable);

            // Type
            infoTable.Controls.Add(new Label { Text = "Type :", Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold), TextAlign = ContentAlignment.MiddleRight, Dock = DockStyle.Fill }, 0, 0);
            infoTable.Controls.Add(new Label { Text = communication.Type, ForeColor = GetTypeColor(communication.Type), TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 1, 0);

            // Direction
            infoTable.Controls.Add(new Label { Text = "Direction :", Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold), TextAlign = ContentAlignment.MiddleRight, Dock = DockStyle.Fill }, 2, 0);
            infoTable.Controls.Add(new Label { Text = communication.Direction, ForeColor = GetDirectionColor(communication.Direction), TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 3, 0);

            // Date
            infoTable.Controls.Add(new Label { Text = "Date :", Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold), TextAlign = ContentAlignment.MiddleRight, Dock = DockStyle.Fill }, 0, 1);
            infoTable.Controls.Add(new Label { Text = communication.DateCommunication.ToString("dd/MM/yyyy HH:mm"), TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 1, 1);

            // Utilisateur
            infoTable.Controls.Add(new Label { Text = "Utilisateur :", Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold), TextAlign = ContentAlignment.MiddleRight, Dock = DockStyle.Fill }, 2, 1);
            infoTable.Controls.Add(new Label { Text = communication.Utilisateur?.NomComplet ?? communication.UtilisateurId.ToString(), TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 3, 1);

            // Objet (si applicable)
            if (!string.IsNullOrEmpty(communication.Objet))
            {
                infoTable.Controls.Add(new Label { Text = "Objet :", Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold), TextAlign = ContentAlignment.MiddleRight, Dock = DockStyle.Fill }, 0, 2);
                infoTable.Controls.Add(new Label { Text = communication.Objet, TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 1, 2);
            }

            // Résultat (si applicable)
            if (!string.IsNullOrEmpty(communication.Resultat))
            {
                infoTable.Controls.Add(new Label { Text = "Résultat :", Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold), TextAlign = ContentAlignment.MiddleRight, Dock = DockStyle.Fill }, 2, 2);
                infoTable.Controls.Add(new Label { Text = communication.Resultat, ForeColor = GetResultatColor(communication.Resultat), TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 3, 2);
            }

            // Contenu de la communication
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle
            };
            tableLayoutPanel.Controls.Add(contentPanel, 0, 1);

            // Ajouter le contenu
            var richTextBox = new RichTextBox
            {
                Dock = DockStyle.Fill,
                ReadOnly = true,
                BackColor = Color.White,
                Font = new Font("Arial", 11),
                Text = communication.Contenu
            };
            contentPanel.Controls.Add(richTextBox);

            // Boutons
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft
            };
            tableLayoutPanel.Controls.Add(buttonPanel, 0, 2);

            // Bouton Fermer
            var closeButton = new Button
            {
                Text = "Fermer",
                Width = 100,
                Height = 30
            };
            closeButton.Click += (sender, e) => form.Close();
            buttonPanel.Controls.Add(closeButton);

            // Afficher le formulaire
            form.ShowDialog();
        }
    }
}
