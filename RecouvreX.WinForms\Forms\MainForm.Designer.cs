namespace RecouvreX.WinForms.Forms
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // MainForm
            //
            this.ClientSize = new System.Drawing.Size(1024, 768);
            this.Name = "MainForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "RecouvreX - Gestion de recouvrement des créances clients";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.Load += new System.EventHandler(this.MainForm_Load);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainForm_FormClosing);
            this.ResumeLayout(false);

            // Créer le menu principal
            System.Windows.Forms.MenuStrip mainMenu = new System.Windows.Forms.MenuStrip();
            mainMenu.Name = "mainMenu";
            mainMenu.Dock = System.Windows.Forms.DockStyle.Top;
            this.Controls.Add(mainMenu);

            // Menu Fichier
            System.Windows.Forms.ToolStripMenuItem fileMenu = new System.Windows.Forms.ToolStripMenuItem("Fichier");
            mainMenu.Items.Add(fileMenu);

            System.Windows.Forms.ToolStripMenuItem exitMenuItem = new System.Windows.Forms.ToolStripMenuItem("Quitter");
            exitMenuItem.Click += new System.EventHandler(this.ExitMenuItem_Click);
            fileMenu.DropDownItems.Add(exitMenuItem);

            // Menu Clients
            System.Windows.Forms.ToolStripMenuItem clientsMenu = new System.Windows.Forms.ToolStripMenuItem("Clients");
            mainMenu.Items.Add(clientsMenu);

            System.Windows.Forms.ToolStripMenuItem listClientsMenuItem = new System.Windows.Forms.ToolStripMenuItem("Liste des clients");
            listClientsMenuItem.Click += new System.EventHandler(this.ListClientsMenuItem_Click);
            clientsMenu.DropDownItems.Add(listClientsMenuItem);

            System.Windows.Forms.ToolStripMenuItem addClientMenuItem = new System.Windows.Forms.ToolStripMenuItem("Ajouter un client");
            addClientMenuItem.Click += new System.EventHandler(this.AddClientMenuItem_Click);
            clientsMenu.DropDownItems.Add(addClientMenuItem);

            clientsMenu.DropDownItems.Add(new System.Windows.Forms.ToolStripSeparator());

            System.Windows.Forms.ToolStripMenuItem scoreRisqueMenuItem = new System.Windows.Forms.ToolStripMenuItem("Scores de risque client");
            scoreRisqueMenuItem.Click += new System.EventHandler(this.ScoreRisqueMenuItem_Click);
            clientsMenu.DropDownItems.Add(scoreRisqueMenuItem);

            System.Windows.Forms.ToolStripMenuItem segmentationMenuItem = new System.Windows.Forms.ToolStripMenuItem("Segmentation des clients");
            segmentationMenuItem.Click += new System.EventHandler(this.SegmentationMenuItem_Click);
            clientsMenu.DropDownItems.Add(segmentationMenuItem);

            // Menu Factures
            System.Windows.Forms.ToolStripMenuItem invoicesMenu = new System.Windows.Forms.ToolStripMenuItem("Factures");
            mainMenu.Items.Add(invoicesMenu);

            System.Windows.Forms.ToolStripMenuItem listInvoicesMenuItem = new System.Windows.Forms.ToolStripMenuItem("Liste des factures");
            listInvoicesMenuItem.Click += new System.EventHandler(this.ListInvoicesMenuItem_Click);
            invoicesMenu.DropDownItems.Add(listInvoicesMenuItem);

            System.Windows.Forms.ToolStripMenuItem addInvoiceMenuItem = new System.Windows.Forms.ToolStripMenuItem("Ajouter une facture");
            addInvoiceMenuItem.Click += new System.EventHandler(this.AddInvoiceMenuItem_Click);
            invoicesMenu.DropDownItems.Add(addInvoiceMenuItem);

            System.Windows.Forms.ToolStripMenuItem overdueInvoicesMenuItem = new System.Windows.Forms.ToolStripMenuItem("Factures en retard");
            overdueInvoicesMenuItem.Click += new System.EventHandler(this.OverdueInvoicesMenuItem_Click);
            invoicesMenu.DropDownItems.Add(overdueInvoicesMenuItem);

            // Menu Paiements
            System.Windows.Forms.ToolStripMenuItem paymentsMenu = new System.Windows.Forms.ToolStripMenuItem("Paiements");
            mainMenu.Items.Add(paymentsMenu);

            System.Windows.Forms.ToolStripMenuItem listPaymentsMenuItem = new System.Windows.Forms.ToolStripMenuItem("Liste des paiements");
            listPaymentsMenuItem.Click += new System.EventHandler(this.ListPaymentsMenuItem_Click);
            paymentsMenu.DropDownItems.Add(listPaymentsMenuItem);

            System.Windows.Forms.ToolStripMenuItem addPaymentMenuItem = new System.Windows.Forms.ToolStripMenuItem("Ajouter un paiement");
            addPaymentMenuItem.Click += new System.EventHandler(this.AddPaymentMenuItem_Click);
            paymentsMenu.DropDownItems.Add(addPaymentMenuItem);

            // Menu Relances
            System.Windows.Forms.ToolStripMenuItem remindersMenu = new System.Windows.Forms.ToolStripMenuItem("Relances");
            mainMenu.Items.Add(remindersMenu);

            System.Windows.Forms.ToolStripMenuItem listRemindersMenuItem = new System.Windows.Forms.ToolStripMenuItem("Liste des relances");
            listRemindersMenuItem.Click += new System.EventHandler(this.ListRemindersMenuItem_Click);
            remindersMenu.DropDownItems.Add(listRemindersMenuItem);

            // Menu Plans de paiement
            System.Windows.Forms.ToolStripMenuItem plansPaiementMenu = new System.Windows.Forms.ToolStripMenuItem("Plans de paiement");
            mainMenu.Items.Add(plansPaiementMenu);

            System.Windows.Forms.ToolStripMenuItem listPlansPaiementMenuItem = new System.Windows.Forms.ToolStripMenuItem("Liste des plans de paiement");
            listPlansPaiementMenuItem.Click += new System.EventHandler(this.ListPlansPaiementMenuItem_Click);
            plansPaiementMenu.DropDownItems.Add(listPlansPaiementMenuItem);

            System.Windows.Forms.ToolStripMenuItem addPlanPaiementMenuItem = new System.Windows.Forms.ToolStripMenuItem("Créer un plan de paiement");
            addPlanPaiementMenuItem.Click += new System.EventHandler(this.AddPlanPaiementMenuItem_Click);
            plansPaiementMenu.DropDownItems.Add(addPlanPaiementMenuItem);

            System.Windows.Forms.ToolStripMenuItem echeancierDashboardMenuItem = new System.Windows.Forms.ToolStripMenuItem("Tableau de bord des échéances");
            echeancierDashboardMenuItem.Click += new System.EventHandler(this.EcheancierDashboardMenuItem_Click);
            plansPaiementMenu.DropDownItems.Add(echeancierDashboardMenuItem);

            System.Windows.Forms.ToolStripMenuItem plannedRemindersMenuItem = new System.Windows.Forms.ToolStripMenuItem("Relances planifiées");
            plannedRemindersMenuItem.Click += new System.EventHandler(this.PlannedRemindersMenuItem_Click);
            remindersMenu.DropDownItems.Add(plannedRemindersMenuItem);

            System.Windows.Forms.ToolStripMenuItem generateRemindersMenuItem = new System.Windows.Forms.ToolStripMenuItem("Générer des relances");
            generateRemindersMenuItem.Click += new System.EventHandler(this.GenerateRemindersMenuItem_Click);
            remindersMenu.DropDownItems.Add(generateRemindersMenuItem);

            System.Windows.Forms.ToolStripMenuItem modeleRelanceMenuItem = new System.Windows.Forms.ToolStripMenuItem("Modèles de relance");
            modeleRelanceMenuItem.Click += new System.EventHandler(this.ModeleRelanceMenuItem_Click);
            remindersMenu.DropDownItems.Add(modeleRelanceMenuItem);

            System.Windows.Forms.ToolStripMenuItem regleRelanceMenuItem = new System.Windows.Forms.ToolStripMenuItem("Règles de relance");
            regleRelanceMenuItem.Click += new System.EventHandler(this.RegleRelanceMenuItem_Click);
            remindersMenu.DropDownItems.Add(regleRelanceMenuItem);

            System.Windows.Forms.ToolStripMenuItem planificationRelanceMenuItem = new System.Windows.Forms.ToolStripMenuItem("Planification des relances");
            planificationRelanceMenuItem.Click += new System.EventHandler(this.PlanificationRelanceMenuItem_Click);
            remindersMenu.DropDownItems.Add(planificationRelanceMenuItem);

            System.Windows.Forms.ToolStripMenuItem communicationMenuItem = new System.Windows.Forms.ToolStripMenuItem("Historique des communications");
            communicationMenuItem.Click += new System.EventHandler(this.CommunicationMenuItem_Click);
            remindersMenu.DropDownItems.Add(communicationMenuItem);

            // Menu Litiges
            System.Windows.Forms.ToolStripMenuItem litigesMenu = new System.Windows.Forms.ToolStripMenuItem("Litiges");
            mainMenu.Items.Add(litigesMenu);

            System.Windows.Forms.ToolStripMenuItem listLitigesMenuItem = new System.Windows.Forms.ToolStripMenuItem("Liste des litiges");
            listLitigesMenuItem.Click += new System.EventHandler(this.ListLitigesMenuItem_Click);
            litigesMenu.DropDownItems.Add(listLitigesMenuItem);

            System.Windows.Forms.ToolStripMenuItem categoriesLitigeMenuItem = new System.Windows.Forms.ToolStripMenuItem("Catégories de litiges");
            categoriesLitigeMenuItem.Click += new System.EventHandler(this.CategoriesLitigeMenuItem_Click);
            litigesMenu.DropDownItems.Add(categoriesLitigeMenuItem);

            System.Windows.Forms.ToolStripMenuItem etapesLitigeMenuItem = new System.Windows.Forms.ToolStripMenuItem("Étapes de litiges");
            etapesLitigeMenuItem.Click += new System.EventHandler(this.EtapesLitigeMenuItem_Click);
            litigesMenu.DropDownItems.Add(etapesLitigeMenuItem);

            // Menu Actions prioritaires
            System.Windows.Forms.ToolStripMenuItem actionsPrioritairesMenu = new System.Windows.Forms.ToolStripMenuItem("Actions prioritaires");
            mainMenu.Items.Add(actionsPrioritairesMenu);

            System.Windows.Forms.ToolStripMenuItem listActionsPrioritairesMenuItem = new System.Windows.Forms.ToolStripMenuItem("Liste des actions prioritaires");
            listActionsPrioritairesMenuItem.Click += new System.EventHandler(this.ActionsPrioritairesMenuItem_Click);
            actionsPrioritairesMenu.DropDownItems.Add(listActionsPrioritairesMenuItem);

            System.Windows.Forms.ToolStripMenuItem generateActionsPrioritairesMenuItem = new System.Windows.Forms.ToolStripMenuItem("Générer des actions prioritaires");
            generateActionsPrioritairesMenuItem.Click += new System.EventHandler(this.GenerateActionsPrioritairesMenuItem_Click);
            actionsPrioritairesMenu.DropDownItems.Add(generateActionsPrioritairesMenuItem);

            System.Windows.Forms.ToolStripMenuItem mesTachesMenuItem = new System.Windows.Forms.ToolStripMenuItem("Mes tâches prioritaires");
            mesTachesMenuItem.Click += new System.EventHandler(this.MesTachesMenuItem_Click);
            actionsPrioritairesMenu.DropDownItems.Add(mesTachesMenuItem);

            // Menu Rapports
            System.Windows.Forms.ToolStripMenuItem reportsMenu = new System.Windows.Forms.ToolStripMenuItem("Rapports");
            mainMenu.Items.Add(reportsMenu);

            System.Windows.Forms.ToolStripMenuItem standardReportsMenuItem = new System.Windows.Forms.ToolStripMenuItem("Rapports standards");
            standardReportsMenuItem.Click += new System.EventHandler(this.StandardReportsMenuItem_Click);
            reportsMenu.DropDownItems.Add(standardReportsMenuItem);

            System.Windows.Forms.ToolStripMenuItem customReportsMenuItem = new System.Windows.Forms.ToolStripMenuItem("Rapports personnalisés");
            customReportsMenuItem.Click += new System.EventHandler(this.CustomReportsMenuItem_Click);
            reportsMenu.DropDownItems.Add(customReportsMenuItem);

            reportsMenu.DropDownItems.Add(new System.Windows.Forms.ToolStripSeparator());

            System.Windows.Forms.ToolStripMenuItem overdueInvoicesReportMenuItem = new System.Windows.Forms.ToolStripMenuItem("Factures en retard");
            overdueInvoicesReportMenuItem.Click += new System.EventHandler(this.OverdueInvoicesReportMenuItem_Click);
            reportsMenu.DropDownItems.Add(overdueInvoicesReportMenuItem);

            System.Windows.Forms.ToolStripMenuItem paymentsByPeriodReportMenuItem = new System.Windows.Forms.ToolStripMenuItem("Paiements par période");
            paymentsByPeriodReportMenuItem.Click += new System.EventHandler(this.PaymentsByPeriodReportMenuItem_Click);
            reportsMenu.DropDownItems.Add(paymentsByPeriodReportMenuItem);

            System.Windows.Forms.ToolStripMenuItem invoicesByPeriodReportMenuItem = new System.Windows.Forms.ToolStripMenuItem("Factures par période");
            invoicesByPeriodReportMenuItem.Click += new System.EventHandler(this.InvoicesByPeriodReportMenuItem_Click);
            reportsMenu.DropDownItems.Add(invoicesByPeriodReportMenuItem);

            System.Windows.Forms.ToolStripMenuItem recoveryRateReportMenuItem = new System.Windows.Forms.ToolStripMenuItem("Taux de recouvrement");
            recoveryRateReportMenuItem.Click += new System.EventHandler(this.RecoveryRateReportMenuItem_Click);
            reportsMenu.DropDownItems.Add(recoveryRateReportMenuItem);

            System.Windows.Forms.ToolStripMenuItem comparativeReportMenuItem = new System.Windows.Forms.ToolStripMenuItem("Analyses comparatives");
            comparativeReportMenuItem.Click += new System.EventHandler(this.ComparativeReportMenuItem_Click);
            reportsMenu.DropDownItems.Add(comparativeReportMenuItem);

            System.Windows.Forms.ToolStripMenuItem predictiveReportMenuItem = new System.Windows.Forms.ToolStripMenuItem("Analyses prédictives");
            predictiveReportMenuItem.Click += new System.EventHandler(this.PredictiveReportMenuItem_Click);
            reportsMenu.DropDownItems.Add(predictiveReportMenuItem);

            // Les éléments de menu "Paiements par période" et "Taux de recouvrement" sont déjà définis plus haut

            // Menu Administration
            System.Windows.Forms.ToolStripMenuItem adminMenu = new System.Windows.Forms.ToolStripMenuItem("Administration");
            adminMenu.Name = "adminMenu";
            mainMenu.Items.Add(adminMenu);

            System.Windows.Forms.ToolStripMenuItem usersMenuItem = new System.Windows.Forms.ToolStripMenuItem("Utilisateurs");
            usersMenuItem.Name = "usersMenuItem";
            usersMenuItem.Click += new System.EventHandler(this.UsersMenuItem_Click);
            adminMenu.DropDownItems.Add(usersMenuItem);

            System.Windows.Forms.ToolStripMenuItem rolesMenuItem = new System.Windows.Forms.ToolStripMenuItem("Rôles et permissions");
            rolesMenuItem.Name = "rolesMenuItem";
            rolesMenuItem.Click += new System.EventHandler(this.RolesMenuItem_Click);
            adminMenu.DropDownItems.Add(rolesMenuItem);

            System.Windows.Forms.ToolStripMenuItem auditLogMenuItem = new System.Windows.Forms.ToolStripMenuItem("Journal d'audit");
            auditLogMenuItem.Click += new System.EventHandler(this.AuditLogMenuItem_Click);
            adminMenu.DropDownItems.Add(auditLogMenuItem);

            // Séparateur
            adminMenu.DropDownItems.Add(new System.Windows.Forms.ToolStripSeparator());

            // Options de gestion de la base de données
            System.Windows.Forms.ToolStripMenuItem databaseMenuItem = new System.Windows.Forms.ToolStripMenuItem("Base de données");
            databaseMenuItem.Name = "databaseMenuItem";
            adminMenu.DropDownItems.Add(databaseMenuItem);

            System.Windows.Forms.ToolStripMenuItem databaseConnectionMenuItem = new System.Windows.Forms.ToolStripMenuItem("Configuration de la connexion");
            databaseConnectionMenuItem.Name = "databaseConnectionMenuItem";
            databaseConnectionMenuItem.Click += new System.EventHandler(this.DatabaseConnectionMenuItem_Click);
            databaseMenuItem.DropDownItems.Add(databaseConnectionMenuItem);

            System.Windows.Forms.ToolStripMenuItem databaseAdminMenuItem = new System.Windows.Forms.ToolStripMenuItem("Administration de la base de données");
            databaseAdminMenuItem.Name = "databaseAdminMenuItem";
            databaseAdminMenuItem.Click += new System.EventHandler(this.DatabaseAdminMenuItem_Click);
            databaseMenuItem.DropDownItems.Add(databaseAdminMenuItem);

            // Menu Aide
            System.Windows.Forms.ToolStripMenuItem helpMenu = new System.Windows.Forms.ToolStripMenuItem("Aide");
            mainMenu.Items.Add(helpMenu);

            System.Windows.Forms.ToolStripMenuItem aboutMenuItem = new System.Windows.Forms.ToolStripMenuItem("À propos");
            aboutMenuItem.Click += new System.EventHandler(this.AboutMenuItem_Click);
            helpMenu.DropDownItems.Add(aboutMenuItem);

            // Ajouter un bouton de notifications dans la barre de menu
            System.Windows.Forms.ToolStripButton notificationsButton = new System.Windows.Forms.ToolStripButton();
            notificationsButton.Name = "notificationsButton";
            notificationsButton.Image = new System.Drawing.Bitmap(System.IO.Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory, "Resources", "notification.png"));
            notificationsButton.ToolTipText = "Notifications";
            notificationsButton.Alignment = System.Windows.Forms.ToolStripItemAlignment.Right;
            notificationsButton.Click += new System.EventHandler(this.NotificationsButton_Click);
            mainMenu.Items.Add(notificationsButton);

            // Ajouter un label pour afficher le nombre de notifications non lues
            System.Windows.Forms.ToolStripLabel notificationsCountLabel = new System.Windows.Forms.ToolStripLabel();
            notificationsCountLabel.Name = "notificationsCountLabel";
            notificationsCountLabel.Text = "";
            notificationsCountLabel.Alignment = System.Windows.Forms.ToolStripItemAlignment.Right;
            notificationsCountLabel.ForeColor = System.Drawing.Color.Red;
            notificationsCountLabel.Font = new System.Drawing.Font(notificationsCountLabel.Font, System.Drawing.FontStyle.Bold);
            mainMenu.Items.Add(notificationsCountLabel);

            // Barre d'état
            System.Windows.Forms.StatusStrip statusStrip = new System.Windows.Forms.StatusStrip();
            statusStrip.Name = "statusStrip";
            statusStrip.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Controls.Add(statusStrip);

            System.Windows.Forms.ToolStripStatusLabel userStatusLabel = new System.Windows.Forms.ToolStripStatusLabel();
            userStatusLabel.Name = "userStatusLabel";
            statusStrip.Items.Add(userStatusLabel);

            System.Windows.Forms.ToolStripStatusLabel sessionStatusLabel = new System.Windows.Forms.ToolStripStatusLabel();
            sessionStatusLabel.Name = "sessionStatusLabel";
            statusStrip.Items.Add(sessionStatusLabel);

            // Panneau principal
            System.Windows.Forms.Panel mainPanel = new System.Windows.Forms.Panel();
            mainPanel.Name = "mainPanel";
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.Controls.Add(mainPanel);

            // Tableau de bord
            CreateDashboard(mainPanel);
        }

        #endregion
    }
}
