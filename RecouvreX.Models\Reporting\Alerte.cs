using System;

namespace RecouvreX.Models.Reporting
{
    /// <summary>
    /// Représente une alerte basée sur un indicateur de performance
    /// </summary>
    public class AlerteIndicateur : BaseEntity
    {

        /// <summary>
        /// Nom de l'alerte
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Description de l'alerte
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Nom de l'indicateur surveillé
        /// </summary>
        public string Indicateur { get; set; }

        /// <summary>
        /// Condition de déclenchement (>, <, =, >=, <=)
        /// </summary>
        public string Condition { get; set; }

        /// <summary>
        /// Valeur seuil pour le déclenchement
        /// </summary>
        public decimal ValeurSeuil { get; set; }

        /// <summary>
        /// Niveau de sévérité (Info, Avertissement, Critique)
        /// </summary>
        public string Severite { get; set; }

        /// <summary>
        /// Indique si l'alerte est active
        /// </summary>
        public bool EstActive { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui recevra l'alerte (0 pour tous les utilisateurs)
        /// </summary>
        public int UtilisateurId { get; set; }

        /// <summary>
        /// Utilisateur qui recevra l'alerte (navigation property)
        /// </summary>
        public Utilisateur Utilisateur { get; set; }

        /// <summary>
        /// Date du dernier déclenchement de l'alerte
        /// </summary>
        public DateTime? DateDernierDeclenchement { get; set; }

        /// <summary>
        /// Nombre de déclenchements de l'alerte
        /// </summary>
        public int NombreDeclenchements { get; set; }

        /// <summary>
        /// Indique si l'alerte doit être envoyée par email
        /// </summary>
        public bool EnvoyerEmail { get; set; }

        /// <summary>
        /// Indique si l'alerte doit être affichée dans l'application
        /// </summary>
        public bool AfficherApplication { get; set; }
    }

    /// <summary>
    /// Niveaux de sévérité des alertes
    /// </summary>
    public static class SeveriteAlerte
    {
        public const string Info = "Info";
        public const string Avertissement = "Avertissement";
        public const string Critique = "Critique";
    }

    /// <summary>
    /// Conditions de déclenchement des alertes
    /// </summary>
    public static class ConditionAlerte
    {
        public const string Superieur = ">";
        public const string Inferieur = "<";
        public const string Egal = "=";
        public const string SuperieurOuEgal = ">=";
        public const string InferieurOuEgal = "<=";
    }
}
