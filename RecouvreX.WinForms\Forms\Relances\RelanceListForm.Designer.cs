namespace RecouvreX.WinForms.Forms.Relances
{
    partial class RelanceListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // RelanceListForm
            //
            this.ClientSize = new System.Drawing.Size(1000, 600);
            this.Name = "RelanceListForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Liste des relances";
            this.Load += new System.EventHandler(this.RelanceListForm_Load);
            this.ResumeLayout(false);

            // Barre d'outils
            System.Windows.Forms.ToolStrip toolStrip = new System.Windows.Forms.ToolStrip();
            toolStrip.Name = "toolStrip";
            toolStrip.Dock = System.Windows.Forms.DockStyle.Top;
            this.Controls.Add(toolStrip);

            // Bouton Ajouter
            System.Windows.Forms.ToolStripButton addButton = new System.Windows.Forms.ToolStripButton();
            addButton.Name = "addButton";
            addButton.Text = "Ajouter";
            addButton.Image = null; // Ajouter une icône
            addButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            addButton.Click += new System.EventHandler(this.AddButton_Click);
            toolStrip.Items.Add(addButton);

            // Bouton Voir
            System.Windows.Forms.ToolStripButton viewButton = new System.Windows.Forms.ToolStripButton();
            viewButton.Name = "viewButton";
            viewButton.Text = "Voir";
            viewButton.Image = null; // Ajouter une icône
            viewButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            viewButton.Click += new System.EventHandler(this.ViewButton_Click);
            toolStrip.Items.Add(viewButton);

            // Séparateur
            toolStrip.Items.Add(new System.Windows.Forms.ToolStripSeparator());

            // Bouton Générer des relances
            System.Windows.Forms.ToolStripButton generateButton = new System.Windows.Forms.ToolStripButton();
            generateButton.Name = "generateButton";
            generateButton.Text = "Générer des relances";
            generateButton.Image = null; // Ajouter une icône
            generateButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            generateButton.Click += new System.EventHandler(this.GenerateButton_Click);
            toolStrip.Items.Add(generateButton);

            // Séparateur
            toolStrip.Items.Add(new System.Windows.Forms.ToolStripSeparator());

            // Bouton Rafraîchir
            System.Windows.Forms.ToolStripButton refreshButton = new System.Windows.Forms.ToolStripButton();
            refreshButton.Name = "refreshButton";
            refreshButton.Text = "Rafraîchir";
            refreshButton.Image = null; // Ajouter une icône
            refreshButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            refreshButton.Click += new System.EventHandler(this.RefreshButton_Click);
            toolStrip.Items.Add(refreshButton);

            // Séparateur
            toolStrip.Items.Add(new System.Windows.Forms.ToolStripSeparator());

            // Filtre par type
            System.Windows.Forms.ToolStripLabel typeLabel = new System.Windows.Forms.ToolStripLabel();
            typeLabel.Text = "Type :";
            toolStrip.Items.Add(typeLabel);

            System.Windows.Forms.ToolStripComboBox typeComboBox = new System.Windows.Forms.ToolStripComboBox();
            typeComboBox.Name = "typeComboBox";
            typeComboBox.Width = 150;
            typeComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            typeComboBox.Items.Add("Tous");
            typeComboBox.Items.Add("Email");
            typeComboBox.Items.Add("Téléphone");
            typeComboBox.Items.Add("Courrier");
            typeComboBox.SelectedIndex = 0;
            typeComboBox.SelectedIndexChanged += new System.EventHandler(this.TypeComboBox_SelectedIndexChanged);
            toolStrip.Items.Add(typeComboBox);

            // Séparateur
            toolStrip.Items.Add(new System.Windows.Forms.ToolStripSeparator());

            // Filtre par statut
            System.Windows.Forms.ToolStripLabel statutLabel = new System.Windows.Forms.ToolStripLabel();
            statutLabel.Text = "Statut :";
            toolStrip.Items.Add(statutLabel);

            System.Windows.Forms.ToolStripComboBox statutComboBox = new System.Windows.Forms.ToolStripComboBox();
            statutComboBox.Name = "statutComboBox";
            statutComboBox.Width = 150;
            statutComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            statutComboBox.Items.Add("Tous");
            statutComboBox.Items.Add("Planifiée");
            statutComboBox.Items.Add("En cours");
            statutComboBox.Items.Add("Terminée");
            statutComboBox.Items.Add("Annulée");
            statutComboBox.SelectedIndex = 0;
            statutComboBox.SelectedIndexChanged += new System.EventHandler(this.StatutComboBox_SelectedIndexChanged);
            toolStrip.Items.Add(statutComboBox);

            // DataGridView pour afficher les relances
            System.Windows.Forms.DataGridView dataGridView = new System.Windows.Forms.DataGridView();
            dataGridView.Name = "dataGridView";
            dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.ReadOnly = true;
            dataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;
            dataGridView.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView.RowHeadersVisible = false;
            dataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.DataGridView_CellDoubleClick);
            this.Controls.Add(dataGridView);

            // Barre d'état
            System.Windows.Forms.StatusStrip statusStrip = new System.Windows.Forms.StatusStrip();
            statusStrip.Name = "statusStrip";
            statusStrip.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Controls.Add(statusStrip);

            System.Windows.Forms.ToolStripStatusLabel countLabel = new System.Windows.Forms.ToolStripStatusLabel();
            countLabel.Name = "countLabel";
            statusStrip.Items.Add(countLabel);

            System.Windows.Forms.ToolStripStatusLabel plannedLabel = new System.Windows.Forms.ToolStripStatusLabel();
            plannedLabel.Name = "plannedLabel";
            statusStrip.Items.Add(plannedLabel);
        }

        #endregion
    }
}
