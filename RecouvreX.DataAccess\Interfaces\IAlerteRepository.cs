using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des alertes
    /// </summary>
    public interface IAlerteRepository : IRepository<Alerte>
    {
        /// <summary>
        /// Récupère les alertes d'un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des alertes de l'utilisateur</returns>
        Task<IEnumerable<Alerte>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Met à jour la date de dernière notification d'une alerte
        /// </summary>
        /// <param name="alerteId">Identifiant de l'alerte</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateDerniereNotificationAsync(int alerteId, int utilisateurId);
    }
}
