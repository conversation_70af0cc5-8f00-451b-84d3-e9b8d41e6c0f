using System;
using System.Collections.Generic;

namespace RecouvreX.Models.Reporting
{
    /// <summary>
    /// Représente des données pour un graphique
    /// </summary>
    public class DonneeGraphique
    {
        /// <summary>
        /// Titre du graphique
        /// </summary>
        public string Titre { get; set; }

        /// <summary>
        /// Type de graphique (ligne, barre, camembert, etc.)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Libellé de l'axe X
        /// </summary>
        public string LibelleAxeX { get; set; }

        /// <summary>
        /// Libellé de l'axe Y
        /// </summary>
        public string LibelleAxeY { get; set; }

        /// <summary>
        /// Liste des étiquettes (axe X pour les graphiques à barres/lignes)
        /// </summary>
        public List<string> Etiquettes { get; set; } = new List<string>();

        /// <summary>
        /// Liste des séries de données
        /// </summary>
        public List<SerieGraphique> Series { get; set; } = new List<SerieGraphique>();
    }

    /// <summary>
    /// Représente une série de données pour un graphique
    /// </summary>
    public class SerieGraphique
    {
        /// <summary>
        /// Nom de la série
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Couleur de la série
        /// </summary>
        public string Couleur { get; set; }

        /// <summary>
        /// Liste des valeurs
        /// </summary>
        public List<decimal> Valeurs { get; set; } = new List<decimal>();
    }

    /// <summary>
    /// Types de graphiques disponibles
    /// </summary>
    public static class TypeGraphique
    {
        public const string Ligne = "Ligne";
        public const string Barre = "Barre";
        public const string BarreEmpilee = "BarreEmpilee";
        public const string Camembert = "Camembert";
        public const string Anneau = "Anneau";
        public const string Radar = "Radar";
        public const string Aire = "Aire";
    }
}
