using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les étapes de litiges
    /// </summary>
    public class EtapeLitigeRepository : BaseRepository<EtapeLitige>, IEtapeLitigeRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public EtapeLitigeRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "EtapesLitige")
        {
        }

        /// <summary>
        /// Récupère une étape de litige par son nom
        /// </summary>
        /// <param name="nom">Nom de l'étape</param>
        /// <returns>Étape de litige trouvée ou null</returns>
        public async Task<EtapeLitige> GetByNomAsync(string nom)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE Nom = @Nom AND EstActif = 1";
                return await connection.QueryFirstOrDefaultAsync<EtapeLitige>(query, new { Nom = nom });
            }
        }

        /// <summary>
        /// Récupère toutes les étapes de litiges triées par ordre
        /// </summary>
        /// <returns>Liste des étapes de litiges triées par ordre</returns>
        public async Task<IEnumerable<EtapeLitige>> GetAllOrderedAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE EstActif = 1 ORDER BY Ordre";
                return await connection.QueryAsync<EtapeLitige>(query);
            }
        }

        /// <summary>
        /// Récupère toutes les étapes de litiges avec le nombre de litiges associés
        /// </summary>
        /// <returns>Liste des étapes de litiges avec le nombre de litiges</returns>
        public async Task<IEnumerable<EtapeLitige>> GetAllWithCountAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT e.*, COUNT(l.Id) AS NombreLitiges
                    FROM EtapesLitige e
                    LEFT JOIN Litiges l ON e.Id = l.EtapeLitigeId AND l.EstActif = 1
                    WHERE e.EstActif = 1
                    GROUP BY e.Id, e.Nom, e.Description, e.Ordre, e.DelaiJours, e.EstEtapeFinale, e.NecessiteValidation, e.DateCreation, e.CreePar, e.DateModification, e.ModifiePar, e.EstActif
                    ORDER BY e.Ordre";

                var etapes = await connection.QueryAsync<EtapeLitige>(query);
                return etapes;
            }
        }

        /// <summary>
        /// Récupère l'étape suivante dans le workflow
        /// </summary>
        /// <param name="etapeId">Identifiant de l'étape actuelle</param>
        /// <returns>Étape suivante ou null si c'est la dernière étape</returns>
        public async Task<EtapeLitige> GetNextEtapeAsync(int etapeId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Récupérer l'ordre de l'étape actuelle
                var ordreQuery = $"SELECT Ordre FROM {_tableName} WHERE Id = @EtapeId AND EstActif = 1";
                var ordre = await connection.QueryFirstOrDefaultAsync<int>(ordreQuery, new { EtapeId = etapeId });

                // Récupérer l'étape suivante
                var nextQuery = $"SELECT TOP 1 * FROM {_tableName} WHERE Ordre > @Ordre AND EstActif = 1 ORDER BY Ordre";
                return await connection.QueryFirstOrDefaultAsync<EtapeLitige>(nextQuery, new { Ordre = ordre });
            }
        }

        /// <summary>
        /// Récupère l'étape précédente dans le workflow
        /// </summary>
        /// <param name="etapeId">Identifiant de l'étape actuelle</param>
        /// <returns>Étape précédente ou null si c'est la première étape</returns>
        public async Task<EtapeLitige> GetPreviousEtapeAsync(int etapeId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Récupérer l'ordre de l'étape actuelle
                var ordreQuery = $"SELECT Ordre FROM {_tableName} WHERE Id = @EtapeId AND EstActif = 1";
                var ordre = await connection.QueryFirstOrDefaultAsync<int>(ordreQuery, new { EtapeId = etapeId });

                // Récupérer l'étape précédente
                var prevQuery = $"SELECT TOP 1 * FROM {_tableName} WHERE Ordre < @Ordre AND EstActif = 1 ORDER BY Ordre DESC";
                return await connection.QueryFirstOrDefaultAsync<EtapeLitige>(prevQuery, new { Ordre = ordre });
            }
        }
    }
}
