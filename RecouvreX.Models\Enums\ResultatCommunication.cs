namespace RecouvreX.Models.Enums
{
    /// <summary>
    /// Résultats de communication
    /// </summary>
    public static class ResultatCommunication
    {
        public const string Reussi = "Réuss<PERSON>";
        public const string Echec = "Échec";
        public const string RappelDemande = "Rappel demandé";
        public const string MessageLaisse = "Message laissé";
        public const string PasDeReponse = "Pas de réponse";
        public const string Occupe = "Occupé";
        public const string Refuse = "Refusé";
        public const string EnAttente = "En attente";
    }
}
