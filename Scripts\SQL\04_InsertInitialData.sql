-- Script d'insertion des données initiales pour la base de données RecouvreX
-- Ce script insère les données de base nécessaires au fonctionnement de l'application

USE RecouvreX;
GO

-- Insertion des rôles par défaut
INSERT INTO app.Roles (Nom, Description, CreePar, EstActif)
VALUES 
    ('Administrateur', 'Accès complet à toutes les fonctionnalités du système', 1, 1),
    ('Gestionnaire', 'Gestion des clients, factures, paiements et relances', 1, 1),
    ('Commercial', 'Gestion des clients et suivi des factures', 1, 1),
    ('Livreur', 'Suivi des livraisons et mise à jour des statuts', 1, 1),
    ('Comptable', 'Gestion des paiements et rapports financiers', 1, 1),
    ('Consultant', 'Accès en lecture seule aux données', 1, 1);
GO

-- Insertion des permissions par module
-- Module Utilisateurs
INSERT INTO app.Permissions (Nom, Description, Code, Module, CreePar, EstActif)
VALUES 
    ('Voir utilisateurs', 'Permet de voir la liste des utilisateurs', 'USERS_VIEW', 'Utilisateurs', 1, 1),
    ('Créer utilisateur', 'Permet de créer un nouvel utilisateur', 'USERS_CREATE', 'Utilisateurs', 1, 1),
    ('Modifier utilisateur', 'Permet de modifier un utilisateur existant', 'USERS_EDIT', 'Utilisateurs', 1, 1),
    ('Supprimer utilisateur', 'Permet de supprimer un utilisateur', 'USERS_DELETE', 'Utilisateurs', 1, 1),
    ('Gérer rôles', 'Permet de gérer les rôles et permissions', 'ROLES_MANAGE', 'Utilisateurs', 1, 1);
GO

-- Module Clients
INSERT INTO app.Permissions (Nom, Description, Code, Module, CreePar, EstActif)
VALUES 
    ('Voir clients', 'Permet de voir la liste des clients', 'CLIENTS_VIEW', 'Clients', 1, 1),
    ('Créer client', 'Permet de créer un nouveau client', 'CLIENTS_CREATE', 'Clients', 1, 1),
    ('Modifier client', 'Permet de modifier un client existant', 'CLIENTS_EDIT', 'Clients', 1, 1),
    ('Supprimer client', 'Permet de supprimer un client', 'CLIENTS_DELETE', 'Clients', 1, 1),
    ('Gérer contacts', 'Permet de gérer les contacts des clients', 'CONTACTS_MANAGE', 'Clients', 1, 1);
GO

-- Module Factures
INSERT INTO app.Permissions (Nom, Description, Code, Module, CreePar, EstActif)
VALUES 
    ('Voir factures', 'Permet de voir la liste des factures', 'INVOICES_VIEW', 'Factures', 1, 1),
    ('Créer facture', 'Permet de créer une nouvelle facture', 'INVOICES_CREATE', 'Factures', 1, 1),
    ('Modifier facture', 'Permet de modifier une facture existante', 'INVOICES_EDIT', 'Factures', 1, 1),
    ('Supprimer facture', 'Permet de supprimer une facture', 'INVOICES_DELETE', 'Factures', 1, 1),
    ('Changer statut facture', 'Permet de changer le statut d''une facture', 'INVOICES_STATUS', 'Factures', 1, 1);
GO

-- Module Paiements
INSERT INTO app.Permissions (Nom, Description, Code, Module, CreePar, EstActif)
VALUES 
    ('Voir paiements', 'Permet de voir la liste des paiements', 'PAYMENTS_VIEW', 'Paiements', 1, 1),
    ('Créer paiement', 'Permet de créer un nouveau paiement', 'PAYMENTS_CREATE', 'Paiements', 1, 1),
    ('Modifier paiement', 'Permet de modifier un paiement existant', 'PAYMENTS_EDIT', 'Paiements', 1, 1),
    ('Supprimer paiement', 'Permet de supprimer un paiement', 'PAYMENTS_DELETE', 'Paiements', 1, 1),
    ('Associer paiement', 'Permet d''associer un paiement à une facture', 'PAYMENTS_ASSOCIATE', 'Paiements', 1, 1);
GO

-- Module Relances
INSERT INTO app.Permissions (Nom, Description, Code, Module, CreePar, EstActif)
VALUES 
    ('Voir relances', 'Permet de voir la liste des relances', 'REMINDERS_VIEW', 'Relances', 1, 1),
    ('Créer relance', 'Permet de créer une nouvelle relance', 'REMINDERS_CREATE', 'Relances', 1, 1),
    ('Modifier relance', 'Permet de modifier une relance existante', 'REMINDERS_EDIT', 'Relances', 1, 1),
    ('Supprimer relance', 'Permet de supprimer une relance', 'REMINDERS_DELETE', 'Relances', 1, 1),
    ('Planifier relance', 'Permet de planifier des relances automatiques', 'REMINDERS_SCHEDULE', 'Relances', 1, 1);
GO

-- Module Documents
INSERT INTO app.Permissions (Nom, Description, Code, Module, CreePar, EstActif)
VALUES 
    ('Voir documents', 'Permet de voir la liste des documents', 'DOCUMENTS_VIEW', 'Documents', 1, 1),
    ('Ajouter document', 'Permet d''ajouter un nouveau document', 'DOCUMENTS_ADD', 'Documents', 1, 1),
    ('Télécharger document', 'Permet de télécharger un document', 'DOCUMENTS_DOWNLOAD', 'Documents', 1, 1),
    ('Supprimer document', 'Permet de supprimer un document', 'DOCUMENTS_DELETE', 'Documents', 1, 1);
GO

-- Module Rapports
INSERT INTO app.Permissions (Nom, Description, Code, Module, CreePar, EstActif)
VALUES 
    ('Voir rapports', 'Permet de voir les rapports', 'REPORTS_VIEW', 'Rapports', 1, 1),
    ('Exporter rapports', 'Permet d''exporter les rapports', 'REPORTS_EXPORT', 'Rapports', 1, 1),
    ('Voir journal audit', 'Permet de voir le journal d''audit', 'AUDIT_VIEW', 'Rapports', 1, 1);
GO

-- Association des permissions aux rôles
-- Administrateur (toutes les permissions)
INSERT INTO app.RolePermissions (RoleId, PermissionId, CreePar, EstActif)
SELECT 1, Id, 1, 1 FROM app.Permissions;
GO

-- Gestionnaire
INSERT INTO app.RolePermissions (RoleId, PermissionId, CreePar, EstActif)
SELECT 2, Id, 1, 1 FROM app.Permissions 
WHERE Code IN (
    'USERS_VIEW', 
    'CLIENTS_VIEW', 'CLIENTS_CREATE', 'CLIENTS_EDIT', 'CONTACTS_MANAGE',
    'INVOICES_VIEW', 'INVOICES_CREATE', 'INVOICES_EDIT', 'INVOICES_STATUS',
    'PAYMENTS_VIEW', 'PAYMENTS_CREATE', 'PAYMENTS_EDIT', 'PAYMENTS_ASSOCIATE',
    'REMINDERS_VIEW', 'REMINDERS_CREATE', 'REMINDERS_EDIT', 'REMINDERS_SCHEDULE',
    'DOCUMENTS_VIEW', 'DOCUMENTS_ADD', 'DOCUMENTS_DOWNLOAD',
    'REPORTS_VIEW', 'REPORTS_EXPORT'
);
GO

-- Commercial
INSERT INTO app.RolePermissions (RoleId, PermissionId, CreePar, EstActif)
SELECT 3, Id, 1, 1 FROM app.Permissions 
WHERE Code IN (
    'CLIENTS_VIEW', 'CLIENTS_CREATE', 'CLIENTS_EDIT', 'CONTACTS_MANAGE',
    'INVOICES_VIEW', 'INVOICES_CREATE',
    'PAYMENTS_VIEW',
    'REMINDERS_VIEW', 'REMINDERS_CREATE',
    'DOCUMENTS_VIEW', 'DOCUMENTS_ADD', 'DOCUMENTS_DOWNLOAD',
    'REPORTS_VIEW'
);
GO

-- Livreur
INSERT INTO app.RolePermissions (RoleId, PermissionId, CreePar, EstActif)
SELECT 4, Id, 1, 1 FROM app.Permissions 
WHERE Code IN (
    'CLIENTS_VIEW',
    'INVOICES_VIEW', 'INVOICES_STATUS',
    'DOCUMENTS_VIEW', 'DOCUMENTS_ADD', 'DOCUMENTS_DOWNLOAD'
);
GO

-- Comptable
INSERT INTO app.RolePermissions (RoleId, PermissionId, CreePar, EstActif)
SELECT 5, Id, 1, 1 FROM app.Permissions 
WHERE Code IN (
    'CLIENTS_VIEW',
    'INVOICES_VIEW', 'INVOICES_STATUS',
    'PAYMENTS_VIEW', 'PAYMENTS_CREATE', 'PAYMENTS_EDIT', 'PAYMENTS_ASSOCIATE',
    'DOCUMENTS_VIEW', 'DOCUMENTS_ADD', 'DOCUMENTS_DOWNLOAD',
    'REPORTS_VIEW', 'REPORTS_EXPORT'
);
GO

-- Consultant
INSERT INTO app.RolePermissions (RoleId, PermissionId, CreePar, EstActif)
SELECT 6, Id, 1, 1 FROM app.Permissions 
WHERE Code IN (
    'CLIENTS_VIEW',
    'INVOICES_VIEW',
    'PAYMENTS_VIEW',
    'REMINDERS_VIEW',
    'DOCUMENTS_VIEW', 'DOCUMENTS_DOWNLOAD',
    'REPORTS_VIEW'
);
GO

-- Création de l'utilisateur administrateur par défaut
-- Mot de passe: Admin123! (haché avec BCrypt)
INSERT INTO app.Utilisateurs (
    NomUtilisateur, MotDePasse, NomComplet, Email, 
    RoleId, CreePar, EstActif
)
VALUES (
    'admin', 
    '$2a$12$FS0VtY6AK5NV7cdXcJJZJOxKYVbSPqwAnpf9BPMN/2.3PVVrY5xxi', 
    'Administrateur Système', 
    '<EMAIL>', 
    1, 1, 1
);
GO

PRINT 'Données initiales insérées avec succès.';
GO
