using Newtonsoft.Json;
using RecouvreX.Business.Interfaces;
using RecouvreX.Models.Reporting;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Rapports
{
    public partial class CustomReportBuilderForm : Form
    {
        private readonly IRapportPersonnaliseService _rapportPersonnaliseService;
        private readonly int _currentUserId;
        private readonly RapportPersonnalise? _rapport;
        private readonly bool _isEditMode;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();
        private ConfigurationRapport _configuration = new ConfigurationRapport();
        private List<ColonneRapport> _availableColumns = new List<ColonneRapport>();
        private List<ColonneRapport> _selectedColumns = new List<ColonneRapport>();
        private List<FiltreRapport> _filters = new List<FiltreRapport>();
        private List<TriRapport> _sorts = new List<TriRapport>();
        private List<RegroupementRapport> _groupings = new List<RegroupementRapport>();
        private OptionsAffichageRapport _displayOptions = new OptionsAffichageRapport();

        public CustomReportBuilderForm(IRapportPersonnaliseService rapportPersonnaliseService, int currentUserId, RapportPersonnalise? rapport = null)
        {
            _rapportPersonnaliseService = rapportPersonnaliseService ?? throw new ArgumentNullException(nameof(rapportPersonnaliseService));
            _currentUserId = currentUserId;
            _rapport = rapport;
            _isEditMode = rapport != null;

            InitializeComponent();

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;

            // Charger les données
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form
            this.Text = _isEditMode ? "Modifier un rapport personnalisé" : "Créer un rapport personnalisé";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimizeBox = true;
            this.MaximizeBox = true;
            this.FormBorderStyle = FormBorderStyle.Sizable;

            // Main panel
            TableLayoutPanel mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 1,
                RowCount = 3,
                RowStyles = {
                    new RowStyle(SizeType.Absolute, 40),
                    new RowStyle(SizeType.Absolute, 120),
                    new RowStyle(SizeType.Percent, 100)
                }
            };
            this.Controls.Add(mainPanel);

            // Title label
            Label titleLabel = new Label
            {
                Text = _isEditMode ? "Modifier un rapport personnalisé" : "Créer un rapport personnalisé",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft
            };
            mainPanel.Controls.Add(titleLabel, 0, 0);

            // Info panel
            TableLayoutPanel infoPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 3,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 15),
                    new ColumnStyle(SizeType.Percent, 35),
                    new ColumnStyle(SizeType.Percent, 15),
                    new ColumnStyle(SizeType.Percent, 35)
                }
            };
            mainPanel.Controls.Add(infoPanel, 0, 1);

            // Nom
            infoPanel.Controls.Add(new Label { Text = "Nom :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 0);
            TextBox nomTextBox = new TextBox
            {
                Name = "nomTextBox",
                Dock = DockStyle.Fill
            };
            nomTextBox.Validating += NomTextBox_Validating;
            infoPanel.Controls.Add(nomTextBox, 1, 0);

            // Type de rapport
            infoPanel.Controls.Add(new Label { Text = "Type de rapport :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 2, 0);
            ComboBox typeRapportComboBox = new ComboBox
            {
                Name = "typeRapportComboBox",
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            typeRapportComboBox.SelectedIndexChanged += TypeRapportComboBox_SelectedIndexChanged;
            typeRapportComboBox.Validating += TypeRapportComboBox_Validating;
            infoPanel.Controls.Add(typeRapportComboBox, 3, 0);

            // Description
            infoPanel.Controls.Add(new Label { Text = "Description :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 1);
            TextBox descriptionTextBox = new TextBox
            {
                Name = "descriptionTextBox",
                Dock = DockStyle.Fill,
                Multiline = true
            };
            infoPanel.Controls.Add(descriptionTextBox, 1, 1);

            // Programmation
            infoPanel.Controls.Add(new Label { Text = "Programmation :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 2, 1);
            CheckBox programmeCheckBox = new CheckBox
            {
                Name = "programmeCheckBox",
                Text = "Rapport programmé",
                Dock = DockStyle.Fill
            };
            programmeCheckBox.CheckedChanged += ProgrammeCheckBox_CheckedChanged;
            infoPanel.Controls.Add(programmeCheckBox, 3, 1);

            // Fréquence
            infoPanel.Controls.Add(new Label { Text = "Fréquence :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 2);
            ComboBox frequenceComboBox = new ComboBox
            {
                Name = "frequenceComboBox",
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Enabled = false
            };
            frequenceComboBox.Items.Add("Quotidien");
            frequenceComboBox.Items.Add("Hebdomadaire");
            frequenceComboBox.Items.Add("Mensuel");
            frequenceComboBox.Items.Add("Trimestriel");
            frequenceComboBox.SelectedIndexChanged += FrequenceComboBox_SelectedIndexChanged;
            infoPanel.Controls.Add(frequenceComboBox, 1, 2);

            // Jour
            infoPanel.Controls.Add(new Label { Text = "Jour :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 2, 2);
            NumericUpDown jourNumeric = new NumericUpDown
            {
                Name = "jourNumeric",
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 31,
                Value = 1,
                Enabled = false
            };
            infoPanel.Controls.Add(jourNumeric, 3, 2);

            // Tabs panel
            TabControl tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Name = "tabControl"
            };
            mainPanel.Controls.Add(tabControl, 0, 2);

            // Colonnes tab
            TabPage colonnesTab = new TabPage("Colonnes");
            tabControl.TabPages.Add(colonnesTab);

            // Filtres tab
            TabPage filtresTab = new TabPage("Filtres");
            tabControl.TabPages.Add(filtresTab);

            // Tri tab
            TabPage triTab = new TabPage("Tri");
            tabControl.TabPages.Add(triTab);

            // Regroupement tab
            TabPage regroupementTab = new TabPage("Regroupement");
            tabControl.TabPages.Add(regroupementTab);

            // Options tab
            TabPage optionsTab = new TabPage("Options d'affichage");
            tabControl.TabPages.Add(optionsTab);

            // Aperçu tab
            TabPage apercuTab = new TabPage("Aperçu");
            tabControl.TabPages.Add(apercuTab);

            // Buttons panel
            TableLayoutPanel buttonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Bottom,
                ColumnCount = 3,
                RowCount = 1,
                Height = 40,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 100),
                    new ColumnStyle(SizeType.Absolute, 120),
                    new ColumnStyle(SizeType.Absolute, 120)
                }
            };
            this.Controls.Add(buttonsPanel);

            Button saveButton = new Button
            {
                Name = "saveButton",
                Text = "Enregistrer",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            saveButton.Click += SaveButton_Click;
            buttonsPanel.Controls.Add(saveButton, 1, 0);

            Button cancelButton = new Button
            {
                Name = "cancelButton",
                Text = "Annuler",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            cancelButton.Click += CancelButton_Click;
            buttonsPanel.Controls.Add(cancelButton, 2, 0);

            this.ResumeLayout(false);
        }

        private async void LoadData()
        {
            try
            {
                // Récupérer les types de rapports disponibles
                var reportTypes = await _rapportPersonnaliseService.GetAvailableReportTypesAsync();
                var typeRapportComboBox = Controls.Find("typeRapportComboBox", true).FirstOrDefault() as ComboBox;
                if (typeRapportComboBox != null)
                {
                    typeRapportComboBox.Items.Clear();
                    typeRapportComboBox.Items.Add("-- Sélectionner un type de rapport --");
                    foreach (var type in reportTypes)
                    {
                        typeRapportComboBox.Items.Add(type);
                    }
                    typeRapportComboBox.SelectedIndex = 0;
                }

                // Si on est en mode édition, charger les données du rapport
                if (_isEditMode && _rapport != null)
                {
                    LoadReportData();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données");
                MessageBox.Show($"Erreur lors du chargement des données : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadReportData()
        {
            if (_rapport == null)
                return;

            try
            {
                // Charger les informations générales
                var nomTextBox = Controls.Find("nomTextBox", true).FirstOrDefault() as TextBox;
                var descriptionTextBox = Controls.Find("descriptionTextBox", true).FirstOrDefault() as TextBox;
                var typeRapportComboBox = Controls.Find("typeRapportComboBox", true).FirstOrDefault() as ComboBox;
                var programmeCheckBox = Controls.Find("programmeCheckBox", true).FirstOrDefault() as CheckBox;
                var frequenceComboBox = Controls.Find("frequenceComboBox", true).FirstOrDefault() as ComboBox;
                var jourNumeric = Controls.Find("jourNumeric", true).FirstOrDefault() as NumericUpDown;

                if (nomTextBox != null)
                    nomTextBox.Text = _rapport.Nom;

                if (descriptionTextBox != null)
                    descriptionTextBox.Text = _rapport.Description;

                // Désérialiser la configuration
                if (!string.IsNullOrEmpty(_rapport.Configuration))
                {
                    _configuration = JsonConvert.DeserializeObject<ConfigurationRapport>(_rapport.Configuration);

                    if (typeRapportComboBox != null && _configuration != null)
                    {
                        int index = typeRapportComboBox.Items.IndexOf(_configuration.TypeRapport);
                        if (index >= 0)
                            typeRapportComboBox.SelectedIndex = index;
                    }
                }

                // Charger les informations de programmation
                if (programmeCheckBox != null)
                    programmeCheckBox.Checked = _rapport.EstProgramme;

                if (frequenceComboBox != null && _rapport.EstProgramme)
                {
                    frequenceComboBox.Enabled = true;
                    int index = frequenceComboBox.Items.IndexOf(_rapport.FrequenceGeneration);
                    if (index >= 0)
                        frequenceComboBox.SelectedIndex = index;
                }

                if (jourNumeric != null && _rapport.EstProgramme && _rapport.JourGeneration.HasValue)
                {
                    jourNumeric.Enabled = _rapport.FrequenceGeneration != "Quotidien";
                    jourNumeric.Value = _rapport.JourGeneration.Value;
                }

                // Charger les colonnes, filtres, tris, regroupements et options d'affichage
                if (_configuration != null)
                {
                    _selectedColumns = _configuration.Colonnes;
                    _filters = _configuration.Filtres;
                    _sorts = _configuration.Tris;
                    _groupings = _configuration.Regroupements;
                    _displayOptions = _configuration.OptionsAffichage;

                    // Mettre à jour les contrôles avec ces données
                    UpdateColumnsTab();
                    UpdateFiltersTab();
                    UpdateSortsTab();
                    UpdateGroupingsTab();
                    UpdateDisplayOptionsTab();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données du rapport {RapportId}", _rapport.Id);
                MessageBox.Show($"Erreur lors du chargement des données du rapport : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Méthodes pour mettre à jour les onglets
        private void UpdateColumnsTab()
        {
            // À implémenter
        }

        private void UpdateFiltersTab()
        {
            // À implémenter
        }

        private void UpdateSortsTab()
        {
            // À implémenter
        }

        private void UpdateGroupingsTab()
        {
            // À implémenter
        }

        private void UpdateDisplayOptionsTab()
        {
            // À implémenter
        }

        // Gestionnaires d'événements
        private void NomTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "Le nom du rapport est obligatoire.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private async void TypeRapportComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null && comboBox.SelectedIndex > 0)
            {
                string selectedType = comboBox.SelectedItem.ToString();
                try
                {
                    // Récupérer les colonnes disponibles pour ce type de rapport
                    _availableColumns = (await _rapportPersonnaliseService.GetAvailableColumnsAsync(selectedType)).ToList();
                    _configuration.TypeRapport = selectedType;

                    // Mettre à jour l'onglet des colonnes
                    UpdateColumnsTab();
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Erreur lors de la récupération des colonnes pour le type de rapport {Type}", selectedType);
                    MessageBox.Show($"Erreur lors de la récupération des colonnes : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void TypeRapportComboBox_Validating(object sender, CancelEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                if (comboBox.SelectedIndex <= 0)
                {
                    _errorProvider.SetError(comboBox, "Veuillez sélectionner un type de rapport.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(comboBox, "");
                }
            }
        }

        private void ProgrammeCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            var checkBox = sender as CheckBox;
            if (checkBox != null)
            {
                var frequenceComboBox = Controls.Find("frequenceComboBox", true).FirstOrDefault() as ComboBox;
                var jourNumeric = Controls.Find("jourNumeric", true).FirstOrDefault() as NumericUpDown;

                if (frequenceComboBox != null)
                {
                    frequenceComboBox.Enabled = checkBox.Checked;
                    if (checkBox.Checked && frequenceComboBox.SelectedIndex == -1)
                        frequenceComboBox.SelectedIndex = 0;
                }

                if (jourNumeric != null)
                {
                    jourNumeric.Enabled = checkBox.Checked && frequenceComboBox?.SelectedIndex > 0;
                }
            }
        }

        private void FrequenceComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                var jourNumeric = Controls.Find("jourNumeric", true).FirstOrDefault() as NumericUpDown;
                if (jourNumeric != null)
                {
                    string selectedFrequence = comboBox.SelectedItem?.ToString();
                    jourNumeric.Enabled = selectedFrequence != "Quotidien";

                    // Ajuster la valeur maximale en fonction de la fréquence
                    if (selectedFrequence == "Hebdomadaire")
                    {
                        jourNumeric.Maximum = 7;
                        jourNumeric.Value = Math.Min(jourNumeric.Value, 7);
                    }
                    else if (selectedFrequence == "Mensuel")
                    {
                        jourNumeric.Maximum = 31;
                    }
                    else if (selectedFrequence == "Trimestriel")
                    {
                        jourNumeric.Maximum = 31;
                    }
                }
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant d'enregistrer.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer les valeurs du formulaire
                var nomTextBox = Controls.Find("nomTextBox", true).FirstOrDefault() as TextBox;
                var descriptionTextBox = Controls.Find("descriptionTextBox", true).FirstOrDefault() as TextBox;
                var typeRapportComboBox = Controls.Find("typeRapportComboBox", true).FirstOrDefault() as ComboBox;
                var programmeCheckBox = Controls.Find("programmeCheckBox", true).FirstOrDefault() as CheckBox;
                var frequenceComboBox = Controls.Find("frequenceComboBox", true).FirstOrDefault() as ComboBox;
                var jourNumeric = Controls.Find("jourNumeric", true).FirstOrDefault() as NumericUpDown;

                if (nomTextBox == null || descriptionTextBox == null || typeRapportComboBox == null ||
                    programmeCheckBox == null || frequenceComboBox == null || jourNumeric == null)
                {
                    throw new InvalidOperationException("Un ou plusieurs contrôles sont introuvables.");
                }

                // Mettre à jour la configuration
                _configuration.Colonnes = _selectedColumns;
                _configuration.Filtres = _filters;
                _configuration.Tris = _sorts;
                _configuration.Regroupements = _groupings;
                _configuration.OptionsAffichage = _displayOptions;

                // Créer ou mettre à jour le rapport
                RapportPersonnalise rapport = _isEditMode ? _rapport : new RapportPersonnalise();
                rapport.Nom = nomTextBox.Text;
                rapport.Description = descriptionTextBox.Text;
                rapport.Configuration = JsonConvert.SerializeObject(_configuration);
                rapport.EstProgramme = programmeCheckBox.Checked;
                rapport.FrequenceGeneration = programmeCheckBox.Checked ? frequenceComboBox.SelectedItem.ToString() : null;
                rapport.JourGeneration = programmeCheckBox.Checked && frequenceComboBox.SelectedItem.ToString() != "Quotidien" ? (int?)jourNumeric.Value : null;
                rapport.EstActif = true;

                if (_isEditMode)
                {
                    bool success = await _rapportPersonnaliseService.UpdateAsync(rapport, _currentUserId);
                    if (success)
                    {
                        MessageBox.Show("Le rapport a été mis à jour avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        DialogResult = DialogResult.OK;
                        Close();
                    }
                    else
                    {
                        MessageBox.Show("Une erreur s'est produite lors de la mise à jour du rapport.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    var createdRapport = await _rapportPersonnaliseService.CreateAsync(rapport, _currentUserId);
                    if (createdRapport != null)
                    {
                        MessageBox.Show("Le rapport a été créé avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        DialogResult = DialogResult.OK;
                        Close();
                    }
                    else
                    {
                        MessageBox.Show("Une erreur s'est produite lors de la création du rapport.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement du rapport");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
}
