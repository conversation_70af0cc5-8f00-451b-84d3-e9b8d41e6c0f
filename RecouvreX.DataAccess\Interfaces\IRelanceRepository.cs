using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des relances
    /// </summary>
    public interface IRelanceRepository : IRepository<Relance>
    {
        /// <summary>
        /// Récupère toutes les relances d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des relances de la facture</returns>
        Task<IEnumerable<Relance>> GetByFactureIdAsync(int factureId);

        /// <summary>
        /// Récupère toutes les relances effectuées par un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des relances effectuées par l'utilisateur</returns>
        Task<IEnumerable<Relance>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les relances par type
        /// </summary>
        /// <param name="type">Type de relance</param>
        /// <returns>Liste des relances du type spécifié</returns>
        Task<IEnumerable<Relance>> GetByTypeAsync(string type);

        /// <summary>
        /// Récupère les relances par statut
        /// </summary>
        /// <param name="statut">Statut de relance</param>
        /// <returns>Liste des relances ayant le statut spécifié</returns>
        Task<IEnumerable<Relance>> GetByStatutAsync(string statut);

        /// <summary>
        /// Récupère les relances par niveau
        /// </summary>
        /// <param name="niveau">Niveau de relance</param>
        /// <returns>Liste des relances du niveau spécifié</returns>
        Task<IEnumerable<Relance>> GetByNiveauAsync(int niveau);

        /// <summary>
        /// Récupère les relances par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des relances effectuées dans la période spécifiée</returns>
        Task<IEnumerable<Relance>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère les relances planifiées pour une date donnée
        /// </summary>
        /// <param name="date">Date des relances planifiées</param>
        /// <returns>Liste des relances planifiées pour la date spécifiée</returns>
        Task<IEnumerable<Relance>> GetPlannedForDateAsync(DateTime date);

        /// <summary>
        /// Récupère toutes les relances associées à un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des relances du client</returns>
        Task<IEnumerable<Relance>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Met à jour le statut d'une relance
        /// </summary>
        /// <param name="relanceId">Identifiant de la relance</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateStatutAsync(int relanceId, string statut, int userId);
    }
}
