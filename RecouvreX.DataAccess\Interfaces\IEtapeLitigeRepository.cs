using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des étapes de litiges
    /// </summary>
    public interface IEtapeLitigeRepository : IRepository<EtapeLitige>
    {
        /// <summary>
        /// Récupère une étape de litige par son nom
        /// </summary>
        /// <param name="nom">Nom de l'étape</param>
        /// <returns>Étape de litige trouvée ou null</returns>
        Task<EtapeLitige> GetByNomAsync(string nom);

        /// <summary>
        /// Récupère toutes les étapes de litiges triées par ordre
        /// </summary>
        /// <returns>Liste des étapes de litiges triées par ordre</returns>
        Task<IEnumerable<EtapeLitige>> GetAllOrderedAsync();

        /// <summary>
        /// Récupère toutes les étapes de litiges avec le nombre de litiges associés
        /// </summary>
        /// <returns>Liste des étapes de litiges avec le nombre de litiges</returns>
        Task<IEnumerable<EtapeLitige>> GetAllWithCountAsync();

        /// <summary>
        /// Récupère l'étape suivante dans le workflow
        /// </summary>
        /// <param name="etapeId">Identifiant de l'étape actuelle</param>
        /// <returns>Étape suivante ou null si c'est la dernière étape</returns>
        Task<EtapeLitige> GetNextEtapeAsync(int etapeId);

        /// <summary>
        /// Récupère l'étape précédente dans le workflow
        /// </summary>
        /// <param name="etapeId">Identifiant de l'étape actuelle</param>
        /// <returns>Étape précédente ou null si c'est la première étape</returns>
        Task<EtapeLitige> GetPreviousEtapeAsync(int etapeId);
    }
}
