using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des contacts
    /// </summary>
    public interface IContactService
    {
        /// <summary>
        /// Récupère tous les contacts
        /// </summary>
        /// <returns>Liste des contacts</returns>
        Task<IEnumerable<Contact>> GetAllAsync();

        /// <summary>
        /// Récupère un contact par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du contact</param>
        /// <returns>Contact trouvé ou null</returns>
        Task<Contact> GetByIdAsync(int id);

        /// <summary>
        /// Récupère tous les contacts d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des contacts du client</returns>
        Task<IEnumerable<Contact>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère le contact principal d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Contact principal du client ou null si aucun contact principal n'est défini</returns>
        Task<Contact> GetPrincipalContactAsync(int clientId);

        /// <summary>
        /// Ajoute un nouveau contact
        /// </summary>
        /// <param name="contact">Contact à ajouter</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact ajouté avec son identifiant généré</returns>
        Task<Contact> AddAsync(Contact contact, int creePar);

        /// <summary>
        /// Met à jour un contact existant
        /// </summary>
        /// <param name="contact">Contact à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact mis à jour</returns>
        Task<Contact> UpdateAsync(Contact contact, int modifiePar);

        /// <summary>
        /// Supprime un contact
        /// </summary>
        /// <param name="id">Identifiant du contact à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);
    }
}
