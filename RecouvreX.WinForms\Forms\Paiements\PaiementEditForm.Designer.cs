namespace RecouvreX.WinForms.Forms.Paiements
{
    partial class PaiementEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // PaiementEditForm
            // 
            this.ClientSize = new System.Drawing.Size(600, 400);
            this.Name = "PaiementEditForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Paiement"; // Titre par défaut, sera mis à jour dans le constructeur
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Load += new System.EventHandler(this.PaiementEditForm_Load);
            this.ResumeLayout(false);

            // Panneau principal
            System.Windows.Forms.TableLayoutPanel mainPanel = new System.Windows.Forms.TableLayoutPanel();
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Padding = new System.Windows.Forms.Padding(10);
            mainPanel.ColumnCount = 2;
            mainPanel.RowCount = 8;
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 30F));
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 70F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            this.Controls.Add(mainPanel);

            // Référence
            System.Windows.Forms.Label referenceLabel = new System.Windows.Forms.Label();
            referenceLabel.Text = "Référence :";
            referenceLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(referenceLabel, 0, 0);

            this.referenceTextBox = new System.Windows.Forms.TextBox();
            this.referenceTextBox.Name = "referenceTextBox";
            this.referenceTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            this.referenceTextBox.Enabled = false; // Sera mis à jour dans le constructeur
            mainPanel.Controls.Add(this.referenceTextBox, 1, 0);

            // Client
            System.Windows.Forms.Label clientLabel = new System.Windows.Forms.Label();
            clientLabel.Text = "Client* :";
            clientLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(clientLabel, 0, 1);

            System.Windows.Forms.ComboBox clientComboBox = new System.Windows.Forms.ComboBox();
            clientComboBox.Name = "clientComboBox";
            clientComboBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            clientComboBox.Validating += new System.ComponentModel.CancelEventHandler(this.ClientComboBox_Validating);
            mainPanel.Controls.Add(clientComboBox, 1, 1);

            // Date de paiement
            System.Windows.Forms.Label datePaiementLabel = new System.Windows.Forms.Label();
            datePaiementLabel.Text = "Date de paiement* :";
            datePaiementLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(datePaiementLabel, 0, 2);

            System.Windows.Forms.DateTimePicker datePaiementPicker = new System.Windows.Forms.DateTimePicker();
            datePaiementPicker.Name = "datePaiementPicker";
            datePaiementPicker.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            datePaiementPicker.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            datePaiementPicker.Value = System.DateTime.Today;
            mainPanel.Controls.Add(datePaiementPicker, 1, 2);

            // Montant
            System.Windows.Forms.Label montantLabel = new System.Windows.Forms.Label();
            montantLabel.Text = "Montant* :";
            montantLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(montantLabel, 0, 3);

            System.Windows.Forms.NumericUpDown montantNumericUpDown = new System.Windows.Forms.NumericUpDown();
            montantNumericUpDown.Name = "montantNumericUpDown";
            montantNumericUpDown.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            montantNumericUpDown.DecimalPlaces = 2;
            montantNumericUpDown.Maximum = 1000000;
            montantNumericUpDown.ThousandsSeparator = true;
            montantNumericUpDown.Validating += new System.ComponentModel.CancelEventHandler(this.MontantNumericUpDown_Validating);
            mainPanel.Controls.Add(montantNumericUpDown, 1, 3);

            // Mode de paiement
            System.Windows.Forms.Label modePaiementLabel = new System.Windows.Forms.Label();
            modePaiementLabel.Text = "Mode de paiement* :";
            modePaiementLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(modePaiementLabel, 0, 4);

            System.Windows.Forms.ComboBox modePaiementComboBox = new System.Windows.Forms.ComboBox();
            modePaiementComboBox.Name = "modePaiementComboBox";
            modePaiementComboBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            modePaiementComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            modePaiementComboBox.Items.Add("Virement");
            modePaiementComboBox.Items.Add("Chèque");
            modePaiementComboBox.Items.Add("Carte bancaire");
            modePaiementComboBox.Items.Add("Espèces");
            modePaiementComboBox.SelectedIndex = 0;
            modePaiementComboBox.Validating += new System.ComponentModel.CancelEventHandler(this.ModePaiementComboBox_Validating);
            mainPanel.Controls.Add(modePaiementComboBox, 1, 4);

            // Référence bancaire
            System.Windows.Forms.Label referenceBancaireLabel = new System.Windows.Forms.Label();
            referenceBancaireLabel.Text = "Référence bancaire :";
            referenceBancaireLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(referenceBancaireLabel, 0, 5);

            System.Windows.Forms.TextBox referenceBancaireTextBox = new System.Windows.Forms.TextBox();
            referenceBancaireTextBox.Name = "referenceBancaireTextBox";
            referenceBancaireTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(referenceBancaireTextBox, 1, 5);

            // Boutons
            System.Windows.Forms.FlowLayoutPanel buttonsPanel = new System.Windows.Forms.FlowLayoutPanel();
            buttonsPanel.FlowDirection = System.Windows.Forms.FlowDirection.RightToLeft;
            buttonsPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            buttonsPanel.Anchor = System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(buttonsPanel, 1, 7);
            mainPanel.SetColumnSpan(buttonsPanel, 2);

            System.Windows.Forms.Button cancelButton = new System.Windows.Forms.Button();
            cancelButton.Name = "cancelButton";
            cancelButton.Text = "Annuler";
            cancelButton.Size = new System.Drawing.Size(100, 30);
            cancelButton.Click += new System.EventHandler(this.CancelButton_Click);
            buttonsPanel.Controls.Add(cancelButton);

            System.Windows.Forms.Button saveButton = new System.Windows.Forms.Button();
            saveButton.Name = "saveButton";
            saveButton.Text = "Enregistrer";
            saveButton.Size = new System.Drawing.Size(100, 30);
            saveButton.Click += new System.EventHandler(this.SaveButton_Click);
            buttonsPanel.Controls.Add(saveButton);

            // Note sur les champs obligatoires
            System.Windows.Forms.Label noteLabel = new System.Windows.Forms.Label();
            noteLabel.Text = "* Champs obligatoires";
            noteLabel.AutoSize = true;
            noteLabel.ForeColor = System.Drawing.Color.Red;
            noteLabel.Location = new System.Drawing.Point(10, this.ClientSize.Height - 30);
            this.Controls.Add(noteLabel);
        }

        #endregion
    }
}
