using RecouvreX.Models.Reporting;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des rapports
    /// </summary>
    public interface IRapportRepository : IRepository<Rapport>
    {
        /// <summary>
        /// Récupère les rapports par type
        /// </summary>
        /// <param name="type">Type de rapport</param>
        /// <returns>Liste des rapports du type spécifié</returns>
        Task<IEnumerable<Rapport>> GetByTypeAsync(string type);

        /// <summary>
        /// Récupère les rapports par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des rapports créés par l'utilisateur spécifié</returns>
        Task<IEnumerable<Rapport>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les rapports par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des rapports pour la période spécifiée</returns>
        Task<IEnumerable<Rapport>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère les rapports programmés à générer
        /// </summary>
        /// <param name="dateReference">Date de référence (par défaut, la date actuelle)</param>
        /// <returns>Liste des rapports programmés à générer</returns>
        Task<IEnumerable<Rapport>> GetScheduledReportsAsync(DateTime? dateReference = null);

        /// <summary>
        /// Met à jour la date de prochaine génération d'un rapport programmé
        /// </summary>
        /// <param name="id">Identifiant du rapport</param>
        /// <param name="dateProchainRapport">Date de prochaine génération</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateNextGenerationDateAsync(int id, DateTime dateProchainRapport, int modifiePar);
    }
}
