using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Clients
{
    public partial class ContactEditForm : Form
    {
        private readonly IContactService _contactService;
        private readonly int _currentUserId;
        private readonly Contact _contact;
        private readonly bool _isNewContact;

        public ContactEditForm(IContactService contactService, int currentUserId, Contact contact)
        {
            _contactService = contactService ?? throw new ArgumentNullException(nameof(contactService));
            _currentUserId = currentUserId;
            _contact = contact ?? throw new ArgumentNullException(nameof(contact));
            _isNewContact = contact.Id == 0;

            InitializeComponent();
        }

        private void ContactEditForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Configurer le titre du formulaire
                this.Text = _isNewContact ? "Ajouter un contact" : "Modifier un contact";

                // Remplir les champs avec les données du contact
                if (!_isNewContact)
                {
                    FillContactData();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'édition de contact");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FillContactData()
        {
            var nomTextBox = this.Controls.Find("nomTextBox", true).FirstOrDefault() as TextBox;
            var prenomTextBox = this.Controls.Find("prenomTextBox", true).FirstOrDefault() as TextBox;
            var fonctionTextBox = this.Controls.Find("fonctionTextBox", true).FirstOrDefault() as TextBox;
            var telephoneTextBox = this.Controls.Find("telephoneTextBox", true).FirstOrDefault() as TextBox;
            var emailTextBox = this.Controls.Find("emailTextBox", true).FirstOrDefault() as TextBox;
            var estPrincipalCheckBox = this.Controls.Find("estPrincipalCheckBox", true).FirstOrDefault() as CheckBox;
            var notesTextBox = this.Controls.Find("notesTextBox", true).FirstOrDefault() as TextBox;

            if (nomTextBox != null) nomTextBox.Text = _contact.Nom;
            if (prenomTextBox != null) prenomTextBox.Text = _contact.Prenom;
            if (fonctionTextBox != null) fonctionTextBox.Text = _contact.Fonction;
            if (telephoneTextBox != null) telephoneTextBox.Text = _contact.Telephone;
            if (emailTextBox != null) emailTextBox.Text = _contact.Email;
            if (estPrincipalCheckBox != null) estPrincipalCheckBox.Checked = _contact.EstPrincipal;
            if (notesTextBox != null) notesTextBox.Text = _contact.Notes;
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer les valeurs des champs
                var nomTextBox = this.Controls.Find("nomTextBox", true).FirstOrDefault() as TextBox;
                var prenomTextBox = this.Controls.Find("prenomTextBox", true).FirstOrDefault() as TextBox;
                var fonctionTextBox = this.Controls.Find("fonctionTextBox", true).FirstOrDefault() as TextBox;
                var telephoneTextBox = this.Controls.Find("telephoneTextBox", true).FirstOrDefault() as TextBox;
                var emailTextBox = this.Controls.Find("emailTextBox", true).FirstOrDefault() as TextBox;
                var estPrincipalCheckBox = this.Controls.Find("estPrincipalCheckBox", true).FirstOrDefault() as CheckBox;
                var notesTextBox = this.Controls.Find("notesTextBox", true).FirstOrDefault() as TextBox;

                // Valider les champs obligatoires
                if (string.IsNullOrWhiteSpace(nomTextBox?.Text))
                {
                    MessageBox.Show("Le nom du contact est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    nomTextBox?.Focus();
                    return;
                }

                // Mettre à jour l'objet contact
                _contact.Nom = nomTextBox?.Text?.Trim();
                _contact.Prenom = prenomTextBox?.Text?.Trim();
                _contact.Fonction = fonctionTextBox?.Text?.Trim();
                _contact.Telephone = telephoneTextBox?.Text?.Trim();
                _contact.Email = emailTextBox?.Text?.Trim();
                _contact.EstPrincipal = estPrincipalCheckBox?.Checked ?? false;
                _contact.Notes = notesTextBox?.Text?.Trim();

                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Enregistrer le contact
                if (_isNewContact)
                {
                    await _contactService.AddAsync(_contact, _currentUserId);
                }
                else
                {
                    await _contactService.UpdateAsync(_contact, _currentUserId);
                }

                // Fermer le formulaire avec succès
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement du contact");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement du contact : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
