using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace RecouvreX.Models.Reporting
{
    /// <summary>
    /// Représente un rapport personnalisé configurable par l'utilisateur
    /// </summary>
    public class RapportPersonnalise : BaseEntity
    {
        /// <summary>
        /// Nom du rapport personnalisé
        /// </summary>
        public string Nom { get; set; } = string.Empty;

        /// <summary>
        /// Description du rapport personnalisé
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Identifiant de l'utilisateur qui a créé le rapport
        /// </summary>
        public int UtilisateurId { get; set; }

        /// <summary>
        /// Utilisateur qui a créé le rapport (navigation property)
        /// </summary>
        public Utilisateur Utilisateur { get; set; } = null!;

        /// <summary>
        /// Date de création du rapport
        /// </summary>
        public new DateTime DateCreation { get; set; } = DateTime.Now;

        /// <summary>
        /// Date de dernière modification du rapport
        /// </summary>
        public new DateTime DateModification { get; set; } = DateTime.Now;

        /// <summary>
        /// Configuration du rapport au format JSON
        /// </summary>
        public string Configuration { get; set; } = string.Empty;

        /// <summary>
        /// Indique si le rapport est programmé pour être généré périodiquement
        /// </summary>
        public bool EstProgramme { get; set; }

        /// <summary>
        /// Fréquence de génération du rapport (Quotidien, Hebdomadaire, Mensuel, etc.)
        /// </summary>
        public string FrequenceGeneration { get; set; } = string.Empty;

        /// <summary>
        /// Jour de génération pour les rapports hebdomadaires ou mensuels
        /// </summary>
        public int? JourGeneration { get; set; }

        /// <summary>
        /// Heure de génération du rapport
        /// </summary>
        public TimeSpan HeureGeneration { get; set; }

        /// <summary>
        /// Liste des destinataires email pour l'envoi automatique
        /// </summary>
        public string DestinatairesEmail { get; set; } = string.Empty;

        /// <summary>
        /// Format d'export par défaut (PDF, Excel, CSV)
        /// </summary>
        public string FormatExport { get; set; } = string.Empty;

        /// <summary>
        /// Indique si le rapport est actif
        /// </summary>
        public new bool EstActif { get; set; } = true;

        /// <summary>
        /// Date de dernière génération du rapport
        /// </summary>
        public DateTime? DerniereGeneration { get; set; }
    }

    /// <summary>
    /// Configuration d'un rapport personnalisé
    /// </summary>
    public class ConfigurationRapport
    {
        /// <summary>
        /// Type de rapport (Factures, Paiements, Clients, etc.)
        /// </summary>
        public string TypeRapport { get; set; } = string.Empty;

        /// <summary>
        /// Liste des colonnes à inclure dans le rapport
        /// </summary>
        public List<ColonneRapport> Colonnes { get; set; } = new List<ColonneRapport>();

        /// <summary>
        /// Liste des filtres à appliquer
        /// </summary>
        public List<FiltreRapport> Filtres { get; set; } = new List<FiltreRapport>();

        /// <summary>
        /// Liste des tris à appliquer
        /// </summary>
        public List<TriRapport> Tris { get; set; } = new List<TriRapport>();

        /// <summary>
        /// Liste des regroupements à appliquer
        /// </summary>
        public List<RegroupementRapport> Regroupements { get; set; } = new List<RegroupementRapport>();

        /// <summary>
        /// Options d'affichage du rapport
        /// </summary>
        public OptionsAffichageRapport OptionsAffichage { get; set; } = new OptionsAffichageRapport();
    }

    /// <summary>
    /// Représente une colonne dans un rapport personnalisé
    /// </summary>
    public class ColonneRapport
    {
        /// <summary>
        /// Nom de la colonne
        /// </summary>
        public string Nom { get; set; } = string.Empty;

        /// <summary>
        /// Titre d'affichage de la colonne
        /// </summary>
        public string Titre { get; set; } = string.Empty;

        /// <summary>
        /// Type de données de la colonne
        /// </summary>
        public string TypeDonnees { get; set; } = string.Empty;

        /// <summary>
        /// Format d'affichage pour la colonne
        /// </summary>
        public string Format { get; set; } = string.Empty;

        /// <summary>
        /// Largeur de la colonne
        /// </summary>
        public int Largeur { get; set; }

        /// <summary>
        /// Indique si la colonne est visible
        /// </summary>
        public bool EstVisible { get; set; } = true;

        /// <summary>
        /// Indique si la colonne est utilisée pour calculer des totaux
        /// </summary>
        public bool CalculerTotal { get; set; }
    }

    /// <summary>
    /// Représente un filtre dans un rapport personnalisé
    /// </summary>
    public class FiltreRapport
    {
        /// <summary>
        /// Nom de la colonne à filtrer
        /// </summary>
        public string Colonne { get; set; } = string.Empty;

        /// <summary>
        /// Opérateur de comparaison (=, >, <, LIKE, etc.)
        /// </summary>
        public string Operateur { get; set; } = string.Empty;

        /// <summary>
        /// Valeur du filtre
        /// </summary>
        public string Valeur { get; set; } = string.Empty;

        /// <summary>
        /// Opérateur logique avec le filtre suivant (AND, OR)
        /// </summary>
        public string OperateurLogique { get; set; } = "AND";
    }

    /// <summary>
    /// Représente un tri dans un rapport personnalisé
    /// </summary>
    public class TriRapport
    {
        /// <summary>
        /// Nom de la colonne à trier
        /// </summary>
        public string Colonne { get; set; } = string.Empty;

        /// <summary>
        /// Direction du tri (ASC, DESC)
        /// </summary>
        public string Direction { get; set; } = "ASC";

        /// <summary>
        /// Ordre du tri
        /// </summary>
        public int Ordre { get; set; }
    }

    /// <summary>
    /// Représente un regroupement dans un rapport personnalisé
    /// </summary>
    public class RegroupementRapport
    {
        /// <summary>
        /// Nom de la colonne pour le regroupement
        /// </summary>
        public string Colonne { get; set; } = string.Empty;

        /// <summary>
        /// Ordre du regroupement
        /// </summary>
        public int Ordre { get; set; }

        /// <summary>
        /// Fonction d'agrégation pour les colonnes numériques (SUM, AVG, MIN, MAX, COUNT)
        /// </summary>
        public Dictionary<string, string> FonctionsAgregation { get; set; } = new Dictionary<string, string>();
    }

    /// <summary>
    /// Options d'affichage pour un rapport personnalisé
    /// </summary>
    public class OptionsAffichageRapport
    {
        /// <summary>
        /// Titre du rapport
        /// </summary>
        public string Titre { get; set; } = string.Empty;

        /// <summary>
        /// Sous-titre du rapport
        /// </summary>
        public string SousTitre { get; set; } = string.Empty;

        /// <summary>
        /// Afficher les en-têtes de page
        /// </summary>
        public bool AfficherEnTetes { get; set; } = true;

        /// <summary>
        /// Afficher les pieds de page
        /// </summary>
        public bool AfficherPiedsDePage { get; set; } = true;

        /// <summary>
        /// Texte de l'en-tête
        /// </summary>
        public string TexteEnTete { get; set; } = string.Empty;

        /// <summary>
        /// Texte du pied de page
        /// </summary>
        public string TextePiedDePage { get; set; } = string.Empty;

        /// <summary>
        /// Afficher les numéros de page
        /// </summary>
        public bool AfficherNumerosPage { get; set; } = true;

        /// <summary>
        /// Afficher la date de génération
        /// </summary>
        public bool AfficherDateGeneration { get; set; } = true;

        /// <summary>
        /// Orientation de la page (Portrait, Paysage)
        /// </summary>
        public string Orientation { get; set; } = "Portrait";

        /// <summary>
        /// Taille de la police
        /// </summary>
        public int TaillePolice { get; set; } = 10;

        /// <summary>
        /// Afficher les totaux
        /// </summary>
        public bool AfficherTotaux { get; set; } = true;

        /// <summary>
        /// Afficher une grille
        /// </summary>
        public bool AfficherGrille { get; set; } = true;

        /// <summary>
        /// Couleur d'alternance des lignes
        /// </summary>
        public string CouleurAlternance { get; set; } = "#F5F5F5";
    }

    /// <summary>
    /// Fréquences de génération disponibles pour les rapports programmés
    /// </summary>
    public static class FrequenceGeneration
    {
        public const string Quotidien = "Quotidien";
        public const string Hebdomadaire = "Hebdomadaire";
        public const string Mensuel = "Mensuel";
        public const string Trimestriel = "Trimestriel";
    }
}
