using System.Globalization;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RecouvreX.Business.DependencyInjection;
using RecouvreX.Business.Interfaces;
using RecouvreX.WinForms.Forms;
using RecouvreX.WinForms.Forms.Admin;
using RecouvreX.WinForms.Forms.Clients;
using RecouvreX.WinForms.Forms.Communications;
using RecouvreX.WinForms.Forms.Factures;
using RecouvreX.WinForms.Forms.Rapports;
using RecouvreX.WinForms.Forms.Relances;
using Serilog;

namespace RecouvreX.WinForms;

internal static class Program
{
    /// <summary>
    ///     Point d'entrée principal de l'application.
    /// </summary>
    [STAThread]
    private static async Task Main()
    {
        // Configuration de l'application
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", false, true)
            .Build();

        // Configuration de Serilog
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .CreateLogger();

        try
        {
            Log.Information("Démarrage de l'application RecouvreX");

            // Configuration de la culture
            var defaultCulture = configuration.GetValue<string>("AppSettings:DefaultCulture") ?? "fr-FR";
            Thread.CurrentThread.CurrentCulture = new CultureInfo(defaultCulture);
            Thread.CurrentThread.CurrentUICulture = new CultureInfo(defaultCulture);

            // Configuration de l'interface utilisateur
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.SetHighDpiMode(HighDpiMode.SystemAware);

            // Configuration des services
            var services = new ServiceCollection();

            // Ajouter la configuration
            services.AddSingleton<IConfiguration>(configuration);

            // Ajouter les services de l'application
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            // Utiliser explicitement la classe ServiceCollectionExtensions pour éviter l'ambiguïté
            ServiceCollectionExtensions.AddRecouvreXServices(services, connectionString);

            // Ajouter les formulaires
            services.AddTransient<LoginForm>();
            services.AddTransient(provider =>
                new MainForm(
                    provider.GetRequiredService<IAuthenticationService>(),
                    provider.GetRequiredService<IUtilisateurService>(),
                    provider.GetRequiredService<IClientService>(),
                    provider.GetRequiredService<IFactureService>(),
                    provider.GetRequiredService<IPaiementService>(),
                    provider.GetRequiredService<IRelanceService>(),
                    provider.GetRequiredService<IAlerteService>(),
                    provider.GetRequiredService<IModeleRelanceService>(),
                    provider.GetRequiredService<IRegleRelanceService>(),
                    provider.GetRequiredService<IPlanificationRelanceService>(),
                    provider.GetRequiredService<ICommunicationService>(),
                    provider.GetRequiredService<IContactClientService>(),
                    provider.GetRequiredService<IActionPrioritaireService>(),
                    provider.GetRequiredService<ILitigeService>(),
                    provider.GetRequiredService<IReportService>(),
                    provider.GetRequiredService<IRapportPersonnaliseService>(),
                    provider.GetRequiredService<IDatabaseAdminService>(),
                    provider.GetRequiredService<IConfiguration>(),
                    provider
                ));
            services.AddTransient<ModeleRelanceListForm>();
            services.AddTransient<ModeleRelanceEditForm>();
            services.AddTransient<RegleRelanceListForm>();
            services.AddTransient<RegleRelanceEditForm>();
            services.AddTransient(provider =>
                new ScoreRisqueClientListForm(
                    provider.GetRequiredService<IScoreRisqueClientService>(),
                    provider.GetRequiredService<IClientService>(),
                    // Utilisateur courant - à remplacer par la valeur réelle
                    1
                ));
            services.AddTransient(provider =>
                new ClientSegmentationForm(
                    provider.GetRequiredService<IClientService>(),
                    // Utilisateur courant - à remplacer par la valeur réelle
                    1
                ));
            services.AddTransient<PlanificationRelanceListForm>();
            services.AddTransient<CommentaireForm>();
            services.AddTransient(provider =>
                new LettreRelanceForm(
                    provider.GetRequiredService<IFactureService>(),
                    provider.GetRequiredService<IClientService>(),
                    provider.GetRequiredService<ICommunicationService>(),
                    provider.GetRequiredService<IContactClientService>(),
                    provider.GetRequiredService<IConfiguration>(),
                    // Utilisateur courant - à remplacer par la valeur réelle
                    1,
                    // Facture ID - sera remplacé lors de l'appel
                    0
                ));
            services.AddTransient(provider =>
                new CommunicationListForm(
                    provider.GetRequiredService<ICommunicationService>(),
                    provider.GetRequiredService<IFactureService>(),
                    provider.GetRequiredService<IClientService>(),
                    provider.GetRequiredService<IContactClientService>(),
                    provider.GetRequiredService<IAuthenticationService>(),
                    provider.GetRequiredService<IConfiguration>(),
                    provider,
                    // Utilisateur courant - à remplacer par la valeur réelle
                    1
                ));
            services.AddTransient<AppelForm>();
            services.AddTransient(provider =>
                new EmailForm(
                    provider.GetRequiredService<ICommunicationService>(),
                    provider.GetRequiredService<IContactClientService>(),
                    provider.GetRequiredService<IConfiguration>(),
                    // Utilisateur courant - à remplacer par la valeur réelle
                    1,
                    // Facture ID - sera remplacé lors de l'appel
                    0
                ));
            services.AddTransient(provider =>
                new WhatsAppForm(
                    provider.GetRequiredService<ICommunicationService>(),
                    provider.GetRequiredService<IContactClientService>(),
                    provider.GetRequiredService<IFactureService>(),
                    provider.GetRequiredService<IClientService>(),
                    // Utilisateur courant - à remplacer par la valeur réelle
                    1,
                    // Facture ID - sera remplacé lors de l'appel
                    0
                ));
            services.AddTransient<NoteForm>();
            services.AddTransient(provider =>
                new FactureDetailsForm(
                    provider.GetRequiredService<IFactureService>(),
                    provider.GetRequiredService<IClientService>(),
                    provider.GetRequiredService<IPaiementService>(),
                    provider.GetRequiredService<IRelanceService>(),
                    provider.GetRequiredService<ICommunicationService>(),
                    provider.GetRequiredService<IContactClientService>(),
                    provider.GetRequiredService<IConfiguration>(),
                    provider,
                    // Utilisateur courant - à remplacer par la valeur réelle
                    1,
                    // Facture ID - sera remplacé lors de l'appel
                    0
                ));
            // Formulaires de rapports et analyses
            services.AddTransient(provider =>
                new ReportGeneratorForm(
                    provider.GetRequiredService<IReportService>(),
                    // Utilisateur courant - à remplacer par la valeur réelle
                    1
                ));
            services.AddTransient(provider =>
                new CustomReportListForm(
                    provider.GetRequiredService<IRapportPersonnaliseService>(),
                    provider.GetRequiredService<IReportService>(),
                    // Utilisateur courant - à remplacer par la valeur réelle
                    1
                ));
            services.AddTransient(provider =>
                new CustomReportBuilderForm(
                    provider.GetRequiredService<IRapportPersonnaliseService>(),
                    // Utilisateur courant - à remplacer par la valeur réelle
                    1
                ));
            services.AddTransient<ReportPreviewForm>();
            services.AddTransient<ExportFormatForm>();
            services.AddTransient<SendReportByEmailForm>();

            // Formulaires d'administration de la base de données
            services.AddTransient<DatabaseConnectionForm>();
            services.AddTransient<DatabaseAdminForm>();

            // Ajouter d'autres formulaires ici...

            // Construire le fournisseur de services
            var serviceProvider = services.BuildServiceProvider();

            // Tester la connexion à la base de données
            var databaseAdminService = serviceProvider.GetRequiredService<IDatabaseAdminService>();
            // Utiliser la chaîne de connexion déjà définie plus haut

            if (!string.IsNullOrEmpty(connectionString))
            {
                // Extraire les paramètres de la chaîne de connexion
                var builder = new SqlConnectionStringBuilder(connectionString);
                var server = builder.DataSource;
                var database = builder.InitialCatalog;
                var integratedSecurity = builder.IntegratedSecurity;
                var userId = builder.UserID;
                var password = builder.Password;

                // Tester la connexion
                var connectionSuccessful = await databaseAdminService.TestConnectionAsync(
                    server,
                    database,
                    integratedSecurity ? null : userId,
                    integratedSecurity ? null : password,
                    integratedSecurity);

                if (!connectionSuccessful)
                {
                    // Si la connexion échoue, afficher le formulaire de configuration de la connexion
                    Log.Warning(
                        "La connexion à la base de données a échoué. Affichage du formulaire de configuration de la connexion.");

                    using (var databaseConnectionForm = serviceProvider.GetRequiredService<DatabaseConnectionForm>())
                    {
                        var result = databaseConnectionForm.ShowDialog();
                        if (result != DialogResult.OK)
                            // Si l'utilisateur annule, fermer l'application
                            return;

                        // Redémarrer l'application pour prendre en compte les nouveaux paramètres
                        Application.Restart();
                        return;
                    }
                }
            }
            else
            {
                // Si la chaîne de connexion est vide, afficher le formulaire de configuration de la connexion
                Log.Warning(
                    "Aucune chaîne de connexion trouvée. Affichage du formulaire de configuration de la connexion.");

                using (var databaseConnectionForm = serviceProvider.GetRequiredService<DatabaseConnectionForm>())
                {
                    var result = databaseConnectionForm.ShowDialog();
                    if (result != DialogResult.OK)
                        // Si l'utilisateur annule, fermer l'application
                        return;

                    // Redémarrer l'application pour prendre en compte les nouveaux paramètres
                    Application.Restart();
                    return;
                }
            }

            // Si la connexion est réussie, démarrer l'application avec le formulaire de connexion
            using (var loginForm = serviceProvider.GetRequiredService<LoginForm>())
            {
                var result = loginForm.ShowDialog();
                if (result == DialogResult.OK)
                    // Si la connexion est réussie, afficher le formulaire principal
                    using (var mainForm = serviceProvider.GetRequiredService<MainForm>())
                    {
                        Application.Run(mainForm);
                    }
            }
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "L'application a rencontré une erreur fatale et s'est arrêtée");
            MessageBox.Show($"Une erreur fatale s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
        finally
        {
            Log.Information("Arrêt de l'application RecouvreX");
            Log.CloseAndFlush();
        }
    }
}