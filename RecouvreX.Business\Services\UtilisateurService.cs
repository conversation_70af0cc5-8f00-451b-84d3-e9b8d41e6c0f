using RecouvreX.Business.Interfaces;
using RecouvreX.Common.Security;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des utilisateurs
    /// </summary>
    public class UtilisateurService : IUtilisateurService
    {
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="utilisateurRepository">Repository des utilisateurs</param>
        /// <param name="roleRepository">Repository des rôles</param>
        /// <param name="journalAuditRepository">Repository du journal d'audit</param>
        public UtilisateurService(
            IUtilisateurRepository utilisateurRepository,
            IRoleRepository roleRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _roleRepository = roleRepository ?? throw new ArgumentNullException(nameof(roleRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère tous les utilisateurs
        /// </summary>
        /// <returns>Liste des utilisateurs</returns>
        public async Task<IEnumerable<Utilisateur>> GetAllAsync()
        {
            return await _utilisateurRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère un utilisateur par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'utilisateur</param>
        /// <returns>Utilisateur trouvé ou null</returns>
        public async Task<Utilisateur> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _utilisateurRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère un utilisateur par son nom d'utilisateur
        /// </summary>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <returns>Utilisateur trouvé ou null</returns>
        public async Task<Utilisateur> GetByNomUtilisateurAsync(string nomUtilisateur)
        {
            if (string.IsNullOrEmpty(nomUtilisateur))
                return null;

            return await _utilisateurRepository.GetByNomUtilisateurAsync(nomUtilisateur);
        }

        /// <summary>
        /// Crée un nouvel utilisateur
        /// </summary>
        /// <param name="utilisateur">Utilisateur à créer</param>
        /// <param name="motDePasse">Mot de passe en clair</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Utilisateur créé avec son identifiant généré</returns>
        public async Task<Utilisateur> CreateAsync(Utilisateur utilisateur, string motDePasse, int creePar)
        {
            if (utilisateur == null || string.IsNullOrEmpty(motDePasse) || creePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la création d'un utilisateur");

            // Vérifier si le nom d'utilisateur existe déjà
            var existingUser = await _utilisateurRepository.GetByNomUtilisateurAsync(utilisateur.NomUtilisateur);
            if (existingUser != null)
                throw new InvalidOperationException($"Le nom d'utilisateur '{utilisateur.NomUtilisateur}' existe déjà");

            // Vérifier si le rôle existe
            var role = await _roleRepository.GetByIdAsync(utilisateur.RoleId);
            if (role == null)
                throw new InvalidOperationException($"Le rôle avec l'ID {utilisateur.RoleId} n'existe pas");

            // Hacher le mot de passe
            utilisateur.MotDePasse = PasswordHasher.HashPassword(motDePasse);

            // Créer l'utilisateur
            var createdUser = await _utilisateurRepository.AddAsync(utilisateur, creePar);

            // Journaliser la création
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = creePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(creePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Creation,
                TypeEntite = "Utilisateur",
                EntiteId = createdUser.Id,
                Description = $"Création de l'utilisateur {createdUser.NomUtilisateur}",
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdUser.Id,
                    createdUser.NomUtilisateur,
                    createdUser.NomComplet,
                    createdUser.Email,
                    createdUser.Telephone,
                    createdUser.RoleId,
                    RoleNom = role.Nom
                })
            });

            return createdUser;
        }

        /// <summary>
        /// Met à jour un utilisateur existant
        /// </summary>
        /// <param name="utilisateur">Utilisateur à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Utilisateur mis à jour</returns>
        public async Task<Utilisateur> UpdateAsync(Utilisateur utilisateur, int modifiePar)
        {
            if (utilisateur == null || utilisateur.Id <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la mise à jour d'un utilisateur");

            // Récupérer l'utilisateur existant
            var existingUser = await _utilisateurRepository.GetByIdAsync(utilisateur.Id);
            if (existingUser == null)
                throw new InvalidOperationException($"L'utilisateur avec l'ID {utilisateur.Id} n'existe pas");

            // Vérifier si le nom d'utilisateur existe déjà pour un autre utilisateur
            if (utilisateur.NomUtilisateur != existingUser.NomUtilisateur)
            {
                var userWithSameName = await _utilisateurRepository.GetByNomUtilisateurAsync(utilisateur.NomUtilisateur);
                if (userWithSameName != null && userWithSameName.Id != utilisateur.Id)
                    throw new InvalidOperationException($"Le nom d'utilisateur '{utilisateur.NomUtilisateur}' existe déjà");
            }

            // Vérifier si le rôle existe
            var role = await _roleRepository.GetByIdAsync(utilisateur.RoleId);
            if (role == null)
                throw new InvalidOperationException($"Le rôle avec l'ID {utilisateur.RoleId} n'existe pas");

            // Conserver le mot de passe existant
            utilisateur.MotDePasse = existingUser.MotDePasse;

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Utilisateur",
                EntiteId = utilisateur.Id,
                Description = $"Modification de l'utilisateur {utilisateur.NomUtilisateur}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingUser.Id,
                    existingUser.NomUtilisateur,
                    existingUser.NomComplet,
                    existingUser.Email,
                    existingUser.Telephone,
                    existingUser.RoleId
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    utilisateur.Id,
                    utilisateur.NomUtilisateur,
                    utilisateur.NomComplet,
                    utilisateur.Email,
                    utilisateur.Telephone,
                    utilisateur.RoleId,
                    RoleNom = role.Nom
                })
            });

            // Mettre à jour l'utilisateur
            return await _utilisateurRepository.UpdateAsync(utilisateur, modifiePar);
        }

        /// <summary>
        /// Supprime un utilisateur
        /// </summary>
        /// <param name="id">Identifiant de l'utilisateur à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0 || supprimePar <= 0)
                return false;

            // Récupérer l'utilisateur à supprimer
            var utilisateur = await _utilisateurRepository.GetByIdAsync(id);
            if (utilisateur == null)
                return false;

            // Journaliser la suppression
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = supprimePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(supprimePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Suppression,
                TypeEntite = "Utilisateur",
                EntiteId = id,
                Description = $"Suppression de l'utilisateur {utilisateur.NomUtilisateur}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    utilisateur.Id,
                    utilisateur.NomUtilisateur,
                    utilisateur.NomComplet,
                    utilisateur.Email,
                    utilisateur.Telephone,
                    utilisateur.RoleId
                })
            });

            // Supprimer l'utilisateur
            return await _utilisateurRepository.DeleteAsync(id, supprimePar);
        }

        /// <summary>
        /// Récupère les utilisateurs par rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Liste des utilisateurs ayant le rôle spécifié</returns>
        public async Task<IEnumerable<Utilisateur>> GetByRoleIdAsync(int roleId)
        {
            if (roleId <= 0)
                return new List<Utilisateur>();

            return await _utilisateurRepository.FindAsync("RoleId = @RoleId", new { RoleId = roleId });
        }

        /// <summary>
        /// Récupère les utilisateurs par rôle (nom du rôle)
        /// </summary>
        /// <param name="roleName">Nom du rôle</param>
        /// <returns>Liste des utilisateurs ayant le rôle spécifié</returns>
        public async Task<IEnumerable<Utilisateur>> GetByRoleNameAsync(string roleName)
        {
            if (string.IsNullOrEmpty(roleName))
                return new List<Utilisateur>();

            var role = await _roleRepository.GetByNomAsync(roleName);
            if (role == null)
                return new List<Utilisateur>();

            return await GetByRoleIdAsync(role.Id);
        }

        /// <summary>
        /// Recherche des utilisateurs par nom ou email
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des utilisateurs correspondant au terme de recherche</returns>
        public async Task<IEnumerable<Utilisateur>> SearchAsync(string searchTerm)
        {
            if (string.IsNullOrEmpty(searchTerm))
                return new List<Utilisateur>();

            return await _utilisateurRepository.FindAsync(
                "NomUtilisateur LIKE @SearchTerm OR NomComplet LIKE @SearchTerm OR Email LIKE @SearchTerm",
                new { SearchTerm = $"%{searchTerm}%" });
        }

        /// <summary>
        /// Réinitialise le mot de passe d'un utilisateur
        /// </summary>
        /// <param name="id">Identifiant de l'utilisateur</param>
        /// <param name="nouveauMotDePasse">Nouveau mot de passe en clair</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la réinitialisation a réussi, sinon False</returns>
        public async Task<bool> ResetPasswordAsync(int id, string nouveauMotDePasse, int modifiePar)
        {
            if (id <= 0 || string.IsNullOrEmpty(nouveauMotDePasse) || modifiePar <= 0)
                return false;

            // Récupérer l'utilisateur
            var utilisateur = await _utilisateurRepository.GetByIdAsync(id);
            if (utilisateur == null)
                return false;

            // Hacher le nouveau mot de passe
            string hashedPassword = PasswordHasher.HashPassword(nouveauMotDePasse);

            // Journaliser la réinitialisation du mot de passe
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Utilisateur",
                EntiteId = id,
                Description = $"Réinitialisation du mot de passe de l'utilisateur {utilisateur.NomUtilisateur}"
            });

            // Mettre à jour le mot de passe
            return await _utilisateurRepository.UpdatePasswordAsync(id, hashedPassword, modifiePar);
        }

        /// <summary>
        /// Active ou désactive un utilisateur
        /// </summary>
        /// <param name="id">Identifiant de l'utilisateur</param>
        /// <param name="estActif">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la modification a réussi, sinon False</returns>
        public async Task<bool> SetActiveStatusAsync(int id, bool estActif, int modifiePar)
        {
            if (id <= 0 || modifiePar <= 0)
                return false;

            // Récupérer l'utilisateur
            var utilisateur = await _utilisateurRepository.GetByIdAsync(id);
            if (utilisateur == null)
                return false;

            // Vérifier si l'état est déjà celui demandé
            if (utilisateur.EstActif == estActif)
                return true; // Déjà dans l'état demandé, considéré comme un succès

            // Journaliser le changement d'état
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Utilisateur",
                EntiteId = id,
                Description = $"{(estActif ? "Activation" : "Désactivation")} de l'utilisateur {utilisateur.NomUtilisateur}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    utilisateur.Id,
                    utilisateur.NomUtilisateur,
                    utilisateur.EstActif
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    utilisateur.Id,
                    utilisateur.NomUtilisateur,
                    EstActif = estActif
                })
            });

            // Mettre à jour l'état d'activation
            return await _utilisateurRepository.UpdateActiveStatusAsync(id, estActif, modifiePar);
        }
    }
}
