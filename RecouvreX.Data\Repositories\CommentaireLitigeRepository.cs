using Dapper;
using Microsoft.Extensions.Configuration;
using RecouvreX.Data.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;
using System.Data.SqlClient;

namespace RecouvreX.Data.Repositories
{
    /// <summary>
    /// Repository pour les commentaires de litiges
    /// </summary>
    public class CommentaireLitigeRepository : ICommentaireLitigeRepository
    {
        private readonly string _connectionString;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="configuration">Configuration de l'application</param>
        public CommentaireLitigeRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        /// <summary>
        /// Récupère tous les commentaires
        /// </summary>
        /// <returns>Liste de tous les commentaires</returns>
        public async Task<IEnumerable<CommentaireLitige>> GetAllAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT c.*, l.*, u.*
                        FROM CommentairesLitige c
                        LEFT JOIN Litiges l ON c.LitigeId = l.Id
                        LEFT JOIN Utilisateurs u ON c.UtilisateurId = u.Id
                        ORDER BY c.DateCommentaire DESC";

                    var commentairesDict = new Dictionary<int, CommentaireLitige>();

                    var commentaires = await connection.QueryAsync<CommentaireLitige, Litige, Utilisateur, CommentaireLitige>(
                        query,
                        (commentaire, litige, utilisateur) =>
                        {
                            if (!commentairesDict.TryGetValue(commentaire.Id, out var existingCommentaire))
                            {
                                existingCommentaire = commentaire;
                                existingCommentaire.Litige = litige;
                                existingCommentaire.Utilisateur = utilisateur;
                                commentairesDict.Add(existingCommentaire.Id, existingCommentaire);
                            }

                            return existingCommentaire;
                        },
                        splitOn: "Id,Id"
                    );

                    return commentairesDict.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération de tous les commentaires");
                throw;
            }
        }

        /// <summary>
        /// Récupère un commentaire par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du commentaire</param>
        /// <returns>Commentaire trouvé ou null</returns>
        public async Task<CommentaireLitige> GetByIdAsync(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT c.*, l.*, u.*
                        FROM CommentairesLitige c
                        LEFT JOIN Litiges l ON c.LitigeId = l.Id
                        LEFT JOIN Utilisateurs u ON c.UtilisateurId = u.Id
                        WHERE c.Id = @Id";

                    var commentaires = await connection.QueryAsync<CommentaireLitige, Litige, Utilisateur, CommentaireLitige>(
                        query,
                        (commentaire, litige, utilisateur) =>
                        {
                            commentaire.Litige = litige;
                            commentaire.Utilisateur = utilisateur;
                            return commentaire;
                        },
                        new { Id = id },
                        splitOn: "Id,Id"
                    );

                    return commentaires.FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération du commentaire {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Récupère tous les commentaires pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des commentaires du litige</returns>
        public async Task<IEnumerable<CommentaireLitige>> GetByLitigeIdAsync(int litigeId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT c.*, l.*, u.*
                        FROM CommentairesLitige c
                        LEFT JOIN Litiges l ON c.LitigeId = l.Id
                        LEFT JOIN Utilisateurs u ON c.UtilisateurId = u.Id
                        WHERE c.LitigeId = @LitigeId
                        ORDER BY c.DateCommentaire DESC";

                    var commentairesDict = new Dictionary<int, CommentaireLitige>();

                    var commentaires = await connection.QueryAsync<CommentaireLitige, Litige, Utilisateur, CommentaireLitige>(
                        query,
                        (commentaire, litige, utilisateur) =>
                        {
                            if (!commentairesDict.TryGetValue(commentaire.Id, out var existingCommentaire))
                            {
                                existingCommentaire = commentaire;
                                existingCommentaire.Litige = litige;
                                existingCommentaire.Utilisateur = utilisateur;
                                commentairesDict.Add(existingCommentaire.Id, existingCommentaire);
                            }

                            return existingCommentaire;
                        },
                        new { LitigeId = litigeId },
                        splitOn: "Id,Id"
                    );

                    return commentairesDict.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des commentaires pour le litige {LitigeId}", litigeId);
                throw;
            }
        }

        /// <summary>
        /// Ajoute un commentaire
        /// </summary>
        /// <param name="commentaire">Commentaire à ajouter</param>
        /// <returns>Commentaire ajouté</returns>
        public async Task<CommentaireLitige> AddAsync(CommentaireLitige commentaire)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        INSERT INTO CommentairesLitige (LitigeId, Contenu, DateCommentaire, UtilisateurId, EstInterne)
                        VALUES (@LitigeId, @Contenu, @DateCommentaire, @UtilisateurId, @EstInterne);
                        SELECT CAST(SCOPE_IDENTITY() as int)";

                    var id = await connection.QuerySingleAsync<int>(query, new
                    {
                        commentaire.LitigeId,
                        commentaire.Contenu,
                        DateCommentaire = commentaire.DateCommentaire,
                        commentaire.UtilisateurId,
                        commentaire.EstInterne
                    });

                    commentaire.Id = id;
                    return commentaire;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ajout d'un commentaire");
                throw;
            }
        }

        /// <summary>
        /// Met à jour un commentaire
        /// </summary>
        /// <param name="commentaire">Commentaire à mettre à jour</param>
        /// <returns>True si le commentaire a été mis à jour</returns>
        public async Task<bool> UpdateAsync(CommentaireLitige commentaire)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        UPDATE CommentairesLitige
                        SET Contenu = @Contenu,
                            EstInterne = @EstInterne
                        WHERE Id = @Id";

                    var rowsAffected = await connection.ExecuteAsync(query, new
                    {
                        commentaire.Id,
                        commentaire.Contenu,
                        commentaire.EstInterne
                    });

                    return rowsAffected > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour du commentaire {Id}", commentaire.Id);
                throw;
            }
        }

        /// <summary>
        /// Supprime un commentaire
        /// </summary>
        /// <param name="id">Identifiant du commentaire</param>
        /// <returns>True si le commentaire a été supprimé</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        DELETE FROM CommentairesLitige
                        WHERE Id = @Id";

                    var rowsAffected = await connection.ExecuteAsync(query, new { Id = id });
                    return rowsAffected > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression du commentaire {Id}", id);
                throw;
            }
        }
    }
}
