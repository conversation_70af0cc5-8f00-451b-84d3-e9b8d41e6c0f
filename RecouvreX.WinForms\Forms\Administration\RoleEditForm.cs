using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.ComponentModel;

namespace RecouvreX.WinForms.Forms.Administration
{
    public partial class RoleEditForm : Form
    {
        private readonly IRoleService _roleService;
        private readonly int _currentUserId;
        private readonly Role? _role;
        private readonly bool _isEditMode;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();

        public RoleEditForm(IRoleService roleService, int currentUserId, Role? role = null)
        {
            _roleService = roleService ?? throw new ArgumentNullException(nameof(roleService));
            _currentUserId = currentUserId;
            _role = role;
            _isEditMode = role != null;

            InitializeComponent();

            // Mettre à jour le titre du formulaire après l'initialisation des composants
            this.Text = _isEditMode ? "Modifier un rôle" : "Ajouter un rôle";

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;
        }

        private void RoleEditForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Si en mode édition, remplir les champs avec les données du rôle
                if (_isEditMode && _role != null)
                {
                    FillFormWithRoleData(_role);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'édition de rôle");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FillFormWithRoleData(Role role)
        {
            var nomTextBox = this.Controls.Find("nomTextBox", true).FirstOrDefault() as TextBox;
            var descriptionTextBox = this.Controls.Find("descriptionTextBox", true).FirstOrDefault() as TextBox;

            if (nomTextBox != null) nomTextBox.Text = role.Nom;
            if (descriptionTextBox != null) descriptionTextBox.Text = role.Description;
        }

        private void NomTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "Le nom du rôle est obligatoire.");
                    e.Cancel = true;
                }
                else if (textBox.Text.Length < 3)
                {
                    _errorProvider.SetError(textBox, "Le nom du rôle doit contenir au moins 3 caractères.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant d'enregistrer.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer les valeurs du formulaire
                var nomTextBox = this.Controls.Find("nomTextBox", true).FirstOrDefault() as TextBox;
                var descriptionTextBox = this.Controls.Find("descriptionTextBox", true).FirstOrDefault() as TextBox;

                if (nomTextBox == null || descriptionTextBox == null)
                {
                    throw new InvalidOperationException("Un ou plusieurs champs sont introuvables.");
                }

                // Créer ou mettre à jour le rôle
                Role role;
                if (_isEditMode && _role != null)
                {
                    // Mode édition
                    role = _role;
                }
                else
                {
                    // Mode ajout
                    role = new Role();
                }

                // Remplir les propriétés du rôle
                role.Nom = nomTextBox.Text.Trim();
                role.Description = descriptionTextBox.Text.Trim();

                // Enregistrer le rôle
                if (_isEditMode)
                {
                    // Mettre à jour le rôle existant
                    await _roleService.UpdateAsync(role, _currentUserId);
                    MessageBox.Show("Le rôle a été mis à jour avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Créer un nouveau rôle
                    await _roleService.CreateAsync(role, _currentUserId);
                    MessageBox.Show("Le rôle a été créé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement du rôle");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement du rôle : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
