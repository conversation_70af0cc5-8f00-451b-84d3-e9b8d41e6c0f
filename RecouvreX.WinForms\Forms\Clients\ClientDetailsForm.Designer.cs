namespace RecouvreX.WinForms.Forms.Clients
{
    partial class ClientDetailsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // ClientDetailsForm
            //
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.Name = "ClientDetailsForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Détails du client";
            this.Load += new System.EventHandler(this.ClientDetailsForm_Load);
            this.ResumeLayout(false);

            // Panneau principal
            System.Windows.Forms.TableLayoutPanel mainPanel = new System.Windows.Forms.TableLayoutPanel();
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Padding = new System.Windows.Forms.Padding(10);
            mainPanel.ColumnCount = 1;
            mainPanel.RowCount = 3;
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 150F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.Controls.Add(mainPanel);

            // Panneau d'informations du client
            System.Windows.Forms.GroupBox clientInfoGroupBox = new System.Windows.Forms.GroupBox();
            clientInfoGroupBox.Text = "Informations du client";
            clientInfoGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(clientInfoGroupBox, 0, 0);

            System.Windows.Forms.TableLayoutPanel clientInfoPanel = new System.Windows.Forms.TableLayoutPanel();
            clientInfoPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            clientInfoPanel.Padding = new System.Windows.Forms.Padding(5);
            clientInfoPanel.ColumnCount = 4;
            clientInfoPanel.RowCount = 3;
            clientInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            clientInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            clientInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 15F));
            clientInfoPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            clientInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            clientInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            clientInfoPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33F));
            clientInfoGroupBox.Controls.Add(clientInfoPanel);

            // Ligne 1
            System.Windows.Forms.Label codeLabel = new System.Windows.Forms.Label();
            codeLabel.Text = "Code :";
            codeLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientInfoPanel.Controls.Add(codeLabel, 0, 0);

            System.Windows.Forms.Label codeValueLabel = new System.Windows.Forms.Label();
            codeValueLabel.Name = "codeValueLabel";
            codeValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            codeValueLabel.Font = new System.Drawing.Font(codeValueLabel.Font, System.Drawing.FontStyle.Bold);
            clientInfoPanel.Controls.Add(codeValueLabel, 1, 0);

            System.Windows.Forms.Label raisonSocialeLabel = new System.Windows.Forms.Label();
            raisonSocialeLabel.Text = "Raison sociale :";
            raisonSocialeLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientInfoPanel.Controls.Add(raisonSocialeLabel, 2, 0);

            System.Windows.Forms.Label raisonSocialeValueLabel = new System.Windows.Forms.Label();
            raisonSocialeValueLabel.Name = "raisonSocialeValueLabel";
            raisonSocialeValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            raisonSocialeValueLabel.Font = new System.Drawing.Font(raisonSocialeValueLabel.Font, System.Drawing.FontStyle.Bold);
            clientInfoPanel.Controls.Add(raisonSocialeValueLabel, 3, 0);

            // Ligne 2
            System.Windows.Forms.Label adresseLabel = new System.Windows.Forms.Label();
            adresseLabel.Text = "Adresse :";
            adresseLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientInfoPanel.Controls.Add(adresseLabel, 0, 1);

            System.Windows.Forms.Label adresseValueLabel = new System.Windows.Forms.Label();
            adresseValueLabel.Name = "adresseValueLabel";
            adresseValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientInfoPanel.Controls.Add(adresseValueLabel, 1, 1);

            System.Windows.Forms.Label villeLabel = new System.Windows.Forms.Label();
            villeLabel.Text = "Ville :";
            villeLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientInfoPanel.Controls.Add(villeLabel, 2, 1);

            System.Windows.Forms.Label villeValueLabel = new System.Windows.Forms.Label();
            villeValueLabel.Name = "villeValueLabel";
            villeValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientInfoPanel.Controls.Add(villeValueLabel, 3, 1);

            // Ligne 3
            System.Windows.Forms.Label telephoneLabel = new System.Windows.Forms.Label();
            telephoneLabel.Text = "Téléphone :";
            telephoneLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientInfoPanel.Controls.Add(telephoneLabel, 0, 2);

            System.Windows.Forms.Label telephoneValueLabel = new System.Windows.Forms.Label();
            telephoneValueLabel.Name = "telephoneValueLabel";
            telephoneValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientInfoPanel.Controls.Add(telephoneValueLabel, 1, 2);

            System.Windows.Forms.Label emailLabel = new System.Windows.Forms.Label();
            emailLabel.Text = "Email :";
            emailLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientInfoPanel.Controls.Add(emailLabel, 2, 2);

            System.Windows.Forms.Label emailValueLabel = new System.Windows.Forms.Label();
            emailValueLabel.Name = "emailValueLabel";
            emailValueLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientInfoPanel.Controls.Add(emailValueLabel, 3, 2);

            // Panneau des contacts
            System.Windows.Forms.GroupBox contactsGroupBox = new System.Windows.Forms.GroupBox();
            contactsGroupBox.Text = "Contacts";
            contactsGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(contactsGroupBox, 0, 1);

            System.Windows.Forms.TableLayoutPanel contactsPanel = new System.Windows.Forms.TableLayoutPanel();
            contactsPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            contactsPanel.Padding = new System.Windows.Forms.Padding(5);
            contactsPanel.ColumnCount = 1;
            contactsPanel.RowCount = 2;
            contactsPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            contactsPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            contactsPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            contactsGroupBox.Controls.Add(contactsPanel);

            // Barre d'outils des contacts
            System.Windows.Forms.ToolStrip contactsToolStrip = new System.Windows.Forms.ToolStrip();
            contactsToolStrip.Name = "contactsToolStrip";
            contactsToolStrip.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
            contactsPanel.Controls.Add(contactsToolStrip, 0, 0);

            System.Windows.Forms.ToolStripButton addContactButton = new System.Windows.Forms.ToolStripButton();
            addContactButton.Name = "addContactButton";
            addContactButton.Text = "Ajouter";
            addContactButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            addContactButton.Click += new System.EventHandler(this.AddContactButton_Click);
            contactsToolStrip.Items.Add(addContactButton);

            System.Windows.Forms.ToolStripButton editContactButton = new System.Windows.Forms.ToolStripButton();
            editContactButton.Name = "editContactButton";
            editContactButton.Text = "Modifier";
            editContactButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            editContactButton.Click += new System.EventHandler(this.EditContactButton_Click);
            contactsToolStrip.Items.Add(editContactButton);

            System.Windows.Forms.ToolStripButton deleteContactButton = new System.Windows.Forms.ToolStripButton();
            deleteContactButton.Name = "deleteContactButton";
            deleteContactButton.Text = "Supprimer";
            deleteContactButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            deleteContactButton.Click += new System.EventHandler(this.DeleteContactButton_Click);
            contactsToolStrip.Items.Add(deleteContactButton);

            // DataGridView des contacts
            System.Windows.Forms.DataGridView contactsDataGridView = new System.Windows.Forms.DataGridView();
            contactsDataGridView.Name = "contactsDataGridView";
            contactsDataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            contactsDataGridView.AllowUserToAddRows = false;
            contactsDataGridView.AllowUserToDeleteRows = false;
            contactsDataGridView.ReadOnly = true;
            contactsDataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            contactsDataGridView.MultiSelect = false;
            contactsDataGridView.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            contactsDataGridView.RowHeadersVisible = false;
            contactsDataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.ContactsDataGridView_CellDoubleClick);
            contactsPanel.Controls.Add(contactsDataGridView, 0, 1);

            // Panneau des factures
            System.Windows.Forms.GroupBox facturesGroupBox = new System.Windows.Forms.GroupBox();
            facturesGroupBox.Text = "Factures";
            facturesGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Controls.Add(facturesGroupBox, 0, 2);

            System.Windows.Forms.TableLayoutPanel facturesPanel = new System.Windows.Forms.TableLayoutPanel();
            facturesPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            facturesPanel.Padding = new System.Windows.Forms.Padding(5);
            facturesPanel.ColumnCount = 1;
            facturesPanel.RowCount = 2;
            facturesPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            facturesPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            facturesPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            facturesGroupBox.Controls.Add(facturesPanel);

            // Barre d'outils des factures
            System.Windows.Forms.ToolStrip facturesToolStrip = new System.Windows.Forms.ToolStrip();
            facturesToolStrip.Name = "facturesToolStrip";
            facturesToolStrip.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
            facturesPanel.Controls.Add(facturesToolStrip, 0, 0);

            System.Windows.Forms.ToolStripButton addFactureButton = new System.Windows.Forms.ToolStripButton();
            addFactureButton.Name = "addFactureButton";
            addFactureButton.Text = "Ajouter";
            addFactureButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            addFactureButton.Click += new System.EventHandler(this.AddFactureButton_Click);
            facturesToolStrip.Items.Add(addFactureButton);

            System.Windows.Forms.ToolStripButton viewFactureButton = new System.Windows.Forms.ToolStripButton();
            viewFactureButton.Name = "viewFactureButton";
            viewFactureButton.Text = "Voir";
            viewFactureButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            viewFactureButton.Click += new System.EventHandler(this.ViewFactureButton_Click);
            facturesToolStrip.Items.Add(viewFactureButton);

            // DataGridView des factures
            System.Windows.Forms.DataGridView facturesDataGridView = new System.Windows.Forms.DataGridView();
            facturesDataGridView.Name = "facturesDataGridView";
            facturesDataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            facturesDataGridView.AllowUserToAddRows = false;
            facturesDataGridView.AllowUserToDeleteRows = false;
            facturesDataGridView.ReadOnly = true;
            facturesDataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            facturesDataGridView.MultiSelect = false;
            facturesDataGridView.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            facturesDataGridView.RowHeadersVisible = false;
            facturesDataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.FacturesDataGridView_CellDoubleClick);
            facturesPanel.Controls.Add(facturesDataGridView, 0, 1);
        }

        #endregion
    }
}
