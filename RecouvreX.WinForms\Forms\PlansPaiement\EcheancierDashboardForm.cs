using Microsoft.Extensions.DependencyInjection;
using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.PlansPaiement
{
    public partial class EcheancierDashboardForm : Form
    {
        private readonly IPlanPaiementService _planPaiementService;
        private readonly IClientService _clientService;
        private readonly IAuthenticationService _authenticationService;
        private readonly IServiceProvider _serviceProvider;
        private readonly int _currentUserId;
        private List<EcheancePaiement> _echeancesAVenir = new List<EcheancePaiement>();
        private List<EcheancePaiement> _echeancesEnRetard = new List<EcheancePaiement>();
        private Dictionary<int, string> _clientNames = new Dictionary<int, string>();
        private DataTable _echeancesAVenirDataTable = new DataTable();
        private DataTable _echeancesEnRetardDataTable = new DataTable();

        public EcheancierDashboardForm(
            IPlanPaiementService planPaiementService,
            IClientService clientService,
            IAuthenticationService authenticationService,
            IServiceProvider serviceProvider)
        {
            _planPaiementService = planPaiementService ?? throw new ArgumentNullException(nameof(planPaiementService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _currentUserId = 1; // Valeur par défaut, à remplacer par la méthode appropriée

            InitializeComponent();
            InitializeDataTables();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form
            this.Text = "Tableau de bord des échéances";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimizeBox = true;
            this.MaximizeBox = true;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            // Icône de l'application
            // this.Icon = Properties.Resources.AppIcon;

            // Main panel
            TableLayoutPanel mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 1,
                RowCount = 5,
                RowStyles = {
                    new RowStyle(SizeType.Absolute, 40),
                    new RowStyle(SizeType.Absolute, 50),
                    new RowStyle(SizeType.Percent, 50),
                    new RowStyle(SizeType.Absolute, 40),
                    new RowStyle(SizeType.Percent, 50)
                }
            };
            this.Controls.Add(mainPanel);

            // Title label
            Label titleLabel = new Label
            {
                Text = "Tableau de bord des échéances",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft
            };
            mainPanel.Controls.Add(titleLabel, 0, 0);

            // Filters panel
            TableLayoutPanel filtersPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 5,
                RowCount = 1,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 25),
                    new ColumnStyle(SizeType.Percent, 25),
                    new ColumnStyle(SizeType.Percent, 25),
                    new ColumnStyle(SizeType.Percent, 25),
                    new ColumnStyle(SizeType.Absolute, 120)
                }
            };
            mainPanel.Controls.Add(filtersPanel, 0, 1);

            // Client filter
            ComboBox clientFilterComboBox = new ComboBox
            {
                Name = "clientFilterComboBox",
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Margin = new Padding(3, 3, 10, 3)
            };
            clientFilterComboBox.SelectedIndexChanged += ClientFilterComboBox_SelectedIndexChanged;
            filtersPanel.Controls.Add(clientFilterComboBox, 0, 0);

            // Date range filter
            DateTimePicker dateDebutPicker = new DateTimePicker
            {
                Name = "dateDebutPicker",
                Dock = DockStyle.Fill,
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today,
                Margin = new Padding(3, 3, 10, 3)
            };
            filtersPanel.Controls.Add(dateDebutPicker, 1, 0);

            DateTimePicker dateFinPicker = new DateTimePicker
            {
                Name = "dateFinPicker",
                Dock = DockStyle.Fill,
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today.AddMonths(1),
                Margin = new Padding(3, 3, 10, 3)
            };
            filtersPanel.Controls.Add(dateFinPicker, 2, 0);

            Button applyDateFilterButton = new Button
            {
                Name = "applyDateFilterButton",
                Text = "Appliquer",
                Dock = DockStyle.Fill,
                Margin = new Padding(3, 3, 10, 3)
            };
            applyDateFilterButton.Click += ApplyDateFilterButton_Click;
            filtersPanel.Controls.Add(applyDateFilterButton, 3, 0);

            // Refresh button
            Button refreshButton = new Button
            {
                Name = "refreshButton",
                Text = "Actualiser",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            refreshButton.Click += RefreshButton_Click;
            filtersPanel.Controls.Add(refreshButton, 4, 0);

            // Échéances à venir label
            Label echeancesAVenirLabel = new Label
            {
                Text = "Échéances à venir",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft
            };
            mainPanel.Controls.Add(echeancesAVenirLabel, 0, 2);

            // Échéances à venir DataGridView
            DataGridView echeancesAVenirDataGridView = new DataGridView
            {
                Name = "echeancesAVenirDataGridView",
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                BackgroundColor = SystemColors.Window,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };
            echeancesAVenirDataGridView.CellDoubleClick += EcheancesAVenirDataGridView_CellDoubleClick;
            echeancesAVenirDataGridView.DataSource = _echeancesAVenirDataTable;
            mainPanel.Controls.Add(echeancesAVenirDataGridView, 0, 3);

            // Échéances en retard label
            Label echeancesEnRetardLabel = new Label
            {
                Text = "Échéances en retard",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.Red
            };
            mainPanel.Controls.Add(echeancesEnRetardLabel, 0, 4);

            // Échéances en retard DataGridView
            DataGridView echeancesEnRetardDataGridView = new DataGridView
            {
                Name = "echeancesEnRetardDataGridView",
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                BackgroundColor = SystemColors.Window,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };
            echeancesEnRetardDataGridView.CellDoubleClick += EcheancesEnRetardDataGridView_CellDoubleClick;
            echeancesEnRetardDataGridView.DataSource = _echeancesEnRetardDataTable;
            mainPanel.Controls.Add(echeancesEnRetardDataGridView, 0, 5);

            this.ResumeLayout(false);
        }

        private void InitializeDataTables()
        {
            // Table des échéances à venir
            _echeancesAVenirDataTable = new DataTable();
            _echeancesAVenirDataTable.Columns.Add("Id", typeof(int));
            _echeancesAVenirDataTable.Columns.Add("PlanId", typeof(int));
            _echeancesAVenirDataTable.Columns.Add("Client", typeof(string));
            _echeancesAVenirDataTable.Columns.Add("Référence plan", typeof(string));
            _echeancesAVenirDataTable.Columns.Add("N° échéance", typeof(int));
            _echeancesAVenirDataTable.Columns.Add("Date échéance", typeof(DateTime));
            _echeancesAVenirDataTable.Columns.Add("Montant", typeof(decimal));
            _echeancesAVenirDataTable.Columns.Add("Jours restants", typeof(int));

            // Table des échéances en retard
            _echeancesEnRetardDataTable = new DataTable();
            _echeancesEnRetardDataTable.Columns.Add("Id", typeof(int));
            _echeancesEnRetardDataTable.Columns.Add("PlanId", typeof(int));
            _echeancesEnRetardDataTable.Columns.Add("Client", typeof(string));
            _echeancesEnRetardDataTable.Columns.Add("Référence plan", typeof(string));
            _echeancesEnRetardDataTable.Columns.Add("N° échéance", typeof(int));
            _echeancesEnRetardDataTable.Columns.Add("Date échéance", typeof(DateTime));
            _echeancesEnRetardDataTable.Columns.Add("Montant", typeof(decimal));
            _echeancesEnRetardDataTable.Columns.Add("Jours retard", typeof(int));
        }

        private async void LoadData()
        {
            try
            {
                // Charger les noms des clients
                var clients = await _clientService.GetAllAsync();
                _clientNames = clients.ToDictionary(c => c.Id, c => c.RaisonSociale);

                // Remplir le filtre client
                var clientFilterComboBox = Controls.Find("clientFilterComboBox", true).FirstOrDefault() as ComboBox;
                if (clientFilterComboBox != null)
                {
                    clientFilterComboBox.Items.Clear();
                    clientFilterComboBox.Items.Add("Tous les clients");
                    foreach (var client in clients.OrderBy(c => c.RaisonSociale))
                    {
                        clientFilterComboBox.Items.Add(client.RaisonSociale);
                    }
                    clientFilterComboBox.SelectedIndex = 0;
                }

                // Charger les échéances à venir
                var dateDebutPicker = Controls.Find("dateDebutPicker", true).FirstOrDefault() as DateTimePicker;
                var dateFinPicker = Controls.Find("dateFinPicker", true).FirstOrDefault() as DateTimePicker;
                if (dateDebutPicker != null && dateFinPicker != null)
                {
                    DateTime dateDebut = dateDebutPicker.Value.Date;
                    DateTime dateFin = dateFinPicker.Value.Date.AddDays(1).AddSeconds(-1);
                    _echeancesAVenir = (await _planPaiementService.GetUpcomingEcheancesAsync(dateDebut, dateFin)).ToList();
                    UpdateEcheancesAVenirDataTable();
                }

                // Charger les échéances en retard
                _echeancesEnRetard = (await _planPaiementService.GetLateEcheancesAsync()).ToList();
                UpdateEcheancesEnRetardDataTable();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données");
                MessageBox.Show($"Erreur lors du chargement des données : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateEcheancesAVenirDataTable()
        {
            _echeancesAVenirDataTable.Clear();

            foreach (var echeance in _echeancesAVenir.OrderBy(e => e.DateEcheance))
            {
                string clientName = "Client inconnu";
                string referencePlan = "Référence inconnue";

                if (echeance.PlanPaiement != null)
                {
                    referencePlan = echeance.PlanPaiement.Reference;
                    if (_clientNames.ContainsKey(echeance.PlanPaiement.ClientId))
                    {
                        clientName = _clientNames[echeance.PlanPaiement.ClientId];
                    }
                }

                int joursRestants = (int)(echeance.DateEcheance - DateTime.Today).TotalDays;

                _echeancesAVenirDataTable.Rows.Add(
                    echeance.Id,
                    echeance.PlanPaiementId,
                    clientName,
                    referencePlan,
                    echeance.NumeroOrdre,
                    echeance.DateEcheance,
                    echeance.MontantPrevu,
                    joursRestants
                );
            }
        }

        private void UpdateEcheancesEnRetardDataTable()
        {
            _echeancesEnRetardDataTable.Clear();

            foreach (var echeance in _echeancesEnRetard.OrderBy(e => e.DateEcheance))
            {
                string clientName = "Client inconnu";
                string referencePlan = "Référence inconnue";

                if (echeance.PlanPaiement != null)
                {
                    referencePlan = echeance.PlanPaiement.Reference;
                    if (_clientNames.ContainsKey(echeance.PlanPaiement.ClientId))
                    {
                        clientName = _clientNames[echeance.PlanPaiement.ClientId];
                    }
                }

                _echeancesEnRetardDataTable.Rows.Add(
                    echeance.Id,
                    echeance.PlanPaiementId,
                    clientName,
                    referencePlan,
                    echeance.NumeroOrdre,
                    echeance.DateEcheance,
                    echeance.MontantPrevu,
                    echeance.JoursRetard
                );
            }
        }

        private void ClientFilterComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyDateFilterButton_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void RefreshButton_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void EcheancesAVenirDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                try
                {
                    var dataGridView = sender as DataGridView;
                    int planId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["PlanId"].Value);

                    using (var form = new PlanPaiementDetailsForm(
                        _planPaiementService,
                        _clientService,
                        _serviceProvider.GetRequiredService<IUtilisateurService>(),
                        _authenticationService,
                        _serviceProvider,
                        planId))
                    {
                        form.ShowDialog();
                        LoadData();
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Erreur lors de l'ouverture du formulaire de détails du plan de paiement");
                    MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void EcheancesEnRetardDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                try
                {
                    var dataGridView = sender as DataGridView;
                    int planId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["PlanId"].Value);

                    using (var form = new PlanPaiementDetailsForm(
                        _planPaiementService,
                        _clientService,
                        _serviceProvider.GetRequiredService<IUtilisateurService>(),
                        _authenticationService,
                        _serviceProvider,
                        planId))
                    {
                        form.ShowDialog();
                        LoadData();
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Erreur lors de l'ouverture du formulaire de détails du plan de paiement");
                    MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void ApplyFilters()
        {
            try
            {
                var clientFilterComboBox = Controls.Find("clientFilterComboBox", true).FirstOrDefault() as ComboBox;
                if (clientFilterComboBox == null)
                    return;

                // Filtrer par client
                if (clientFilterComboBox.SelectedIndex > 0)
                {
                    string selectedClientName = clientFilterComboBox.SelectedItem.ToString();
                    int clientId = _clientNames.FirstOrDefault(c => c.Value == selectedClientName).Key;

                    // Filtrer les échéances à venir
                    var filteredEcheancesAVenir = _echeancesAVenir.Where(e => e.PlanPaiement?.ClientId == clientId).ToList();
                    _echeancesAVenirDataTable.Clear();
                    foreach (var echeance in filteredEcheancesAVenir.OrderBy(e => e.DateEcheance))
                    {
                        int joursRestants = (int)(echeance.DateEcheance - DateTime.Today).TotalDays;
                        _echeancesAVenirDataTable.Rows.Add(
                            echeance.Id,
                            echeance.PlanPaiementId,
                            selectedClientName,
                            echeance.PlanPaiement?.Reference ?? "Référence inconnue",
                            echeance.NumeroOrdre,
                            echeance.DateEcheance,
                            echeance.MontantPrevu,
                            joursRestants
                        );
                    }

                    // Filtrer les échéances en retard
                    var filteredEcheancesEnRetard = _echeancesEnRetard.Where(e => e.PlanPaiement?.ClientId == clientId).ToList();
                    _echeancesEnRetardDataTable.Clear();
                    foreach (var echeance in filteredEcheancesEnRetard.OrderBy(e => e.DateEcheance))
                    {
                        _echeancesEnRetardDataTable.Rows.Add(
                            echeance.Id,
                            echeance.PlanPaiementId,
                            selectedClientName,
                            echeance.PlanPaiement?.Reference ?? "Référence inconnue",
                            echeance.NumeroOrdre,
                            echeance.DateEcheance,
                            echeance.MontantPrevu,
                            echeance.JoursRetard
                        );
                    }
                }
                else
                {
                    // Afficher toutes les échéances
                    UpdateEcheancesAVenirDataTable();
                    UpdateEcheancesEnRetardDataTable();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'application des filtres");
                MessageBox.Show($"Erreur lors de l'application des filtres : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
