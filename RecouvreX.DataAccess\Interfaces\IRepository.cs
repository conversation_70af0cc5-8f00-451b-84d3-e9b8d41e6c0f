using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface générique pour les repositories
    /// </summary>
    /// <typeparam name="T">Type d'entité</typeparam>
    public interface IRepository<T> where T : BaseEntity
    {
        /// <summary>
        /// Récupère toutes les entités
        /// </summary>
        /// <returns>Liste des entités</returns>
        Task<IEnumerable<T>> GetAllAsync();

        /// <summary>
        /// Récupère une entité par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'entité</param>
        /// <returns>Entité trouvée ou null</returns>
        Task<T> GetByIdAsync(int id);

        /// <summary>
        /// Ajoute une nouvelle entité
        /// </summary>
        /// <param name="entity">Entité à ajouter</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Entité ajoutée avec son identifiant généré</returns>
        Task<T> AddAsync(T entity, int userId);

        /// <summary>
        /// Met à jour une entité existante
        /// </summary>
        /// <param name="entity">Entité à mettre à jour</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Entité mise à jour</returns>
        Task<T> UpdateAsync(T entity, int userId);

        /// <summary>
        /// Supprime logiquement une entité (désactivation)
        /// </summary>
        /// <param name="id">Identifiant de l'entité à supprimer</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int userId);

        /// <summary>
        /// Récupère les entités selon un critère de recherche
        /// </summary>
        /// <param name="whereClause">Clause WHERE SQL</param>
        /// <param name="parameters">Paramètres de la clause WHERE</param>
        /// <returns>Liste des entités correspondant au critère</returns>
        Task<IEnumerable<T>> FindAsync(string whereClause, object parameters);

        /// <summary>
        /// Récupère la connexion à la base de données
        /// </summary>
        /// <returns>Connexion à la base de données</returns>
        DatabaseConnection GetDatabaseConnection();
    }
}
