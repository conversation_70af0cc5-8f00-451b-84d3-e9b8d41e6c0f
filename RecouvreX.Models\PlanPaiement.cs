using System;
using System.Collections.Generic;
using System.Linq;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un plan de paiement (échéancier) pour un client
    /// </summary>
    public class PlanPaiement : BaseEntity
    {
        /// <summary>
        /// Numéro de référence unique du plan de paiement
        /// </summary>
        public string Reference { get; set; }

        /// <summary>
        /// Identifiant du client concerné par le plan de paiement
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// Client associé (navigation property)
        /// </summary>
        public Client Client { get; set; }

        /// <summary>
        /// Date de création du plan de paiement
        /// </summary>
        public DateTime DateCreationPlan { get; set; }

        /// <summary>
        /// Date de début du plan de paiement
        /// </summary>
        public DateTime DateDebut { get; set; }

        /// <summary>
        /// Date de fin prévue du plan de paiement
        /// </summary>
        public DateTime DateFinPrevue { get; set; }

        /// <summary>
        /// Montant total du plan de paiement
        /// </summary>
        public decimal MontantTotal { get; set; }

        /// <summary>
        /// Montant des intérêts inclus dans le plan
        /// </summary>
        public decimal MontantInterets { get; set; }

        /// <summary>
        /// Montant des frais inclus dans le plan
        /// </summary>
        public decimal MontantFrais { get; set; }

        /// <summary>
        /// Statut du plan de paiement (En cours, Terminé, Annulé, etc.)
        /// </summary>
        public string Statut { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur responsable du plan
        /// </summary>
        public int ResponsableId { get; set; }

        /// <summary>
        /// Utilisateur responsable (navigation property)
        /// </summary>
        public Utilisateur Responsable { get; set; }

        /// <summary>
        /// Notes ou commentaires sur le plan de paiement
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Indique si un accord a été signé pour ce plan
        /// </summary>
        public bool AccordSigne { get; set; }

        /// <summary>
        /// Date de signature de l'accord
        /// </summary>
        public DateTime? DateSignatureAccord { get; set; }

        /// <summary>
        /// Chemin du fichier PDF de l'accord
        /// </summary>
        public string CheminAccord { get; set; }

        /// <summary>
        /// Liste des échéances du plan de paiement (navigation property)
        /// </summary>
        public ICollection<EcheancePaiement> Echeances { get; set; }

        /// <summary>
        /// Liste des factures associées au plan de paiement (navigation property)
        /// </summary>
        public ICollection<Facture> Factures { get; set; }

        /// <summary>
        /// Liste des documents associés au plan de paiement (navigation property)
        /// </summary>
        public ICollection<Document> Documents { get; set; }

        /// <summary>
        /// Montant déjà payé du plan (calculé à partir des échéances)
        /// </summary>
        public decimal MontantPaye
        {
            get
            {
                if (Echeances == null || !Echeances.Any())
                    return 0;

                return Echeances.Where(e => e.EstPayee).Sum(e => e.MontantPrevu);
            }
        }

        /// <summary>
        /// Montant restant à payer (calculé à partir du montant total et du montant payé)
        /// </summary>
        public decimal MontantRestant
        {
            get
            {
                return MontantTotal - MontantPaye;
            }
        }

        /// <summary>
        /// Pourcentage de progression du plan (calculé à partir du montant payé et du montant total)
        /// </summary>
        public decimal PourcentageProgression
        {
            get
            {
                if (MontantTotal == 0)
                    return 0;

                return Math.Round((MontantPaye / MontantTotal) * 100, 2);
            }
        }

        /// <summary>
        /// Nombre d'échéances du plan
        /// </summary>
        public int NombreEcheances
        {
            get
            {
                return Echeances?.Count ?? 0;
            }
        }

        /// <summary>
        /// Nombre d'échéances payées
        /// </summary>
        public int NombreEcheancesPaye
        {
            get
            {
                if (Echeances == null)
                    return 0;

                return Echeances.Count(e => e.EstPayee);
            }
        }

        /// <summary>
        /// Nombre d'échéances en retard
        /// </summary>
        public int NombreEcheancesRetard
        {
            get
            {
                if (Echeances == null)
                    return 0;

                return Echeances.Count(e => !e.EstPayee && e.DateEcheance < DateTime.Now);
            }
        }

        /// <summary>
        /// Prochaine échéance à payer
        /// </summary>
        public EcheancePaiement ProchaineEcheance
        {
            get
            {
                if (Echeances == null || !Echeances.Any())
                    return null;

                return Echeances
                    .Where(e => !e.EstPayee)
                    .OrderBy(e => e.DateEcheance)
                    .FirstOrDefault();
            }
        }
    }
}
