using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using Serilog;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Communications
{
    /// <summary>
    /// Formulaire pour l'ajout d'une note
    /// </summary>
    public partial class NoteForm : Form
    {
        private readonly ICommunicationService _communicationService;
        private readonly int _currentUserId;
        private readonly int _factureId;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="communicationService">Service de gestion des communications</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        /// <param name="factureId">Identifiant de la facture</param>
        public NoteForm(ICommunicationService communicationService, int currentUserId, int factureId)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _currentUserId = currentUserId;
            _factureId = factureId;

            InitializeComponent();

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;
        }

        // Les méthodes InitializeComponent() et CreateControls() ont été déplacées dans le fichier NoteForm.Designer.cs

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private void NoteForm_Load(object sender, EventArgs e)
        {
            // Rien à faire pour l'instant
        }

        /// <summary>
        /// Validation du contenu
        /// </summary>
        private void ContenuTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "Le contenu est obligatoire.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        /// <summary>
        /// Gestion du changement d'état de la case à cocher "Suivi nécessaire"
        /// </summary>
        private void SuiviNecessaireCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            // TODO: Ajouter un sélecteur de date pour le suivi si la case est cochée
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Enregistrer"
        /// </summary>
        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant de continuer.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer les valeurs du formulaire
                var contenuTextBox = this.Controls.Find("contenuTextBox", true).FirstOrDefault() as TextBox;
                var suiviNecessaireCheckBox = this.Controls.Find("suiviNecessaireCheckBox", true).FirstOrDefault() as CheckBox;

                // Enregistrer la note
                var communication = await _communicationService.AjouterNoteAsync(
                    _factureId,
                    contenuTextBox?.Text,
                    suiviNecessaireCheckBox?.Checked ?? false,
                    null, // Date de suivi
                    _currentUserId);

                // Fermer le formulaire
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement de la note");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement de la note : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Annuler"
        /// </summary>
        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
