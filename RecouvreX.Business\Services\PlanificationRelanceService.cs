using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des planifications de relance
    /// </summary>
    public class PlanificationRelanceService : IPlanificationRelanceService
    {
        private readonly IPlanificationRelanceRepository _planificationRelanceRepository;
        private readonly IFactureRepository _factureRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IModeleRelanceRepository _modeleRelanceRepository;
        private readonly IModeleRelanceService _modeleRelanceService;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// Constructeur
        /// </summary>
        public PlanificationRelanceService(
            IPlanificationRelanceRepository planificationRelanceRepository,
            IFactureRepository factureRepository,
            IClientRepository clientRepository,
            IModeleRelanceRepository modeleRelanceRepository,
            IModeleRelanceService modeleRelanceService,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository,
            IConfiguration configuration)
        {
            _planificationRelanceRepository = planificationRelanceRepository ?? throw new ArgumentNullException(nameof(planificationRelanceRepository));
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _modeleRelanceRepository = modeleRelanceRepository ?? throw new ArgumentNullException(nameof(modeleRelanceRepository));
            _modeleRelanceService = modeleRelanceService ?? throw new ArgumentNullException(nameof(modeleRelanceService));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        /// <summary>
        /// Récupère toutes les planifications de relance
        /// </summary>
        /// <returns>Liste des planifications de relance</returns>
        public async Task<IEnumerable<PlanificationRelance>> GetAllAsync()
        {
            return await _planificationRelanceRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère une planification de relance par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance</param>
        /// <returns>Planification de relance trouvée ou null</returns>
        public async Task<PlanificationRelance> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _planificationRelanceRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère les planifications de relance par facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des planifications de relance pour la facture spécifiée</returns>
        public async Task<IEnumerable<PlanificationRelance>> GetByFactureIdAsync(int factureId)
        {
            if (factureId <= 0)
                return Enumerable.Empty<PlanificationRelance>();

            return await _planificationRelanceRepository.GetByFactureIdAsync(factureId);
        }

        /// <summary>
        /// Récupère les planifications de relance par statut
        /// </summary>
        /// <param name="statut">Statut des planifications</param>
        /// <returns>Liste des planifications de relance avec le statut spécifié</returns>
        public async Task<IEnumerable<PlanificationRelance>> GetByStatutAsync(string statut)
        {
            if (string.IsNullOrEmpty(statut))
                return Enumerable.Empty<PlanificationRelance>();

            return await _planificationRelanceRepository.GetByStatutAsync(statut);
        }

        /// <summary>
        /// Récupère les planifications de relance prévues pour une date
        /// </summary>
        /// <param name="date">Date prévue</param>
        /// <returns>Liste des planifications de relance prévues pour la date spécifiée</returns>
        public async Task<IEnumerable<PlanificationRelance>> GetPlannedForDateAsync(DateTime date)
        {
            return await _planificationRelanceRepository.GetPlannedForDateAsync(date);
        }

        /// <summary>
        /// Récupère les planifications de relance en attente de validation
        /// </summary>
        /// <returns>Liste des planifications de relance en attente de validation</returns>
        public async Task<IEnumerable<PlanificationRelance>> GetPendingValidationAsync()
        {
            return await _planificationRelanceRepository.GetByStatutAsync(StatutPlanificationRelance.EnAttenteValidation);
        }

        /// <summary>
        /// Crée une nouvelle planification de relance
        /// </summary>
        /// <param name="planificationRelance">Planification de relance à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Planification de relance créée avec son identifiant généré</returns>
        public async Task<PlanificationRelance> CreateAsync(PlanificationRelance planificationRelance, int creePar)
        {
            if (planificationRelance == null)
                throw new ArgumentNullException(nameof(planificationRelance));

            if (creePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que la facture existe
            var facture = await _factureRepository.GetByIdAsync(planificationRelance.FactureId);
            if (facture == null)
                throw new InvalidOperationException($"La facture avec l'ID {planificationRelance.FactureId} n'existe pas");

            // Vérifier que le modèle de relance existe
            if (planificationRelance.ModeleRelanceId.HasValue)
            {
                var modele = await _modeleRelanceRepository.GetByIdAsync(planificationRelance.ModeleRelanceId.Value);
                if (modele == null)
                    throw new InvalidOperationException($"Le modèle de relance avec l'ID {planificationRelance.ModeleRelanceId.Value} n'existe pas");
            }

            // Créer la planification de relance
            var createdPlanification = await _planificationRelanceRepository.AddAsync(planificationRelance, creePar);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "PlanificationRelance",
                EntiteId = createdPlanification.Id,
                TypeAction = "Création",
                UtilisateurId = creePar,
                DateAction = DateTime.Now,
                Description = $"Création d'une planification de relance pour la facture {facture.Numero:s} (niveau {createdPlanification.NiveauRelance})",
                DonneesAvant = null,
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdPlanification.Id,
                    createdPlanification.FactureId,
                    FactureNumero = facture.Numero,
                    createdPlanification.RegleRelanceId,
                    createdPlanification.NiveauRelance,
                    createdPlanification.DatePrevue,
                    createdPlanification.Statut,
                    createdPlanification.ModeleRelanceId,
                    createdPlanification.Canal
                })
            });

            return createdPlanification;
        }

        /// <summary>
        /// Met à jour une planification de relance existante
        /// </summary>
        /// <param name="planificationRelance">Planification de relance à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Planification de relance mise à jour</returns>
        public async Task<PlanificationRelance> UpdateAsync(PlanificationRelance planificationRelance, int modifiePar)
        {
            if (planificationRelance == null)
                throw new ArgumentNullException(nameof(planificationRelance));

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que la planification de relance existe
            var existingPlanification = await _planificationRelanceRepository.GetByIdAsync(planificationRelance.Id);
            if (existingPlanification == null)
                throw new InvalidOperationException($"La planification de relance avec l'ID {planificationRelance.Id} n'existe pas");

            // Vérifier que la facture existe
            var facture = await _factureRepository.GetByIdAsync(planificationRelance.FactureId);
            if (facture == null)
                throw new InvalidOperationException($"La facture avec l'ID {planificationRelance.FactureId} n'existe pas");

            // Vérifier que le modèle de relance existe
            if (planificationRelance.ModeleRelanceId.HasValue)
            {
                var modele = await _modeleRelanceRepository.GetByIdAsync(planificationRelance.ModeleRelanceId.Value);
                if (modele == null)
                    throw new InvalidOperationException($"Le modèle de relance avec l'ID {planificationRelance.ModeleRelanceId.Value} n'existe pas");
            }

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "PlanificationRelance",
                EntiteId = planificationRelance.Id,
                TypeAction = "Modification",
                UtilisateurId = modifiePar,
                DateAction = DateTime.Now,
                Description = $"Modification d'une planification de relance pour la facture {facture.Numero:s} (niveau {planificationRelance.NiveauRelance})",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingPlanification.Id,
                    existingPlanification.FactureId,
                    FactureNumero = facture.Numero,
                    existingPlanification.RegleRelanceId,
                    existingPlanification.NiveauRelance,
                    existingPlanification.DatePrevue,
                    existingPlanification.Statut,
                    existingPlanification.ModeleRelanceId,
                    existingPlanification.Canal
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    planificationRelance.Id,
                    planificationRelance.FactureId,
                    FactureNumero = facture.Numero,
                    planificationRelance.RegleRelanceId,
                    planificationRelance.NiveauRelance,
                    planificationRelance.DatePrevue,
                    planificationRelance.Statut,
                    planificationRelance.ModeleRelanceId,
                    planificationRelance.Canal
                })
            });

            // Mettre à jour la planification de relance
            return await _planificationRelanceRepository.UpdateAsync(planificationRelance, modifiePar);
        }

        /// <summary>
        /// Supprime une planification de relance
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant de la planification de relance ne peut pas être négatif ou nul");

            if (supprimePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que la planification de relance existe
            var planification = await _planificationRelanceRepository.GetByIdAsync(id);
            if (planification == null)
                return false;

            // Récupérer la facture pour le journal d'audit
            var facture = await _factureRepository.GetByIdAsync(planification.FactureId);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "PlanificationRelance",
                EntiteId = id,
                TypeAction = "Suppression",
                UtilisateurId = supprimePar,
                DateAction = DateTime.Now,
                Description = $"Suppression d'une planification de relance pour la facture {facture?.Numero ?? planification.FactureId.ToString()} (niveau {planification.NiveauRelance})",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    planification.Id,
                    planification.FactureId,
                    FactureNumero = facture?.Numero,
                    planification.RegleRelanceId,
                    planification.NiveauRelance,
                    planification.DatePrevue,
                    planification.Statut,
                    planification.ModeleRelanceId,
                    planification.Canal
                }),
                DonneesApres = null
            });

            // Supprimer la planification de relance
            return await _planificationRelanceRepository.DeleteAsync(id, supprimePar);
        }

        /// <summary>
        /// Valide une planification de relance
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance</param>
        /// <param name="validePar">Identifiant de l'utilisateur qui effectue la validation</param>
        /// <param name="commentaire">Commentaire de validation (optionnel)</param>
        /// <returns>True si la validation a réussi, sinon False</returns>
        public async Task<bool> ValidateAsync(int id, int validePar, string commentaire = null)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant de la planification de relance ne peut pas être négatif ou nul");

            if (validePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que la planification de relance existe
            var planification = await _planificationRelanceRepository.GetByIdAsync(id);
            if (planification == null)
                return false;

            // Vérifier que la planification est en attente de validation
            if (planification.Statut != StatutPlanificationRelance.EnAttenteValidation)
                return false;

            // Mettre à jour le statut de la planification
            var success = await _planificationRelanceRepository.UpdateStatutAsync(id, StatutPlanificationRelance.Validee, validePar, commentaire);
            if (!success)
                return false;

            // Récupérer la facture pour le journal d'audit
            var facture = await _factureRepository.GetByIdAsync(planification.FactureId);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "PlanificationRelance",
                EntiteId = id,
                TypeAction = "Validation",
                UtilisateurId = validePar,
                DateAction = DateTime.Now,
                Description = $"Validation d'une planification de relance pour la facture {facture?.Numero ?? planification.FactureId.ToString()} (niveau {planification.NiveauRelance})",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    Statut = StatutPlanificationRelance.EnAttenteValidation
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    Statut = StatutPlanificationRelance.Validee,
                    ValidePar = validePar,
                    DateValidation = DateTime.Now,
                    Commentaire = commentaire
                })
            });

            return true;
        }

        /// <summary>
        /// Annule une planification de relance
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance</param>
        /// <param name="annulePar">Identifiant de l'utilisateur qui effectue l'annulation</param>
        /// <param name="commentaire">Commentaire d'annulation (optionnel)</param>
        /// <returns>True si l'annulation a réussi, sinon False</returns>
        public async Task<bool> CancelAsync(int id, int annulePar, string commentaire = null)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant de la planification de relance ne peut pas être négatif ou nul");

            if (annulePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que la planification de relance existe
            var planification = await _planificationRelanceRepository.GetByIdAsync(id);
            if (planification == null)
                return false;

            // Vérifier que la planification n'est pas déjà envoyée ou annulée
            if (planification.Statut == StatutPlanificationRelance.Envoyee || planification.Statut == StatutPlanificationRelance.Annulee)
                return false;

            // Mettre à jour le statut de la planification
            var success = await _planificationRelanceRepository.UpdateStatutAsync(id, StatutPlanificationRelance.Annulee, annulePar, commentaire);
            if (!success)
                return false;

            // Récupérer la facture pour le journal d'audit
            var facture = await _factureRepository.GetByIdAsync(planification.FactureId);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "PlanificationRelance",
                EntiteId = id,
                TypeAction = "Annulation",
                UtilisateurId = annulePar,
                DateAction = DateTime.Now,
                Description = $"Annulation d'une planification de relance pour la facture {facture?.Numero ?? planification.FactureId.ToString()} (niveau {planification.NiveauRelance})",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    Statut = planification.Statut
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    Statut = StatutPlanificationRelance.Annulee,
                    Commentaire = commentaire
                })
            });

            return true;
        }

        /// <summary>
        /// Marque une planification de relance comme envoyée
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance</param>
        /// <param name="envoyePar">Identifiant de l'utilisateur qui effectue l'envoi</param>
        /// <param name="dateEnvoi">Date d'envoi (par défaut, la date actuelle)</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> MarkAsSentAsync(int id, int envoyePar, DateTime? dateEnvoi = null)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant de la planification de relance ne peut pas être négatif ou nul");

            if (envoyePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que la planification de relance existe
            var planification = await _planificationRelanceRepository.GetByIdAsync(id);
            if (planification == null)
                return false;

            // Vérifier que la planification est planifiée ou validée
            if (planification.Statut != StatutPlanificationRelance.Planifiee && planification.Statut != StatutPlanificationRelance.Validee)
                return false;

            // Mettre à jour le statut de la planification
            var success = await _planificationRelanceRepository.MarkAsSentAsync(id, dateEnvoi ?? DateTime.Now, envoyePar);
            if (!success)
                return false;

            // Récupérer la facture pour le journal d'audit
            var facture = await _factureRepository.GetByIdAsync(planification.FactureId);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "PlanificationRelance",
                EntiteId = id,
                TypeAction = "Envoi",
                UtilisateurId = envoyePar,
                DateAction = DateTime.Now,
                Description = $"Envoi d'une relance pour la facture {facture?.Numero ?? planification.FactureId.ToString()} (niveau {planification.NiveauRelance})",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    Statut = planification.Statut
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    Statut = StatutPlanificationRelance.Envoyee,
                    DateEnvoi = dateEnvoi ?? DateTime.Now
                })
            });

            return true;
        }

        /// <summary>
        /// Exécute les planifications de relance prévues pour aujourd'hui
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre de relances envoyées</returns>
        public async Task<int> ExecutePlannedRemindersAsync(int utilisateurId)
        {
            if (utilisateurId <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Récupérer les planifications prévues pour aujourd'hui
            var planifications = await GetPlannedForDateAsync(DateTime.Today);

            // Filtrer les planifications qui sont planifiées ou validées
            planifications = planifications.Where(p =>
                p.Statut == StatutPlanificationRelance.Planifiee ||
                p.Statut == StatutPlanificationRelance.Validee);

            int count = 0;
            foreach (var planification in planifications)
            {
                try
                {
                    // Récupérer la facture
                    var facture = await _factureRepository.GetByIdAsync(planification.FactureId);
                    if (facture == null)
                        continue;

                    // Vérifier que la facture est toujours en retard
                    if (facture.Statut != StatutFacture.EnRetard)
                    {
                        // Annuler la planification si la facture n'est plus en retard
                        await CancelAsync(planification.Id, utilisateurId, "Facture n'est plus en retard");
                        continue;
                    }

                    // Récupérer le client
                    var client = await _clientRepository.GetByIdAsync(facture.ClientId);
                    if (client == null)
                        continue;

                    // Récupérer le modèle de relance
                    if (!planification.ModeleRelanceId.HasValue)
                        continue;

                    var modele = await _modeleRelanceRepository.GetByIdAsync(planification.ModeleRelanceId.Value);
                    if (modele == null)
                        continue;

                    // Appliquer les variables dynamiques au modèle
                    string contenu = await _modeleRelanceService.ApplyVariablesAsync(modele, facture.Id);

                    // Envoyer la relance selon le canal
                    bool success = false;
                    switch (planification.Canal)
                    {
                        case CanalRelance.Email:
                            // Envoyer par email
                            if (!string.IsNullOrEmpty(client.Email))
                            {
                                success = await Business.Helpers.EmailHelper.SendEmailAsync(modele.Objet, contenu, client.Email, _configuration);
                            }
                            break;

                        case CanalRelance.Courrier:
                            // Générer un PDF
                            string cheminFichier = Path.Combine(
                                _configuration.GetSection("AppSettings")["DocumentsPath"] ?? "Documents",
                                "Relances",
                                $"Relance_{facture.Numero}_{DateTime.Now:yyyyMMdd}.pdf");

                            success = Business.Helpers.PdfHelper.GeneratePdf(contenu, facture, client, cheminFichier);
                            break;

                        case CanalRelance.SMS:
                            // À implémenter plus tard
                            success = false;
                            break;
                    }

                    if (success)
                    {
                        // Marquer la planification comme envoyée
                        await MarkAsSentAsync(planification.Id, utilisateurId);
                        count++;
                    }
                }
                catch (Exception ex)
                {
                    // Journaliser l'erreur
                    Serilog.Log.Error(ex, "Erreur lors de l'exécution de la planification de relance {PlanificationId}", planification.Id);
                }
            }

            return count;
        }
    }
}
