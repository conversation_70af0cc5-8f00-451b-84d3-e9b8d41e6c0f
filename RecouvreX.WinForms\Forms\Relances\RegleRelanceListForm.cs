using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Relances
{
    public partial class RegleRelanceListForm : Form
    {
        private readonly IRegleRelanceService _regleRelanceService;
        private readonly IAuthenticationService _authenticationService;
        private readonly int _currentUserId;
        private List<RegleRelance> _regles = new List<RegleRelance>();
        private DataTable _dataTable = new DataTable();

        public RegleRelanceListForm(IRegleRelanceService regleRelanceService, IAuthenticationService authenticationService, int currentUserId)
        {
            _regleRelanceService = regleRelanceService ?? throw new ArgumentNullException(nameof(regleRelanceService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }

        private async void RegleRelanceListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Vérifier les permissions
                bool canView = await _authenticationService.HasPermissionAsync(_currentUserId, "REGLES_RELANCE_VIEW");
                bool canAdd = await _authenticationService.HasPermissionAsync(_currentUserId, "REGLES_RELANCE_ADD");
                bool canEdit = await _authenticationService.HasPermissionAsync(_currentUserId, "REGLES_RELANCE_EDIT");
                bool canDelete = await _authenticationService.HasPermissionAsync(_currentUserId, "REGLES_RELANCE_DELETE");

                if (!canView)
                {
                    MessageBox.Show("Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.",
                        "Accès refusé", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return;
                }

                // Configurer les boutons en fonction des permissions
                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                var addButton = toolStrip?.Items.Find("addButton", false).FirstOrDefault() as ToolStripButton;
                var editButton = toolStrip?.Items.Find("editButton", false).FirstOrDefault() as ToolStripButton;
                var deleteButton = toolStrip?.Items.Find("deleteButton", false).FirstOrDefault() as ToolStripButton;

                if (addButton != null) addButton.Enabled = canAdd;
                if (editButton != null) editButton.Enabled = canEdit;
                if (deleteButton != null) deleteButton.Enabled = canDelete;

                // Initialiser la table de données
                InitializeDataTable();

                // Charger les règles de relance
                await LoadReglesAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire de liste des règles de relance");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Nom", typeof(string));
            _dataTable.Columns.Add("Type client", typeof(string));
            _dataTable.Columns.Add("Jours après échéance", typeof(int));
            _dataTable.Columns.Add("Jours entre 1-2", typeof(int));
            _dataTable.Columns.Add("Jours entre 2-3", typeof(int));
            _dataTable.Columns.Add("Priorité", typeof(int));
            _dataTable.Columns.Add("Validation requise", typeof(bool));
            _dataTable.Columns.Add("Active", typeof(bool));

            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;
            }
        }

        private async Task LoadReglesAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer les règles de relance
                _regles = (await _regleRelanceService.GetAllAsync()).ToList();

                // Mettre à jour la table de données
                UpdateDataTable(_regles);

                // Mettre à jour le compteur
                var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
                var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
                if (countLabel != null)
                {
                    countLabel.Text = $"Nombre de règles : {_regles.Count}";
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des règles de relance");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des règles de relance : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void UpdateDataTable(List<RegleRelance> regles)
        {
            _dataTable.Clear();

            foreach (var regle in regles)
            {
                _dataTable.Rows.Add(
                    regle.Id,
                    regle.Nom,
                    string.IsNullOrEmpty(regle.TypeClient) ? "Tous" : regle.TypeClient,
                    regle.JoursApresPremiere,
                    regle.JoursEntrePremiereDeuxieme,
                    regle.JoursEntreDeuxiemeTroisieme,
                    regle.Priorite,
                    regle.RequiertValidation,
                    regle.EstActive
                );
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            // Recharger les règles de relance
            await LoadReglesAsync();
        }

        private void SearchButton_Click(object sender, EventArgs e)
        {
            Search();
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                Search();
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        private void Search()
        {
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var searchTextBox = toolStrip?.Items.Find("searchTextBox", false).FirstOrDefault() as ToolStripTextBox;
            if (searchTextBox != null)
            {
                string searchText = searchTextBox.Text.Trim().ToLower();
                if (string.IsNullOrEmpty(searchText))
                {
                    // Afficher toutes les règles
                    UpdateDataTable(_regles);
                }
                else
                {
                    // Filtrer les règles
                    var filteredRegles = _regles.Where(r =>
                        r.Nom.ToLower().Contains(searchText) ||
                        r.Description.ToLower().Contains(searchText) ||
                        (r.TypeClient?.ToLower().Contains(searchText) == true)
                    ).ToList();

                    // Mettre à jour la table de données
                    UpdateDataTable(filteredRegles);
                }
            }
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir le formulaire d'ajout de règle de relance
                using (var form = new RegleRelanceEditForm(_regleRelanceService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les règles de relance
                        LoadReglesAsync().Wait();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout de règle de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer la règle de relance sélectionnée
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int regleId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    EditRegle(regleId);
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une règle de relance à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de modification de règle de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int regleId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    EditRegle(regleId);
                }
            }
        }

        private async void EditRegle(int regleId)
        {
            try
            {
                // Récupérer la règle de relance
                var regle = await _regleRelanceService.GetByIdAsync(regleId);
                if (regle == null)
                {
                    MessageBox.Show("La règle de relance demandée n'existe pas ou a été supprimée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Ouvrir le formulaire de modification de règle de relance
                using (var form = new RegleRelanceEditForm(_regleRelanceService, _currentUserId, regle))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les règles de relance
                        await LoadReglesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de modification de règle de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer la règle de relance sélectionnée
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int regleId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string regleName = dataGridView.SelectedRows[0].Cells["Nom"].Value.ToString();

                    // Demander confirmation
                    var result = MessageBox.Show($"Êtes-vous sûr de vouloir supprimer la règle de relance '{regleName}' ?\n\nCette action est irréversible.",
                        "Confirmation de suppression", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        // Supprimer la règle de relance
                        bool success = await _regleRelanceService.DeleteAsync(regleId, _currentUserId);
                        if (success)
                        {
                            // Recharger les règles de relance
                            await LoadReglesAsync();
                            MessageBox.Show("La règle de relance a été supprimée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("Une erreur s'est produite lors de la suppression de la règle de relance.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une règle de relance à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression d'une règle de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void ToggleActiveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer la règle de relance sélectionnée
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int regleId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string regleName = dataGridView.SelectedRows[0].Cells["Nom"].Value.ToString();
                    bool isActive = Convert.ToBoolean(dataGridView.SelectedRows[0].Cells["Active"].Value);

                    // Demander confirmation
                    string action = isActive ? "désactiver" : "activer";
                    var result = MessageBox.Show($"Êtes-vous sûr de vouloir {action} la règle de relance '{regleName}' ?",
                        $"Confirmation de {action}", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Activer/désactiver la règle de relance
                        bool success = await _regleRelanceService.SetActiveStatusAsync(regleId, !isActive, _currentUserId);
                        if (success)
                        {
                            // Recharger les règles de relance
                            await LoadReglesAsync();
                            MessageBox.Show($"La règle de relance a été {(isActive ? "désactivée" : "activée")} avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show($"Une erreur s'est produite lors de la {(isActive ? "désactivation" : "activation")} de la règle de relance.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une règle de relance à activer/désactiver.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'activation/désactivation d'une règle de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
