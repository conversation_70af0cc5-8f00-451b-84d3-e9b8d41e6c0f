using Dapper;
using RecouvreX.Common.Security;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les utilisateurs
    /// </summary>
    public class UtilisateurRepository : BaseRepository<Utilisateur>, IUtilisateurRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public UtilisateurRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "Utilisateurs")
        {
        }

        /// <summary>
        /// Récupère un utilisateur par son nom d'utilisateur
        /// </summary>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <returns>Utilisateur trouvé ou null</returns>
        public async Task<Utilisateur> GetByNomUtilisateurAsync(string nomUtilisateur)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT u.*, r.Nom as RoleNom
                    FROM Utilisateurs u
                    LEFT JOIN Roles r ON u.RoleId = r.Id
                    WHERE u.NomUtilisateur = @NomUtilisateur AND u.EstActif = 1";

                var result = await connection.QueryAsync<Utilisateur, Role, Utilisateur>(
                    query,
                    (utilisateur, role) =>
                    {
                        if (role != null)
                        {
                            utilisateur.Role = role;
                        }
                        return utilisateur;
                    },
                    new { NomUtilisateur = nomUtilisateur },
                    splitOn: "RoleNom");

                return result.FirstOrDefault();
            }
        }

        /// <summary>
        /// Vérifie si les informations de connexion sont valides
        /// </summary>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="motDePasse">Mot de passe en clair</param>
        /// <returns>Utilisateur authentifié ou null</returns>
        public async Task<Utilisateur> AuthenticateAsync(string nomUtilisateur, string motDePasse)
        {
            var utilisateur = await GetByNomUtilisateurAsync(nomUtilisateur);
            if (utilisateur == null || utilisateur.EstVerrouille)
                return null;

            // Vérifier le mot de passe
            if (PasswordHasher.VerifyPassword(motDePasse, utilisateur.MotDePasse))
            {
                // Réinitialiser le compteur de tentatives échouées
                await ResetFailedLoginAttemptsAsync(utilisateur.Id);
                return utilisateur;
            }
            else
            {
                // Incrémenter le compteur de tentatives échouées
                await IncrementFailedLoginAttemptsAsync(nomUtilisateur);
                return null;
            }
        }

        /// <summary>
        /// Met à jour la date de dernière connexion d'un utilisateur
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateLastLoginAsync(int userId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE Utilisateurs
                    SET DerniereConnexion = @DerniereConnexion
                    WHERE Id = @Id";

                var parameters = new
                {
                    Id = userId,
                    DerniereConnexion = DateTime.Now
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }

        /// <summary>
        /// Incrémente le compteur de tentatives de connexion échouées
        /// </summary>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <returns>Nombre de tentatives échouées</returns>
        public async Task<int> IncrementFailedLoginAttemptsAsync(string nomUtilisateur)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Récupérer le nombre actuel de tentatives
                var queryGet = @"
                    SELECT TentativesConnexionEchouees
                    FROM Utilisateurs
                    WHERE NomUtilisateur = @NomUtilisateur AND EstActif = 1";

                var tentatives = await connection.QueryFirstOrDefaultAsync<int>(queryGet, new { NomUtilisateur = nomUtilisateur });

                // Incrémenter le compteur
                tentatives++;

                // Mettre à jour le compteur
                var queryUpdate = @"
                    UPDATE Utilisateurs
                    SET TentativesConnexionEchouees = @Tentatives,
                        EstVerrouille = CASE WHEN @Tentatives >= 5 THEN 1 ELSE EstVerrouille END
                    WHERE NomUtilisateur = @NomUtilisateur";

                await connection.ExecuteAsync(queryUpdate, new { NomUtilisateur = nomUtilisateur, Tentatives = tentatives });

                return tentatives;
            }
        }

        /// <summary>
        /// Réinitialise le compteur de tentatives de connexion échouées
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <returns>True si la réinitialisation a réussi, sinon False</returns>
        public async Task<bool> ResetFailedLoginAttemptsAsync(int userId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE Utilisateurs
                    SET TentativesConnexionEchouees = 0
                    WHERE Id = @Id";

                var result = await connection.ExecuteAsync(query, new { Id = userId });
                return result > 0;
            }
        }

        /// <summary>
        /// Verrouille ou déverrouille un compte utilisateur
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <param name="estVerrouille">True pour verrouiller, False pour déverrouiller</param>
        /// <returns>True si l'opération a réussi, sinon False</returns>
        public async Task<bool> SetAccountLockedStatusAsync(int userId, bool estVerrouille)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE Utilisateurs
                    SET EstVerrouille = @EstVerrouille,
                        TentativesConnexionEchouees = CASE WHEN @EstVerrouille = 0 THEN 0 ELSE TentativesConnexionEchouees END
                    WHERE Id = @Id";

                var parameters = new
                {
                    Id = userId,
                    EstVerrouille = estVerrouille
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }

        /// <summary>
        /// Change le mot de passe d'un utilisateur
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <param name="nouveauMotDePasse">Nouveau mot de passe en clair</param>
        /// <returns>True si le changement a réussi, sinon False</returns>
        public async Task<bool> ChangePasswordAsync(int userId, string nouveauMotDePasse)
        {
            // Hacher le nouveau mot de passe
            var motDePasseHache = PasswordHasher.HashPassword(nouveauMotDePasse);

            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE Utilisateurs
                    SET MotDePasse = @MotDePasse,
                        DateModification = @DateModification
                    WHERE Id = @Id";

                var parameters = new
                {
                    Id = userId,
                    MotDePasse = motDePasseHache,
                    DateModification = DateTime.Now
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }

        /// <summary>
        /// Met à jour le mot de passe d'un utilisateur (déjà haché)
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <param name="motDePasseHache">Mot de passe haché</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdatePasswordAsync(int userId, string motDePasseHache, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE Utilisateurs
                    SET MotDePasse = @MotDePasse,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id";

                var parameters = new
                {
                    Id = userId,
                    MotDePasse = motDePasseHache,
                    DateModification = DateTime.Now,
                    ModifiePar = modifiePar
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }

        /// <summary>
        /// Active ou désactive un utilisateur
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <param name="estActif">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateActiveStatusAsync(int userId, bool estActif, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE Utilisateurs
                    SET EstActif = @EstActif,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id";

                var parameters = new
                {
                    Id = userId,
                    EstActif = estActif,
                    DateModification = DateTime.Now,
                    ModifiePar = modifiePar
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }
    }
}
