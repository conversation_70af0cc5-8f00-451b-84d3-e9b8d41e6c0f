-- Script de création des index pour la base de données RecouvreX
-- Ce script crée les index nécessaires pour optimiser les performances des requêtes

USE RecouvreX;
GO

-- Index pour la table Utilisateurs
CREATE INDEX IX_Utilisateurs_NomUtilisateur ON app.Utilisateurs(NomUtilisateur);
CREATE INDEX IX_Utilisateurs_RoleId ON app.Utilisateurs(RoleId);
CREATE INDEX IX_Utilisateurs_EstActif ON app.Utilisateurs(EstActif);
GO

-- Index pour la table Roles
CREATE INDEX IX_Roles_Nom ON app.Roles(Nom);
CREATE INDEX IX_Roles_EstActif ON app.Roles(EstActif);
GO

-- Index pour la table Permissions
CREATE INDEX IX_Permissions_Code ON app.Permissions(Code);
CREATE INDEX IX_Permissions_Module ON app.Permissions(Module);
CREATE INDEX IX_Permissions_EstActif ON app.Permissions(EstActif);
GO

-- Index pour la table RolePermissions
CREATE INDEX IX_RolePermissions_RoleId ON app.RolePermissions(RoleId);
CREATE INDEX IX_RolePermissions_PermissionId ON app.RolePermissions(PermissionId);
CREATE INDEX IX_RolePermissions_EstActif ON app.RolePermissions(EstActif);
GO

-- Index pour la table Clients
CREATE INDEX IX_Clients_Code ON app.Clients(Code);
CREATE INDEX IX_Clients_RaisonSociale ON app.Clients(RaisonSociale);
CREATE INDEX IX_Clients_CommercialId ON app.Clients(CommercialId);
CREATE INDEX IX_Clients_EstActif ON app.Clients(EstActif);
GO

-- Index pour la table Contacts
CREATE INDEX IX_Contacts_ClientId ON app.Contacts(ClientId);
CREATE INDEX IX_Contacts_Nom ON app.Contacts(Nom);
CREATE INDEX IX_Contacts_EstPrincipal ON app.Contacts(EstPrincipal);
CREATE INDEX IX_Contacts_EstActif ON app.Contacts(EstActif);
GO

-- Index pour la table Factures
CREATE INDEX IX_Factures_Numero ON app.Factures(Numero);
CREATE INDEX IX_Factures_ClientId ON app.Factures(ClientId);
CREATE INDEX IX_Factures_DateEmission ON app.Factures(DateEmission);
CREATE INDEX IX_Factures_DateEcheance ON app.Factures(DateEcheance);
CREATE INDEX IX_Factures_Statut ON app.Factures(Statut);
CREATE INDEX IX_Factures_CommercialId ON app.Factures(CommercialId);
CREATE INDEX IX_Factures_LivreurId ON app.Factures(LivreurId);
CREATE INDEX IX_Factures_EstActif ON app.Factures(EstActif);
GO

-- Index pour la table Paiements
CREATE INDEX IX_Paiements_Reference ON app.Paiements(Reference);
CREATE INDEX IX_Paiements_ClientId ON app.Paiements(ClientId);
CREATE INDEX IX_Paiements_DatePaiement ON app.Paiements(DatePaiement);
CREATE INDEX IX_Paiements_ModePaiement ON app.Paiements(ModePaiement);
CREATE INDEX IX_Paiements_EstActif ON app.Paiements(EstActif);
GO

-- Index pour la table FacturePaiements
CREATE INDEX IX_FacturePaiements_FactureId ON app.FacturePaiements(FactureId);
CREATE INDEX IX_FacturePaiements_PaiementId ON app.FacturePaiements(PaiementId);
CREATE INDEX IX_FacturePaiements_EstActif ON app.FacturePaiements(EstActif);
GO

-- Index pour la table Relances
CREATE INDEX IX_Relances_FactureId ON app.Relances(FactureId);
CREATE INDEX IX_Relances_Type ON app.Relances(Type);
CREATE INDEX IX_Relances_DateRelance ON app.Relances(DateRelance);
CREATE INDEX IX_Relances_Niveau ON app.Relances(Niveau);
CREATE INDEX IX_Relances_Statut ON app.Relances(Statut);
CREATE INDEX IX_Relances_UtilisateurId ON app.Relances(UtilisateurId);
CREATE INDEX IX_Relances_DateProchaineRelance ON app.Relances(DateProchaineRelance);
CREATE INDEX IX_Relances_EstActif ON app.Relances(EstActif);
GO

-- Index pour la table Documents
CREATE INDEX IX_Documents_Nom ON app.Documents(Nom);
CREATE INDEX IX_Documents_Type ON app.Documents(Type);
CREATE INDEX IX_Documents_TypeEntite_EntiteId ON app.Documents(TypeEntite, EntiteId);
CREATE INDEX IX_Documents_EstActif ON app.Documents(EstActif);
GO

-- Index pour la table JournalAudit
CREATE INDEX IX_JournalAudit_DateAction ON app.JournalAudit(DateAction);
CREATE INDEX IX_JournalAudit_UtilisateurId ON app.JournalAudit(UtilisateurId);
CREATE INDEX IX_JournalAudit_TypeAction ON app.JournalAudit(TypeAction);
CREATE INDEX IX_JournalAudit_TypeEntite_EntiteId ON app.JournalAudit(TypeEntite, EntiteId);
GO

PRINT 'Index créés avec succès.';
GO
