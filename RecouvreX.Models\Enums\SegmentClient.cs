namespace RecouvreX.Models.Enums
{
    /// <summary>
    /// Énumération des segments de clients
    /// </summary>
    public enum SegmentClient
    {
        /// <summary>
        /// Segment A - Clients stratégiques (haute valeur, faible risque)
        /// </summary>
        A = 1,

        /// <summary>
        /// Segment B - Clients importants (valeur moyenne, risque modéré)
        /// </summary>
        B = 2,

        /// <summary>
        /// Segment C - Clients standards (faible valeur ou risque élevé)
        /// </summary>
        C = 3,

        /// <summary>
        /// Non segmenté
        /// </summary>
        NonSegmente = 0
    }
}
