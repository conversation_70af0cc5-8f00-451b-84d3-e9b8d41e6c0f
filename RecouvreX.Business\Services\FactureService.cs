using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des factures
    /// </summary>
    public class FactureService : IFactureService
    {
        private readonly IFactureRepository _factureRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="factureRepository">Repository des factures</param>
        /// <param name="clientRepository">Repository des clients</param>
        /// <param name="utilisateurRepository">Repository des utilisateurs</param>
        /// <param name="journalAuditRepository">Repository du journal d'audit</param>
        public FactureService(
            IFactureRepository factureRepository,
            IClientRepository clientRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère toutes les factures
        /// </summary>
        /// <returns>Liste des factures</returns>
        public async Task<IEnumerable<Facture>> GetAllAsync()
        {
            return await _factureRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère une facture par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la facture</param>
        /// <returns>Facture trouvée ou null</returns>
        public async Task<Facture> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _factureRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère une facture par son numéro
        /// </summary>
        /// <param name="numero">Numéro de la facture</param>
        /// <returns>Facture trouvée ou null</returns>
        public async Task<Facture> GetByNumeroAsync(string numero)
        {
            if (string.IsNullOrEmpty(numero))
                return null;

            return await _factureRepository.GetByNumeroAsync(numero);
        }

        /// <summary>
        /// Récupère une facture avec ses paiements
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Facture avec ses paiements</returns>
        public async Task<Facture> GetWithPaiementsAsync(int factureId)
        {
            if (factureId <= 0)
                return null;

            return await _factureRepository.GetWithPaiementsAsync(factureId);
        }

        /// <summary>
        /// Récupère une facture avec ses relances
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Facture avec ses relances</returns>
        public async Task<Facture> GetWithRelancesAsync(int factureId)
        {
            if (factureId <= 0)
                return null;

            return await _factureRepository.GetWithRelancesAsync(factureId);
        }

        /// <summary>
        /// Récupère une facture avec ses documents
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Facture avec ses documents</returns>
        public async Task<Facture> GetWithDocumentsAsync(int factureId)
        {
            if (factureId <= 0)
                return null;

            return await _factureRepository.GetWithDocumentsAsync(factureId);
        }

        /// <summary>
        /// Récupère toutes les factures d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des factures du client</returns>
        public async Task<IEnumerable<Facture>> GetByClientIdAsync(int clientId)
        {
            if (clientId <= 0)
                return new List<Facture>();

            return await _factureRepository.GetByClientIdAsync(clientId);
        }

        /// <summary>
        /// Récupère toutes les factures d'un commercial
        /// </summary>
        /// <param name="commercialId">Identifiant du commercial</param>
        /// <returns>Liste des factures du commercial</returns>
        public async Task<IEnumerable<Facture>> GetByCommercialIdAsync(int commercialId)
        {
            if (commercialId <= 0)
                return new List<Facture>();

            return await _factureRepository.GetByCommercialIdAsync(commercialId);
        }

        /// <summary>
        /// Récupère toutes les factures d'un livreur
        /// </summary>
        /// <param name="livreurId">Identifiant du livreur</param>
        /// <returns>Liste des factures du livreur</returns>
        public async Task<IEnumerable<Facture>> GetByLivreurIdAsync(int livreurId)
        {
            if (livreurId <= 0)
                return new List<Facture>();

            return await _factureRepository.GetByLivreurIdAsync(livreurId);
        }

        /// <summary>
        /// Récupère les factures par statut
        /// </summary>
        /// <param name="statut">Statut des factures</param>
        /// <returns>Liste des factures ayant le statut spécifié</returns>
        public async Task<IEnumerable<Facture>> GetByStatutAsync(string statut)
        {
            if (string.IsNullOrEmpty(statut))
                return new List<Facture>();

            return await _factureRepository.GetByStatutAsync(statut);
        }

        /// <summary>
        /// Récupère les factures par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des factures émises dans la période spécifiée</returns>
        public async Task<IEnumerable<Facture>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            return await _factureRepository.GetByPeriodAsync(dateDebut, dateFin);
        }

        /// <summary>
        /// Récupère les factures en retard de paiement
        /// </summary>
        /// <returns>Liste des factures en retard</returns>
        public async Task<IEnumerable<Facture>> GetOverdueInvoicesAsync()
        {
            return await _factureRepository.GetOverdueInvoicesAsync();
        }

        /// <summary>
        /// Crée une nouvelle facture
        /// </summary>
        /// <param name="facture">Facture à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Facture créée avec son identifiant généré</returns>
        public async Task<Facture> CreateAsync(Facture facture, int creePar)
        {
            if (facture == null || creePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la création d'une facture");

            // Vérifier si le client existe
            var client = await _clientRepository.GetByIdAsync(facture.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {facture.ClientId} n'existe pas");

            // Vérifier si le commercial existe
            if (facture.CommercialId.HasValue && facture.CommercialId.Value > 0)
            {
                var commercial = await _utilisateurRepository.GetByIdAsync(facture.CommercialId.Value);
                if (commercial == null)
                    throw new InvalidOperationException($"Le commercial avec l'ID {facture.CommercialId.Value} n'existe pas");
            }

            // Vérifier si le livreur existe
            if (facture.LivreurId.HasValue && facture.LivreurId.Value > 0)
            {
                var livreur = await _utilisateurRepository.GetByIdAsync(facture.LivreurId.Value);
                if (livreur == null)
                    throw new InvalidOperationException($"Le livreur avec l'ID {facture.LivreurId.Value} n'existe pas");
            }

            // Générer un numéro de facture unique si non fourni
            if (string.IsNullOrEmpty(facture.Numero))
            {
                facture.Numero = await GenerateNumeroFactureAsync();
            }
            else
            {
                // Vérifier si le numéro de facture existe déjà
                var existingFacture = await _factureRepository.GetByNumeroAsync(facture.Numero);
                if (existingFacture != null)
                    throw new InvalidOperationException($"Le numéro de facture '{facture.Numero}' existe déjà");
            }

            // Calculer le montant restant
            facture.MontantRestant = facture.MontantTTC - facture.MontantPaye;

            // Déterminer le statut initial
            if (facture.MontantPaye >= facture.MontantTTC)
            {
                facture.Statut = StatutFacture.Payee;
                facture.MontantRestant = 0;
            }
            else if (facture.MontantPaye > 0)
            {
                facture.Statut = StatutFacture.PayeePartiellement;
            }
            else if (facture.DateEcheance < DateTime.Today)
            {
                facture.Statut = StatutFacture.EnRetard;
            }
            else
            {
                facture.Statut = StatutFacture.EnAttente;
            }

            // Créer la facture
            var createdFacture = await _factureRepository.AddAsync(facture, creePar);

            // Mettre à jour le solde du client
            await _clientRepository.UpdateSoldeAsync(facture.ClientId, facture.MontantRestant);

            // Journaliser la création
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = creePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(creePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Creation,
                TypeEntite = "Facture",
                EntiteId = createdFacture.Id,
                Description = $"Création de la facture {createdFacture.Numero} pour le client {client.RaisonSociale}",
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdFacture.Id,
                    createdFacture.Numero,
                    createdFacture.ClientId,
                    ClientNom = client.RaisonSociale,
                    createdFacture.DateEmission,
                    createdFacture.DateEcheance,
                    createdFacture.MontantHT,
                    createdFacture.MontantTVA,
                    createdFacture.MontantTTC,
                    createdFacture.MontantPaye,
                    createdFacture.MontantRestant,
                    createdFacture.Statut,
                    createdFacture.CommercialId,
                    createdFacture.LivreurId
                })
            });

            return createdFacture;
        }

        /// <summary>
        /// Met à jour une facture existante
        /// </summary>
        /// <param name="facture">Facture à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Facture mise à jour</returns>
        public async Task<Facture> UpdateAsync(Facture facture, int modifiePar)
        {
            if (facture == null || facture.Id <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la mise à jour d'une facture");

            // Récupérer la facture existante
            var existingFacture = await _factureRepository.GetByIdAsync(facture.Id);
            if (existingFacture == null)
                throw new InvalidOperationException($"La facture avec l'ID {facture.Id} n'existe pas");

            // Vérifier si le client existe
            var client = await _clientRepository.GetByIdAsync(facture.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {facture.ClientId} n'existe pas");

            // Vérifier si le commercial existe
            if (facture.CommercialId.HasValue && facture.CommercialId.Value > 0)
            {
                var commercial = await _utilisateurRepository.GetByIdAsync(facture.CommercialId.Value);
                if (commercial == null)
                    throw new InvalidOperationException($"Le commercial avec l'ID {facture.CommercialId.Value} n'existe pas");
            }

            // Vérifier si le livreur existe
            if (facture.LivreurId.HasValue && facture.LivreurId.Value > 0)
            {
                var livreur = await _utilisateurRepository.GetByIdAsync(facture.LivreurId.Value);
                if (livreur == null)
                    throw new InvalidOperationException($"Le livreur avec l'ID {facture.LivreurId.Value} n'existe pas");
            }

            // Vérifier si le numéro de facture existe déjà pour une autre facture
            if (facture.Numero != existingFacture.Numero && !string.IsNullOrEmpty(facture.Numero))
            {
                var factureWithSameNumero = await _factureRepository.GetByNumeroAsync(facture.Numero);
                if (factureWithSameNumero != null && factureWithSameNumero.Id != facture.Id)
                    throw new InvalidOperationException($"Le numéro de facture '{facture.Numero}' existe déjà");
            }

            // Calculer le montant restant
            facture.MontantRestant = facture.MontantTTC - facture.MontantPaye;

            // Déterminer le statut
            if (facture.MontantPaye >= facture.MontantTTC)
            {
                facture.Statut = StatutFacture.Payee;
                facture.MontantRestant = 0;
            }
            else if (facture.MontantPaye > 0)
            {
                facture.Statut = StatutFacture.PayeePartiellement;
            }
            else if (facture.DateEcheance < DateTime.Today)
            {
                facture.Statut = StatutFacture.EnRetard;
            }
            else
            {
                facture.Statut = StatutFacture.EnAttente;
            }

            // Calculer la différence de montant restant pour mettre à jour le solde du client
            decimal differenceRestant = facture.MontantRestant - existingFacture.MontantRestant;

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Facture",
                EntiteId = facture.Id,
                Description = $"Modification de la facture {facture.Numero} pour le client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingFacture.Id,
                    existingFacture.Numero,
                    existingFacture.ClientId,
                    existingFacture.DateEmission,
                    existingFacture.DateEcheance,
                    existingFacture.MontantHT,
                    existingFacture.MontantTVA,
                    existingFacture.MontantTTC,
                    existingFacture.MontantPaye,
                    existingFacture.MontantRestant,
                    existingFacture.Statut,
                    existingFacture.CommercialId,
                    existingFacture.LivreurId
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    facture.Id,
                    facture.Numero,
                    facture.ClientId,
                    ClientNom = client.RaisonSociale,
                    facture.DateEmission,
                    facture.DateEcheance,
                    facture.MontantHT,
                    facture.MontantTVA,
                    facture.MontantTTC,
                    facture.MontantPaye,
                    facture.MontantRestant,
                    facture.Statut,
                    facture.CommercialId,
                    facture.LivreurId
                })
            });

            // Mettre à jour la facture
            var updatedFacture = await _factureRepository.UpdateAsync(facture, modifiePar);

            // Mettre à jour le solde du client si nécessaire
            if (differenceRestant != 0)
            {
                await _clientRepository.UpdateSoldeAsync(facture.ClientId, differenceRestant);
            }

            return updatedFacture;
        }

        /// <summary>
        /// Supprime une facture
        /// </summary>
        /// <param name="id">Identifiant de la facture à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0 || supprimePar <= 0)
                return false;

            // Récupérer la facture à supprimer
            var facture = await _factureRepository.GetByIdAsync(id);
            if (facture == null)
                return false;

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(facture.ClientId);
            if (client == null)
                return false;

            // Journaliser la suppression
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = supprimePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(supprimePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Suppression,
                TypeEntite = "Facture",
                EntiteId = id,
                Description = $"Suppression de la facture {facture.Numero} pour le client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    facture.Id,
                    facture.Numero,
                    facture.ClientId,
                    ClientNom = client.RaisonSociale,
                    facture.DateEmission,
                    facture.DateEcheance,
                    facture.MontantHT,
                    facture.MontantTVA,
                    facture.MontantTTC,
                    facture.MontantPaye,
                    facture.MontantRestant,
                    facture.Statut,
                    facture.CommercialId,
                    facture.LivreurId
                })
            });

            // Supprimer la facture
            var result = await _factureRepository.DeleteAsync(id, supprimePar);

            // Mettre à jour le solde du client
            if (result && facture.MontantRestant != 0)
            {
                await _clientRepository.UpdateSoldeAsync(facture.ClientId, -facture.MontantRestant);
            }

            return result;
        }

        /// <summary>
        /// Met à jour le statut d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateStatutAsync(int factureId, string statut, int modifiePar)
        {
            if (factureId <= 0 || string.IsNullOrEmpty(statut) || modifiePar <= 0)
                return false;

            // Récupérer la facture
            var facture = await _factureRepository.GetByIdAsync(factureId);
            if (facture == null)
                return false;

            // Vérifier si le statut est valide
            if (statut != StatutFacture.EnAttente &&
                statut != StatutFacture.PayeePartiellement &&
                statut != StatutFacture.Payee &&
                statut != StatutFacture.EnRetard &&
                statut != StatutFacture.Annulee &&
                statut != StatutFacture.Litige)
            {
                throw new ArgumentException($"Le statut '{statut}' n'est pas valide");
            }

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Facture",
                EntiteId = factureId,
                Description = $"Modification du statut de la facture {facture.Numero} de '{facture.Statut}' à '{statut}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new { facture.Statut }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new { Statut = statut })
            });

            // Mettre à jour le statut
            return await _factureRepository.UpdateStatutAsync(factureId, statut, modifiePar);
        }

        /// <summary>
        /// Génère un numéro de facture unique
        /// </summary>
        /// <returns>Numéro de facture unique</returns>
        public async Task<string> GenerateNumeroFactureAsync()
        {
            // Format: YYYYMMDD-XXXX où XXXX est un numéro séquentiel
            string datePrefix = DateTime.Now.ToString("yyyyMMdd");
            
            // Récupérer toutes les factures dont le numéro commence par le préfixe de date
            var factures = await _factureRepository.FindAsync("Numero LIKE @Prefix", new { Prefix = $"{datePrefix}%" });
            
            // Trouver le numéro séquentiel le plus élevé
            int maxSequence = 0;
            foreach (var facture in factures)
            {
                if (facture.Numero.StartsWith(datePrefix) && facture.Numero.Length > datePrefix.Length + 1)
                {
                    string sequencePart = facture.Numero.Substring(datePrefix.Length + 1);
                    if (int.TryParse(sequencePart, out int sequence) && sequence > maxSequence)
                    {
                        maxSequence = sequence;
                    }
                }
            }
            
            // Générer le nouveau numéro de facture
            return $"{datePrefix}-{maxSequence + 1:D4}";
        }

        /// <summary>
        /// Met à jour les factures en retard
        /// </summary>
        /// <returns>Nombre de factures mises à jour</returns>
        public async Task<int> UpdateOverdueInvoicesAsync()
        {
            // Récupérer les factures qui devraient être en retard
            var factures = await _factureRepository.FindAsync(
                "DateEcheance < @Today AND Statut IN (@StatutEnAttente, @StatutPayeePartiellement)",
                new
                {
                    Today = DateTime.Today,
                    StatutEnAttente = StatutFacture.EnAttente,
                    StatutPayeePartiellement = StatutFacture.PayeePartiellement
                });

            int count = 0;
            foreach (var facture in factures)
            {
                // Mettre à jour le statut de la facture
                if (await _factureRepository.UpdateStatutAsync(facture.Id, StatutFacture.EnRetard, 1)) // 1 = Utilisateur système
                {
                    count++;
                }
            }

            return count;
        }
    }
}
