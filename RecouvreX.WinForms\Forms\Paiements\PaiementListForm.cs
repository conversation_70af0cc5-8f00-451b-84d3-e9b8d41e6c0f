using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.Paiements
{
    public partial class PaiementListForm : Form
    {
        private readonly IPaiementService _paiementService;
        private readonly IClientService _clientService;
        private readonly IAuthenticationService _authenticationService;
        private readonly int _currentUserId;
        private List<Paiement> _paiements = new List<Paiement>();
        private DataTable _dataTable = new DataTable();
        private Dictionary<int, string> _clientNames = new Dictionary<int, string>();

        public PaiementListForm(IPaiementService paiementService, IClientService clientService, IAuthenticationService authenticationService, int currentUserId)
        {
            _paiementService = paiementService ?? throw new ArgumentNullException(nameof(paiementService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }



        private async void PaiementListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Vérifier les permissions
                bool canView = await _authenticationService.HasPermissionAsync(_currentUserId, "PAIEMENTS_VIEW");
                bool canAdd = await _authenticationService.HasPermissionAsync(_currentUserId, "PAIEMENTS_ADD");

                if (!canView)
                {
                    MessageBox.Show("Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.",
                        "Accès refusé", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return;
                }

                // Configurer les boutons en fonction des permissions
                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                var addButton = toolStrip?.Items.Find("addButton", false).FirstOrDefault() as ToolStripButton;
                if (addButton != null) addButton.Enabled = canAdd;

                // Initialiser la table de données
                InitializeDataTable();

                // Charger les paiements
                await LoadPaiementsAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement de la liste des paiements");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des paiements : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Référence", typeof(string));
            _dataTable.Columns.Add("Client", typeof(string));
            _dataTable.Columns.Add("Date", typeof(DateTime));
            _dataTable.Columns.Add("Montant", typeof(decimal));
            _dataTable.Columns.Add("Mode", typeof(string));
            _dataTable.Columns.Add("Référence Bancaire", typeof(string));

            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (dataGridView.Columns["Date"] != null)
                    dataGridView.Columns["Date"].DefaultCellStyle.Format = "dd/MM/yyyy";
                if (dataGridView.Columns["Montant"] != null)
                    dataGridView.Columns["Montant"].DefaultCellStyle.Format = "C2";
            }
        }

        private async Task LoadPaiementsAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer les paiements
                _paiements = (await _paiementService.GetAllAsync()).ToList();

                // Récupérer les noms des clients
                await LoadClientNamesAsync();

                // Appliquer les filtres
                ApplyFilters();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des paiements");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des paiements : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private async Task LoadClientNamesAsync()
        {
            try
            {
                // Récupérer les IDs des clients uniques
                var clientIds = _paiements.Select(p => p.ClientId).Distinct().ToList();

                // Récupérer les noms des clients
                _clientNames.Clear();
                foreach (var clientId in clientIds)
                {
                    var client = await _clientService.GetByIdAsync(clientId);
                    if (client != null)
                    {
                        _clientNames[clientId] = client.RaisonSociale;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des noms des clients");
                throw;
            }
        }

        private void ApplyFilters()
        {
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var modeComboBox = toolStrip?.Items.Find("modeComboBox", false).FirstOrDefault() as ToolStripComboBox;
            var periodeComboBox = toolStrip?.Items.Find("periodeComboBox", false).FirstOrDefault() as ToolStripComboBox;

            if (modeComboBox != null && periodeComboBox != null)
            {
                string selectedMode = modeComboBox.SelectedItem?.ToString() ?? "Tous";
                string selectedPeriode = periodeComboBox.SelectedItem?.ToString() ?? "Tous";

                // Filtrer les paiements par mode et période
                List<Paiement> filteredPaiements = _paiements;

                // Filtre par mode de paiement
                if (selectedMode != "Tous")
                {
                    filteredPaiements = filteredPaiements.Where(p => p.ModePaiement == selectedMode).ToList();
                }

                // Filtre par période
                if (selectedPeriode != "Tous")
                {
                    DateTime dateDebut = DateTime.Today;
                    DateTime dateFin = DateTime.Today.AddDays(1).AddSeconds(-1);

                    switch (selectedPeriode)
                    {
                        case "Cette semaine":
                            // Début de la semaine (lundi)
                            dateDebut = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek + 1);
                            if (dateDebut > DateTime.Today)
                                dateDebut = dateDebut.AddDays(-7);
                            dateFin = dateDebut.AddDays(7).AddSeconds(-1);
                            break;
                        case "Ce mois":
                            dateDebut = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                            dateFin = dateDebut.AddMonths(1).AddSeconds(-1);
                            break;
                        case "Cette année":
                            dateDebut = new DateTime(DateTime.Today.Year, 1, 1);
                            dateFin = dateDebut.AddYears(1).AddSeconds(-1);
                            break;
                    }

                    filteredPaiements = filteredPaiements.Where(p => p.DatePaiement >= dateDebut && p.DatePaiement <= dateFin).ToList();
                }

                // Mettre à jour la table de données
                UpdateDataTable(filteredPaiements);

                // Mettre à jour les compteurs
                UpdateCounters(filteredPaiements);
            }
        }

        private void UpdateDataTable(List<Paiement> paiements)
        {
            _dataTable.Clear();

            foreach (var paiement in paiements)
            {
                string clientName = _clientNames.ContainsKey(paiement.ClientId) ? _clientNames[paiement.ClientId] : "Client inconnu";

                _dataTable.Rows.Add(
                    paiement.Id,
                    paiement.Reference,
                    clientName,
                    paiement.DatePaiement,
                    paiement.Montant,
                    paiement.ModePaiement,
                    paiement.ReferenceBancaire
                );
            }
        }

        private void UpdateCounters(List<Paiement> paiements)
        {
            var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
            var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
            var totalLabel = statusStrip?.Items.Find("totalLabel", false).FirstOrDefault() as ToolStripStatusLabel;

            if (countLabel != null)
            {
                countLabel.Text = $"Nombre de paiements : {paiements.Count}";
            }

            if (totalLabel != null)
            {
                decimal totalMontant = paiements.Sum(p => p.Montant);
                totalLabel.Text = $"Montant total : {totalMontant:C2}";
            }
        }

        private void ModeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void PeriodeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private async void SearchButton_Click(object sender, EventArgs e)
        {
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var searchTextBox = toolStrip?.Items.Find("searchTextBox", false).FirstOrDefault() as ToolStripTextBox;
            if (searchTextBox != null)
            {
                string searchTerm = searchTextBox.Text.Trim();
                await SearchPaiementsAsync(searchTerm);
            }
        }

        private async void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                e.SuppressKeyPress = true;

                var searchTextBox = sender as ToolStripTextBox;
                if (searchTextBox != null)
                {
                    string searchTerm = searchTextBox.Text.Trim();
                    await SearchPaiementsAsync(searchTerm);
                }
            }
        }

        private async Task SearchPaiementsAsync(string searchTerm)
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                if (string.IsNullOrEmpty(searchTerm))
                {
                    // Si la recherche est vide, charger tous les paiements
                    await LoadPaiementsAsync();
                }
                else
                {
                    // Rechercher les paiements par référence
                    var paiement = await _paiementService.GetByReferenceAsync(searchTerm);
                    if (paiement != null)
                    {
                        _paiements = new List<Paiement> { paiement };
                    }
                    else
                    {
                        _paiements = new List<Paiement>();
                    }

                    // Récupérer les noms des clients
                    await LoadClientNamesAsync();

                    // Appliquer les filtres
                    ApplyFilters();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la recherche des paiements");
                MessageBox.Show($"Une erreur s'est produite lors de la recherche des paiements : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            // Réinitialiser le champ de recherche
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var searchTextBox = toolStrip?.Items.Find("searchTextBox", false).FirstOrDefault() as ToolStripTextBox;
            if (searchTextBox != null)
            {
                searchTextBox.Text = string.Empty;
            }

            // Recharger les paiements
            await LoadPaiementsAsync();
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir le formulaire d'ajout de paiement
                using (var form = new PaiementEditForm(_paiementService, _clientService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les paiements
                        LoadPaiementsAsync().Wait();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout de paiement");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewButton_Click(object sender, EventArgs e)
        {
            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
            {
                int paiementId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                ViewPaiement(paiementId);
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un paiement à consulter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int paiementId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    ViewPaiement(paiementId);
                }
            }
        }

        private void ViewPaiement(int paiementId)
        {
            try
            {
                // Ouvrir le formulaire de détails de paiement
                using (var form = new PaiementDetailsForm(_paiementService, _clientService, _currentUserId, paiementId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de détails de paiement");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
