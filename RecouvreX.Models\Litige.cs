using System;
using System.Collections.Generic;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un litige sur une facture
    /// </summary>
    public class Litige : BaseEntity
    {
        /// <summary>
        /// Identifiant de la facture concernée
        /// </summary>
        public int FactureId { get; set; }

        /// <summary>
        /// Facture associée (navigation property)
        /// </summary>
        public Facture Facture { get; set; }

        /// <summary>
        /// Identifiant de la catégorie du litige
        /// </summary>
        public int CategorieLitigeId { get; set; }

        /// <summary>
        /// Catégorie du litige (navigation property)
        /// </summary>
        public CategorieLitige CategorieLitige { get; set; }

        /// <summary>
        /// Identifiant de l'étape actuelle du litige
        /// </summary>
        public int EtapeLitigeId { get; set; }

        /// <summary>
        /// Étape actuelle du litige (navigation property)
        /// </summary>
        public EtapeLitige EtapeLitige { get; set; }

        /// <summary>
        /// Montant contesté
        /// </summary>
        public decimal MontantConteste { get; set; }

        /// <summary>
        /// Description du litige
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Date d'ouverture du litige
        /// </summary>
        public DateTime DateOuverture { get; set; }

        /// <summary>
        /// Date de résolution du litige (null si non résolu)
        /// </summary>
        public DateTime? DateResolution { get; set; }

        /// <summary>
        /// Date d'échéance pour la résolution du litige
        /// </summary>
        public DateTime DateEcheance { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur responsable du litige
        /// </summary>
        public int ResponsableId { get; set; }

        /// <summary>
        /// Utilisateur responsable du litige (navigation property)
        /// </summary>
        public Utilisateur Responsable { get; set; }

        /// <summary>
        /// Priorité du litige (1 = Basse, 2 = Moyenne, 3 = Haute, 4 = Critique)
        /// </summary>
        public int Priorite { get; set; }

        /// <summary>
        /// Statut du litige (Ouvert, En cours, Résolu)
        /// </summary>
        public string Statut { get; set; }

        /// <summary>
        /// Solution appliquée pour résoudre le litige
        /// </summary>
        public string Solution { get; set; }

        /// <summary>
        /// Liste des commentaires associés au litige
        /// </summary>
        public List<CommentaireLitige> Commentaires { get; set; }

        /// <summary>
        /// Liste des documents associés au litige
        /// </summary>
        public List<DocumentLitige> Documents { get; set; }

        /// <summary>
        /// Liste des communications associées au litige
        /// </summary>
        public List<Communication> Communications { get; set; }

        /// <summary>
        /// Liste des historiques d'étapes du litige
        /// </summary>
        public List<HistoriqueEtapeLitige> HistoriqueEtapes { get; set; }

        /// <summary>
        /// Liste des notifications associées au litige
        /// </summary>
        public List<NotificationLitige> Notifications { get; set; }
    }
}
