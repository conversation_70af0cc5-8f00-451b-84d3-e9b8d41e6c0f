using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.Factures
{
    public partial class FactureListForm : Form
    {
        private readonly IFactureService _factureService;
        private readonly IClientService _clientService;
        private readonly IAuthenticationService _authenticationService;
        private readonly int _currentUserId;
        private readonly string _statutFiltre;
        private readonly bool _selectionMode;
        private List<Facture> _factures = new List<Facture>();
        private DataTable _dataTable = new DataTable();
        private Dictionary<int, string> _clientNames = new Dictionary<int, string>();

        public int? SelectedFactureId { get; private set; }

        public FactureListForm(IFactureService factureService, IClientService clientService, IAuthenticationService authenticationService, int currentUserId, string statutFiltre = null, bool selectionMode = false)
        {
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _currentUserId = currentUserId;
            _statutFiltre = statutFiltre;
            _selectionMode = selectionMode;

            InitializeComponent();

            // Si en mode sélection, ajuster le formulaire
            if (_selectionMode)
            {
                this.Text = "Sélectionner une facture";

                // Ajouter un bouton de sélection
                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                if (toolStrip != null)
                {
                    // Ajouter un séparateur
                    toolStrip.Items.Add(new ToolStripSeparator());

                    // Ajouter le bouton de sélection
                    var selectButton = new ToolStripButton();
                    selectButton.Name = "selectButton";
                    selectButton.Text = "Sélectionner";
                    selectButton.Image = null; // Ajouter une icône
                    selectButton.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
                    selectButton.Click += new EventHandler(this.SelectButton_Click);
                    toolStrip.Items.Add(selectButton);
                }
            }
        }



        private async void FactureListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Vérifier les permissions
                bool canView = await _authenticationService.HasPermissionAsync(_currentUserId, "FACTURES_VIEW");
                bool canAdd = await _authenticationService.HasPermissionAsync(_currentUserId, "FACTURES_ADD");

                if (!canView)
                {
                    MessageBox.Show("Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.",
                        "Accès refusé", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return;
                }

                // Configurer les boutons en fonction des permissions
                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                var addButton = toolStrip?.Items.Find("addButton", false).FirstOrDefault() as ToolStripButton;
                if (addButton != null) addButton.Enabled = canAdd;

                // Initialiser la table de données
                InitializeDataTable();

                // Charger les factures
                await LoadFacturesAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement de la liste des factures");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des factures : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Numéro", typeof(string));
            _dataTable.Columns.Add("Client", typeof(string));
            _dataTable.Columns.Add("Date Émission", typeof(DateTime));
            _dataTable.Columns.Add("Date Échéance", typeof(DateTime));
            _dataTable.Columns.Add("Montant TTC", typeof(decimal));
            _dataTable.Columns.Add("Montant Payé", typeof(decimal));
            _dataTable.Columns.Add("Montant Restant", typeof(decimal));
            _dataTable.Columns.Add("Statut", typeof(string));

            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (dataGridView.Columns["Date Émission"] != null)
                    dataGridView.Columns["Date Émission"].DefaultCellStyle.Format = "dd/MM/yyyy";
                if (dataGridView.Columns["Date Échéance"] != null)
                    dataGridView.Columns["Date Échéance"].DefaultCellStyle.Format = "dd/MM/yyyy";
                if (dataGridView.Columns["Montant TTC"] != null)
                    dataGridView.Columns["Montant TTC"].DefaultCellStyle.Format = "C2";
                if (dataGridView.Columns["Montant Payé"] != null)
                    dataGridView.Columns["Montant Payé"].DefaultCellStyle.Format = "C2";
                if (dataGridView.Columns["Montant Restant"] != null)
                    dataGridView.Columns["Montant Restant"].DefaultCellStyle.Format = "C2";
            }
        }

        private async Task LoadFacturesAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer les factures
                _factures = (await _factureService.GetAllAsync()).ToList();

                // Récupérer les noms des clients
                await LoadClientNamesAsync();

                // Appliquer le filtre de statut
                ApplyStatusFilter();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des factures");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des factures : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private async Task LoadClientNamesAsync()
        {
            try
            {
                // Récupérer les IDs des clients uniques
                var clientIds = _factures.Select(f => f.ClientId).Distinct().ToList();

                // Récupérer les noms des clients
                _clientNames.Clear();
                foreach (var clientId in clientIds)
                {
                    var client = await _clientService.GetByIdAsync(clientId);
                    if (client != null)
                    {
                        _clientNames[clientId] = client.RaisonSociale;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des noms des clients");
                throw;
            }
        }

        private void ApplyStatusFilter()
        {
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var statutComboBox = toolStrip?.Items.Find("statutComboBox", false).FirstOrDefault() as ToolStripComboBox;
            if (statutComboBox != null)
            {
                string selectedStatut = statutComboBox.SelectedItem?.ToString() ?? "Tous";

                // Filtrer les factures par statut
                List<Facture> filteredFactures;
                if (selectedStatut == "Tous")
                {
                    filteredFactures = _factures;
                }
                else
                {
                    filteredFactures = _factures.Where(f => f.Statut == selectedStatut).ToList();
                }

                // Mettre à jour la table de données
                UpdateDataTable(filteredFactures);

                // Mettre à jour les compteurs
                UpdateCounters(filteredFactures);
            }
        }

        private void UpdateDataTable(List<Facture> factures)
        {
            _dataTable.Clear();

            foreach (var facture in factures)
            {
                string clientName = _clientNames.ContainsKey(facture.ClientId) ? _clientNames[facture.ClientId] : "Client inconnu";

                _dataTable.Rows.Add(
                    facture.Id,
                    facture.Numero,
                    clientName,
                    facture.DateEmission,
                    facture.DateEcheance,
                    facture.MontantTTC,
                    facture.MontantPaye,
                    facture.MontantRestant,
                    facture.Statut
                );
            }
        }

        private void UpdateCounters(List<Facture> factures)
        {
            var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
            var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
            var totalLabel = statusStrip?.Items.Find("totalLabel", false).FirstOrDefault() as ToolStripStatusLabel;

            if (countLabel != null)
            {
                countLabel.Text = $"Nombre de factures : {factures.Count}";
            }

            if (totalLabel != null)
            {
                decimal totalMontantRestant = factures.Sum(f => f.MontantRestant);
                totalLabel.Text = $"Montant total restant : {totalMontantRestant:C2}";
            }
        }

        private void StatutComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyStatusFilter();
        }

        private async void SearchButton_Click(object sender, EventArgs e)
        {
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var searchTextBox = toolStrip?.Items.Find("searchTextBox", false).FirstOrDefault() as ToolStripTextBox;
            if (searchTextBox != null)
            {
                string searchTerm = searchTextBox.Text.Trim();
                await SearchFacturesAsync(searchTerm);
            }
        }

        private async void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                e.SuppressKeyPress = true;

                var searchTextBox = sender as ToolStripTextBox;
                if (searchTextBox != null)
                {
                    string searchTerm = searchTextBox.Text.Trim();
                    await SearchFacturesAsync(searchTerm);
                }
            }
        }

        private async Task SearchFacturesAsync(string searchTerm)
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                if (string.IsNullOrEmpty(searchTerm))
                {
                    // Si la recherche est vide, charger toutes les factures
                    await LoadFacturesAsync();
                }
                else
                {
                    // Rechercher les factures par numéro
                    var facture = await _factureService.GetByNumeroAsync(searchTerm);
                    if (facture != null)
                    {
                        _factures = new List<Facture> { facture };
                    }
                    else
                    {
                        _factures = new List<Facture>();
                    }

                    // Récupérer les noms des clients
                    await LoadClientNamesAsync();

                    // Appliquer le filtre de statut
                    ApplyStatusFilter();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la recherche des factures");
                MessageBox.Show($"Une erreur s'est produite lors de la recherche des factures : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            // Réinitialiser le champ de recherche
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var searchTextBox = toolStrip?.Items.Find("searchTextBox", false).FirstOrDefault() as ToolStripTextBox;
            if (searchTextBox != null)
            {
                searchTextBox.Text = string.Empty;
            }

            // Recharger les factures
            await LoadFacturesAsync();
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir le formulaire d'ajout de facture
                using (var form = new FactureEditForm(_factureService, _clientService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les factures
                        LoadFacturesAsync().Wait();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout de facture");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewButton_Click(object sender, EventArgs e)
        {
            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
            {
                int factureId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                ViewFacture(factureId);
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une facture à consulter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int factureId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);

                    if (_selectionMode)
                    {
                        // En mode sélection, retourner l'ID de la facture
                        SelectedFactureId = factureId;
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        // En mode normal, ouvrir le formulaire de détails
                        ViewFacture(factureId);
                    }
                }
            }
        }

        private void SelectButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer la facture sélectionnée
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int factureId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    SelectedFactureId = factureId;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une facture.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la sélection d'une facture");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewFacture(int factureId)
        {
            try
            {
                // Ouvrir le formulaire de détails de facture
                using (var form = new FactureDetailsForm(_factureService, _clientService, _currentUserId, factureId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de détails de facture");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
