using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface du repository pour les contacts
    /// </summary>
    public interface IContactRepository : IRepository<Contact>
    {
        /// <summary>
        /// Récupère tous les contacts d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des contacts du client</returns>
        Task<IEnumerable<Contact>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère le contact principal d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Contact principal du client ou null si aucun contact principal n'est défini</returns>
        Task<Contact> GetPrincipalContactAsync(int clientId);
    }
}
