using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.Litiges
{
    /// <summary>
    /// Formulaire de liste des étapes de litiges
    /// </summary>
    public partial class EtapeLitigeListForm : Form
    {
        private readonly ILitigeService _litigeService;
        private readonly int _currentUserId;
        private List<EtapeLitige> _etapes = new List<EtapeLitige>();

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="litigeService">Service de gestion des litiges</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        public EtapeLitigeListForm(ILitigeService litigeService, int currentUserId)
        {
            _litigeService = litigeService ?? throw new ArgumentNullException(nameof(litigeService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }

        // Les méthodes InitializeComponent() et ConfigureDataGridView() ont été déplacées dans le fichier EtapeLitigeListForm.Designer.cs

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void EtapeLitigeListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Charger l'icône du formulaire
                try
                {
                    this.Icon = new Icon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "dispute.ico"));
                }
                catch
                {
                    // Ignorer les erreurs d'icône
                }

                // Charger les étapes
                await LoadEtapesAsync();

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors du chargement de la liste des étapes de litiges");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Charge les étapes de litiges
        /// </summary>
        private async Task LoadEtapesAsync()
        {
            try
            {
                // Récupérer les étapes avec le nombre de litiges
                var etapes = await _litigeService.GetAllEtapesWithCountAsync();
                _etapes = etapes.ToList();

                // Afficher les étapes dans le DataGridView
                DisplayEtapes();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des étapes de litiges");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Affiche les étapes dans le DataGridView
        /// </summary>
        private void DisplayEtapes()
        {
            var dataGridView = this.Controls.Find("etapesDataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                // Créer une table de données
                var dataTable = new DataTable();
                dataTable.Columns.Add("Id", typeof(int));
                dataTable.Columns.Add("Ordre", typeof(int));
                dataTable.Columns.Add("Nom", typeof(string));
                dataTable.Columns.Add("Description", typeof(string));
                dataTable.Columns.Add("DelaiJours", typeof(int));
                dataTable.Columns.Add("EstEtapeFinale", typeof(bool));
                dataTable.Columns.Add("NecessiteValidation", typeof(bool));
                dataTable.Columns.Add("NombreLitiges", typeof(int));

                // Remplir la table avec les données des étapes
                foreach (var etape in _etapes.OrderBy(e => e.Ordre))
                {
                    dataTable.Rows.Add(
                        etape.Id,
                        etape.Ordre,
                        etape.Nom,
                        etape.Description,
                        etape.DelaiJours,
                        etape.EstEtapeFinale,
                        etape.NecessiteValidation,
                        etape.Litiges?.Count ?? 0
                    );
                }

                // Assigner la table au DataGridView
                dataGridView.DataSource = dataTable;
            }
        }

        /// <summary>
        /// Événement de double-clic sur une cellule du DataGridView
        /// </summary>
        private void EtapesDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditSelectedEtape();
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Nouvelle étape
        /// </summary>
        private void NewButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Créer un formulaire d'édition d'étape
                using (var form = new EtapeLitigeEditForm(_litigeService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les étapes
                        _ = LoadEtapesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la création d'une nouvelle étape de litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Modifier
        /// </summary>
        private void EditButton_Click(object sender, EventArgs e)
        {
            EditSelectedEtape();
        }

        /// <summary>
        /// Édite l'étape sélectionnée
        /// </summary>
        private void EditSelectedEtape()
        {
            try
            {
                var dataGridView = this.Controls.Find("etapesDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    // Récupérer l'ID de l'étape sélectionnée
                    int etapeId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);

                    // Récupérer l'étape correspondante
                    var etape = _etapes.FirstOrDefault(e => e.Id == etapeId);
                    if (etape != null)
                    {
                        // Créer un formulaire d'édition d'étape
                        using (var form = new EtapeLitigeEditForm(_litigeService, _currentUserId, etape))
                        {
                            if (form.ShowDialog() == DialogResult.OK)
                            {
                                // Recharger les étapes
                                _ = LoadEtapesAsync();
                            }
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une étape à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la modification d'une étape de litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Supprimer
        /// </summary>
        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = this.Controls.Find("etapesDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    // Récupérer l'ID de l'étape sélectionnée
                    int etapeId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    int nombreLitiges = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["NombreLitiges"].Value);

                    // Vérifier si l'étape est utilisée
                    if (nombreLitiges > 0)
                    {
                        MessageBox.Show(
                            "Cette étape est utilisée par des litiges et ne peut pas être supprimée.\nVeuillez d'abord modifier les litiges associés.",
                            "Suppression impossible",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Warning);
                        return;
                    }

                    // Demander confirmation
                    var result = MessageBox.Show(
                        "Êtes-vous sûr de vouloir supprimer cette étape ?",
                        "Confirmation",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Supprimer l'étape
                        await _litigeService.DeleteEtapeAsync(etapeId, _currentUserId);

                        // Recharger les étapes
                        await LoadEtapesAsync();

                        MessageBox.Show("L'étape a été supprimée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une étape à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression d'une étape de litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Rafraîchir
        /// </summary>
        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            await LoadEtapesAsync();
        }

        /// <summary>
        /// Événement de clic sur le bouton Fermer
        /// </summary>
        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
