using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RecouvreX.WinForms.Helpers
{
    /// <summary>
    /// Classe utilitaire pour les calculs et analyses du tableau de bord
    /// </summary>
    public static class DashboardHelper
    {
        /// <summary>
        /// Calcule le DSO (Days Sales Outstanding) - <PERSON><PERSON><PERSON> moyen de paiement des clients
        /// </summary>
        /// <param name="factures">Liste des factures</param>
        /// <param name="paiements">Liste des paiements</param>
        /// <returns>Nombre de jours moyen de paiement</returns>
        public static double CalculateDSO(IEnumerable<Facture> factures, IEnumerable<Paiement> paiements)
        {
            if (factures == null || !factures.Any() || paiements == null || !paiements.Any())
                return 0;

            // Filtrer les factures payées
            var facturesPayees = factures.Where(f => f.Statut == Models.Enums.StatutFacture.Payee).ToList();
            if (!facturesPayees.Any())
                return 0;

            // Calculer le délai moyen entre la date d'émission et la date de paiement
            double totalDays = 0;
            int count = 0;

            foreach (var facture in facturesPayees)
            {
                // Trouver le dernier paiement pour cette facture
                var paiementsFacture = paiements
                    .Where(p => p.FacturePaiements != null && 
                           p.FacturePaiements.Any(fp => fp.FactureId == facture.Id))
                    .OrderByDescending(p => p.DatePaiement)
                    .FirstOrDefault();

                if (paiementsFacture != null)
                {
                    // Calculer le nombre de jours entre l'émission et le paiement
                    var days = (paiementsFacture.DatePaiement - facture.DateEmission).TotalDays;
                    totalDays += days;
                    count++;
                }
            }

            return count > 0 ? Math.Round(totalDays / count, 1) : 0;
        }

        /// <summary>
        /// Calcule le taux de recouvrement global
        /// </summary>
        /// <param name="factures">Liste des factures</param>
        /// <returns>Taux de recouvrement en pourcentage</returns>
        public static double CalculateTauxRecouvrement(IEnumerable<Facture> factures)
        {
            if (factures == null || !factures.Any())
                return 0;

            decimal totalMontantTTC = factures.Sum(f => f.MontantTTC);
            decimal totalMontantPaye = factures.Sum(f => f.MontantPaye);

            if (totalMontantTTC == 0)
                return 0;

            return Math.Round((double)(totalMontantPaye / totalMontantTTC * 100), 2);
        }

        /// <summary>
        /// Calcule le taux de recouvrement par client
        /// </summary>
        /// <param name="factures">Liste des factures</param>
        /// <returns>Dictionnaire avec l'ID du client et son taux de recouvrement</returns>
        public static Dictionary<int, double> CalculateTauxRecouvrementParClient(IEnumerable<Facture> factures)
        {
            if (factures == null || !factures.Any())
                return new Dictionary<int, double>();

            var result = new Dictionary<int, double>();
            var facturesParClient = factures.GroupBy(f => f.ClientId);

            foreach (var groupe in facturesParClient)
            {
                decimal totalMontantTTC = groupe.Sum(f => f.MontantTTC);
                decimal totalMontantPaye = groupe.Sum(f => f.MontantPaye);

                if (totalMontantTTC > 0)
                {
                    double taux = Math.Round((double)(totalMontantPaye / totalMontantTTC * 100), 2);
                    result.Add(groupe.Key, taux);
                }
            }

            return result;
        }

        /// <summary>
        /// Calcule l'âge moyen des créances
        /// </summary>
        /// <param name="factures">Liste des factures</param>
        /// <returns>Âge moyen des créances en jours</returns>
        public static double CalculateAgeMoyenCreances(IEnumerable<Facture> factures)
        {
            if (factures == null || !factures.Any())
                return 0;

            // Filtrer les factures non payées ou partiellement payées
            var facturesNonPayees = factures.Where(f => 
                f.Statut != Models.Enums.StatutFacture.Payee && 
                f.Statut != Models.Enums.StatutFacture.Annulee).ToList();

            if (!facturesNonPayees.Any())
                return 0;

            // Calculer l'âge moyen des créances
            double totalDays = 0;
            DateTime today = DateTime.Today;

            foreach (var facture in facturesNonPayees)
            {
                // Calculer le nombre de jours depuis l'émission
                var days = (today - facture.DateEmission).TotalDays;
                totalDays += days;
            }

            return Math.Round(totalDays / facturesNonPayees.Count, 1);
        }

        /// <summary>
        /// Répartit les factures par tranche d'ancienneté
        /// </summary>
        /// <param name="factures">Liste des factures</param>
        /// <returns>Dictionnaire avec la tranche d'ancienneté et le montant total</returns>
        public static Dictionary<string, decimal> GetMontantParTrancheAnciennete(IEnumerable<Facture> factures)
        {
            if (factures == null || !factures.Any())
                return new Dictionary<string, decimal>();

            var result = new Dictionary<string, decimal>
            {
                { "0-30j", 0 },
                { "31-60j", 0 },
                { "61-90j", 0 },
                { ">90j", 0 }
            };

            // Filtrer les factures non payées ou partiellement payées
            var facturesNonPayees = factures.Where(f => 
                f.Statut != Models.Enums.StatutFacture.Payee && 
                f.Statut != Models.Enums.StatutFacture.Annulee).ToList();

            if (!facturesNonPayees.Any())
                return result;

            DateTime today = DateTime.Today;

            foreach (var facture in facturesNonPayees)
            {
                // Calculer le nombre de jours depuis l'émission
                var days = (today - facture.DateEmission).TotalDays;

                if (days <= 30)
                    result["0-30j"] += facture.MontantRestant;
                else if (days <= 60)
                    result["31-60j"] += facture.MontantRestant;
                else if (days <= 90)
                    result["61-90j"] += facture.MontantRestant;
                else
                    result[">90j"] += facture.MontantRestant;
            }

            return result;
        }
    }
}
