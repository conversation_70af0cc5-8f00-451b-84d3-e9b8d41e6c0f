-- Script pour ajouter la table ScoresRisqueClient
USE [RecouvreX]
GO

-- Vérifier si la table existe déjà
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[app].[ScoresRisqueClient]') AND type in (N'U'))
BEGIN
    -- Créer la table ScoresRisqueClient
    CREATE TABLE [app].[ScoresRisqueClient] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [ClientId] INT NOT NULL,
        [Score] INT NOT NULL,
        [Categorie] NVARCHAR(1) NOT NULL,
        [DateMiseAJour] DATETIME NOT NULL,
        [FacteursRisque] NVARCHAR(MAX) NULL,
        [DelaiMoyenPaiement] FLOAT NOT NULL DEFAULT 0,
        [PourcentageFacturesRetard] FLOAT NOT NULL DEFAULT 0,
        [MontantTotalRetard] DECIMAL(18, 2) NOT NULL DEFAULT 0,
        [NombreMoyenRelances] FLOAT NOT NULL DEFAULT 0,
        [DateCreation] DATETIME NOT NULL DEFAULT GETDATE(),
        [CreePar] INT NOT NULL,
        [DateModification] DATETIME NULL,
        [ModifiePar] INT NULL,
        [EstActif] BIT NOT NULL DEFAULT 1,
        CONSTRAINT [FK_ScoresRisqueClient_Clients] FOREIGN KEY ([ClientId]) REFERENCES [app].[Clients] ([Id])
    );

    -- Ajouter un index sur ClientId
    CREATE INDEX [IX_ScoresRisqueClient_ClientId] ON [app].[ScoresRisqueClient] ([ClientId]);

    -- Ajouter un index sur Categorie
    CREATE INDEX [IX_ScoresRisqueClient_Categorie] ON [app].[ScoresRisqueClient] ([Categorie]);

    PRINT 'Table ScoresRisqueClient créée avec succès.';
END
ELSE
BEGIN
    PRINT 'La table ScoresRisqueClient existe déjà.';
END
GO
