using RecouvreX.Models;
using System.Data;

namespace RecouvreX.WinForms.Forms.Common
{
    /// <summary>
    /// Formulaire de sélection d'une facture
    /// </summary>
    public partial class SelectionFactureForm : Form
    {
        private readonly List<Facture> _factures;
        private readonly decimal _montantDisponible;
        private DataTable _dataTable = new DataTable();

        /// <summary>
        /// Identifiant de la facture sélectionnée
        /// </summary>
        public int? SelectedFactureId { get; private set; }

        /// <summary>
        /// Montant affecté à la facture sélectionnée
        /// </summary>
        public decimal? MontantAffecte { get; private set; }

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="factures">Liste des factures disponibles</param>
        /// <param name="montantDisponible">Montant disponible pour l'affectation</param>
        public SelectionFactureForm(List<Facture> factures, decimal montantDisponible)
        {
            _factures = factures ?? throw new ArgumentNullException(nameof(factures));
            _montantDisponible = montantDisponible;

            InitializeComponent();
        }

        private void SelectionFactureForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialiser la table de données
                InitializeDataTable();

                // Remplir la table avec les factures
                UpdateDataTable(_factures);

                // Mettre à jour le label du montant disponible
                var montantDisponibleLabel = this.Controls.Find("montantDisponibleLabel", true).FirstOrDefault() as Label;
                if (montantDisponibleLabel != null)
                {
                    montantDisponibleLabel.Text = $"Montant disponible : {_montantDisponible:C2}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Numéro", typeof(string));
            _dataTable.Columns.Add("Date Émission", typeof(DateTime));
            _dataTable.Columns.Add("Date Échéance", typeof(DateTime));
            _dataTable.Columns.Add("Montant TTC", typeof(decimal));
            _dataTable.Columns.Add("Montant Restant", typeof(decimal));
            _dataTable.Columns.Add("Statut", typeof(string));

            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (dataGridView.Columns["Date Émission"] != null)
                    dataGridView.Columns["Date Émission"].DefaultCellStyle.Format = "dd/MM/yyyy";
                if (dataGridView.Columns["Date Échéance"] != null)
                    dataGridView.Columns["Date Échéance"].DefaultCellStyle.Format = "dd/MM/yyyy";
                if (dataGridView.Columns["Montant TTC"] != null)
                    dataGridView.Columns["Montant TTC"].DefaultCellStyle.Format = "C2";
                if (dataGridView.Columns["Montant Restant"] != null)
                    dataGridView.Columns["Montant Restant"].DefaultCellStyle.Format = "C2";
            }
        }

        private void UpdateDataTable(List<Facture> factures)
        {
            _dataTable.Clear();

            foreach (var facture in factures)
            {
                _dataTable.Rows.Add(
                    facture.Id,
                    facture.Numero,
                    facture.DateEmission,
                    facture.DateEcheance,
                    facture.MontantTTC,
                    facture.MontantRestant,
                    facture.Statut
                );
            }
        }

        private void SelectButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer la facture sélectionnée
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int factureId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    decimal montantRestant = Convert.ToDecimal(dataGridView.SelectedRows[0].Cells["Montant Restant"].Value);

                    // Récupérer le montant à affecter
                    var montantNumericUpDown = this.Controls.Find("montantNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                    if (montantNumericUpDown != null)
                    {
                        decimal montantAffecte = montantNumericUpDown.Value;

                        // Vérifier que le montant est valide
                        if (montantAffecte <= 0)
                        {
                            MessageBox.Show("Le montant doit être supérieur à zéro.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        if (montantAffecte > _montantDisponible)
                        {
                            MessageBox.Show($"Le montant ne peut pas dépasser le montant disponible ({_montantDisponible:C2}).",
                                "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        if (montantAffecte > montantRestant)
                        {
                            MessageBox.Show($"Le montant ne peut pas dépasser le montant restant sur la facture ({montantRestant:C2}).",
                                "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        // Tout est OK, retourner les valeurs
                        SelectedFactureId = factureId;
                        MontantAffecte = montantAffecte;
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une facture.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void DataGridView_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    decimal montantRestant = Convert.ToDecimal(dataGridView.SelectedRows[0].Cells["Montant Restant"].Value);
                    
                    // Mettre à jour le montant suggéré
                    var montantNumericUpDown = this.Controls.Find("montantNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                    if (montantNumericUpDown != null)
                    {
                        decimal montantSuggere = Math.Min(_montantDisponible, montantRestant);
                        montantNumericUpDown.Maximum = montantSuggere;
                        montantNumericUpDown.Value = montantSuggere;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
