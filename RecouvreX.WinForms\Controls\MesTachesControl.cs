using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Controls
{
    /// <summary>
    /// Contrôle utilisateur pour afficher les tâches prioritaires assignées à l'utilisateur
    /// </summary>
    public partial class MesTachesControl : UserControl
    {
        private readonly IActionPrioritaireService _actionPrioritaireService;
        private readonly IUtilisateurService _utilisateurService;
        private readonly int _currentUserId;
        private DataTable _tachesDataTable;
        private List<ActionPrioritaire> _taches;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="actionPrioritaireService">Service de gestion des actions prioritaires</param>
        /// <param name="utilisateurService">Service de gestion des utilisateurs</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        public MesTachesControl(
            IActionPrioritaireService actionPrioritaireService,
            IUtilisateurService utilisateurService,
            int currentUserId)
        {
            _actionPrioritaireService = actionPrioritaireService ?? throw new ArgumentNullException(nameof(actionPrioritaireService));
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _currentUserId = currentUserId;

            InitializeComponent();
            InitializeDataTable();

            // Charger les données au chargement du contrôle
            this.Load += async (s, e) => await LoadDataAsync();
        }

        /// <summary>
        /// Initialise la table de données
        /// </summary>
        private void InitializeDataTable()
        {
            _tachesDataTable = new DataTable();
            _tachesDataTable.Columns.Add("Id", typeof(int));
            _tachesDataTable.Columns.Add("Client", typeof(string));
            _tachesDataTable.Columns.Add("Description", typeof(string));
            _tachesDataTable.Columns.Add("TypeAction", typeof(string));
            _tachesDataTable.Columns.Add("NiveauPriorite", typeof(string));
            _tachesDataTable.Columns.Add("DateEcheance", typeof(DateTime));
            _tachesDataTable.Columns.Add("Statut", typeof(string));
            _tachesDataTable.Columns.Add("ScorePriorite", typeof(int));

            // Configurer la source de données du DataGridView
            tachesDataGridView.DataSource = _tachesDataTable;
        }

        /// <summary>
        /// Charge les données des tâches prioritaires
        /// </summary>
        public async Task LoadDataAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Récupérer les tâches prioritaires assignées à l'utilisateur courant
                _taches = (await _actionPrioritaireService.GetByUtilisateurAssigneIdAsync(_currentUserId))
                    .Where(a => !a.EstCompletee)
                    .OrderByDescending(a => a.ScorePriorite)
                    .ThenBy(a => a.DateEcheance)
                    .ToList();

                // Mettre à jour la table de données
                UpdateDataTable(_taches);

                // Mettre à jour le compteur
                countLabel.Text = $"{_taches.Count} tâche(s) prioritaire(s)";

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des tâches prioritaires");
                MessageBox.Show($"Erreur lors du chargement des tâches prioritaires : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Met à jour la table de données avec les tâches
        /// </summary>
        private void UpdateDataTable(List<ActionPrioritaire> taches)
        {
            // Effacer les données existantes
            _tachesDataTable.Rows.Clear();

            // Ajouter les nouvelles données
            foreach (var tache in taches)
            {
                string statut = tache.EstCompletee ? "Complétée" : (tache.DateEcheance < DateTime.Now ? "En retard" : "À faire");

                _tachesDataTable.Rows.Add(
                    tache.Id,
                    tache.Client?.RaisonSociale,
                    tache.Description,
                    tache.TypeAction.ToString(),
                    tache.NiveauPriorite.ToString(),
                    tache.DateEcheance,
                    statut,
                    tache.ScorePriorite
                );
            }

            // Configurer les colonnes
            if (tachesDataGridView.Columns.Count > 0)
            {
                tachesDataGridView.Columns["Id"].Visible = false;
                tachesDataGridView.Columns["ScorePriorite"].Visible = false;
                tachesDataGridView.Columns["Client"].HeaderText = "Client";
                tachesDataGridView.Columns["Description"].HeaderText = "Description";
                tachesDataGridView.Columns["TypeAction"].HeaderText = "Type d'action";
                tachesDataGridView.Columns["NiveauPriorite"].HeaderText = "Priorité";
                tachesDataGridView.Columns["DateEcheance"].HeaderText = "Échéance";
                tachesDataGridView.Columns["Statut"].HeaderText = "Statut";

                // Ajuster la largeur des colonnes
                tachesDataGridView.Columns["Client"].Width = 150;
                tachesDataGridView.Columns["Description"].Width = 300;
                tachesDataGridView.Columns["TypeAction"].Width = 120;
                tachesDataGridView.Columns["NiveauPriorite"].Width = 80;
                tachesDataGridView.Columns["DateEcheance"].Width = 100;
                tachesDataGridView.Columns["Statut"].Width = 80;

                // Formater les dates
                tachesDataGridView.Columns["DateEcheance"].DefaultCellStyle.Format = "dd/MM/yyyy";
            }
        }

        /// <summary>
        /// Gère le formatage des cellules du DataGridView
        /// </summary>
        private void TachesDataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            // Colorer les cellules selon le niveau de priorité
            if (tachesDataGridView.Columns[e.ColumnIndex].Name == "NiveauPriorite" && e.Value != null)
            {
                string priorite = e.Value.ToString();
                switch (priorite)
                {
                    case "Critique":
                        e.CellStyle.BackColor = Color.Red;
                        e.CellStyle.ForeColor = Color.White;
                        break;
                    case "Haute":
                        e.CellStyle.BackColor = Color.Orange;
                        e.CellStyle.ForeColor = Color.Black;
                        break;
                    case "Moyenne":
                        e.CellStyle.BackColor = Color.Yellow;
                        e.CellStyle.ForeColor = Color.Black;
                        break;
                    case "Basse":
                        e.CellStyle.BackColor = Color.LightGreen;
                        e.CellStyle.ForeColor = Color.Black;
                        break;
                }
            }

            // Colorer les cellules selon le statut
            if (tachesDataGridView.Columns[e.ColumnIndex].Name == "Statut" && e.Value != null)
            {
                string statut = e.Value.ToString();
                switch (statut)
                {
                    case "En retard":
                        e.CellStyle.BackColor = Color.Red;
                        e.CellStyle.ForeColor = Color.White;
                        break;
                    case "À faire":
                        e.CellStyle.BackColor = Color.LightBlue;
                        e.CellStyle.ForeColor = Color.Black;
                        break;
                    case "Complétée":
                        e.CellStyle.BackColor = Color.LightGreen;
                        e.CellStyle.ForeColor = Color.Black;
                        break;
                }
            }
        }

        /// <summary>
        /// Gère le clic sur le bouton "Marquer comme complétée"
        /// </summary>
        private async void CompleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Vérifier qu'une tâche est sélectionnée
                if (tachesDataGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("Veuillez sélectionner une tâche à marquer comme complétée.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Récupérer l'ID de la tâche sélectionnée
                int tacheId = (int)tachesDataGridView.SelectedRows[0].Cells["Id"].Value;

                // Demander confirmation
                var result = MessageBox.Show(
                    "Voulez-vous marquer cette tâche comme complétée ?",
                    "Confirmation",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Afficher un indicateur de chargement
                    this.Cursor = Cursors.WaitCursor;

                    // Marquer la tâche comme complétée
                    await _actionPrioritaireService.CompleteActionAsync(tacheId, _currentUserId);

                    // Recharger les données
                    await LoadDataAsync();

                    // Afficher un message de confirmation
                    MessageBox.Show("La tâche a été marquée comme complétée avec succès.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du marquage d'une tâche comme complétée");
                MessageBox.Show($"Erreur lors du marquage d'une tâche comme complétée : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Gère le clic sur le bouton "Voir toutes les tâches"
        /// </summary>
        private void ViewAllButton_Click(object sender, EventArgs e)
        {
            // Ouvrir le formulaire de liste des actions prioritaires
            var actionPrioritaireListForm = new Forms.Actions.ActionPrioritaireListForm(
                _actionPrioritaireService,
                null, // ClientService n'est pas disponible dans ce contexte, mais nous devons le passer
                _utilisateurService,
                _currentUserId);

            actionPrioritaireListForm.ShowDialog();

            // Recharger les données après la fermeture du formulaire
            _ = LoadDataAsync();
        }
    }
}
