using Dapper;
using Microsoft.Extensions.Configuration;
using RecouvreX.Data.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;
using System.Data.SqlClient;

namespace RecouvreX.Data.Repositories
{
    /// <summary>
    /// Repository pour les historiques d'étapes de litiges
    /// </summary>
    public class HistoriqueEtapeLitigeRepository : IHistoriqueEtapeLitigeRepository
    {
        private readonly string _connectionString;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="configuration">Configuration de l'application</param>
        public HistoriqueEtapeLitigeRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        /// <summary>
        /// Récupère tous les historiques d'étapes
        /// </summary>
        /// <returns>Liste de tous les historiques d'étapes</returns>
        public async Task<IEnumerable<HistoriqueEtapeLitige>> GetAllAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT h.*, l.*, e.*, u.*
                        FROM HistoriqueEtapesLitige h
                        LEFT JOIN Litiges l ON h.LitigeId = l.Id
                        LEFT JOIN EtapesLitige e ON h.EtapeLitigeId = e.Id
                        LEFT JOIN Utilisateurs u ON h.UtilisateurId = u.Id
                        ORDER BY h.DateChangement DESC";

                    var historiqueDict = new Dictionary<int, HistoriqueEtapeLitige>();

                    var historiques = await connection.QueryAsync<HistoriqueEtapeLitige, Litige, EtapeLitige, Utilisateur, HistoriqueEtapeLitige>(
                        query,
                        (historique, litige, etape, utilisateur) =>
                        {
                            if (!historiqueDict.TryGetValue(historique.Id, out var existingHistorique))
                            {
                                existingHistorique = historique;
                                existingHistorique.Litige = litige;
                                existingHistorique.EtapeLitige = etape;
                                existingHistorique.Utilisateur = utilisateur;
                                historiqueDict.Add(existingHistorique.Id, existingHistorique);
                            }

                            return existingHistorique;
                        },
                        splitOn: "Id,Id,Id"
                    );

                    return historiqueDict.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération de tous les historiques d'étapes");
                throw;
            }
        }

        /// <summary>
        /// Récupère un historique d'étape par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'historique d'étape</param>
        /// <returns>Historique d'étape trouvé ou null</returns>
        public async Task<HistoriqueEtapeLitige> GetByIdAsync(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT h.*, l.*, e.*, u.*
                        FROM HistoriqueEtapesLitige h
                        LEFT JOIN Litiges l ON h.LitigeId = l.Id
                        LEFT JOIN EtapesLitige e ON h.EtapeLitigeId = e.Id
                        LEFT JOIN Utilisateurs u ON h.UtilisateurId = u.Id
                        WHERE h.Id = @Id";

                    var historiques = await connection.QueryAsync<HistoriqueEtapeLitige, Litige, EtapeLitige, Utilisateur, HistoriqueEtapeLitige>(
                        query,
                        (historique, litige, etape, utilisateur) =>
                        {
                            historique.Litige = litige;
                            historique.EtapeLitige = etape;
                            historique.Utilisateur = utilisateur;
                            return historique;
                        },
                        new { Id = id },
                        splitOn: "Id,Id,Id"
                    );

                    return historiques.FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération de l'historique d'étape {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Récupère tous les historiques d'étapes pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des historiques d'étapes du litige</returns>
        public async Task<IEnumerable<HistoriqueEtapeLitige>> GetByLitigeIdAsync(int litigeId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT h.*, l.*, e.*, u.*
                        FROM HistoriqueEtapesLitige h
                        LEFT JOIN Litiges l ON h.LitigeId = l.Id
                        LEFT JOIN EtapesLitige e ON h.EtapeLitigeId = e.Id
                        LEFT JOIN Utilisateurs u ON h.UtilisateurId = u.Id
                        WHERE h.LitigeId = @LitigeId
                        ORDER BY h.DateChangement DESC";

                    var historiqueDict = new Dictionary<int, HistoriqueEtapeLitige>();

                    var historiques = await connection.QueryAsync<HistoriqueEtapeLitige, Litige, EtapeLitige, Utilisateur, HistoriqueEtapeLitige>(
                        query,
                        (historique, litige, etape, utilisateur) =>
                        {
                            if (!historiqueDict.TryGetValue(historique.Id, out var existingHistorique))
                            {
                                existingHistorique = historique;
                                existingHistorique.Litige = litige;
                                existingHistorique.EtapeLitige = etape;
                                existingHistorique.Utilisateur = utilisateur;
                                historiqueDict.Add(existingHistorique.Id, existingHistorique);
                            }

                            return existingHistorique;
                        },
                        new { LitigeId = litigeId },
                        splitOn: "Id,Id,Id"
                    );

                    return historiqueDict.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des historiques d'étapes pour le litige {LitigeId}", litigeId);
                throw;
            }
        }

        /// <summary>
        /// Ajoute un historique d'étape
        /// </summary>
        /// <param name="historiqueEtape">Historique d'étape à ajouter</param>
        /// <returns>Historique d'étape ajouté</returns>
        public async Task<HistoriqueEtapeLitige> AddAsync(HistoriqueEtapeLitige historiqueEtape)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        INSERT INTO HistoriqueEtapesLitige (LitigeId, EtapeLitigeId, DateChangement, Commentaire, UtilisateurId)
                        VALUES (@LitigeId, @EtapeLitigeId, @DateChangement, @Commentaire, @UtilisateurId);
                        SELECT CAST(SCOPE_IDENTITY() as int)";

                    var id = await connection.QuerySingleAsync<int>(query, new
                    {
                        historiqueEtape.LitigeId,
                        historiqueEtape.EtapeLitigeId,
                        DateChangement = historiqueEtape.DateChangement,
                        historiqueEtape.Commentaire,
                        historiqueEtape.UtilisateurId
                    });

                    historiqueEtape.Id = id;
                    return historiqueEtape;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ajout d'un historique d'étape");
                throw;
            }
        }

        /// <summary>
        /// Met à jour un historique d'étape
        /// </summary>
        /// <param name="historiqueEtape">Historique d'étape à mettre à jour</param>
        /// <returns>True si l'historique d'étape a été mis à jour</returns>
        public async Task<bool> UpdateAsync(HistoriqueEtapeLitige historiqueEtape)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        UPDATE HistoriqueEtapesLitige
                        SET DateChangement = @DateChangement,
                            Commentaire = @Commentaire
                        WHERE Id = @Id";

                    var rowsAffected = await connection.ExecuteAsync(query, new
                    {
                        historiqueEtape.Id,
                        DateChangement = historiqueEtape.DateChangement,
                        historiqueEtape.Commentaire
                    });

                    return rowsAffected > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour de l'historique d'étape {Id}", historiqueEtape.Id);
                throw;
            }
        }
    }
}
