# Guide de Développement - RecouvreX

Ce guide est destiné aux développeurs qui souhaitent contribuer au projet RecouvreX ou le maintenir.

## Table des matières

1. [Architecture du projet](#architecture-du-projet)
2. [Environnement de développement](#environnement-de-développement)
3. [Structure du code](#structure-du-code)
4. [Conventions de codage](#conventions-de-codage)
5. [Base de données](#base-de-données)
6. [Tests](#tests)
7. [Déploiement](#déploiement)
8. [Bonnes pratiques](#bonnes-pratiques)

## Architecture du projet

RecouvreX est une application Windows Forms développée en .NET 6+ avec une architecture multi-couches :

```
RecouvreX
├── RecouvreX.WinForms     # Couche présentation (UI)
├── RecouvreX.Business     # Couche métier (services)
├── RecouvreX.DataAccess   # Couche d'accès aux données (repositories)
├── RecouvreX.Models       # Modèles de données
└── RecouvreX.Tests        # Tests unitaires
```

### Couche Présentation (RecouvreX.WinForms)

La couche présentation contient l'interface utilisateur Windows Forms. Elle est responsable de :
- L'affichage des données à l'utilisateur
- La capture des entrées utilisateur
- La validation des entrées utilisateur
- L'appel aux services métier pour traiter les données

### Couche Métier (RecouvreX.Business)

La couche métier contient la logique métier de l'application. Elle est responsable de :
- L'implémentation des règles métier
- La coordination entre la couche présentation et la couche d'accès aux données
- La validation des données métier

### Couche d'Accès aux Données (RecouvreX.DataAccess)

La couche d'accès aux données est responsable de :
- L'accès à la base de données
- L'exécution des requêtes SQL
- La conversion des données de la base de données en objets métier

### Couche Modèles (RecouvreX.Models)

La couche modèles contient les classes qui représentent les entités métier de l'application.

## Environnement de développement

### Prérequis

- Visual Studio 2022 ou supérieur
- .NET 6 SDK ou supérieur
- SQL Server Express 2019 ou supérieur
- Git

### Configuration de l'environnement

1. Clonez le dépôt Git :
   ```
   git clone https://github.com/votre-organisation/RecouvreX.git
   ```

2. Ouvrez la solution `RecouvreX.sln` dans Visual Studio.

3. Restaurez les packages NuGet :
   - Clic droit sur la solution dans l'Explorateur de solutions
   - Sélectionnez "Restaurer les packages NuGet"

4. Créez la base de données de développement :
   - Ouvrez SQL Server Management Studio
   - Connectez-vous à votre instance SQL Server locale
   - Exécutez les scripts SQL dans le dossier `scripts`

5. Configurez la chaîne de connexion :
   - Ouvrez le fichier `appsettings.Development.json` dans le projet `RecouvreX.WinForms`
   - Modifiez la chaîne de connexion selon votre configuration locale

6. Compilez la solution :
   - Dans Visual Studio, sélectionnez "Générer" > "Générer la solution"

7. Exécutez l'application :
   - Définissez `RecouvreX.WinForms` comme projet de démarrage
   - Appuyez sur F5 pour démarrer l'application en mode débogage

## Structure du code

### RecouvreX.WinForms

```
RecouvreX.WinForms
├── Forms                  # Formulaires Windows Forms
│   ├── Clients            # Formulaires liés aux clients
│   ├── Factures           # Formulaires liés aux factures
│   ├── Paiements          # Formulaires liés aux paiements
│   ├── Relances           # Formulaires liés aux relances
│   ├── Rapports           # Formulaires liés aux rapports
│   └── Administration     # Formulaires d'administration
├── Controls               # Contrôles utilisateur personnalisés
├── Helpers                # Classes utilitaires pour l'UI
├── Resources              # Ressources (images, icônes, etc.)
└── Program.cs             # Point d'entrée de l'application
```

### RecouvreX.Business

```
RecouvreX.Business
├── Interfaces             # Interfaces des services
├── Services               # Implémentations des services
├── Validators             # Validateurs de données
└── Helpers                # Classes utilitaires pour la logique métier
```

### RecouvreX.DataAccess

```
RecouvreX.DataAccess
├── Interfaces             # Interfaces des repositories
├── Repositories           # Implémentations des repositories
├── Context                # Contexte de base de données
└── Helpers                # Classes utilitaires pour l'accès aux données
```

### RecouvreX.Models

```
RecouvreX.Models
├── Client.cs              # Modèle de client
├── Contact.cs             # Modèle de contact
├── Facture.cs             # Modèle de facture
├── Paiement.cs            # Modèle de paiement
├── Relance.cs             # Modèle de relance
├── Utilisateur.cs         # Modèle d'utilisateur
├── Role.cs                # Modèle de rôle
├── Permission.cs          # Modèle de permission
└── AuditLog.cs            # Modèle d'audit
```

## Conventions de codage

### Nommage

- **Classes** : PascalCase (ex: `ClientService`)
- **Interfaces** : Préfixe I + PascalCase (ex: `IClientService`)
- **Méthodes** : PascalCase (ex: `GetAllClients`)
- **Variables locales** : camelCase (ex: `clientList`)
- **Champs privés** : Préfixe _ + camelCase (ex: `_clientRepository`)
- **Propriétés** : PascalCase (ex: `RaisonSociale`)
- **Constantes** : UPPER_CASE (ex: `MAX_RETRY_COUNT`)

### Organisation du code

- Une classe par fichier
- Regroupement des méthodes par fonctionnalité
- Ordre des membres dans une classe :
  1. Champs privés
  2. Constructeurs
  3. Propriétés
  4. Méthodes publiques
  5. Méthodes privées

### Documentation

- Commentaires XML pour les classes, interfaces, méthodes et propriétés publiques
- Commentaires en ligne pour expliquer les parties complexes du code

Exemple :
```csharp
/// <summary>
/// Service de gestion des clients.
/// </summary>
public class ClientService : IClientService
{
    private readonly IClientRepository _clientRepository;
    private readonly IAuditService _auditService;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="ClientService"/>.
    /// </summary>
    /// <param name="clientRepository">Repository de clients.</param>
    /// <param name="auditService">Service d'audit.</param>
    public ClientService(IClientRepository clientRepository, IAuditService auditService)
    {
        _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
        _auditService = auditService ?? throw new ArgumentNullException(nameof(auditService));
    }

    /// <summary>
    /// Récupère tous les clients.
    /// </summary>
    /// <returns>Liste des clients.</returns>
    public async Task<IEnumerable<Client>> GetAllAsync()
    {
        return await _clientRepository.GetAllAsync();
    }
}
```

## Base de données

### Schéma de la base de données

La base de données RecouvreX comprend les tables suivantes :

- **Clients** : Informations sur les clients
- **Contacts** : Contacts associés aux clients
- **Factures** : Factures émises aux clients
- **Paiements** : Paiements reçus des clients
- **Relances** : Relances effectuées pour les factures en retard
- **Utilisateurs** : Utilisateurs de l'application
- **Roles** : Rôles des utilisateurs
- **Permissions** : Permissions associées aux rôles
- **RolePermissions** : Table de jointure entre rôles et permissions
- **AuditLogs** : Journal des actions effectuées dans l'application

### Accès aux données

L'accès aux données se fait via Dapper ORM. Chaque entité a son propre repository qui implémente les opérations CRUD.

Exemple d'utilisation de Dapper :
```csharp
public async Task<IEnumerable<Client>> GetAllAsync()
{
    using (var connection = _context.CreateConnection())
    {
        return await connection.QueryAsync<Client>("SELECT * FROM Clients");
    }
}
```

## Tests

### Tests unitaires

Les tests unitaires sont écrits avec xUnit et Moq. Ils se trouvent dans le projet `RecouvreX.Tests`.

Exemple de test unitaire :
```csharp
public class ClientServiceTests
{
    private readonly Mock<IClientRepository> _mockClientRepository;
    private readonly Mock<IAuditService> _mockAuditService;
    private readonly IClientService _clientService;

    public ClientServiceTests()
    {
        _mockClientRepository = new Mock<IClientRepository>();
        _mockAuditService = new Mock<IAuditService>();
        _clientService = new ClientService(_mockClientRepository.Object, _mockAuditService.Object);
    }

    [Fact]
    public async Task GetAllAsync_ShouldReturnAllClients()
    {
        // Arrange
        var expectedClients = new List<Client>
        {
            new Client { Id = 1, RaisonSociale = "Client 1" },
            new Client { Id = 2, RaisonSociale = "Client 2" }
        };

        _mockClientRepository.Setup(repo => repo.GetAllAsync())
            .ReturnsAsync(expectedClients);

        // Act
        var result = await _clientService.GetAllAsync();

        // Assert
        Assert.Equal(expectedClients, result);
    }
}
```

### Exécution des tests

Pour exécuter les tests unitaires :
1. Dans Visual Studio, ouvrez l'Explorateur de tests (Test > Explorateur de tests)
2. Cliquez sur "Exécuter tout" pour exécuter tous les tests
3. Ou cliquez sur "Exécuter" à côté d'un test spécifique pour l'exécuter individuellement

## Déploiement

### Création d'un package de déploiement

1. Dans Visual Studio, cliquez avec le bouton droit sur le projet `RecouvreX.WinForms`
2. Sélectionnez "Publier..."
3. Configurez les options de publication selon vos besoins
4. Cliquez sur "Publier" pour créer le package de déploiement

### Déploiement manuel

1. Copiez les fichiers publiés sur le serveur ou le poste cible
2. Configurez le fichier `appsettings.json` avec les paramètres appropriés
3. Créez un raccourci vers l'exécutable principal

## Bonnes pratiques

### Gestion des exceptions

- Utilisez des blocs try-catch pour gérer les exceptions
- Journalisez les exceptions avec Serilog
- Affichez des messages d'erreur conviviaux à l'utilisateur

Exemple :
```csharp
try
{
    await _clientService.CreateAsync(client, userId);
    MessageBox.Show("Le client a été créé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    this.DialogResult = DialogResult.OK;
    this.Close();
}
catch (Exception ex)
{
    Log.Error(ex, "Erreur lors de la création du client");
    MessageBox.Show($"Une erreur s'est produite lors de la création du client : {ex.Message}", 
        "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
}
```

### Injection de dépendances

- Utilisez l'injection de dépendances pour découpler les composants
- Configurez les services dans la méthode `ConfigureServices` du fichier `Program.cs`

Exemple :
```csharp
var services = new ServiceCollection();

// Configuration des services
services.AddSingleton<IDbContext, DbContext>();
services.AddScoped<IClientRepository, ClientRepository>();
services.AddScoped<IClientService, ClientService>();
// ...

// Construction du conteneur
var serviceProvider = services.BuildServiceProvider();
```

### Validation des données

- Validez les données d'entrée dans la couche présentation
- Validez les données métier dans la couche métier
- Utilisez des validateurs dédiés pour les validations complexes

### Journalisation

- Utilisez Serilog pour la journalisation
- Journalisez les informations importantes, les avertissements et les erreurs
- Configurez différents niveaux de journalisation selon l'environnement

Exemple :
```csharp
// Information
Log.Information("Utilisateur {UserId} connecté", userId);

// Avertissement
Log.Warning("Tentative de connexion échouée pour l'utilisateur {Username}", username);

// Erreur
Log.Error(ex, "Erreur lors de la création du client");
```

### Sécurité

- Stockez les mots de passe hachés avec BCrypt
- Validez toutes les entrées utilisateur
- Utilisez des requêtes paramétrées pour éviter les injections SQL
- Vérifiez les permissions avant d'exécuter des actions sensibles
