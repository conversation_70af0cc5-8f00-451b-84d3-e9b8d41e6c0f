using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour la gestion des planifications de relance
    /// </summary>
    public class PlanificationRelanceRepository : BaseRepository<PlanificationRelance>, IPlanificationRelanceRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public PlanificationRelanceRepository(DatabaseConnection dbConnection) : base(dbConnection, "PlanificationsRelance")
        {
        }

        /// <summary>
        /// Récupère les planifications de relance par facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des planifications de relance pour la facture spécifiée</returns>
        public async Task<IEnumerable<PlanificationRelance>> GetByFactureIdAsync(int factureId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE FactureId = @FactureId AND EstActif = 1 ORDER BY DatePrevue";
                return await connection.QueryAsync<PlanificationRelance>(query, new { FactureId = factureId });
            }
        }

        /// <summary>
        /// Récupère les planifications de relance par statut
        /// </summary>
        /// <param name="statut">Statut des planifications</param>
        /// <returns>Liste des planifications de relance avec le statut spécifié</returns>
        public async Task<IEnumerable<PlanificationRelance>> GetByStatutAsync(string statut)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE Statut = @Statut AND EstActif = 1 ORDER BY DatePrevue";
                return await connection.QueryAsync<PlanificationRelance>(query, new { Statut = statut });
            }
        }

        /// <summary>
        /// Récupère les planifications de relance prévues pour une date
        /// </summary>
        /// <param name="date">Date prévue</param>
        /// <returns>Liste des planifications de relance prévues pour la date spécifiée</returns>
        public async Task<IEnumerable<PlanificationRelance>> GetPlannedForDateAsync(DateTime date)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var dateDebut = date.Date;
                var dateFin = date.Date.AddDays(1).AddSeconds(-1);

                var query = $@"
                    SELECT * FROM {_tableName}
                    WHERE DatePrevue BETWEEN @DateDebut AND @DateFin
                    AND (Statut = 'Planifiée' OR Statut = 'Validée')
                    AND EstActif = 1
                    ORDER BY DatePrevue";

                return await connection.QueryAsync<PlanificationRelance>(query, new { DateDebut = dateDebut, DateFin = dateFin });
            }
        }

        /// <summary>
        /// Récupère les planifications de relance avec leurs relations (facture, modèle, etc.)
        /// </summary>
        /// <returns>Liste des planifications de relance avec leurs relations</returns>
        public async Task<IEnumerable<PlanificationRelance>> GetWithRelationsAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT p.*, f.*, m.*, r.*
                    FROM PlanificationsRelance p
                    LEFT JOIN Factures f ON p.FactureId = f.Id
                    LEFT JOIN ModelesRelance m ON p.ModeleRelanceId = m.Id
                    LEFT JOIN ReglesRelance r ON p.RegleRelanceId = r.Id
                    WHERE p.EstActif = 1
                    ORDER BY p.DatePrevue";

                var planificationDict = new Dictionary<int, PlanificationRelance>();

                await connection.QueryAsync<PlanificationRelance, Facture, ModeleRelance, RegleRelance, PlanificationRelance>(
                    query,
                    (planification, facture, modele, regle) =>
                    {
                        if (!planificationDict.TryGetValue(planification.Id, out var planificationEntry))
                        {
                            planificationEntry = planification;
                            planificationDict.Add(planification.Id, planificationEntry);
                        }

                        if (facture != null)
                            planificationEntry.Facture = facture;

                        if (modele != null)
                            planificationEntry.ModeleRelance = modele;

                        if (regle != null)
                            planificationEntry.RegleRelance = regle;

                        return planificationEntry;
                    },
                    splitOn: "Id,Id,Id");

                return planificationDict.Values;
            }
        }

        /// <summary>
        /// Met à jour le statut d'une planification de relance
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <param name="commentaire">Commentaire (optionnel)</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateStatutAsync(int id, string statut, int modifiePar, string commentaire = null)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $@"
                    UPDATE {_tableName}
                    SET Statut = @Statut,
                        Commentaire = CASE WHEN @Commentaire IS NULL THEN Commentaire ELSE @Commentaire END,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id AND EstActif = 1";

                var parameters = new
                {
                    Id = id,
                    Statut = statut,
                    Commentaire = commentaire,
                    DateModification = DateTime.Now,
                    ModifiePar = modifiePar
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }

        /// <summary>
        /// Marque une planification de relance comme envoyée
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance</param>
        /// <param name="dateEnvoi">Date d'envoi</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> MarkAsSentAsync(int id, DateTime dateEnvoi, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $@"
                    UPDATE {_tableName}
                    SET Statut = 'Envoyée',
                        DateEnvoi = @DateEnvoi,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id AND EstActif = 1";

                var parameters = new
                {
                    Id = id,
                    DateEnvoi = dateEnvoi,
                    DateModification = DateTime.Now,
                    ModifiePar = modifiePar
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }
    }
}
