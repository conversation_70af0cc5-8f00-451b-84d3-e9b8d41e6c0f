using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository du journal d'audit
    /// </summary>
    public interface IJournalAuditRepository
    {
        /// <summary>
        /// Ajoute une entrée dans le journal d'audit
        /// </summary>
        /// <param name="journalAudit">Entrée à ajouter</param>
        /// <returns>Entrée ajoutée avec son identifiant généré</returns>
        Task<JournalAudit> AddAsync(JournalAudit journalAudit);

        /// <summary>
        /// Récupère toutes les entrées du journal d'audit
        /// </summary>
        /// <returns>Liste des entrées du journal</returns>
        Task<IEnumerable<JournalAudit>> GetAllAsync();

        /// <summary>
        /// Récupère une entrée du journal par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'entrée</param>
        /// <returns>Entrée trouvée ou null</returns>
        Task<JournalAudit> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les entrées du journal par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des entrées de l'utilisateur</returns>
        Task<IEnumerable<JournalAudit>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les entrées du journal par type d'action
        /// </summary>
        /// <param name="typeAction">Type d'action</param>
        /// <returns>Liste des entrées du type spécifié</returns>
        Task<IEnumerable<JournalAudit>> GetByTypeActionAsync(string typeAction);

        /// <summary>
        /// Récupère les entrées du journal par entité
        /// </summary>
        /// <param name="typeEntite">Type d'entité</param>
        /// <param name="entiteId">Identifiant de l'entité</param>
        /// <returns>Liste des entrées concernant l'entité spécifiée</returns>
        Task<IEnumerable<JournalAudit>> GetByEntityAsync(string typeEntite, int entiteId);

        /// <summary>
        /// Récupère les entrées du journal par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des entrées dans la période spécifiée</returns>
        Task<IEnumerable<JournalAudit>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Recherche dans le journal d'audit
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des entrées correspondant au terme de recherche</returns>
        Task<IEnumerable<JournalAudit>> SearchAsync(string searchTerm);
    }
}
