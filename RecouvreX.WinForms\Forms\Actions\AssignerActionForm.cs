using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Actions
{
    /// <summary>
    /// Formulaire pour assigner une action prioritaire à un utilisateur
    /// </summary>
    public partial class AssignerActionForm : Form
    {
        private readonly IActionPrioritaireService _actionPrioritaireService;
        private readonly IUtilisateurService _utilisateurService;
        private readonly int _currentUserId;
        private readonly ActionPrioritaire _actionPrioritaire;
        private List<Utilisateur> _utilisateurs;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="actionPrioritaireService">Service de gestion des actions prioritaires</param>
        /// <param name="utilisateurService">Service de gestion des utilisateurs</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        /// <param name="actionPrioritaire">Action prioritaire à assigner</param>
        public AssignerActionForm(
            IActionPrioritaireService actionPrioritaireService,
            IUtilisateurService utilisateurService,
            int currentUserId,
            ActionPrioritaire actionPrioritaire)
        {
            _actionPrioritaireService = actionPrioritaireService ?? throw new ArgumentNullException(nameof(actionPrioritaireService));
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _currentUserId = currentUserId;
            _actionPrioritaire = actionPrioritaire ?? throw new ArgumentNullException(nameof(actionPrioritaire));

            InitializeComponent();

            // Charger les données au chargement du formulaire
            this.Load += async (s, e) => await LoadDataAsync();
        }

        /// <summary>
        /// Charge les données nécessaires au formulaire
        /// </summary>
        private async Task LoadDataAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Récupérer tous les utilisateurs actifs
                _utilisateurs = (await _utilisateurService.GetAllAsync())
                    .Where(u => u.EstActif)
                    .OrderBy(u => u.NomComplet)
                    .ToList();

                // Remplir la ComboBox des utilisateurs
                utilisateurComboBox.DataSource = null;
                utilisateurComboBox.DisplayMember = "NomComplet";
                utilisateurComboBox.ValueMember = "Id";
                utilisateurComboBox.DataSource = _utilisateurs;

                // Sélectionner l'utilisateur actuellement assigné s'il existe
                if (_actionPrioritaire.UtilisateurAssigneId.HasValue)
                {
                    var utilisateurAssigne = _utilisateurs.FirstOrDefault(u => u.Id == _actionPrioritaire.UtilisateurAssigneId.Value);
                    if (utilisateurAssigne != null)
                    {
                        utilisateurComboBox.SelectedValue = utilisateurAssigne.Id;
                    }
                }

                // Afficher les informations de l'action prioritaire
                clientLabel.Text = _actionPrioritaire.Client?.RaisonSociale ?? "Client inconnu";
                descriptionLabel.Text = _actionPrioritaire.Description;
                typeActionLabel.Text = _actionPrioritaire.TypeAction.ToString();
                prioriteLabel.Text = _actionPrioritaire.NiveauPriorite.ToString();
                dateEcheanceLabel.Text = _actionPrioritaire.DateEcheance.ToString("dd/MM/yyyy");

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données pour l'assignation d'action");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des données : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
                this.Close();
            }
        }

        /// <summary>
        /// Gère le clic sur le bouton d'assignation
        /// </summary>
        private async void AssignButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Vérifier qu'un utilisateur est sélectionné
                if (utilisateurComboBox.SelectedItem == null)
                {
                    MessageBox.Show("Veuillez sélectionner un utilisateur.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Récupérer l'utilisateur sélectionné
                var utilisateur = (Utilisateur)utilisateurComboBox.SelectedItem;

                // Demander confirmation
                var result = MessageBox.Show(
                    $"Voulez-vous assigner cette action à {utilisateur.NomComplet} ?",
                    "Confirmation",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Afficher un indicateur de chargement
                    this.Cursor = Cursors.WaitCursor;

                    // Assigner l'action à l'utilisateur
                    await _actionPrioritaireService.AssignerAsync(_actionPrioritaire.Id, utilisateur.Id, _currentUserId);

                    // Afficher un message de confirmation
                    MessageBox.Show($"L'action a été assignée à {utilisateur.NomComplet}.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Fermer le formulaire avec DialogResult.OK
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'assignation de l'action");
                MessageBox.Show($"Une erreur s'est produite lors de l'assignation de l'action : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Gère le clic sur le bouton d'annulation
        /// </summary>
        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
