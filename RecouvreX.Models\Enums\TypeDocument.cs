namespace RecouvreX.Models.Enums
{
    /// <summary>
    /// Énumération des types de document possibles
    /// </summary>
    public static class TypeDocument
    {
        public const string Facture = "Facture";
        public const string BonLivraison = "<PERSON> de livraison";
        public const string Contrat = "Contrat";
        public const string JustificatifPaiement = "Justificatif de paiement";
        public const string Relance = "Relance";
        public const string Autre = "Autre";
    }
}
