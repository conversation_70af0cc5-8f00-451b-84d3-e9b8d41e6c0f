using RecouvreX.Business.Interfaces;
using RecouvreX.Models.Reporting;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Rapports
{
    public partial class CustomReportListForm : Form
    {
        private readonly IRapportPersonnaliseService _rapportPersonnaliseService;
        private readonly IReportService _reportService;
        private readonly int _currentUserId;
        private List<RapportPersonnalise> _rapports = new List<RapportPersonnalise>();
        private DataTable _dataTable = new DataTable();

        public CustomReportListForm(IRapportPersonnaliseService rapportPersonnaliseService, IReportService reportService, int currentUserId)
        {
            _rapportPersonnaliseService = rapportPersonnaliseService ?? throw new ArgumentNullException(nameof(rapportPersonnaliseService));
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _currentUserId = currentUserId;

            InitializeComponent();
            InitializeDataTable();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form
            this.Text = "Gestion des rapports personnalisés";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimizeBox = true;
            this.MaximizeBox = true;
            this.FormBorderStyle = FormBorderStyle.Sizable;

            // Main panel
            TableLayoutPanel mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 1,
                RowCount = 3,
                RowStyles = {
                    new RowStyle(SizeType.Absolute, 40),
                    new RowStyle(SizeType.Absolute, 40),
                    new RowStyle(SizeType.Percent, 100)
                }
            };
            this.Controls.Add(mainPanel);

            // Title label
            Label titleLabel = new Label
            {
                Text = "Rapports personnalisés",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft
            };
            mainPanel.Controls.Add(titleLabel, 0, 0);

            // Buttons panel
            TableLayoutPanel buttonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 5,
                RowCount = 1,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 100),
                    new ColumnStyle(SizeType.Absolute, 120),
                    new ColumnStyle(SizeType.Absolute, 120),
                    new ColumnStyle(SizeType.Absolute, 120),
                    new ColumnStyle(SizeType.Absolute, 120)
                }
            };
            mainPanel.Controls.Add(buttonsPanel, 0, 1);

            Button newButton = new Button
            {
                Name = "newButton",
                Text = "Nouveau",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            newButton.Click += NewButton_Click;
            buttonsPanel.Controls.Add(newButton, 1, 0);

            Button editButton = new Button
            {
                Name = "editButton",
                Text = "Modifier",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            editButton.Click += EditButton_Click;
            buttonsPanel.Controls.Add(editButton, 2, 0);

            Button deleteButton = new Button
            {
                Name = "deleteButton",
                Text = "Supprimer",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            deleteButton.Click += DeleteButton_Click;
            buttonsPanel.Controls.Add(deleteButton, 3, 0);

            Button refreshButton = new Button
            {
                Name = "refreshButton",
                Text = "Actualiser",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            refreshButton.Click += RefreshButton_Click;
            buttonsPanel.Controls.Add(refreshButton, 4, 0);

            // DataGridView
            DataGridView dataGridView = new DataGridView
            {
                Name = "dataGridView",
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                BackgroundColor = SystemColors.Window,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };
            dataGridView.CellDoubleClick += DataGridView_CellDoubleClick;
            dataGridView.DataSource = _dataTable;
            mainPanel.Controls.Add(dataGridView, 0, 2);

            // Context menu
            ContextMenuStrip contextMenu = new ContextMenuStrip();
            
            ToolStripMenuItem generateMenuItem = new ToolStripMenuItem("Générer");
            generateMenuItem.Click += GenerateMenuItem_Click;
            contextMenu.Items.Add(generateMenuItem);

            ToolStripMenuItem exportMenuItem = new ToolStripMenuItem("Exporter");
            exportMenuItem.Click += ExportMenuItem_Click;
            contextMenu.Items.Add(exportMenuItem);

            ToolStripMenuItem sendMenuItem = new ToolStripMenuItem("Envoyer par email");
            sendMenuItem.Click += SendMenuItem_Click;
            contextMenu.Items.Add(sendMenuItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            ToolStripMenuItem editMenuItem = new ToolStripMenuItem("Modifier");
            editMenuItem.Click += EditButton_Click;
            contextMenu.Items.Add(editMenuItem);

            ToolStripMenuItem deleteMenuItem = new ToolStripMenuItem("Supprimer");
            deleteMenuItem.Click += DeleteButton_Click;
            contextMenu.Items.Add(deleteMenuItem);

            dataGridView.ContextMenuStrip = contextMenu;

            this.ResumeLayout(false);
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Nom", typeof(string));
            _dataTable.Columns.Add("Description", typeof(string));
            _dataTable.Columns.Add("Type", typeof(string));
            _dataTable.Columns.Add("Date création", typeof(DateTime));
            _dataTable.Columns.Add("Date modification", typeof(DateTime));
            _dataTable.Columns.Add("Programmé", typeof(bool));
            _dataTable.Columns.Add("Fréquence", typeof(string));
            _dataTable.Columns.Add("Dernière génération", typeof(DateTime));
        }

        private async void LoadData()
        {
            try
            {
                // Récupérer les rapports personnalisés de l'utilisateur
                _rapports = (await _rapportPersonnaliseService.GetByUtilisateurIdAsync(_currentUserId)).ToList();

                // Mettre à jour la table de données
                UpdateDataTable();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des rapports personnalisés");
                MessageBox.Show($"Erreur lors du chargement des données : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateDataTable()
        {
            _dataTable.Clear();

            foreach (var rapport in _rapports)
            {
                // Extraire le type de rapport de la configuration
                string typeRapport = "Non défini";
                try
                {
                    var configuration = Newtonsoft.Json.JsonConvert.DeserializeObject<ConfigurationRapport>(rapport.Configuration);
                    if (configuration != null)
                    {
                        typeRapport = configuration.TypeRapport;
                    }
                }
                catch { }

                _dataTable.Rows.Add(
                    rapport.Id,
                    rapport.Nom,
                    rapport.Description,
                    typeRapport,
                    rapport.DateCreation,
                    rapport.DateModification,
                    rapport.EstProgramme,
                    rapport.FrequenceGeneration,
                    rapport.DerniereGeneration
                );
            }
        }

        private void NewButton_Click(object sender, EventArgs e)
        {
            try
            {
                using (var form = new CustomReportBuilderForm(_rapportPersonnaliseService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de création de rapport");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int rapportId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    var rapport = _rapports.FirstOrDefault(r => r.Id == rapportId);
                    if (rapport != null)
                    {
                        using (var form = new CustomReportBuilderForm(_rapportPersonnaliseService, _currentUserId, rapport))
                        {
                            if (form.ShowDialog() == DialogResult.OK)
                            {
                                LoadData();
                            }
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un rapport à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de modification de rapport");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int rapportId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string rapportNom = dataGridView.SelectedRows[0].Cells["Nom"].Value.ToString();

                    if (MessageBox.Show($"Êtes-vous sûr de vouloir supprimer le rapport '{rapportNom}' ?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        bool success = await _rapportPersonnaliseService.DeleteAsync(rapportId, _currentUserId);
                        if (success)
                        {
                            MessageBox.Show("Le rapport a été supprimé avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("Une erreur s'est produite lors de la suppression du rapport.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un rapport à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression du rapport");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshButton_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditButton_Click(sender, e);
            }
        }

        private async void GenerateMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int rapportId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    
                    // Afficher un indicateur de chargement
                    Cursor.Current = Cursors.WaitCursor;
                    
                    // Générer le rapport
                    string reportData = await _rapportPersonnaliseService.GenerateReportAsync(rapportId, _currentUserId);
                    
                    // Afficher l'aperçu du rapport
                    using (var form = new ReportPreviewForm(reportData))
                    {
                        form.ShowDialog();
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un rapport à générer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération du rapport");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private async void ExportMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int rapportId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    
                    // Demander le format d'export
                    string format = "PDF";
                    using (var form = new ExportFormatForm())
                    {
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            format = form.SelectedFormat;
                        }
                        else
                        {
                            return;
                        }
                    }
                    
                    // Demander le chemin d'export
                    string extension = format.ToLower();
                    string filter = $"Fichiers {format} (*.{extension})|*.{extension}";
                    
                    using (SaveFileDialog saveFileDialog = new SaveFileDialog())
                    {
                        saveFileDialog.Filter = filter;
                        saveFileDialog.DefaultExt = extension;
                        saveFileDialog.AddExtension = true;
                        saveFileDialog.FileName = $"Rapport_{DateTime.Now:yyyyMMdd}.{extension}";
                        
                        if (saveFileDialog.ShowDialog() == DialogResult.OK)
                        {
                            // Afficher un indicateur de chargement
                            Cursor.Current = Cursors.WaitCursor;
                            
                            // Exporter le rapport
                            bool success = await _rapportPersonnaliseService.ExportReportAsync(rapportId, format, saveFileDialog.FileName, _currentUserId);
                            
                            if (success)
                            {
                                MessageBox.Show($"Le rapport a été exporté avec succès vers {saveFileDialog.FileName}.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                            else
                            {
                                MessageBox.Show("Une erreur s'est produite lors de l'exportation du rapport.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un rapport à exporter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'exportation du rapport");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void SendMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int rapportId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    
                    // Ouvrir le formulaire d'envoi par email
                    using (var form = new SendReportByEmailForm(_rapportPersonnaliseService, rapportId, _currentUserId))
                    {
                        form.ShowDialog();
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un rapport à envoyer par email.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'envoi par email");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
