using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des permissions
    /// </summary>
    public interface IPermissionRepository : IRepository<Permission>
    {
        /// <summary>
        /// Récupère une permission par son code
        /// </summary>
        /// <param name="code">Code de la permission</param>
        /// <returns>Permission trouvée ou null</returns>
        Task<Permission> GetByCodeAsync(string code);

        /// <summary>
        /// Récupère toutes les permissions par module
        /// </summary>
        /// <param name="module">Nom du module</param>
        /// <returns>Liste des permissions du module</returns>
        Task<IEnumerable<Permission>> GetByModuleAsync(string module);

        /// <summary>
        /// Récupère toutes les permissions d'un utilisateur
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des permissions de l'utilisateur</returns>
        Task<IEnumerable<Permission>> GetByUserIdAsync(int userId);

        /// <summary>
        /// Vérifie si un utilisateur possède une permission spécifique
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <param name="permissionCode">Code de la permission</param>
        /// <returns>True si l'utilisateur possède la permission, sinon False</returns>
        Task<bool> UserHasPermissionAsync(int userId, string permissionCode);

        /// <summary>
        /// Récupère les permissions associées à un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Liste des permissions du rôle</returns>
        Task<IEnumerable<Permission>> GetByRoleIdAsync(int roleId);
    }
}
