﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RecouvreX.WinForms", "RecouvreX.WinForms\RecouvreX.WinForms.csproj", "{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RecouvreX.Business", "RecouvreX.Business\RecouvreX.Business.csproj", "{F6E01715-C76C-4211-82BC-FA33ED177337}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RecouvreX.DataAccess", "RecouvreX.DataAccess\RecouvreX.DataAccess.csproj", "{E9CEF544-D4AF-41C2-8CFF-61736F47E046}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RecouvreX.Models", "RecouvreX.Models\RecouvreX.Models.csproj", "{19A28481-DFD5-4284-B1A7-EAC6F30931BB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RecouvreX.Common", "RecouvreX.Common\RecouvreX.Common.csproj", "{C34668F0-D9D4-49C8-A479-CD85A614F189}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}.Debug|x64.Build.0 = Debug|Any CPU
		{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}.Debug|x86.Build.0 = Debug|Any CPU
		{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}.Release|x64.ActiveCfg = Release|Any CPU
		{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}.Release|x64.Build.0 = Release|Any CPU
		{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}.Release|x86.ActiveCfg = Release|Any CPU
		{5E6F7A8B-9C0D-1E2F-3A4B-5C6D7E8F9A0B}.Release|x86.Build.0 = Release|Any CPU
		{F6E01715-C76C-4211-82BC-FA33ED177337}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6E01715-C76C-4211-82BC-FA33ED177337}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6E01715-C76C-4211-82BC-FA33ED177337}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F6E01715-C76C-4211-82BC-FA33ED177337}.Debug|x64.Build.0 = Debug|Any CPU
		{F6E01715-C76C-4211-82BC-FA33ED177337}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F6E01715-C76C-4211-82BC-FA33ED177337}.Debug|x86.Build.0 = Debug|Any CPU
		{F6E01715-C76C-4211-82BC-FA33ED177337}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6E01715-C76C-4211-82BC-FA33ED177337}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6E01715-C76C-4211-82BC-FA33ED177337}.Release|x64.ActiveCfg = Release|Any CPU
		{F6E01715-C76C-4211-82BC-FA33ED177337}.Release|x64.Build.0 = Release|Any CPU
		{F6E01715-C76C-4211-82BC-FA33ED177337}.Release|x86.ActiveCfg = Release|Any CPU
		{F6E01715-C76C-4211-82BC-FA33ED177337}.Release|x86.Build.0 = Release|Any CPU
		{E9CEF544-D4AF-41C2-8CFF-61736F47E046}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9CEF544-D4AF-41C2-8CFF-61736F47E046}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9CEF544-D4AF-41C2-8CFF-61736F47E046}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E9CEF544-D4AF-41C2-8CFF-61736F47E046}.Debug|x64.Build.0 = Debug|Any CPU
		{E9CEF544-D4AF-41C2-8CFF-61736F47E046}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E9CEF544-D4AF-41C2-8CFF-61736F47E046}.Debug|x86.Build.0 = Debug|Any CPU
		{E9CEF544-D4AF-41C2-8CFF-61736F47E046}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9CEF544-D4AF-41C2-8CFF-61736F47E046}.Release|Any CPU.Build.0 = Release|Any CPU
		{E9CEF544-D4AF-41C2-8CFF-61736F47E046}.Release|x64.ActiveCfg = Release|Any CPU
		{E9CEF544-D4AF-41C2-8CFF-61736F47E046}.Release|x64.Build.0 = Release|Any CPU
		{E9CEF544-D4AF-41C2-8CFF-61736F47E046}.Release|x86.ActiveCfg = Release|Any CPU
		{E9CEF544-D4AF-41C2-8CFF-61736F47E046}.Release|x86.Build.0 = Release|Any CPU
		{19A28481-DFD5-4284-B1A7-EAC6F30931BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{19A28481-DFD5-4284-B1A7-EAC6F30931BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{19A28481-DFD5-4284-B1A7-EAC6F30931BB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{19A28481-DFD5-4284-B1A7-EAC6F30931BB}.Debug|x64.Build.0 = Debug|Any CPU
		{19A28481-DFD5-4284-B1A7-EAC6F30931BB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{19A28481-DFD5-4284-B1A7-EAC6F30931BB}.Debug|x86.Build.0 = Debug|Any CPU
		{19A28481-DFD5-4284-B1A7-EAC6F30931BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{19A28481-DFD5-4284-B1A7-EAC6F30931BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{19A28481-DFD5-4284-B1A7-EAC6F30931BB}.Release|x64.ActiveCfg = Release|Any CPU
		{19A28481-DFD5-4284-B1A7-EAC6F30931BB}.Release|x64.Build.0 = Release|Any CPU
		{19A28481-DFD5-4284-B1A7-EAC6F30931BB}.Release|x86.ActiveCfg = Release|Any CPU
		{19A28481-DFD5-4284-B1A7-EAC6F30931BB}.Release|x86.Build.0 = Release|Any CPU
		{C34668F0-D9D4-49C8-A479-CD85A614F189}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C34668F0-D9D4-49C8-A479-CD85A614F189}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C34668F0-D9D4-49C8-A479-CD85A614F189}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C34668F0-D9D4-49C8-A479-CD85A614F189}.Debug|x64.Build.0 = Debug|Any CPU
		{C34668F0-D9D4-49C8-A479-CD85A614F189}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C34668F0-D9D4-49C8-A479-CD85A614F189}.Debug|x86.Build.0 = Debug|Any CPU
		{C34668F0-D9D4-49C8-A479-CD85A614F189}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C34668F0-D9D4-49C8-A479-CD85A614F189}.Release|Any CPU.Build.0 = Release|Any CPU
		{C34668F0-D9D4-49C8-A479-CD85A614F189}.Release|x64.ActiveCfg = Release|Any CPU
		{C34668F0-D9D4-49C8-A479-CD85A614F189}.Release|x64.Build.0 = Release|Any CPU
		{C34668F0-D9D4-49C8-A479-CD85A614F189}.Release|x86.ActiveCfg = Release|Any CPU
		{C34668F0-D9D4-49C8-A479-CD85A614F189}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
