using RecouvreX.Models;
using ScottPlot;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;

namespace RecouvreX.WinForms.Helpers
{
    /// <summary>
    /// Classe utilitaire pour la création et la configuration des graphiques
    /// </summary>
    public static class ChartHelper
    {
        /// <summary>
        /// Crée un graphique d'évolution des paiements sur 12 mois
        /// </summary>
        /// <param name="plot">Objet Plot à configurer</param>
        /// <param name="paiements">Liste des paiements</param>
        public static void CreatePaymentEvolutionChart(Plot plot, IEnumerable<Paiement> paiements)
        {
            if (plot == null || paiements == null || !paiements.Any())
                return;

            // Regrouper les paiements par mois
            var dateDebut = DateTime.Today.AddMonths(-11);
            var dateFin = DateTime.Today;

            var paiementsParMois = new Dictionary<DateTime, decimal>();

            // Initialiser tous les mois avec 0
            for (var date = dateDebut; date <= dateFin; date = date.AddMonths(1))
            {
                var premierJourDuMois = new DateTime(date.Year, date.Month, 1);
                paiementsParMois[premierJourDuMois] = 0;
            }

            // Remplir avec les données réelles
            foreach (var paiement in paiements.Where(p => p.DatePaiement >= dateDebut && p.DatePaiement <= dateFin))
            {
                var premierJourDuMois = new DateTime(paiement.DatePaiement.Year, paiement.DatePaiement.Month, 1);
                if (paiementsParMois.ContainsKey(premierJourDuMois))
                {
                    paiementsParMois[premierJourDuMois] += paiement.Montant;
                }
                else
                {
                    paiementsParMois[premierJourDuMois] = paiement.Montant;
                }
            }

            // Convertir en tableaux pour le graphique
            var mois = paiementsParMois.Keys.OrderBy(d => d).ToArray();
            var montants = mois.Select(m => (double)paiementsParMois[m]).ToArray();
            var positions = Enumerable.Range(0, mois.Length).Select(i => (double)i).ToArray();

            // Créer le graphique
            var bar = plot.AddBar(montants, positions);
            bar.FillColor = Color.SteelBlue;
            bar.BorderColor = Color.DarkBlue;

            // Configurer les étiquettes de l'axe X
            plot.XTicks(positions, mois.Select(m => m.ToString("MMM yyyy")).ToArray());
            plot.XAxis.TickLabelStyle(rotation: 45);

            // Configurer les titres et légendes
            plot.Title("Évolution des paiements sur 12 mois");
            plot.YLabel("Montant (€)");

            // Configurer l'apparence
            plot.Grid(lineStyle: LineStyle.Dot);
            plot.Frameless();
        }

        /// <summary>
        /// Crée un graphique de répartition des factures par statut
        /// </summary>
        /// <param name="plot">Objet Plot à configurer</param>
        /// <param name="factures">Liste des factures</param>
        public static void CreateInvoiceStatusChart(Plot plot, IEnumerable<Facture> factures)
        {
            if (plot == null || factures == null || !factures.Any())
                return;

            // Regrouper les factures par statut
            var facturesParStatut = factures
                .GroupBy(f => f.Statut)
                .Select(g => new { Statut = g.Key, Montant = g.Sum(f => f.MontantTTC) })
                .OrderByDescending(x => x.Montant)
                .ToList();

            // Préparer les données pour le graphique
            var statuts = facturesParStatut.Select(x => x.Statut).ToArray();
            var montants = facturesParStatut.Select(x => (double)x.Montant).ToArray();

            // Créer le graphique en camembert
            plot.AddPie(montants);

            // Configurer les titres et légendes
            plot.Title("Répartition des factures par statut");
            plot.Legend(true, Alignment.UpperRight);

            // Configurer l'apparence
            plot.Frameless();
        }

        /// <summary>
        /// Crée un graphique des performances de recouvrement par client
        /// </summary>
        /// <param name="plot">Objet Plot à configurer</param>
        /// <param name="factures">Liste des factures</param>
        /// <param name="clients">Liste des clients</param>
        /// <param name="topN">Nombre de clients à afficher</param>
        public static void CreateRecoveryPerformanceChart(Plot plot, IEnumerable<Facture> factures, IEnumerable<Client> clients, int topN = 10)
        {
            if (plot == null || factures == null || !factures.Any() || clients == null || !clients.Any())
                return;

            // Calculer le taux de recouvrement par client
            var tauxParClient = DashboardHelper.CalculateTauxRecouvrementParClient(factures);

            // Associer les noms des clients
            var clientsDict = clients.ToDictionary(c => c.Id, c => c.RaisonSociale);

            // Sélectionner les N clients avec le plus de factures
            var topClients = factures
                .GroupBy(f => f.ClientId)
                .Select(g => new { ClientId = g.Key, NombreFactures = g.Count() })
                .OrderByDescending(x => x.NombreFactures)
                .Take(topN)
                .ToList();

            // Préparer les données pour le graphique
            var clientIds = topClients.Select(c => c.ClientId).ToList();
            var nomsClients = clientIds
                .Select(id => clientsDict.ContainsKey(id) ? clientsDict[id] : $"Client {id}")
                .ToArray();

            var taux = clientIds
                .Select(id => tauxParClient.ContainsKey(id) ? tauxParClient[id] : 0)
                .ToArray();

            var positions = Enumerable.Range(0, clientIds.Count).Select(i => (double)i).ToArray();

            // Créer le graphique
            var bar = plot.AddBar(taux, positions);
            bar.FillColor = Color.ForestGreen;
            bar.BorderColor = Color.DarkGreen;

            // Configurer les étiquettes de l'axe X
            plot.XTicks(positions, nomsClients);
            plot.XAxis.TickLabelStyle(rotation: 45);

            // Configurer les titres et légendes
            plot.Title("Taux de recouvrement par client");
            plot.YLabel("Taux de recouvrement (%)");

            // Configurer l'apparence
            plot.Grid(lineStyle: LineStyle.Dot);
            plot.Frameless();
        }

        /// <summary>
        /// Crée un graphique de répartition des factures par tranche d'ancienneté
        /// </summary>
        /// <param name="plot">Objet Plot à configurer</param>
        /// <param name="factures">Liste des factures</param>
        public static void CreateAgeDistributionChart(Plot plot, IEnumerable<Facture> factures)
        {
            if (plot == null || factures == null || !factures.Any())
                return;

            // Obtenir la répartition par tranche d'ancienneté
            var montantsParTranche = DashboardHelper.GetMontantParTrancheAnciennete(factures);

            // Préparer les données pour le graphique
            var tranches = montantsParTranche.Keys.ToArray();
            var montants = tranches.Select(t => (double)montantsParTranche[t]).ToArray();
            var positions = Enumerable.Range(0, tranches.Length).Select(i => (double)i).ToArray();

            // Créer le graphique
            var bar = plot.AddBar(montants, positions);

            // Définir des couleurs différentes selon la tranche
            var colors = new Color[] { Color.Green, Color.YellowGreen, Color.Orange, Color.Red };
            // Utiliser une seule couleur pour toutes les barres
            bar.FillColor = colors[0];
            bar.BorderColor = Color.FromArgb(colors[0].R - 40, colors[0].G - 40, colors[0].B - 40);

            // Configurer les étiquettes de l'axe X
            plot.XTicks(positions, tranches);

            // Configurer les titres et légendes
            plot.Title("Répartition des créances par ancienneté");
            plot.YLabel("Montant (€)");

            // Configurer l'apparence
            plot.Grid(lineStyle: LineStyle.Dot);
            plot.Frameless();
        }
    }
}
