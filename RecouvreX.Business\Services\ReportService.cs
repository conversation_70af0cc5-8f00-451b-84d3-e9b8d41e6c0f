using Newtonsoft.Json;
using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using iText.Kernel.Pdf;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de génération de rapports
    /// </summary>
    public class ReportService : IReportService
    {
        private readonly IFactureRepository _factureRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IPaiementRepository _paiementRepository;
        private readonly IRelanceRepository _relanceRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="factureRepository">Repository des factures</param>
        /// <param name="clientRepository">Repository des clients</param>
        /// <param name="paiementRepository">Repository des paiements</param>
        /// <param name="relanceRepository">Repository des relances</param>
        /// <param name="utilisateurRepository">Repository des utilisateurs</param>
        public ReportService(
            IFactureRepository factureRepository,
            IClientRepository clientRepository,
            IPaiementRepository paiementRepository,
            IRelanceRepository relanceRepository,
            IUtilisateurRepository utilisateurRepository)
        {
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _paiementRepository = paiementRepository ?? throw new ArgumentNullException(nameof(paiementRepository));
            _relanceRepository = relanceRepository ?? throw new ArgumentNullException(nameof(relanceRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
        }

        /// <summary>
        /// Génère un rapport sur les factures en retard
        /// </summary>
        /// <returns>Données du rapport au format JSON</returns>
        public async Task<string> GenerateOverdueInvoicesReportAsync()
        {
            // Récupérer les factures en retard
            var facturesEnRetard = await _factureRepository.GetOverdueInvoicesAsync();

            // Récupérer les clients associés
            var clientIds = facturesEnRetard.Select(f => f.ClientId).Distinct().ToList();
            var clients = new Dictionary<int, string>();
            foreach (var clientId in clientIds)
            {
                var client = await _clientRepository.GetByIdAsync(clientId);
                if (client != null)
                {
                    clients.Add(clientId, client.RaisonSociale);
                }
            }

            // Préparer les données du rapport
            var reportData = new
            {
                Title = "Rapport des factures en retard",
                GeneratedAt = DateTime.Now,
                TotalCount = facturesEnRetard.Count(),
                TotalAmount = facturesEnRetard.Sum(f => f.MontantRestant),
                Invoices = facturesEnRetard.Select(f => new
                {
                    f.Id,
                    f.Numero,
                    ClientId = f.ClientId,
                    ClientName = clients.ContainsKey(f.ClientId) ? clients[f.ClientId] : "Client inconnu",
                    f.DateEmission,
                    f.DateEcheance,
                    DaysOverdue = (int)(DateTime.Now - f.DateEcheance).TotalDays,
                    f.MontantTTC,
                    f.MontantPaye,
                    f.MontantRestant
                }).OrderByDescending(f => f.DaysOverdue)
            };

            // Convertir les données en JSON
            return JsonConvert.SerializeObject(reportData, Formatting.Indented);
        }

        /// <summary>
        /// Génère un rapport sur les paiements par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Données du rapport au format JSON</returns>
        public async Task<string> GeneratePaymentsByPeriodReportAsync(DateTime dateDebut, DateTime dateFin)
        {
            // Récupérer les paiements de la période
            var paiements = await _paiementRepository.GetByPeriodAsync(dateDebut, dateFin);

            // Récupérer les clients associés
            var clientIds = paiements.Select(p => p.ClientId).Distinct().ToList();
            var clients = new Dictionary<int, string>();
            foreach (var clientId in clientIds)
            {
                var client = await _clientRepository.GetByIdAsync(clientId);
                if (client != null)
                {
                    clients.Add(clientId, client.RaisonSociale);
                }
            }

            // Préparer les données du rapport
            var reportData = new
            {
                Title = "Rapport des paiements par période",
                GeneratedAt = DateTime.Now,
                Period = new { Start = dateDebut, End = dateFin },
                TotalCount = paiements.Count(),
                TotalAmount = paiements.Sum(p => p.Montant),
                PaymentsByMethod = paiements.GroupBy(p => p.ModePaiement)
                    .Select(g => new
                    {
                        Method = g.Key,
                        Count = g.Count(),
                        Amount = g.Sum(p => p.Montant)
                    }),
                Payments = paiements.Select(p => new
                {
                    p.Id,
                    p.Reference,
                    ClientId = p.ClientId,
                    ClientName = clients.ContainsKey(p.ClientId) ? clients[p.ClientId] : "Client inconnu",
                    p.DatePaiement,
                    p.Montant,
                    p.ModePaiement,
                    p.ReferenceBancaire
                }).OrderByDescending(p => p.DatePaiement)
            };

            // Convertir les données en JSON
            return JsonConvert.SerializeObject(reportData, Formatting.Indented);
        }

        /// <summary>
        /// Génère un rapport sur les factures par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Données du rapport au format JSON</returns>
        public async Task<string> GenerateInvoicesByPeriodReportAsync(DateTime dateDebut, DateTime dateFin)
        {
            // Récupérer les factures de la période
            var factures = await _factureRepository.GetByPeriodAsync(dateDebut, dateFin);

            // Récupérer les clients associés
            var clientIds = factures.Select(f => f.ClientId).Distinct().ToList();
            var clients = new Dictionary<int, string>();
            foreach (var clientId in clientIds)
            {
                var client = await _clientRepository.GetByIdAsync(clientId);
                if (client != null)
                {
                    clients.Add(clientId, client.RaisonSociale);
                }
            }

            // Préparer les données du rapport
            var reportData = new
            {
                Title = "Rapport des factures par période",
                GeneratedAt = DateTime.Now,
                Period = new { Start = dateDebut, End = dateFin },
                TotalCount = factures.Count(),
                TotalAmount = factures.Sum(f => f.MontantTTC),
                PaidAmount = factures.Sum(f => f.MontantPaye),
                RemainingAmount = factures.Sum(f => f.MontantRestant),
                PaymentRate = factures.Sum(f => f.MontantTTC) > 0
                    ? (factures.Sum(f => f.MontantPaye) / factures.Sum(f => f.MontantTTC)) * 100
                    : 0,
                InvoicesByStatus = factures.GroupBy(f => f.Statut)
                    .Select(g => new
                    {
                        Status = g.Key,
                        Count = g.Count(),
                        Amount = g.Sum(f => f.MontantTTC)
                    }),
                Invoices = factures.Select(f => new
                {
                    f.Id,
                    f.Numero,
                    ClientId = f.ClientId,
                    ClientName = clients.ContainsKey(f.ClientId) ? clients[f.ClientId] : "Client inconnu",
                    f.DateEmission,
                    f.DateEcheance,
                    f.MontantHT,
                    f.MontantTVA,
                    f.MontantTTC,
                    f.MontantPaye,
                    f.MontantRestant,
                    f.Statut
                }).OrderByDescending(f => f.DateEmission)
            };

            // Convertir les données en JSON
            return JsonConvert.SerializeObject(reportData, Formatting.Indented);
        }

        /// <summary>
        /// Génère un rapport sur les clients avec des factures en retard
        /// </summary>
        /// <returns>Données du rapport au format JSON</returns>
        public async Task<string> GenerateClientsWithOverdueInvoicesReportAsync()
        {
            // Récupérer les clients avec des factures en retard
            var clients = await _clientRepository.GetWithOverdueInvoicesAsync();

            // Récupérer les factures en retard pour chaque client
            var clientsWithInvoices = new List<object>();
            foreach (var client in clients)
            {
                var factures = await _factureRepository.GetByClientIdAsync(client.Id);
                var facturesEnRetard = factures.Where(f => f.Statut == "En retard").ToList();

                clientsWithInvoices.Add(new
                {
                    client.Id,
                    client.Code,
                    client.RaisonSociale,
                    client.Adresse,
                    client.Ville,
                    client.CodePostal,
                    client.Telephone,
                    client.Email,
                    client.SoldeActuel,
                    OverdueInvoicesCount = facturesEnRetard.Count,
                    OverdueAmount = facturesEnRetard.Sum(f => f.MontantRestant),
                    MaxDaysOverdue = facturesEnRetard.Any()
                        ? facturesEnRetard.Max(f => (int)(DateTime.Now - f.DateEcheance).TotalDays)
                        : 0,
                    OverdueInvoices = facturesEnRetard.Select(f => new
                    {
                        f.Id,
                        f.Numero,
                        f.DateEmission,
                        f.DateEcheance,
                        DaysOverdue = (int)(DateTime.Now - f.DateEcheance).TotalDays,
                        f.MontantTTC,
                        f.MontantPaye,
                        f.MontantRestant
                    }).OrderByDescending(f => f.DaysOverdue)
                });
            }

            // Préparer les données du rapport
            var reportData = new
            {
                Title = "Rapport des clients avec des factures en retard",
                GeneratedAt = DateTime.Now,
                TotalCount = clients.Count(),
                TotalOverdueAmount = clientsWithInvoices.Sum(c => (decimal)((dynamic)c).OverdueAmount),
                Clients = clientsWithInvoices.OrderByDescending(c => ((dynamic)c).OverdueAmount)
            };

            // Convertir les données en JSON
            return JsonConvert.SerializeObject(reportData, Formatting.Indented);
        }

        /// <summary>
        /// Génère un rapport sur les performances des commerciaux
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Données du rapport au format JSON</returns>
        public async Task<string> GenerateSalesPerformanceReportAsync(DateTime dateDebut, DateTime dateFin)
        {
            // Récupérer les factures de la période
            var factures = await _factureRepository.GetByPeriodAsync(dateDebut, dateFin);

            // Récupérer les commerciaux
            var commercialIds = factures.Where(f => f.CommercialId.HasValue).Select(f => f.CommercialId.Value).Distinct().ToList();
            var commerciaux = new Dictionary<int, string>();
            foreach (var commercialId in commercialIds)
            {
                var commercial = await _utilisateurRepository.GetByIdAsync(commercialId);
                if (commercial != null)
                {
                    commerciaux.Add(commercialId, commercial.NomComplet);
                }
            }

            // Préparer les données du rapport
            var reportData = new
            {
                Title = "Rapport des performances des commerciaux",
                GeneratedAt = DateTime.Now,
                Period = new { Start = dateDebut, End = dateFin },
                TotalInvoices = factures.Count(),
                TotalAmount = factures.Sum(f => f.MontantTTC),
                SalesByRepresentative = commercialIds.Select(commercialId =>
                {
                    var commercialFactures = factures.Where(f => f.CommercialId == commercialId).ToList();
                    return new
                    {
                        RepresentativeId = commercialId,
                        RepresentativeName = commerciaux.ContainsKey(commercialId) ? commerciaux[commercialId] : "Commercial inconnu",
                        InvoiceCount = commercialFactures.Count,
                        TotalAmount = commercialFactures.Sum(f => f.MontantTTC),
                        PaidAmount = commercialFactures.Sum(f => f.MontantPaye),
                        RemainingAmount = commercialFactures.Sum(f => f.MontantRestant),
                        PaymentRate = commercialFactures.Sum(f => f.MontantTTC) > 0
                            ? (commercialFactures.Sum(f => f.MontantPaye) / commercialFactures.Sum(f => f.MontantTTC)) * 100
                            : 0,
                        AverageInvoiceAmount = commercialFactures.Any()
                            ? commercialFactures.Average(f => f.MontantTTC)
                            : 0
                    };
                }).OrderByDescending(s => s.TotalAmount)
            };

            // Convertir les données en JSON
            return JsonConvert.SerializeObject(reportData, Formatting.Indented);
        }

        /// <summary>
        /// Génère un rapport sur les modes de paiement
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Données du rapport au format JSON</returns>
        public async Task<string> GeneratePaymentMethodsReportAsync(DateTime dateDebut, DateTime dateFin)
        {
            // Récupérer les paiements de la période
            var paiements = await _paiementRepository.GetByPeriodAsync(dateDebut, dateFin);

            // Préparer les données du rapport
            var reportData = new
            {
                Title = "Rapport des modes de paiement",
                GeneratedAt = DateTime.Now,
                Period = new { Start = dateDebut, End = dateFin },
                TotalPayments = paiements.Count(),
                TotalAmount = paiements.Sum(p => p.Montant),
                PaymentMethods = paiements.GroupBy(p => p.ModePaiement)
                    .Select(g => new
                    {
                        Method = g.Key,
                        Count = g.Count(),
                        Amount = g.Sum(p => p.Montant),
                        Percentage = paiements.Sum(p => p.Montant) > 0
                            ? (g.Sum(p => p.Montant) / paiements.Sum(p => p.Montant)) * 100
                            : 0
                    }).OrderByDescending(m => m.Amount)
            };

            // Convertir les données en JSON
            return JsonConvert.SerializeObject(reportData, Formatting.Indented);
        }

        /// <summary>
        /// Génère un rapport sur les relances effectuées
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Données du rapport au format JSON</returns>
        public async Task<string> GenerateRemindersReportAsync(DateTime dateDebut, DateTime dateFin)
        {
            // Récupérer les relances de la période
            var relances = await _relanceRepository.GetByPeriodAsync(dateDebut, dateFin);

            // Récupérer les utilisateurs associés
            var utilisateurIds = relances.Where(r => r.UtilisateurId.HasValue).Select(r => r.UtilisateurId.Value).Distinct().ToList();
            var utilisateurs = new Dictionary<int, string>();
            foreach (var utilisateurId in utilisateurIds)
            {
                var utilisateur = await _utilisateurRepository.GetByIdAsync(utilisateurId);
                if (utilisateur != null)
                {
                    utilisateurs.Add(utilisateurId, utilisateur.NomComplet);
                }
            }

            // Récupérer les factures associées
            var factureIds = relances.Select(r => r.FactureId).Distinct().ToList();
            var factures = new Dictionary<int, object>();
            foreach (var factureId in factureIds)
            {
                var facture = await _factureRepository.GetByIdAsync(factureId);
                if (facture != null)
                {
                    var client = await _clientRepository.GetByIdAsync(facture.ClientId);
                    factures.Add(factureId, new
                    {
                        facture.Id,
                        facture.Numero,
                        facture.ClientId,
                        ClientName = client?.RaisonSociale ?? "Client inconnu",
                        facture.MontantTTC,
                        facture.MontantRestant
                    });
                }
            }

            // Préparer les données du rapport
            var reportData = new
            {
                Title = "Rapport des relances effectuées",
                GeneratedAt = DateTime.Now,
                Period = new { Start = dateDebut, End = dateFin },
                TotalReminders = relances.Count(),
                RemindersByType = relances.GroupBy(r => r.Type)
                    .Select(g => new
                    {
                        Type = g.Key,
                        Count = g.Count(),
                        Percentage = relances.Count() > 0 ? (g.Count() / (double)relances.Count()) * 100 : 0
                    }),
                RemindersByStatus = relances.GroupBy(r => r.Statut)
                    .Select(g => new
                    {
                        Status = g.Key,
                        Count = g.Count(),
                        Percentage = relances.Count() > 0 ? (g.Count() / (double)relances.Count()) * 100 : 0
                    }),
                RemindersByLevel = relances.GroupBy(r => r.Niveau)
                    .Select(g => new
                    {
                        Level = g.Key,
                        Count = g.Count(),
                        Percentage = relances.Count() > 0 ? (g.Count() / (double)relances.Count()) * 100 : 0
                    }).OrderBy(r => r.Level),
                RemindersByUser = utilisateurIds.Select(utilisateurId =>
                {
                    var userReminders = relances.Where(r => r.UtilisateurId == utilisateurId).ToList();
                    return new
                    {
                        UserId = utilisateurId,
                        UserName = utilisateurs.ContainsKey(utilisateurId) ? utilisateurs[utilisateurId] : "Utilisateur inconnu",
                        Count = userReminders.Count,
                        Percentage = relances.Count() > 0 ? (userReminders.Count / (double)relances.Count()) * 100 : 0
                    };
                }).OrderByDescending(r => r.Count),
                Reminders = relances.Select(r => new
                {
                    r.Id,
                    r.FactureId,
                    Invoice = factures.ContainsKey(r.FactureId) ? factures[r.FactureId] : null,
                    r.Type,
                    r.DateRelance,
                    r.Niveau,
                    r.Statut,
                    r.UtilisateurId,
                    UserName = r.UtilisateurId.HasValue && utilisateurs.ContainsKey(r.UtilisateurId.Value)
                        ? utilisateurs[r.UtilisateurId.Value]
                        : "Utilisateur inconnu",
                    r.DateProchaineRelance
                }).OrderByDescending(r => r.DateRelance)
            };

            // Convertir les données en JSON
            return JsonConvert.SerializeObject(reportData, Formatting.Indented);
        }

        /// <summary>
        /// Génère un rapport sur le taux de recouvrement
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Données du rapport au format JSON</returns>
        public async Task<string> GenerateRecoveryRateReportAsync(DateTime dateDebut, DateTime dateFin)
        {
            // Récupérer les factures de la période
            var factures = await _factureRepository.GetByPeriodAsync(dateDebut, dateFin);

            // Calculer les statistiques globales
            var totalAmount = factures.Sum(f => f.MontantTTC);
            var paidAmount = factures.Sum(f => f.MontantPaye);
            var remainingAmount = factures.Sum(f => f.MontantRestant);
            var recoveryRate = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;

            // Calculer les statistiques par mois
            var monthlyStats = factures.GroupBy(f => new { Year = f.DateEmission.Year, Month = f.DateEmission.Month })
                .Select(g => new
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    Period = new DateTime(g.Key.Year, g.Key.Month, 1).ToString("MMMM yyyy"),
                    InvoiceCount = g.Count(),
                    TotalAmount = g.Sum(f => f.MontantTTC),
                    PaidAmount = g.Sum(f => f.MontantPaye),
                    RemainingAmount = g.Sum(f => f.MontantRestant),
                    RecoveryRate = g.Sum(f => f.MontantTTC) > 0 ? (g.Sum(f => f.MontantPaye) / g.Sum(f => f.MontantTTC)) * 100 : 0
                }).OrderBy(s => s.Year).ThenBy(s => s.Month);

            // Préparer les données du rapport
            var reportData = new
            {
                Title = "Rapport sur le taux de recouvrement",
                GeneratedAt = DateTime.Now,
                Period = new { Start = dateDebut, End = dateFin },
                GlobalStats = new
                {
                    InvoiceCount = factures.Count(),
                    TotalAmount = totalAmount,
                    PaidAmount = paidAmount,
                    RemainingAmount = remainingAmount,
                    RecoveryRate = recoveryRate
                },
                MonthlyStats = monthlyStats,
                StatusStats = factures.GroupBy(f => f.Statut)
                    .Select(g => new
                    {
                        Status = g.Key,
                        InvoiceCount = g.Count(),
                        TotalAmount = g.Sum(f => f.MontantTTC),
                        Percentage = factures.Count() > 0 ? (g.Count() / (double)factures.Count()) * 100 : 0
                    }).OrderByDescending(s => s.InvoiceCount)
            };

            // Convertir les données en JSON
            return JsonConvert.SerializeObject(reportData, Formatting.Indented);
        }

        /// <summary>
        /// Exporte un rapport au format CSV
        /// </summary>
        /// <param name="reportData">Données du rapport au format JSON</param>
        /// <param name="filePath">Chemin du fichier de destination</param>
        /// <returns>True si l'exportation a réussi, sinon False</returns>
        public async Task<bool> ExportReportToCsvAsync(string reportData, string filePath)
        {
            if (string.IsNullOrEmpty(reportData) || string.IsNullOrEmpty(filePath))
                return false;

            try
            {
                // Désérialiser les données JSON
                var data = JsonConvert.DeserializeObject<dynamic>(reportData);
                if (data == null)
                    return false;

                // Créer le répertoire de destination si nécessaire
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    Directory.CreateDirectory(directory);

                // Déterminer les données à exporter
                var csvData = new StringBuilder();

                // Ajouter l'en-tête du rapport
                csvData.AppendLine($"Titre: {data.Title}");
                csvData.AppendLine($"Généré le: {data.GeneratedAt}");
                csvData.AppendLine();

                // Exporter les données principales
                if (data.Invoices != null)
                {
                    // Exporter les factures
                    csvData.AppendLine("Factures:");
                    csvData.AppendLine("Numéro,Client,Date émission,Date échéance,Montant TTC,Montant payé,Montant restant,Statut");
                    foreach (var invoice in data.Invoices)
                    {
                        csvData.AppendLine($"{invoice.Numero},{invoice.ClientName},{invoice.DateEmission:yyyy-MM-dd},{invoice.DateEcheance:yyyy-MM-dd},{invoice.MontantTTC},{invoice.MontantPaye},{invoice.MontantRestant},{invoice.Statut}");
                    }
                }
                else if (data.Payments != null)
                {
                    // Exporter les paiements
                    csvData.AppendLine("Paiements:");
                    csvData.AppendLine("Référence,Client,Date paiement,Montant,Mode de paiement,Référence bancaire");
                    foreach (var payment in data.Payments)
                    {
                        csvData.AppendLine($"{payment.Reference},{payment.ClientName},{payment.DatePaiement:yyyy-MM-dd},{payment.Montant},{payment.ModePaiement},{payment.ReferenceBancaire}");
                    }
                }
                else if (data.Clients != null)
                {
                    // Exporter les clients
                    csvData.AppendLine("Clients:");
                    csvData.AppendLine("Code,Raison sociale,Solde actuel,Nombre de factures en retard,Montant en retard");
                    foreach (var client in data.Clients)
                    {
                        csvData.AppendLine($"{client.Code},{client.RaisonSociale},{client.SoldeActuel},{client.OverdueInvoicesCount},{client.OverdueAmount}");
                    }
                }
                else if (data.Reminders != null)
                {
                    // Exporter les relances
                    csvData.AppendLine("Relances:");
                    csvData.AppendLine("Facture,Client,Type,Date relance,Niveau,Statut,Utilisateur");
                    foreach (var reminder in data.Reminders)
                    {
                        var invoice = reminder.Invoice;
                        csvData.AppendLine($"{invoice?.Numero},{invoice?.ClientName},{reminder.Type},{reminder.DateRelance:yyyy-MM-dd},{reminder.Niveau},{reminder.Statut},{reminder.UserName}");
                    }
                }

                // Enregistrer le fichier CSV
                await File.WriteAllTextAsync(filePath, csvData.ToString(), Encoding.UTF8);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Exporte un rapport au format Excel
        /// </summary>
        /// <param name="reportData">Données du rapport au format JSON</param>
        /// <param name="filePath">Chemin du fichier de destination</param>
        /// <returns>True si l'exportation a réussi, sinon False</returns>
        public async Task<bool> ExportReportToExcelAsync(string reportData, string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(reportData) || string.IsNullOrEmpty(filePath))
                    return false;

                // Désérialiser les données JSON
                dynamic data = JsonConvert.DeserializeObject<dynamic>(reportData);
                if (data == null)
                    return false;

                // Créer un nouveau document Excel en utilisant une approche sans dépendance externe
                // Nous allons générer un fichier XML au format Excel 2003 (SpreadsheetML)
                var sb = new StringBuilder();

                // En-tête XML
                sb.AppendLine("<?xml version=\"1.0\"?>");
                sb.AppendLine("<?mso-application progid=\"Excel.Sheet\"?>");
                sb.AppendLine("<Workbook xmlns=\"urn:schemas-microsoft-com:office:spreadsheet\"");
                sb.AppendLine(" xmlns:o=\"urn:schemas-microsoft-com:office:office\"");
                sb.AppendLine(" xmlns:x=\"urn:schemas-microsoft-com:office:excel\"");
                sb.AppendLine(" xmlns:ss=\"urn:schemas-microsoft-com:office:spreadsheet\"");
                sb.AppendLine(" xmlns:html=\"http://www.w3.org/TR/REC-html40\">");

                // Styles
                sb.AppendLine(" <Styles>");
                sb.AppendLine("  <Style ss:ID=\"Default\" ss:Name=\"Normal\">");
                sb.AppendLine("   <Alignment ss:Vertical=\"Bottom\"/>");
                sb.AppendLine("   <Borders/>");
                sb.AppendLine("   <Font ss:FontName=\"Calibri\" x:Family=\"Swiss\" ss:Size=\"11\" ss:Color=\"#000000\"/>");
                sb.AppendLine("   <Interior/>");
                sb.AppendLine("   <NumberFormat/>");
                sb.AppendLine("   <Protection/>");
                sb.AppendLine("  </Style>");
                sb.AppendLine("  <Style ss:ID=\"s62\">");
                sb.AppendLine("   <Font ss:FontName=\"Calibri\" x:Family=\"Swiss\" ss:Size=\"11\" ss:Color=\"#000000\" ss:Bold=\"1\"/>");
                sb.AppendLine("   <Interior ss:Color=\"#D9D9D9\" ss:Pattern=\"Solid\"/>");
                sb.AppendLine("  </Style>");
                sb.AppendLine(" </Styles>");

                // Feuille de calcul
                sb.AppendLine(" <Worksheet ss:Name=\"Rapport\">");
                sb.AppendLine("  <Table ss:ExpandedColumnCount=\"20\" ss:ExpandedRowCount=\"1000\" x:FullColumns=\"1\" x:FullRows=\"1\">");

                // En-tête du rapport
                sb.AppendLine("   <Row>");
                sb.AppendLine($"    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">{data.Title}</Data></Cell>");
                sb.AppendLine("   </Row>");
                sb.AppendLine("   <Row>");
                sb.AppendLine($"    <Cell><Data ss:Type=\"String\">Généré le: {data.GeneratedAt}</Data></Cell>");
                sb.AppendLine("   </Row>");
                sb.AppendLine("   <Row></Row>");

                // Exporter les données principales
                if (data.Invoices != null)
                {
                    // En-tête des colonnes pour les factures
                    sb.AppendLine("   <Row>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Numéro</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Client</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Date émission</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Date échéance</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Montant TTC</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Montant payé</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Montant restant</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Statut</Data></Cell>");
                    sb.AppendLine("   </Row>");

                    // Données des factures
                    foreach (var invoice in data.Invoices)
                    {
                        sb.AppendLine("   <Row>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{invoice.Numero}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{invoice.ClientName}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{Convert.ToDateTime(invoice.DateEmission).ToString("yyyy-MM-dd")}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{Convert.ToDateTime(invoice.DateEcheance).ToString("yyyy-MM-dd")}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"Number\">{invoice.MontantTTC}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"Number\">{invoice.MontantPaye}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"Number\">{invoice.MontantRestant}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{invoice.Statut}</Data></Cell>");
                        sb.AppendLine("   </Row>");
                    }
                }
                else if (data.Payments != null)
                {
                    // En-tête des colonnes pour les paiements
                    sb.AppendLine("   <Row>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Référence</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Client</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Facture</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Date</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Montant</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Mode</Data></Cell>");
                    sb.AppendLine("   </Row>");

                    // Données des paiements
                    foreach (var payment in data.Payments)
                    {
                        sb.AppendLine("   <Row>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{payment.Reference}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{payment.ClientName}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{payment.InvoiceNumber}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{Convert.ToDateTime(payment.Date).ToString("yyyy-MM-dd")}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"Number\">{payment.Amount}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{payment.Method}</Data></Cell>");
                        sb.AppendLine("   </Row>");
                    }
                }
                else if (data.Clients != null)
                {
                    // En-tête des colonnes pour les clients
                    sb.AppendLine("   <Row>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Code</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Raison sociale</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Solde actuel</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Factures en retard</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Montant en retard</Data></Cell>");
                    sb.AppendLine("   </Row>");

                    // Données des clients
                    foreach (var client in data.Clients)
                    {
                        sb.AppendLine("   <Row>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{client.Code}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{client.RaisonSociale}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"Number\">{client.SoldeActuel}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"Number\">{client.OverdueInvoicesCount}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"Number\">{client.OverdueAmount}</Data></Cell>");
                        sb.AppendLine("   </Row>");
                    }
                }
                else if (data.Reminders != null)
                {
                    // En-tête des colonnes pour les relances
                    sb.AppendLine("   <Row>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Facture</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Client</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Type</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Date relance</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Niveau</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Statut</Data></Cell>");
                    sb.AppendLine("    <Cell ss:StyleID=\"s62\"><Data ss:Type=\"String\">Utilisateur</Data></Cell>");
                    sb.AppendLine("   </Row>");

                    // Données des relances
                    foreach (var reminder in data.Reminders)
                    {
                        var invoice = reminder.Invoice;
                        sb.AppendLine("   <Row>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{(invoice != null ? invoice.Numero : "")}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{(invoice != null ? invoice.ClientName : "")}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{reminder.Type}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{Convert.ToDateTime(reminder.DateRelance).ToString("yyyy-MM-dd")}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"Number\">{reminder.Niveau}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{reminder.Statut}</Data></Cell>");
                        sb.AppendLine($"    <Cell><Data ss:Type=\"String\">{reminder.UserName}</Data></Cell>");
                        sb.AppendLine("   </Row>");
                    }
                }

                // Fermer la table et la feuille de calcul
                sb.AppendLine("  </Table>");
                sb.AppendLine(" </Worksheet>");
                sb.AppendLine("</Workbook>");

                // Enregistrer le fichier Excel
                await File.WriteAllTextAsync(filePath, sb.ToString(), Encoding.UTF8);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Exporte un rapport au format PDF
        /// </summary>
        /// <param name="reportData">Données du rapport au format JSON</param>
        /// <param name="filePath">Chemin du fichier de destination</param>
        /// <param name="title">Titre du rapport</param>
        /// <returns>True si l'exportation a réussi, sinon False</returns>
        public async Task<bool> ExportReportToPdfAsync(string reportData, string filePath, string title)
        {
            try
            {
                if (string.IsNullOrEmpty(reportData) || string.IsNullOrEmpty(filePath))
                    return false;

                // Désérialiser les données JSON
                dynamic data = JsonConvert.DeserializeObject<dynamic>(reportData);
                if (data == null)
                    return false;

                // Utiliser iText7 pour générer le PDF
                using (var writer = new PdfWriter(filePath))
                {
                    using (var pdf = new iText.Kernel.Pdf.PdfDocument(writer))
                    {
                        var document = new iText.Layout.Document(pdf);

                        // Ajouter un en-tête
                        var headerText = new iText.Layout.Element.Paragraph(data.Title.ToString())
                            .SetFontSize(18)
                            .SetBold()
                            .SetTextAlignment(iText.Layout.Properties.TextAlignment.CENTER);
                        document.Add(headerText);

                        // Ajouter la date de génération
                        var dateText = new iText.Layout.Element.Paragraph($"Généré le: {data.GeneratedAt}")
                            .SetFontSize(10)
                            .SetTextAlignment(iText.Layout.Properties.TextAlignment.RIGHT);
                        document.Add(dateText);

                        document.Add(new iText.Layout.Element.Paragraph("\n"));

                        // Créer une table pour les données
                        if (data.Invoices != null)
                        {
                            // Table pour les factures
                            var table = new iText.Layout.Element.Table(8).UseAllAvailableWidth();

                            // En-tête de la table
                            table.AddHeaderCell("Numéro");
                            table.AddHeaderCell("Client");
                            table.AddHeaderCell("Date émission");
                            table.AddHeaderCell("Date échéance");
                            table.AddHeaderCell("Montant TTC");
                            table.AddHeaderCell("Montant payé");
                            table.AddHeaderCell("Montant restant");
                            table.AddHeaderCell("Statut");

                            // Données des factures
                            foreach (var invoice in data.Invoices)
                            {
                                table.AddCell(invoice.Numero.ToString());
                                table.AddCell(invoice.ClientName.ToString());
                                table.AddCell(Convert.ToDateTime(invoice.DateEmission).ToString("yyyy-MM-dd"));
                                table.AddCell(Convert.ToDateTime(invoice.DateEcheance).ToString("yyyy-MM-dd"));
                                table.AddCell(invoice.MontantTTC.ToString());
                                table.AddCell(invoice.MontantPaye.ToString());
                                table.AddCell(invoice.MontantRestant.ToString());
                                table.AddCell(invoice.Statut.ToString());
                            }

                            document.Add(table);
                        }
                        else if (data.Payments != null)
                        {
                            // Table pour les paiements
                            var table = new iText.Layout.Element.Table(6).UseAllAvailableWidth();

                            // En-tête de la table
                            table.AddHeaderCell("Référence");
                            table.AddHeaderCell("Client");
                            table.AddHeaderCell("Facture");
                            table.AddHeaderCell("Date");
                            table.AddHeaderCell("Montant");
                            table.AddHeaderCell("Mode");

                            // Données des paiements
                            foreach (var payment in data.Payments)
                            {
                                table.AddCell(payment.Reference.ToString());
                                table.AddCell(payment.ClientName.ToString());
                                table.AddCell(payment.InvoiceNumber.ToString());
                                table.AddCell(Convert.ToDateTime(payment.Date).ToString("yyyy-MM-dd"));
                                table.AddCell(payment.Amount.ToString());
                                table.AddCell(payment.Method.ToString());
                            }

                            document.Add(table);
                        }
                        else if (data.Clients != null)
                        {
                            // Table pour les clients
                            var table = new iText.Layout.Element.Table(5).UseAllAvailableWidth();

                            // En-tête de la table
                            table.AddHeaderCell("Code");
                            table.AddHeaderCell("Raison sociale");
                            table.AddHeaderCell("Solde actuel");
                            table.AddHeaderCell("Factures en retard");
                            table.AddHeaderCell("Montant en retard");

                            // Données des clients
                            foreach (var client in data.Clients)
                            {
                                table.AddCell(client.Code.ToString());
                                table.AddCell(client.RaisonSociale.ToString());
                                table.AddCell(client.SoldeActuel.ToString());
                                table.AddCell(client.OverdueInvoicesCount.ToString());
                                table.AddCell(client.OverdueAmount.ToString());
                            }

                            document.Add(table);
                        }
                        else if (data.Reminders != null)
                        {
                            // Table pour les relances
                            var table = new iText.Layout.Element.Table(7).UseAllAvailableWidth();

                            // En-tête de la table
                            table.AddHeaderCell("Facture");
                            table.AddHeaderCell("Client");
                            table.AddHeaderCell("Type");
                            table.AddHeaderCell("Date relance");
                            table.AddHeaderCell("Niveau");
                            table.AddHeaderCell("Statut");
                            table.AddHeaderCell("Utilisateur");

                            // Données des relances
                            foreach (var reminder in data.Reminders)
                            {
                                var invoice = reminder.Invoice;
                                table.AddCell(invoice != null ? invoice.Numero.ToString() : "");
                                table.AddCell(invoice != null ? invoice.ClientName.ToString() : "");
                                table.AddCell(reminder.Type.ToString());
                                table.AddCell(Convert.ToDateTime(reminder.DateRelance).ToString("yyyy-MM-dd"));
                                table.AddCell(reminder.Niveau.ToString());
                                table.AddCell(reminder.Statut.ToString());
                                table.AddCell(reminder.UserName.ToString());
                            }

                            document.Add(table);
                        }

                        // Ajouter un pied de page
                        var footer = new iText.Layout.Element.Paragraph("RecouvreX - Rapport généré automatiquement")
                            .SetFontSize(8)
                            .SetTextAlignment(iText.Layout.Properties.TextAlignment.CENTER);
                        document.Add(footer);
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
