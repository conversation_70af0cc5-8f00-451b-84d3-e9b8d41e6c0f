using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des plans de paiement
    /// </summary>
    public interface IPlanPaiementRepository : IRepository<PlanPaiement>
    {
        /// <summary>
        /// Récupère un plan de paiement par sa référence
        /// </summary>
        /// <param name="reference">Référence du plan de paiement</param>
        /// <returns>Plan de paiement trouvé ou null</returns>
        Task<PlanPaiement> GetByReferenceAsync(string reference);

        /// <summary>
        /// Récupère un plan de paiement avec ses échéances
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement avec ses échéances</returns>
        Task<PlanPaiement> GetWithEcheancesAsync(int planPaiementId);

        /// <summary>
        /// Récupère un plan de paiement avec ses factures associées
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement avec ses factures</returns>
        Task<PlanPaiement> GetWithFacturesAsync(int planPaiementId);

        /// <summary>
        /// Récupère un plan de paiement avec ses documents associés
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement avec ses documents</returns>
        Task<PlanPaiement> GetWithDocumentsAsync(int planPaiementId);

        /// <summary>
        /// Récupère un plan de paiement complet avec toutes ses relations
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement complet</returns>
        Task<PlanPaiement> GetCompleteAsync(int planPaiementId);

        /// <summary>
        /// Récupère tous les plans de paiement d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des plans de paiement du client</returns>
        Task<IEnumerable<PlanPaiement>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère tous les plans de paiement associés à une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des plans de paiement associés à la facture</returns>
        Task<IEnumerable<PlanPaiement>> GetByFactureIdAsync(int factureId);

        /// <summary>
        /// Récupère les plans de paiement par statut
        /// </summary>
        /// <param name="statut">Statut des plans de paiement</param>
        /// <returns>Liste des plans de paiement ayant le statut spécifié</returns>
        Task<IEnumerable<PlanPaiement>> GetByStatutAsync(string statut);

        /// <summary>
        /// Récupère les plans de paiement par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des plans de paiement créés dans la période spécifiée</returns>
        Task<IEnumerable<PlanPaiement>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère les plans de paiement avec des échéances à venir
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des plans de paiement avec des échéances dans la période spécifiée</returns>
        Task<IEnumerable<PlanPaiement>> GetWithUpcomingEcheancesAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère les plans de paiement avec des échéances en retard
        /// </summary>
        /// <returns>Liste des plans de paiement avec des échéances en retard</returns>
        Task<IEnumerable<PlanPaiement>> GetWithLateEcheancesAsync();

        /// <summary>
        /// Associe un plan de paiement à une facture
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="montantCouvert">Montant de la facture couvert par le plan</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'association a réussi, sinon False</returns>
        Task<bool> AssociateToFactureAsync(int planPaiementId, int factureId, decimal montantCouvert, int userId);

        /// <summary>
        /// Dissocie un plan de paiement d'une facture
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la dissociation a réussi, sinon False</returns>
        Task<bool> DissociateFromFactureAsync(int planPaiementId, int factureId, int userId);

        /// <summary>
        /// Associe un document à un plan de paiement
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="documentId">Identifiant du document</param>
        /// <param name="typeDocument">Type de document</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'association a réussi, sinon False</returns>
        Task<bool> AssociateDocumentAsync(int planPaiementId, int documentId, string typeDocument, int userId);

        /// <summary>
        /// Met à jour le statut d'un plan de paiement
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateStatutAsync(int planPaiementId, string statut, int userId);
    }
}
