-- Script pour ajouter les tables de gestion des litiges
USE RecouvreX;
GO

-- Vérifier si la table CategoriesLitige existe déjà
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[app].[CategoriesLitige]') AND type in (N'U'))
BEGIN
    -- Créer la table CategoriesLitige
    CREATE TABLE [app].[CategoriesLitige] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [Nom] NVARCHAR(100) NOT NULL,
        [Description] NVARCHAR(MAX) NULL,
        [Couleur] NVARCHAR(7) NOT NULL DEFAULT '#007BFF',
        [DelaiResolutionJours] INT NOT NULL DEFAULT 30,
        [DateCreation] DATETIME NOT NULL DEFAULT GETDATE(),
        [CreePar] INT NOT NULL,
        [DateModification] DATETIME NULL,
        [ModifiePar] INT NULL,
        [EstActif] BIT NOT NULL DEFAULT 1,
        CONSTRAINT [UQ_CategoriesLitige_Nom] UNIQUE ([Nom])
    );

    PRINT 'Table CategoriesLitige créée avec succès.';
END
ELSE
BEGIN
    PRINT 'La table CategoriesLitige existe déjà.';
END
GO

-- Vérifier si la table EtapesLitige existe déjà
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[app].[EtapesLitige]') AND type in (N'U'))
BEGIN
    -- Créer la table EtapesLitige
    CREATE TABLE [app].[EtapesLitige] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [Nom] NVARCHAR(100) NOT NULL,
        [Description] NVARCHAR(MAX) NULL,
        [Ordre] INT NOT NULL,
        [DelaiJours] INT NOT NULL DEFAULT 7,
        [EstEtapeFinale] BIT NOT NULL DEFAULT 0,
        [NecessiteValidation] BIT NOT NULL DEFAULT 0,
        [DateCreation] DATETIME NOT NULL DEFAULT GETDATE(),
        [CreePar] INT NOT NULL,
        [DateModification] DATETIME NULL,
        [ModifiePar] INT NULL,
        [EstActif] BIT NOT NULL DEFAULT 1,
        CONSTRAINT [UQ_EtapesLitige_Nom] UNIQUE ([Nom]),
        CONSTRAINT [UQ_EtapesLitige_Ordre] UNIQUE ([Ordre])
    );

    PRINT 'Table EtapesLitige créée avec succès.';
END
ELSE
BEGIN
    PRINT 'La table EtapesLitige existe déjà.';
END
GO

-- Vérifier si la table Litiges existe déjà
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[app].[Litiges]') AND type in (N'U'))
BEGIN
    -- Créer la table Litiges
    CREATE TABLE [app].[Litiges] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [FactureId] INT NOT NULL,
        [CategorieLitigeId] INT NOT NULL,
        [EtapeLitigeId] INT NOT NULL,
        [MontantConteste] DECIMAL(18, 2) NOT NULL DEFAULT 0,
        [Description] NVARCHAR(MAX) NOT NULL,
        [DateOuverture] DATETIME NOT NULL DEFAULT GETDATE(),
        [DateResolution] DATETIME NULL,
        [DateEcheance] DATETIME NOT NULL,
        [ResponsableId] INT NOT NULL,
        [Priorite] INT NOT NULL DEFAULT 2,
        [Statut] NVARCHAR(50) NOT NULL DEFAULT 'Ouvert',
        [Solution] NVARCHAR(MAX) NULL,
        [DateCreation] DATETIME NOT NULL DEFAULT GETDATE(),
        [CreePar] INT NOT NULL,
        [DateModification] DATETIME NULL,
        [ModifiePar] INT NULL,
        [EstActif] BIT NOT NULL DEFAULT 1,
        CONSTRAINT [FK_Litiges_Factures] FOREIGN KEY ([FactureId]) REFERENCES [app].[Factures] ([Id]),
        CONSTRAINT [FK_Litiges_CategoriesLitige] FOREIGN KEY ([CategorieLitigeId]) REFERENCES [app].[CategoriesLitige] ([Id]),
        CONSTRAINT [FK_Litiges_EtapesLitige] FOREIGN KEY ([EtapeLitigeId]) REFERENCES [app].[EtapesLitige] ([Id]),
        CONSTRAINT [FK_Litiges_Utilisateurs] FOREIGN KEY ([ResponsableId]) REFERENCES [app].[Utilisateurs] ([Id])
    );

    PRINT 'Table Litiges créée avec succès.';
END
ELSE
BEGIN
    PRINT 'La table Litiges existe déjà.';
END
GO

-- Vérifier si la table CommentairesLitige existe déjà
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[app].[CommentairesLitige]') AND type in (N'U'))
BEGIN
    -- Créer la table CommentairesLitige
    CREATE TABLE [app].[CommentairesLitige] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [LitigeId] INT NOT NULL,
        [Contenu] NVARCHAR(MAX) NOT NULL,
        [DateCommentaire] DATETIME NOT NULL DEFAULT GETDATE(),
        [UtilisateurId] INT NOT NULL,
        [EstInterne] BIT NOT NULL DEFAULT 0,
        [DateCreation] DATETIME NOT NULL DEFAULT GETDATE(),
        [CreePar] INT NOT NULL,
        [DateModification] DATETIME NULL,
        [ModifiePar] INT NULL,
        [EstActif] BIT NOT NULL DEFAULT 1,
        CONSTRAINT [FK_CommentairesLitige_Litiges] FOREIGN KEY ([LitigeId]) REFERENCES [app].[Litiges] ([Id]),
        CONSTRAINT [FK_CommentairesLitige_Utilisateurs] FOREIGN KEY ([UtilisateurId]) REFERENCES [app].[Utilisateurs] ([Id])
    );

    PRINT 'Table CommentairesLitige créée avec succès.';
END
ELSE
BEGIN
    PRINT 'La table CommentairesLitige existe déjà.';
END
GO

-- Vérifier si la table HistoriqueEtapesLitige existe déjà
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[app].[HistoriqueEtapesLitige]') AND type in (N'U'))
BEGIN
    -- Créer la table HistoriqueEtapesLitige
    CREATE TABLE [app].[HistoriqueEtapesLitige] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [LitigeId] INT NOT NULL,
        [EtapeLitigeId] INT NOT NULL,
        [DateDebut] DATETIME NOT NULL DEFAULT GETDATE(),
        [DateFin] DATETIME NULL,
        [UtilisateurId] INT NOT NULL,
        [Commentaire] NVARCHAR(MAX) NULL,
        [DateCreation] DATETIME NOT NULL DEFAULT GETDATE(),
        [CreePar] INT NOT NULL,
        [DateModification] DATETIME NULL,
        [ModifiePar] INT NULL,
        [EstActif] BIT NOT NULL DEFAULT 1,
        CONSTRAINT [FK_HistoriqueEtapesLitige_Litiges] FOREIGN KEY ([LitigeId]) REFERENCES [app].[Litiges] ([Id]),
        CONSTRAINT [FK_HistoriqueEtapesLitige_EtapesLitige] FOREIGN KEY ([EtapeLitigeId]) REFERENCES [app].[EtapesLitige] ([Id]),
        CONSTRAINT [FK_HistoriqueEtapesLitige_Utilisateurs] FOREIGN KEY ([UtilisateurId]) REFERENCES [app].[Utilisateurs] ([Id])
    );

    PRINT 'Table HistoriqueEtapesLitige créée avec succès.';
END
ELSE
BEGIN
    PRINT 'La table HistoriqueEtapesLitige existe déjà.';
END
GO

-- Créer les index pour améliorer les performances
CREATE INDEX [IX_Litiges_FactureId] ON [app].[Litiges] ([FactureId]);
CREATE INDEX [IX_Litiges_CategorieLitigeId] ON [app].[Litiges] ([CategorieLitigeId]);
CREATE INDEX [IX_Litiges_EtapeLitigeId] ON [app].[Litiges] ([EtapeLitigeId]);
CREATE INDEX [IX_Litiges_ResponsableId] ON [app].[Litiges] ([ResponsableId]);
CREATE INDEX [IX_Litiges_Statut] ON [app].[Litiges] ([Statut]);
CREATE INDEX [IX_Litiges_DateEcheance] ON [app].[Litiges] ([DateEcheance]);
CREATE INDEX [IX_Litiges_EstActif] ON [app].[Litiges] ([EstActif]);

CREATE INDEX [IX_CommentairesLitige_LitigeId] ON [app].[CommentairesLitige] ([LitigeId]);
CREATE INDEX [IX_CommentairesLitige_UtilisateurId] ON [app].[CommentairesLitige] ([UtilisateurId]);
CREATE INDEX [IX_CommentairesLitige_EstActif] ON [app].[CommentairesLitige] ([EstActif]);

CREATE INDEX [IX_HistoriqueEtapesLitige_LitigeId] ON [app].[HistoriqueEtapesLitige] ([LitigeId]);
CREATE INDEX [IX_HistoriqueEtapesLitige_EtapeLitigeId] ON [app].[HistoriqueEtapesLitige] ([EtapeLitigeId]);
CREATE INDEX [IX_HistoriqueEtapesLitige_UtilisateurId] ON [app].[HistoriqueEtapesLitige] ([UtilisateurId]);
CREATE INDEX [IX_HistoriqueEtapesLitige_EstActif] ON [app].[HistoriqueEtapesLitige] ([EstActif]);
GO

-- Insérer des données initiales pour les catégories de litiges
INSERT INTO [app].[CategoriesLitige] ([Nom], [Description], [Couleur], [DelaiResolutionJours], [CreePar])
VALUES 
    ('Facturation incorrecte', 'Erreur dans le montant, les quantités ou les prix facturés', '#DC3545', 15, 1),
    ('Produit défectueux', 'Produit livré ne correspondant pas aux attentes ou défectueux', '#FFC107', 30, 1),
    ('Livraison incomplète', 'Livraison partielle ou manquante', '#17A2B8', 20, 1),
    ('Retard de livraison', 'Livraison effectuée après la date convenue', '#28A745', 10, 1),
    ('Erreur administrative', 'Erreur dans les informations client, adresse, etc.', '#6C757D', 7, 1),
    ('Autre', 'Autre type de litige', '#007BFF', 30, 1);
GO

-- Insérer des données initiales pour les étapes de litiges
INSERT INTO [app].[EtapesLitige] ([Nom], [Description], [Ordre], [DelaiJours], [EstEtapeFinale], [NecessiteValidation], [CreePar])
VALUES 
    ('Ouverture', 'Ouverture et enregistrement du litige', 1, 1, 0, 0, 1),
    ('Analyse', 'Analyse du litige et collecte d''informations', 2, 3, 0, 0, 1),
    ('Vérification', 'Vérification des faits et documents', 3, 5, 0, 0, 1),
    ('Négociation', 'Négociation avec le client', 4, 7, 0, 0, 1),
    ('Proposition', 'Proposition de solution', 5, 3, 0, 1, 1),
    ('Validation', 'Validation de la solution par le client', 6, 5, 0, 1, 1),
    ('Mise en œuvre', 'Mise en œuvre de la solution', 7, 5, 0, 0, 1),
    ('Clôture', 'Clôture du litige', 8, 1, 1, 0, 1);
GO

-- Ajouter des permissions pour la gestion des litiges
INSERT INTO [app].[Permissions] ([Nom], [Description], [Code], [Module], [CreePar], [EstActif])
VALUES 
    ('Voir litiges', 'Permet de voir la liste des litiges', 'DISPUTES_VIEW', 'Litiges', 1, 1),
    ('Créer litige', 'Permet de créer un nouveau litige', 'DISPUTES_CREATE', 'Litiges', 1, 1),
    ('Modifier litige', 'Permet de modifier un litige existant', 'DISPUTES_EDIT', 'Litiges', 1, 1),
    ('Supprimer litige', 'Permet de supprimer un litige', 'DISPUTES_DELETE', 'Litiges', 1, 1),
    ('Changer étape litige', 'Permet de changer l''étape d''un litige', 'DISPUTES_CHANGE_STAGE', 'Litiges', 1, 1),
    ('Résoudre litige', 'Permet de résoudre un litige', 'DISPUTES_RESOLVE', 'Litiges', 1, 1),
    ('Gérer catégories litige', 'Permet de gérer les catégories de litiges', 'DISPUTES_MANAGE_CATEGORIES', 'Litiges', 1, 1),
    ('Gérer étapes litige', 'Permet de gérer les étapes de litiges', 'DISPUTES_MANAGE_STAGES', 'Litiges', 1, 1);
GO

PRINT 'Script d''ajout des tables de gestion des litiges exécuté avec succès.';
GO
