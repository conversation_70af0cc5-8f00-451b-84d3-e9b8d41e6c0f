# Script PowerShell pour créer la base de données RecouvreX
# Ce script exécute tous les scripts SQL dans l'ordre

param (
    [string]$ServerInstance = "(local)\SQLEXPRESS",
    [string]$DatabaseName = "RecouvreX",
    [switch]$UseWindowsAuth = $true,
    [string]$Username,
    [string]$Password
)

# Fonction pour afficher un message avec une couleur
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# Vérifier si sqlcmd est installé
try {
    $sqlcmdVersion = sqlcmd -? | Select-String -Pattern "Version"
    Write-ColorOutput Green "Utilisation de $sqlcmdVersion"
}
catch {
    Write-ColorOutput Red "Erreur: sqlcmd n'est pas installé ou n'est pas dans le PATH."
    Write-ColorOutput Red "Veuillez installer SQL Server Command Line Utilities."
    exit 1
}

# Construire la chaîne de connexion
$connectionParams = "-S `"$ServerInstance`""
if ($UseWindowsAuth) {
    $connectionParams += " -E"
    Write-ColorOutput Green "Utilisation de l'authentification Windows"
}
else {
    if (-not $Username -or -not $Password) {
        Write-ColorOutput Red "Erreur: Nom d'utilisateur et mot de passe requis pour l'authentification SQL Server."
        exit 1
    }
    $connectionParams += " -U `"$Username`" -P `"$Password`""
    Write-ColorOutput Green "Utilisation de l'authentification SQL Server avec l'utilisateur $Username"
}

# Chemin vers le dossier des scripts SQL
$scriptPath = Join-Path $PSScriptRoot "SQL"
$mainScript = Join-Path $scriptPath "00_RunAllScripts.sql"

# Vérifier si le script principal existe
if (-not (Test-Path $mainScript)) {
    Write-ColorOutput Red "Erreur: Le script principal n'existe pas: $mainScript"
    exit 1
}

# Exécuter le script principal
Write-ColorOutput Cyan "=== Création de la base de données $DatabaseName sur $ServerInstance ==="
Write-ColorOutput Cyan "Exécution du script principal: $mainScript"
Write-ColorOutput Yellow "Veuillez patienter, cela peut prendre quelques minutes..."

try {
    # Changer le répertoire courant pour que les références relatives dans le script fonctionnent
    Push-Location $scriptPath
    
    # Exécuter le script SQL
    $command = "sqlcmd $connectionParams -i `"00_RunAllScripts.sql`" -b"
    Write-ColorOutput Gray "Commande: $command"
    
    $output = Invoke-Expression $command
    
    # Restaurer le répertoire courant
    Pop-Location
    
    # Afficher la sortie
    $output | ForEach-Object {
        if ($_ -match "Erreur") {
            Write-ColorOutput Red $_
        }
        else {
            Write-Output $_
        }
    }
    
    Write-ColorOutput Green "=== Base de données $DatabaseName créée avec succès ==="
    Write-ColorOutput Green "Informations de connexion par défaut:"
    Write-ColorOutput Green "- Nom d'utilisateur: admin"
    Write-ColorOutput Green "- Mot de passe: Admin123!"
    Write-ColorOutput Yellow "N'oubliez pas de changer le mot de passe par défaut après la première connexion."
}
catch {
    Write-ColorOutput Red "Erreur lors de l'exécution du script SQL:"
    Write-ColorOutput Red $_.Exception.Message
    
    # Restaurer le répertoire courant en cas d'erreur
    Pop-Location
    exit 1
}
