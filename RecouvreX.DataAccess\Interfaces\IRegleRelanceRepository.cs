using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des règles de relance
    /// </summary>
    public interface IRegleRelanceRepository : IRepository<RegleRelance>
    {
        /// <summary>
        /// Récupère les règles de relance actives
        /// </summary>
        /// <returns>Liste des règles de relance actives</returns>
        Task<IEnumerable<RegleRelance>> GetActiveAsync();

        /// <summary>
        /// Récupère les règles de relance par type de client
        /// </summary>
        /// <param name="typeClient">Type de client</param>
        /// <returns>Liste des règles de relance pour le type de client spécifié</returns>
        Task<IEnumerable<RegleRelance>> GetByTypeClientAsync(string typeClient);

        /// <summary>
        /// Récupère les règles de relance avec leurs modèles associés
        /// </summary>
        /// <returns>Liste des règles de relance avec leurs modèles</returns>
        Task<IEnumerable<RegleRelance>> GetWithModelesAsync();
    }
}
