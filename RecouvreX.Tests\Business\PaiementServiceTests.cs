using Moq;
using RecouvreX.Business.Interfaces;
using RecouvreX.Business.Services;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Xunit;

namespace RecouvreX.Tests.Business
{
    public class PaiementServiceTests
    {
        private readonly Mock<IPaiementRepository> _mockPaiementRepository;
        private readonly Mock<IFactureRepository> _mockFactureRepository;
        private readonly Mock<IAuditService> _mockAuditService;
        private readonly IPaiementService _paiementService;

        public PaiementServiceTests()
        {
            _mockPaiementRepository = new Mock<IPaiementRepository>();
            _mockFactureRepository = new Mock<IFactureRepository>();
            _mockAuditService = new Mock<IAuditService>();
            _paiementService = new PaiementService(
                _mockPaiementRepository.Object,
                _mockFactureRepository.Object,
                _mockAuditService.Object);
        }

        [Fact]
        public async Task GetAllAsync_ShouldReturnAllPaiements()
        {
            // Arrange
            var expectedPaiements = new List<Paiement>
            {
                new Paiement { Id = 1, FactureId = 1, Montant = 500, DatePaiement = DateTime.Today.AddDays(-5) },
                new Paiement { Id = 2, FactureId = 2, Montant = 1000, DatePaiement = DateTime.Today.AddDays(-3) },
                new Paiement { Id = 3, FactureId = 1, Montant = 500, DatePaiement = DateTime.Today }
            };

            _mockPaiementRepository.Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(expectedPaiements);

            // Act
            var result = await _paiementService.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedPaiements.Count, result.Count());
            Assert.Equal(expectedPaiements, result);
            _mockPaiementRepository.Verify(repo => repo.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithValidId_ShouldReturnPaiement()
        {
            // Arrange
            int paiementId = 1;
            var expectedPaiement = new Paiement { Id = paiementId, FactureId = 1, Montant = 500, DatePaiement = DateTime.Today.AddDays(-5) };

            _mockPaiementRepository.Setup(repo => repo.GetByIdAsync(paiementId))
                .ReturnsAsync(expectedPaiement);

            // Act
            var result = await _paiementService.GetByIdAsync(paiementId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedPaiement, result);
            _mockPaiementRepository.Verify(repo => repo.GetByIdAsync(paiementId), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            int paiementId = 999;

            _mockPaiementRepository.Setup(repo => repo.GetByIdAsync(paiementId))
                .ReturnsAsync((Paiement)null);

            // Act
            var result = await _paiementService.GetByIdAsync(paiementId);

            // Assert
            Assert.Null(result);
            _mockPaiementRepository.Verify(repo => repo.GetByIdAsync(paiementId), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_ShouldCreatePaiementUpdateFactureAndReturnId()
        {
            // Arrange
            int userId = 1;
            int expectedPaiementId = 1;
            var paiement = new Paiement
            {
                FactureId = 1,
                Montant = 500,
                DatePaiement = DateTime.Today,
                ModePaiement = "Virement",
                Reference = "REF123"
            };

            var facture = new Facture
            {
                Id = 1,
                Numero = "FAC001",
                ClientId = 1,
                MontantTTC = 1000,
                MontantPaye = 0,
                MontantRestant = 1000,
                Statut = "En attente"
            };

            _mockPaiementRepository.Setup(repo => repo.CreateAsync(It.IsAny<Paiement>()))
                .ReturnsAsync(expectedPaiementId);

            _mockFactureRepository.Setup(repo => repo.GetByIdAsync(paiement.FactureId))
                .ReturnsAsync(facture);

            _mockFactureRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Facture>()))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _paiementService.CreateAsync(paiement, userId);

            // Assert
            Assert.Equal(expectedPaiementId, result);
            Assert.Equal(expectedPaiementId, paiement.Id);
            Assert.Equal(userId, paiement.UtilisateurId);
            
            // Vérifier que la facture a été mise à jour
            _mockFactureRepository.Verify(repo => repo.UpdateAsync(It.Is<Facture>(f =>
                f.Id == facture.Id &&
                f.MontantPaye == paiement.Montant &&
                f.MontantRestant == facture.MontantTTC - paiement.Montant &&
                f.Statut == "Payée partiellement"
            )), Times.Once);
            
            _mockPaiementRepository.Verify(repo => repo.CreateAsync(paiement), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Paiement",
                It.IsAny<string>(),
                expectedPaiementId,
                userId), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_WithFullPayment_ShouldUpdateFactureStatusToPaid()
        {
            // Arrange
            int userId = 1;
            int expectedPaiementId = 1;
            var paiement = new Paiement
            {
                FactureId = 1,
                Montant = 1000, // Paiement complet
                DatePaiement = DateTime.Today,
                ModePaiement = "Virement",
                Reference = "REF123"
            };

            var facture = new Facture
            {
                Id = 1,
                Numero = "FAC001",
                ClientId = 1,
                MontantTTC = 1000,
                MontantPaye = 0,
                MontantRestant = 1000,
                Statut = "En attente"
            };

            _mockPaiementRepository.Setup(repo => repo.CreateAsync(It.IsAny<Paiement>()))
                .ReturnsAsync(expectedPaiementId);

            _mockFactureRepository.Setup(repo => repo.GetByIdAsync(paiement.FactureId))
                .ReturnsAsync(facture);

            _mockFactureRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Facture>()))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _paiementService.CreateAsync(paiement, userId);

            // Assert
            Assert.Equal(expectedPaiementId, result);
            
            // Vérifier que la facture a été mise à jour avec le statut "Payée"
            _mockFactureRepository.Verify(repo => repo.UpdateAsync(It.Is<Facture>(f =>
                f.Id == facture.Id &&
                f.MontantPaye == paiement.Montant &&
                f.MontantRestant == 0 &&
                f.Statut == "Payée"
            )), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_WithExcessPayment_ShouldThrowException()
        {
            // Arrange
            int userId = 1;
            var paiement = new Paiement
            {
                FactureId = 1,
                Montant = 1500, // Montant supérieur au montant de la facture
                DatePaiement = DateTime.Today,
                ModePaiement = "Virement",
                Reference = "REF123"
            };

            var facture = new Facture
            {
                Id = 1,
                Numero = "FAC001",
                ClientId = 1,
                MontantTTC = 1000,
                MontantPaye = 0,
                MontantRestant = 1000,
                Statut = "En attente"
            };

            _mockFactureRepository.Setup(repo => repo.GetByIdAsync(paiement.FactureId))
                .ReturnsAsync(facture);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _paiementService.CreateAsync(paiement, userId));
            Assert.Contains("Le montant du paiement ne peut pas être supérieur au montant restant", exception.Message);
            
            _mockPaiementRepository.Verify(repo => repo.CreateAsync(It.IsAny<Paiement>()), Times.Never);
            _mockFactureRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Facture>()), Times.Never);
        }

        [Fact]
        public async Task UpdateAsync_ShouldUpdatePaiementAndReturnTrue()
        {
            // Arrange
            int userId = 1;
            var paiement = new Paiement
            {
                Id = 1,
                FactureId = 1,
                Montant = 500,
                DatePaiement = DateTime.Today,
                ModePaiement = "Chèque",
                Reference = "CHQ456",
                UtilisateurId = 1
            };

            _mockPaiementRepository.Setup(repo => repo.UpdateAsync(paiement))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _paiementService.UpdateAsync(paiement, userId);

            // Assert
            Assert.True(result);
            _mockPaiementRepository.Verify(repo => repo.UpdateAsync(paiement), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Paiement",
                It.IsAny<string>(),
                paiement.Id,
                userId), Times.Once);
        }

        [Fact]
        public async Task DeleteAsync_ShouldDeletePaiementUpdateFactureAndReturnTrue()
        {
            // Arrange
            int paiementId = 1;
            int userId = 1;
            var paiement = new Paiement
            {
                Id = paiementId,
                FactureId = 1,
                Montant = 500,
                DatePaiement = DateTime.Today.AddDays(-5),
                ModePaiement = "Virement",
                Reference = "REF123"
            };

            var facture = new Facture
            {
                Id = 1,
                Numero = "FAC001",
                ClientId = 1,
                MontantTTC = 1000,
                MontantPaye = 500,
                MontantRestant = 500,
                Statut = "Payée partiellement"
            };

            _mockPaiementRepository.Setup(repo => repo.GetByIdAsync(paiementId))
                .ReturnsAsync(paiement);

            _mockFactureRepository.Setup(repo => repo.GetByIdAsync(paiement.FactureId))
                .ReturnsAsync(facture);

            _mockPaiementRepository.Setup(repo => repo.DeleteAsync(paiementId))
                .ReturnsAsync(true);

            _mockFactureRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Facture>()))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _paiementService.DeleteAsync(paiementId, userId);

            // Assert
            Assert.True(result);
            
            // Vérifier que la facture a été mise à jour
            _mockFactureRepository.Verify(repo => repo.UpdateAsync(It.Is<Facture>(f =>
                f.Id == facture.Id &&
                f.MontantPaye == 0 &&
                f.MontantRestant == facture.MontantTTC &&
                f.Statut == "En attente"
            )), Times.Once);
            
            _mockPaiementRepository.Verify(repo => repo.DeleteAsync(paiementId), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Paiement",
                It.IsAny<string>(),
                paiementId,
                userId), Times.Once);
        }

        [Fact]
        public async Task GetByFactureIdAsync_ShouldReturnPaiementsForFacture()
        {
            // Arrange
            int factureId = 1;
            var expectedPaiements = new List<Paiement>
            {
                new Paiement { Id = 1, FactureId = factureId, Montant = 500, DatePaiement = DateTime.Today.AddDays(-5) },
                new Paiement { Id = 3, FactureId = factureId, Montant = 500, DatePaiement = DateTime.Today }
            };

            _mockPaiementRepository.Setup(repo => repo.GetByFactureIdAsync(factureId))
                .ReturnsAsync(expectedPaiements);

            // Act
            var result = await _paiementService.GetByFactureIdAsync(factureId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedPaiements.Count, result.Count());
            Assert.Equal(expectedPaiements, result);
            _mockPaiementRepository.Verify(repo => repo.GetByFactureIdAsync(factureId), Times.Once);
        }

        [Fact]
        public async Task GetByPeriodAsync_ShouldReturnPaiementsInPeriod()
        {
            // Arrange
            DateTime dateDebut = new DateTime(2023, 1, 1);
            DateTime dateFin = new DateTime(2023, 1, 31);
            var expectedPaiements = new List<Paiement>
            {
                new Paiement { Id = 1, FactureId = 1, Montant = 500, DatePaiement = new DateTime(2023, 1, 15) },
                new Paiement { Id = 2, FactureId = 2, Montant = 1000, DatePaiement = new DateTime(2023, 1, 20) }
            };

            _mockPaiementRepository.Setup(repo => repo.GetByPeriodAsync(dateDebut, dateFin))
                .ReturnsAsync(expectedPaiements);

            // Act
            var result = await _paiementService.GetByPeriodAsync(dateDebut, dateFin);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedPaiements.Count, result.Count());
            Assert.Equal(expectedPaiements, result);
            _mockPaiementRepository.Verify(repo => repo.GetByPeriodAsync(dateDebut, dateFin), Times.Once);
        }
    }
}
