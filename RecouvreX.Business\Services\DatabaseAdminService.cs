using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RecouvreX.Business.Interfaces;
using RecouvreX.Common.Security;
using RecouvreX.DataAccess;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service d'administration de la base de données
    /// </summary>
    public class DatabaseAdminService : IDatabaseAdminService
    {
        private readonly IConfiguration _configuration;
        private readonly string _configFilePath;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="configuration">Configuration de l'application</param>
        public DatabaseAdminService(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

            // Déterminer le chemin du fichier de configuration
            string basePath = AppDomain.CurrentDomain.BaseDirectory;
            _configFilePath = Path.Combine(basePath, "appsettings.json");
        }

        /// <summary>
        /// Teste la connexion à la base de données avec les paramètres fournis
        /// </summary>
        /// <param name="server">Nom ou adresse IP du serveur</param>
        /// <param name="database">Nom de la base de données</param>
        /// <param name="userId">Identifiant utilisateur (si authentification SQL)</param>
        /// <param name="password">Mot de passe (si authentification SQL)</param>
        /// <param name="integratedSecurity">True pour utiliser l'authentification Windows, sinon False</param>
        /// <returns>True si la connexion est établie avec succès, sinon False</returns>
        public async Task<bool> TestConnectionAsync(string server, string database, string userId = null, string password = null, bool integratedSecurity = false)
        {
            try
            {
                // Construire la chaîne de connexion
                string connectionString = DatabaseConnection.BuildConnectionString(
                    server,
                    database,
                    userId,
                    password,
                    integratedSecurity);

                // Tester la connexion
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    return true;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du test de connexion à la base de données");
                return false;
            }
        }

        /// <summary>
        /// Sauvegarde la chaîne de connexion dans le fichier de configuration
        /// </summary>
        /// <param name="connectionString">Chaîne de connexion à sauvegarder</param>
        /// <param name="encrypt">True pour chiffrer les informations sensibles, sinon False</param>
        /// <returns>True si la sauvegarde est réussie, sinon False</returns>
        public async Task<bool> SaveConnectionStringAsync(string connectionString, bool encrypt = true)
        {
            try
            {
                // Lire le fichier de configuration existant
                string json = await File.ReadAllTextAsync(_configFilePath);
                JObject config = JObject.Parse(json);

                // Chiffrer la chaîne de connexion si demandé
                string finalConnectionString = connectionString;
                if (encrypt)
                {
                    // Extraire le mot de passe de la chaîne de connexion
                    var builder = new SqlConnectionStringBuilder(connectionString);
                    if (!string.IsNullOrEmpty(builder.Password))
                    {
                        // Chiffrer le mot de passe
                        string encryptedPassword = EncryptionHelper.Encrypt(builder.Password);

                        // Remplacer le mot de passe par sa version chiffrée
                        builder.Password = $"ENC:{encryptedPassword}";
                        finalConnectionString = builder.ConnectionString;
                    }
                }

                // Mettre à jour la chaîne de connexion
                config["ConnectionStrings"]["DefaultConnection"] = finalConnectionString;

                // Sauvegarder le fichier de configuration
                await File.WriteAllTextAsync(_configFilePath, config.ToString(Formatting.Indented));

                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la sauvegarde de la chaîne de connexion");
                return false;
            }
        }

        /// <summary>
        /// Effectue une sauvegarde de la base de données
        /// </summary>
        /// <param name="backupPath">Chemin du fichier de sauvegarde</param>
        /// <param name="backupType">Type de sauvegarde (complète, différentielle, etc.)</param>
        /// <param name="description">Description de la sauvegarde</param>
        /// <returns>True si la sauvegarde est réussie, sinon False</returns>
        public async Task<bool> BackupDatabaseAsync(string backupPath, string backupType = "FULL", string description = null)
        {
            try
            {
                // Créer le répertoire de sauvegarde s'il n'existe pas
                string directory = Path.GetDirectoryName(backupPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Récupérer la chaîne de connexion
                string connectionString = _configuration.GetConnectionString("DefaultConnection");

                // Extraire le nom de la base de données
                var builder = new SqlConnectionStringBuilder(connectionString);
                string databaseName = builder.InitialCatalog;

                // Construire la requête de sauvegarde
                StringBuilder query = new StringBuilder();
                query.AppendLine($"BACKUP DATABASE [{databaseName}] TO DISK = N'{backupPath}'");
                query.AppendLine($"WITH {backupType}");

                if (!string.IsNullOrEmpty(description))
                {
                    query.AppendLine($", DESCRIPTION = N'{description}'");
                }

                query.AppendLine(", NOFORMAT, NOINIT, NAME = N'RecouvreX-Full Database Backup'");
                query.AppendLine(", SKIP, NOREWIND, NOUNLOAD, STATS = 10");

                // Exécuter la requête
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    await connection.ExecuteAsync(query.ToString());
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la sauvegarde de la base de données");
                return false;
            }
        }

        /// <summary>
        /// Restaure une base de données à partir d'une sauvegarde
        /// </summary>
        /// <param name="backupPath">Chemin du fichier de sauvegarde</param>
        /// <param name="newDatabaseName">Nouveau nom de la base de données (optionnel)</param>
        /// <param name="replaceExisting">True pour remplacer la base de données existante, sinon False</param>
        /// <returns>True si la restauration est réussie, sinon False</returns>
        public async Task<bool> RestoreDatabaseAsync(string backupPath, string newDatabaseName = null, bool replaceExisting = false)
        {
            try
            {
                // Récupérer la chaîne de connexion
                string connectionString = _configuration.GetConnectionString("DefaultConnection");

                // Extraire le nom de la base de données
                var builder = new SqlConnectionStringBuilder(connectionString);
                string databaseName = !string.IsNullOrEmpty(newDatabaseName) ? newDatabaseName : builder.InitialCatalog;

                // Construire la requête de restauration
                StringBuilder query = new StringBuilder();

                // Mettre la base de données en mode utilisateur unique si on remplace une base existante
                if (replaceExisting)
                {
                    query.AppendLine($"ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;");
                }

                query.AppendLine($"RESTORE DATABASE [{databaseName}] FROM DISK = N'{backupPath}'");
                query.AppendLine("WITH FILE = 1, NOUNLOAD, REPLACE, STATS = 5");

                // Remettre la base de données en mode multi-utilisateurs
                if (replaceExisting)
                {
                    query.AppendLine($"ALTER DATABASE [{databaseName}] SET MULTI_USER;");
                }

                // Exécuter la requête sur la base master
                builder.InitialCatalog = "master";
                using (var connection = new SqlConnection(builder.ConnectionString))
                {
                    await connection.OpenAsync();
                    await connection.ExecuteAsync(query.ToString());
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la restauration de la base de données");
                return false;
            }
        }

        /// <summary>
        /// Vérifie l'intégrité de la base de données
        /// </summary>
        /// <param name="repairOption">Option de réparation (NONE, REPAIR_ALLOW_DATA_LOSS, etc.)</param>
        /// <returns>Résultat de la vérification</returns>
        public async Task<string> CheckDatabaseIntegrityAsync(string repairOption = "NONE")
        {
            try
            {
                // Récupérer la chaîne de connexion
                string connectionString = _configuration.GetConnectionString("DefaultConnection");

                // Extraire le nom de la base de données
                var builder = new SqlConnectionStringBuilder(connectionString);
                string databaseName = builder.InitialCatalog;

                // Construire la requête de vérification
                string query = $"DBCC CHECKDB (N'{databaseName}') WITH {repairOption}, TABLERESULTS";

                // Exécuter la requête
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Capturer les résultats dans une table temporaire
                    var result = await connection.QueryAsync<string>(query);
                    return string.Join(Environment.NewLine, result);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la vérification de l'intégrité de la base de données");
                return $"Erreur: {ex.Message}";
            }
        }

        /// <summary>
        /// Exécute une requête SQL personnalisée
        /// </summary>
        /// <param name="sqlQuery">Requête SQL à exécuter</param>
        /// <returns>Résultat de la requête sous forme de DataTable</returns>
        public async Task<DataTable> ExecuteCustomQueryAsync(string sqlQuery)
        {
            try
            {
                // Récupérer la chaîne de connexion
                string connectionString = _configuration.GetConnectionString("DefaultConnection");

                // Exécuter la requête
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand(sqlQuery, connection))
                    {
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            var dataTable = new DataTable();
                            adapter.Fill(dataTable);
                            return dataTable;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'exécution d'une requête SQL personnalisée");
                throw;
            }
        }

        /// <summary>
        /// Exécute une commande SQL personnalisée (non-query)
        /// </summary>
        /// <param name="sqlCommand">Commande SQL à exécuter</param>
        /// <returns>Nombre de lignes affectées</returns>
        public async Task<int> ExecuteCustomCommandAsync(string sqlCommand)
        {
            try
            {
                // Récupérer la chaîne de connexion
                string connectionString = _configuration.GetConnectionString("DefaultConnection");

                // Exécuter la commande
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    return await connection.ExecuteAsync(sqlCommand);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'exécution d'une commande SQL personnalisée");
                throw;
            }
        }

        /// <summary>
        /// Obtient la liste des bases de données sur le serveur
        /// </summary>
        /// <returns>Liste des noms de bases de données</returns>
        public async Task<List<string>> GetDatabasesAsync()
        {
            try
            {
                // Récupérer la chaîne de connexion
                string connectionString = _configuration.GetConnectionString("DefaultConnection");

                // Modifier la chaîne de connexion pour se connecter à la base master
                var builder = new SqlConnectionStringBuilder(connectionString);
                builder.InitialCatalog = "master";

                // Exécuter la requête
                using (var connection = new SqlConnection(builder.ConnectionString))
                {
                    await connection.OpenAsync();

                    // Récupérer la liste des bases de données
                    var databases = await connection.QueryAsync<string>(
                        "SELECT name FROM sys.databases WHERE name NOT IN ('master', 'tempdb', 'model', 'msdb') ORDER BY name");

                    return databases.ToList();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération de la liste des bases de données");
                throw;
            }
        }

        /// <summary>
        /// Obtient la liste des tables dans la base de données
        /// </summary>
        /// <returns>Liste des noms de tables</returns>
        public async Task<List<string>> GetTablesAsync()
        {
            try
            {
                // Récupérer la chaîne de connexion
                string connectionString = _configuration.GetConnectionString("DefaultConnection");

                // Exécuter la requête
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Récupérer la liste des tables
                    var tables = await connection.QueryAsync<string>(
                        "SELECT SCHEMA_NAME(schema_id) + '.' + name FROM sys.tables ORDER BY name");

                    return tables.ToList();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération de la liste des tables");
                throw;
            }
        }

        /// <summary>
        /// Obtient des informations sur la taille et l'utilisation de la base de données
        /// </summary>
        /// <returns>Informations sur la base de données</returns>
        public async Task<Dictionary<string, object>> GetDatabaseInfoAsync()
        {
            try
            {
                // Récupérer la chaîne de connexion
                string connectionString = _configuration.GetConnectionString("DefaultConnection");

                // Extraire le nom de la base de données
                var builder = new SqlConnectionStringBuilder(connectionString);
                string databaseName = builder.InitialCatalog;

                // Exécuter la requête
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Requête pour obtenir des informations sur la base de données
                    string query = @"
                        SELECT
                            d.name AS DatabaseName,
                            CONVERT(VARCHAR, d.create_date, 103) AS CreateDate,
                            CONVERT(VARCHAR, d.compatibility_level) AS CompatibilityLevel,
                            CONVERT(VARCHAR, d.collation_name) AS Collation,
                            CONVERT(VARCHAR, DATABASEPROPERTYEX(d.name, 'Recovery')) AS RecoveryModel,
                            SUM(CAST(mf.size AS BIGINT) * 8 / 1024) AS SizeMB
                        FROM sys.databases d
                        JOIN sys.master_files mf ON d.database_id = mf.database_id
                        WHERE d.name = @DatabaseName
                        GROUP BY d.name, d.create_date, d.compatibility_level, d.collation_name";

                    var dbInfo = await connection.QueryFirstAsync<dynamic>(query, new { DatabaseName = databaseName });

                    // Requête pour obtenir des informations sur les tables
                    query = @"
                        SELECT
                            COUNT(*) AS TableCount,
                            SUM(p.rows) AS TotalRows
                        FROM sys.tables t
                        JOIN sys.partitions p ON t.object_id = p.object_id
                        WHERE p.index_id IN (0, 1)";

                    var tableInfo = await connection.QueryFirstAsync<dynamic>(query);

                    // Combiner les informations
                    var result = new Dictionary<string, object>();

                    // Ajouter les informations de la base de données
                    foreach (var prop in ((IDictionary<string, object>)dbInfo))
                    {
                        result.Add(prop.Key, prop.Value);
                    }

                    // Ajouter les informations des tables
                    foreach (var prop in ((IDictionary<string, object>)tableInfo))
                    {
                        result.Add(prop.Key, prop.Value);
                    }

                    return result;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des informations sur la base de données");
                throw;
            }
        }

        /// <summary>
        /// Planifie une sauvegarde automatique
        /// </summary>
        /// <param name="backupPath">Chemin du fichier de sauvegarde</param>
        /// <param name="schedule">Planification (quotidienne, hebdomadaire, etc.)</param>
        /// <param name="time">Heure de la sauvegarde</param>
        /// <returns>True si la planification est réussie, sinon False</returns>
        public async Task<bool> ScheduleBackupAsync(string backupPath, string schedule, TimeSpan time)
        {
            try
            {
                // Récupérer la chaîne de connexion
                string connectionString = _configuration.GetConnectionString("DefaultConnection");

                // Extraire le nom de la base de données
                var builder = new SqlConnectionStringBuilder(connectionString);
                string databaseName = builder.InitialCatalog;

                // Construire la requête pour créer un job SQL Server Agent
                StringBuilder query = new StringBuilder();

                // Nom du job
                string jobName = $"RecouvreX_Backup_{databaseName}_{Guid.NewGuid().ToString("N").Substring(0, 8)}";

                // Créer le job
                query.AppendLine("USE msdb;");
                query.AppendLine("GO");
                query.AppendLine($"EXEC dbo.sp_add_job @job_name = N'{jobName}', @description = N'Sauvegarde automatique de la base de données {databaseName}', @category_name = N'Database Maintenance';");

                // Créer l'étape du job
                query.AppendLine($"EXEC dbo.sp_add_jobstep @job_name = N'{jobName}', @step_name = N'Backup Database', @subsystem = N'TSQL', @command = N'BACKUP DATABASE [{databaseName}] TO DISK = N''{backupPath}'' WITH NOFORMAT, NOINIT, NAME = N''RecouvreX-Full Database Backup'', SKIP, NOREWIND, NOUNLOAD, STATS = 10';");

                // Créer la planification
                string scheduleType;
                switch (schedule.ToLower())
                {
                    case "quotidienne":
                    case "daily":
                        scheduleType = "1"; // Daily
                        break;
                    case "hebdomadaire":
                    case "weekly":
                        scheduleType = "4"; // Weekly
                        break;
                    case "mensuelle":
                    case "monthly":
                        scheduleType = "8"; // Monthly
                        break;
                    default:
                        scheduleType = "1"; // Daily par défaut
                        break;
                }

                query.AppendLine($"EXEC dbo.sp_add_schedule @schedule_name = N'{jobName}_Schedule', @freq_type = {scheduleType}, @active_start_time = {time.Hours * 10000 + time.Minutes * 100};");

                // Associer la planification au job
                query.AppendLine($"EXEC dbo.sp_attach_schedule @job_name = N'{jobName}', @schedule_name = N'{jobName}_Schedule';");

                // Activer le job
                query.AppendLine($"EXEC dbo.sp_add_jobserver @job_name = N'{jobName}';");

                // Exécuter la requête sur la base msdb
                builder.InitialCatalog = "msdb";
                using (var connection = new SqlConnection(builder.ConnectionString))
                {
                    await connection.OpenAsync();
                    await connection.ExecuteAsync(query.ToString());
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la planification d'une sauvegarde automatique");
                return false;
            }
        }
    }
}
