using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des paiements
    /// </summary>
    public class PaiementService : IPaiementService
    {
        private readonly IPaiementRepository _paiementRepository;
        private readonly IFactureRepository _factureRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="paiementRepository">Repository des paiements</param>
        /// <param name="factureRepository">Repository des factures</param>
        /// <param name="clientRepository">Repository des clients</param>
        /// <param name="utilisateurRepository">Repository des utilisateurs</param>
        /// <param name="journalAuditRepository">Repository du journal d'audit</param>
        public PaiementService(
            IPaiementRepository paiementRepository,
            IFactureRepository factureRepository,
            IClientRepository clientRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _paiementRepository = paiementRepository ?? throw new ArgumentNullException(nameof(paiementRepository));
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère tous les paiements
        /// </summary>
        /// <returns>Liste des paiements</returns>
        public async Task<IEnumerable<Paiement>> GetAllAsync()
        {
            return await _paiementRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère un paiement par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du paiement</param>
        /// <returns>Paiement trouvé ou null</returns>
        public async Task<Paiement> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _paiementRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère un paiement par sa référence
        /// </summary>
        /// <param name="reference">Référence du paiement</param>
        /// <returns>Paiement trouvé ou null</returns>
        public async Task<Paiement> GetByReferenceAsync(string reference)
        {
            if (string.IsNullOrEmpty(reference))
                return null;

            return await _paiementRepository.GetByReferenceAsync(reference);
        }

        /// <summary>
        /// Récupère un paiement avec ses factures associées
        /// </summary>
        /// <param name="paiementId">Identifiant du paiement</param>
        /// <returns>Paiement avec ses factures</returns>
        public async Task<Paiement> GetWithFacturesAsync(int paiementId)
        {
            if (paiementId <= 0)
                return null;

            return await _paiementRepository.GetWithFacturesAsync(paiementId);
        }

        /// <summary>
        /// Récupère tous les paiements d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des paiements du client</returns>
        public async Task<IEnumerable<Paiement>> GetByClientIdAsync(int clientId)
        {
            if (clientId <= 0)
                return new List<Paiement>();

            return await _paiementRepository.GetByClientIdAsync(clientId);
        }

        /// <summary>
        /// Récupère tous les paiements associés à une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des paiements associés à la facture</returns>
        public async Task<IEnumerable<Paiement>> GetByFactureIdAsync(int factureId)
        {
            if (factureId <= 0)
                return new List<Paiement>();

            return await _paiementRepository.GetByFactureIdAsync(factureId);
        }

        /// <summary>
        /// Récupère les paiements par mode de paiement
        /// </summary>
        /// <param name="modePaiement">Mode de paiement</param>
        /// <returns>Liste des paiements effectués avec le mode spécifié</returns>
        public async Task<IEnumerable<Paiement>> GetByModePaiementAsync(string modePaiement)
        {
            if (string.IsNullOrEmpty(modePaiement))
                return new List<Paiement>();

            return await _paiementRepository.GetByModePaiementAsync(modePaiement);
        }

        /// <summary>
        /// Récupère les paiements par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des paiements effectués dans la période spécifiée</returns>
        public async Task<IEnumerable<Paiement>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            return await _paiementRepository.GetByPeriodAsync(dateDebut, dateFin);
        }

        /// <summary>
        /// Crée un nouveau paiement
        /// </summary>
        /// <param name="paiement">Paiement à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Paiement créé avec son identifiant généré</returns>
        public async Task<Paiement> CreateAsync(Paiement paiement, int creePar)
        {
            if (paiement == null || creePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la création d'un paiement");

            // Vérifier si le client existe
            var client = await _clientRepository.GetByIdAsync(paiement.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {paiement.ClientId} n'existe pas");

            // Vérifier si le mode de paiement est valide
            if (string.IsNullOrEmpty(paiement.ModePaiement))
                throw new ArgumentException("Le mode de paiement ne peut pas être vide");

            // Générer une référence unique si non fournie
            if (string.IsNullOrEmpty(paiement.Reference))
            {
                paiement.Reference = await GenerateReferencePaiementAsync();
            }
            else
            {
                // Vérifier si la référence existe déjà
                var existingPaiement = await _paiementRepository.GetByReferenceAsync(paiement.Reference);
                if (existingPaiement != null)
                    throw new InvalidOperationException($"La référence de paiement '{paiement.Reference}' existe déjà");
            }

            // Créer le paiement
            var createdPaiement = await _paiementRepository.AddAsync(paiement, creePar);

            // Journaliser la création
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = creePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(creePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Creation,
                TypeEntite = "Paiement",
                EntiteId = createdPaiement.Id,
                Description = $"Création du paiement {createdPaiement.Reference} pour le client {client.RaisonSociale}",
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdPaiement.Id,
                    createdPaiement.Reference,
                    createdPaiement.ClientId,
                    ClientNom = client.RaisonSociale,
                    createdPaiement.DatePaiement,
                    createdPaiement.Montant,
                    createdPaiement.ModePaiement,
                    createdPaiement.ReferenceBancaire
                })
            });

            return createdPaiement;
        }

        /// <summary>
        /// Met à jour un paiement existant
        /// </summary>
        /// <param name="paiement">Paiement à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Paiement mis à jour</returns>
        public async Task<Paiement> UpdateAsync(Paiement paiement, int modifiePar)
        {
            if (paiement == null || paiement.Id <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la mise à jour d'un paiement");

            // Récupérer le paiement existant
            var existingPaiement = await _paiementRepository.GetByIdAsync(paiement.Id);
            if (existingPaiement == null)
                throw new InvalidOperationException($"Le paiement avec l'ID {paiement.Id} n'existe pas");

            // Vérifier si le client existe
            var client = await _clientRepository.GetByIdAsync(paiement.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {paiement.ClientId} n'existe pas");

            // Vérifier si la référence existe déjà pour un autre paiement
            if (paiement.Reference != existingPaiement.Reference && !string.IsNullOrEmpty(paiement.Reference))
            {
                var paiementWithSameReference = await _paiementRepository.GetByReferenceAsync(paiement.Reference);
                if (paiementWithSameReference != null && paiementWithSameReference.Id != paiement.Id)
                    throw new InvalidOperationException($"La référence de paiement '{paiement.Reference}' existe déjà");
            }

            // Vérifier si le mode de paiement est valide
            if (string.IsNullOrEmpty(paiement.ModePaiement))
                throw new ArgumentException("Le mode de paiement ne peut pas être vide");

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Paiement",
                EntiteId = paiement.Id,
                Description = $"Modification du paiement {paiement.Reference} pour le client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingPaiement.Id,
                    existingPaiement.Reference,
                    existingPaiement.ClientId,
                    existingPaiement.DatePaiement,
                    existingPaiement.Montant,
                    existingPaiement.ModePaiement,
                    existingPaiement.ReferenceBancaire
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    paiement.Id,
                    paiement.Reference,
                    paiement.ClientId,
                    ClientNom = client.RaisonSociale,
                    paiement.DatePaiement,
                    paiement.Montant,
                    paiement.ModePaiement,
                    paiement.ReferenceBancaire
                })
            });

            // Mettre à jour le paiement
            return await _paiementRepository.UpdateAsync(paiement, modifiePar);
        }

        /// <summary>
        /// Supprime un paiement
        /// </summary>
        /// <param name="id">Identifiant du paiement à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0 || supprimePar <= 0)
                return false;

            // Récupérer le paiement à supprimer
            var paiement = await _paiementRepository.GetByIdAsync(id);
            if (paiement == null)
                return false;

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(paiement.ClientId);
            if (client == null)
                return false;

            // Récupérer les factures associées au paiement
            var paiementWithFactures = await _paiementRepository.GetWithFacturesAsync(id);
            var facturePaiements = paiementWithFactures?.FacturePaiements ?? new List<FacturePaiement>();

            // Journaliser la suppression
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = supprimePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(supprimePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Suppression,
                TypeEntite = "Paiement",
                EntiteId = id,
                Description = $"Suppression du paiement {paiement.Reference} pour le client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    paiement.Id,
                    paiement.Reference,
                    paiement.ClientId,
                    ClientNom = client.RaisonSociale,
                    paiement.DatePaiement,
                    paiement.Montant,
                    paiement.ModePaiement,
                    paiement.ReferenceBancaire,
                    FacturesAssociees = facturePaiements
                })
            });

            // Supprimer le paiement
            var result = await _paiementRepository.DeleteAsync(id, supprimePar);

            // Mettre à jour les factures associées
            if (result)
            {
                foreach (var facturePaiement in facturePaiements)
                {
                    await _factureRepository.UpdateMontantPayeAsync(facturePaiement.FactureId, 0, supprimePar);
                }
            }

            return result;
        }

        /// <summary>
        /// Associe un paiement à une facture
        /// </summary>
        /// <param name="paiementId">Identifiant du paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="montantAffecte">Montant affecté à la facture</param>
        /// <param name="associePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'association a réussi, sinon False</returns>
        public async Task<bool> AssociateToFactureAsync(int paiementId, int factureId, decimal montantAffecte, int associePar)
        {
            if (paiementId <= 0 || factureId <= 0 || montantAffecte <= 0 || associePar <= 0)
                return false;

            // Récupérer le paiement
            var paiement = await _paiementRepository.GetByIdAsync(paiementId);
            if (paiement == null)
                return false;

            // Récupérer la facture
            var facture = await _factureRepository.GetByIdAsync(factureId);
            if (facture == null)
                return false;

            // Vérifier que le paiement et la facture appartiennent au même client
            if (paiement.ClientId != facture.ClientId)
                throw new InvalidOperationException("Le paiement et la facture doivent appartenir au même client");

            // Vérifier que le montant affecté ne dépasse pas le montant du paiement
            var paiementWithFactures = await _paiementRepository.GetWithFacturesAsync(paiementId);
            decimal montantDejaAffecte = 0;
            foreach (var fp in paiementWithFactures?.FacturePaiements ?? new List<FacturePaiement>())
            {
                if (fp.FactureId != factureId) // Ne pas compter le montant déjà affecté à cette facture
                {
                    montantDejaAffecte += fp.MontantAffecte;
                }
            }

            decimal montantDisponible = paiement.Montant - montantDejaAffecte;
            if (montantAffecte > montantDisponible)
                throw new InvalidOperationException($"Le montant affecté ({montantAffecte}) ne peut pas dépasser le montant disponible du paiement ({montantDisponible})");

            // Vérifier que le montant affecté ne dépasse pas le montant restant de la facture
            if (montantAffecte > facture.MontantRestant)
                throw new InvalidOperationException($"Le montant affecté ({montantAffecte}) ne peut pas dépasser le montant restant de la facture ({facture.MontantRestant})");

            // Journaliser l'association
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = associePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(associePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "FacturePaiement",
                EntiteId = paiementId,
                Description = $"Association du paiement {paiement.Reference} à la facture {facture.Numero} pour un montant de {montantAffecte}",
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    PaiementId = paiementId,
                    PaiementReference = paiement.Reference,
                    FactureId = factureId,
                    FactureNumero = facture.Numero,
                    MontantAffecte = montantAffecte
                })
            });

            // Associer le paiement à la facture
            return await _paiementRepository.AssociateToFactureAsync(paiementId, factureId, montantAffecte, associePar);
        }

        /// <summary>
        /// Dissocie un paiement d'une facture
        /// </summary>
        /// <param name="paiementId">Identifiant du paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="dissociePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la dissociation a réussi, sinon False</returns>
        public async Task<bool> DissociateFromFactureAsync(int paiementId, int factureId, int dissociePar)
        {
            if (paiementId <= 0 || factureId <= 0 || dissociePar <= 0)
                return false;

            // Récupérer le paiement
            var paiement = await _paiementRepository.GetByIdAsync(paiementId);
            if (paiement == null)
                return false;

            // Récupérer la facture
            var facture = await _factureRepository.GetByIdAsync(factureId);
            if (facture == null)
                return false;

            // Récupérer l'association facture-paiement
            var paiementWithFactures = await _paiementRepository.GetWithFacturesAsync(paiementId);
            var facturePaiement = paiementWithFactures?.FacturePaiements?.FirstOrDefault(fp => fp.FactureId == factureId);
            if (facturePaiement == null)
                return false;

            // Journaliser la dissociation
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = dissociePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(dissociePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Suppression,
                TypeEntite = "FacturePaiement",
                EntiteId = paiementId,
                Description = $"Dissociation du paiement {paiement.Reference} de la facture {facture.Numero}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    PaiementId = paiementId,
                    PaiementReference = paiement.Reference,
                    FactureId = factureId,
                    FactureNumero = facture.Numero,
                    MontantAffecte = facturePaiement.MontantAffecte
                })
            });

            // Dissocier le paiement de la facture
            return await _paiementRepository.DissociateFromFactureAsync(paiementId, factureId);
        }

        /// <summary>
        /// Génère une référence de paiement unique
        /// </summary>
        /// <returns>Référence de paiement unique</returns>
        public async Task<string> GenerateReferencePaiementAsync()
        {
            // Format: PAY-YYYYMMDD-XXXX où XXXX est un numéro séquentiel
            string datePrefix = DateTime.Now.ToString("yyyyMMdd");
            string prefix = $"PAY-{datePrefix}-";
            
            // Récupérer tous les paiements dont la référence commence par le préfixe
            var paiements = await _paiementRepository.FindAsync("Reference LIKE @Prefix", new { Prefix = $"{prefix}%" });
            
            // Trouver le numéro séquentiel le plus élevé
            int maxSequence = 0;
            foreach (var paiement in paiements)
            {
                if (paiement.Reference.StartsWith(prefix) && paiement.Reference.Length > prefix.Length)
                {
                    string sequencePart = paiement.Reference.Substring(prefix.Length);
                    if (int.TryParse(sequencePart, out int sequence) && sequence > maxSequence)
                    {
                        maxSequence = sequence;
                    }
                }
            }
            
            // Générer la nouvelle référence de paiement
            return $"{prefix}{maxSequence + 1:D4}";
        }
    }
}
