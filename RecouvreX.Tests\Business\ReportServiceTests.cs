using Moq;
using RecouvreX.Business.Interfaces;
using RecouvreX.Business.Services;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System.Text.Json;
using Xunit;

namespace RecouvreX.Tests.Business
{
    public class ReportServiceTests
    {
        private readonly Mock<IFactureRepository> _mockFactureRepository;
        private readonly Mock<IClientRepository> _mockClientRepository;
        private readonly Mock<IPaiementRepository> _mockPaiementRepository;
        private readonly Mock<IRelanceRepository> _mockRelanceRepository;
        private readonly Mock<IUtilisateurRepository> _mockUtilisateurRepository;
        private readonly IReportService _reportService;

        public ReportServiceTests()
        {
            _mockFactureRepository = new Mock<IFactureRepository>();
            _mockClientRepository = new Mock<IClientRepository>();
            _mockPaiementRepository = new Mock<IPaiementRepository>();
            _mockRelanceRepository = new Mock<IRelanceRepository>();
            _mockUtilisateurRepository = new Mock<IUtilisateurRepository>();
            _reportService = new ReportService(
                _mockFactureRepository.Object,
                _mockClientRepository.Object,
                _mockPaiementRepository.Object,
                _mockRelanceRepository.Object,
                _mockUtilisateurRepository.Object);
        }

        [Fact]
        public async Task GenerateOverdueInvoicesReportAsync_ShouldReturnJsonReport()
        {
            // Arrange
            var overdueInvoices = new List<Facture>
            {
                new Facture { Id = 1, Numero = "FAC001", ClientId = 1, MontantTTC = 1000, DateEcheance = DateTime.Today.AddDays(-10), Statut = "En retard" },
                new Facture { Id = 2, Numero = "FAC002", ClientId = 2, MontantTTC = 2000, DateEcheance = DateTime.Today.AddDays(-5), Statut = "En retard" }
            };

            var clients = new List<Client>
            {
                new Client { Id = 1, Code = "CLI001", RaisonSociale = "Client 1" },
                new Client { Id = 2, Code = "CLI002", RaisonSociale = "Client 2" }
            };

            _mockFactureRepository.Setup(repo => repo.GetOverdueInvoicesAsync())
                .ReturnsAsync(overdueInvoices);

            _mockClientRepository.Setup(repo => repo.GetByIdAsync(1))
                .ReturnsAsync(clients[0]);

            _mockClientRepository.Setup(repo => repo.GetByIdAsync(2))
                .ReturnsAsync(clients[1]);

            // Act
            var result = await _reportService.GenerateOverdueInvoicesReportAsync();

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);

            // Vérifier que le résultat est un JSON valide
            var options = new JsonDocumentOptions
            {
                AllowTrailingCommas = true
            };
            var jsonDocument = JsonDocument.Parse(result, options);
            Assert.NotNull(jsonDocument);

            // Vérifier que le rapport contient les factures en retard
            var reportData = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.Equal(2, reportData.GetProperty("factures").GetArrayLength());

            _mockFactureRepository.Verify(repo => repo.GetOverdueInvoicesAsync(), Times.Once);
            _mockClientRepository.Verify(repo => repo.GetByIdAsync(1), Times.Once);
            _mockClientRepository.Verify(repo => repo.GetByIdAsync(2), Times.Once);
        }

        [Fact]
        public async Task GeneratePaymentsByPeriodReportAsync_ShouldReturnJsonReport()
        {
            // Arrange
            DateTime dateDebut = new DateTime(2023, 1, 1);
            DateTime dateFin = new DateTime(2023, 1, 31);

            var paiements = new List<Paiement>
            {
                new Paiement { Id = 1, FactureId = 1, Montant = 500, DatePaiement = new DateTime(2023, 1, 15), ModePaiement = "Virement" },
                new Paiement { Id = 2, FactureId = 2, Montant = 1000, DatePaiement = new DateTime(2023, 1, 20), ModePaiement = "Chèque" }
            };

            var factures = new List<Facture>
            {
                new Facture { Id = 1, Numero = "FAC001", ClientId = 1, MontantTTC = 1000 },
                new Facture { Id = 2, Numero = "FAC002", ClientId = 2, MontantTTC = 2000 }
            };

            var clients = new List<Client>
            {
                new Client { Id = 1, Code = "CLI001", RaisonSociale = "Client 1" },
                new Client { Id = 2, Code = "CLI002", RaisonSociale = "Client 2" }
            };

            _mockPaiementRepository.Setup(repo => repo.GetByPeriodAsync(dateDebut, dateFin))
                .ReturnsAsync(paiements);

            _mockFactureRepository.Setup(repo => repo.GetByIdAsync(1))
                .ReturnsAsync(factures[0]);

            _mockFactureRepository.Setup(repo => repo.GetByIdAsync(2))
                .ReturnsAsync(factures[1]);

            _mockClientRepository.Setup(repo => repo.GetByIdAsync(1))
                .ReturnsAsync(clients[0]);

            _mockClientRepository.Setup(repo => repo.GetByIdAsync(2))
                .ReturnsAsync(clients[1]);

            // Act
            var result = await _reportService.GeneratePaymentsByPeriodReportAsync(dateDebut, dateFin);

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);

            // Vérifier que le résultat est un JSON valide
            var options = new JsonDocumentOptions
            {
                AllowTrailingCommas = true
            };
            var jsonDocument = JsonDocument.Parse(result, options);
            Assert.NotNull(jsonDocument);

            // Vérifier que le rapport contient les paiements
            var reportData = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.Equal(2, reportData.GetProperty("paiements").GetArrayLength());

            _mockPaiementRepository.Verify(repo => repo.GetByPeriodAsync(dateDebut, dateFin), Times.Once);
            _mockFactureRepository.Verify(repo => repo.GetByIdAsync(1), Times.Once);
            _mockFactureRepository.Verify(repo => repo.GetByIdAsync(2), Times.Once);
            _mockClientRepository.Verify(repo => repo.GetByIdAsync(1), Times.Once);
            _mockClientRepository.Verify(repo => repo.GetByIdAsync(2), Times.Once);
        }

        [Fact]
        public async Task GenerateInvoicesByPeriodReportAsync_ShouldReturnJsonReport()
        {
            // Arrange
            DateTime dateDebut = new DateTime(2023, 1, 1);
            DateTime dateFin = new DateTime(2023, 1, 31);

            var factures = new List<Facture>
            {
                new Facture { Id = 1, Numero = "FAC001", ClientId = 1, MontantTTC = 1000, DateEmission = new DateTime(2023, 1, 15) },
                new Facture { Id = 2, Numero = "FAC002", ClientId = 2, MontantTTC = 2000, DateEmission = new DateTime(2023, 1, 20) }
            };

            var clients = new List<Client>
            {
                new Client { Id = 1, Code = "CLI001", RaisonSociale = "Client 1" },
                new Client { Id = 2, Code = "CLI002", RaisonSociale = "Client 2" }
            };

            _mockFactureRepository.Setup(repo => repo.GetByPeriodAsync(dateDebut, dateFin))
                .ReturnsAsync(factures);

            _mockClientRepository.Setup(repo => repo.GetByIdAsync(1))
                .ReturnsAsync(clients[0]);

            _mockClientRepository.Setup(repo => repo.GetByIdAsync(2))
                .ReturnsAsync(clients[1]);

            // Act
            var result = await _reportService.GenerateInvoicesByPeriodReportAsync(dateDebut, dateFin);

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);

            // Vérifier que le résultat est un JSON valide
            var options = new JsonDocumentOptions
            {
                AllowTrailingCommas = true
            };
            var jsonDocument = JsonDocument.Parse(result, options);
            Assert.NotNull(jsonDocument);

            // Vérifier que le rapport contient les factures
            var reportData = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.Equal(2, reportData.GetProperty("factures").GetArrayLength());

            _mockFactureRepository.Verify(repo => repo.GetByPeriodAsync(dateDebut, dateFin), Times.Once);
            _mockClientRepository.Verify(repo => repo.GetByIdAsync(1), Times.Once);
            _mockClientRepository.Verify(repo => repo.GetByIdAsync(2), Times.Once);
        }

        [Fact]
        public async Task GenerateClientsWithOverdueInvoicesReportAsync_ShouldReturnJsonReport()
        {
            // Arrange
            var overdueInvoices = new List<Facture>
            {
                new Facture { Id = 1, Numero = "FAC001", ClientId = 1, MontantTTC = 1000, DateEcheance = DateTime.Today.AddDays(-10), Statut = "En retard" },
                new Facture { Id = 2, Numero = "FAC002", ClientId = 1, MontantTTC = 2000, DateEcheance = DateTime.Today.AddDays(-5), Statut = "En retard" },
                new Facture { Id = 3, Numero = "FAC003", ClientId = 2, MontantTTC = 3000, DateEcheance = DateTime.Today.AddDays(-15), Statut = "En retard" }
            };

            var clients = new List<Client>
            {
                new Client { Id = 1, Code = "CLI001", RaisonSociale = "Client 1" },
                new Client { Id = 2, Code = "CLI002", RaisonSociale = "Client 2" }
            };

            _mockFactureRepository.Setup(repo => repo.GetOverdueInvoicesAsync())
                .ReturnsAsync(overdueInvoices);

            _mockClientRepository.Setup(repo => repo.GetByIdAsync(1))
                .ReturnsAsync(clients[0]);

            _mockClientRepository.Setup(repo => repo.GetByIdAsync(2))
                .ReturnsAsync(clients[1]);

            // Act
            var result = await _reportService.GenerateClientsWithOverdueInvoicesReportAsync();

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);

            // Vérifier que le résultat est un JSON valide
            var options = new JsonDocumentOptions
            {
                AllowTrailingCommas = true
            };
            var jsonDocument = JsonDocument.Parse(result, options);
            Assert.NotNull(jsonDocument);

            // Vérifier que le rapport contient les clients
            var reportData = JsonSerializer.Deserialize<JsonElement>(result);
            Assert.Equal(2, reportData.GetProperty("clients").GetArrayLength());

            _mockFactureRepository.Verify(repo => repo.GetOverdueInvoicesAsync(), Times.Once);
            _mockClientRepository.Verify(repo => repo.GetByIdAsync(1), Times.Once);
            _mockClientRepository.Verify(repo => repo.GetByIdAsync(2), Times.Once);
        }

        [Fact]
        public async Task ExportReportToCsvAsync_ShouldReturnTrue()
        {
            // Arrange
            string jsonData = @"{
                ""factures"": [
                    {
                        ""id"": 1,
                        ""numero"": ""FAC001"",
                        ""client"": ""Client 1"",
                        ""montantTTC"": 1000,
                        ""dateEcheance"": ""2023-01-20"",
                        ""statut"": ""En retard""
                    },
                    {
                        ""id"": 2,
                        ""numero"": ""FAC002"",
                        ""client"": ""Client 2"",
                        ""montantTTC"": 2000,
                        ""dateEcheance"": ""2023-01-25"",
                        ""statut"": ""En retard""
                    }
                ]
            }";
            string filePath = Path.GetTempFileName();

            try
            {
                // Act
                var result = await _reportService.ExportReportToCsvAsync(jsonData, filePath);

                // Assert
                Assert.True(result);
                Assert.True(File.Exists(filePath));
                
                // Vérifier le contenu du fichier CSV
                string csvContent = File.ReadAllText(filePath);
                Assert.Contains("numero", csvContent);
                Assert.Contains("client", csvContent);
                Assert.Contains("montantTTC", csvContent);
                Assert.Contains("FAC001", csvContent);
                Assert.Contains("FAC002", csvContent);
                Assert.Contains("Client 1", csvContent);
                Assert.Contains("Client 2", csvContent);
            }
            finally
            {
                // Nettoyer
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
        }

        [Fact]
        public async Task ExportReportToExcelAsync_ShouldReturnTrue()
        {
            // Arrange
            string jsonData = @"{
                ""factures"": [
                    {
                        ""id"": 1,
                        ""numero"": ""FAC001"",
                        ""client"": ""Client 1"",
                        ""montantTTC"": 1000,
                        ""dateEcheance"": ""2023-01-20"",
                        ""statut"": ""En retard""
                    },
                    {
                        ""id"": 2,
                        ""numero"": ""FAC002"",
                        ""client"": ""Client 2"",
                        ""montantTTC"": 2000,
                        ""dateEcheance"": ""2023-01-25"",
                        ""statut"": ""En retard""
                    }
                ]
            }";
            string filePath = Path.GetTempFileName() + ".xlsx";

            try
            {
                // Act
                var result = await _reportService.ExportReportToExcelAsync(jsonData, filePath);

                // Assert
                Assert.True(result);
                Assert.True(File.Exists(filePath));
                
                // Vérifier que le fichier Excel a été créé avec une taille non nulle
                var fileInfo = new FileInfo(filePath);
                Assert.True(fileInfo.Length > 0);
            }
            finally
            {
                // Nettoyer
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
        }

        [Fact]
        public async Task ExportReportToPdfAsync_ShouldReturnTrue()
        {
            // Arrange
            string jsonData = @"{
                ""factures"": [
                    {
                        ""id"": 1,
                        ""numero"": ""FAC001"",
                        ""client"": ""Client 1"",
                        ""montantTTC"": 1000,
                        ""dateEcheance"": ""2023-01-20"",
                        ""statut"": ""En retard""
                    },
                    {
                        ""id"": 2,
                        ""numero"": ""FAC002"",
                        ""client"": ""Client 2"",
                        ""montantTTC"": 2000,
                        ""dateEcheance"": ""2023-01-25"",
                        ""statut"": ""En retard""
                    }
                ]
            }";
            string filePath = Path.GetTempFileName() + ".pdf";
            string title = "Factures en retard";

            try
            {
                // Act
                var result = await _reportService.ExportReportToPdfAsync(jsonData, filePath, title);

                // Assert
                Assert.True(result);
                Assert.True(File.Exists(filePath));
                
                // Vérifier que le fichier PDF a été créé avec une taille non nulle
                var fileInfo = new FileInfo(filePath);
                Assert.True(fileInfo.Length > 0);
            }
            finally
            {
                // Nettoyer
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
        }
    }
}
