using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Helpers
{
    /// <summary>
    /// Classe utilitaire pour la gestion des notifications
    /// </summary>
    public static class NotificationHelper
    {
        // Dictionnaire pour stocker les notifications affichées
        private static Dictionary<int, NotifyIcon> _notifyIcons = new Dictionary<int, NotifyIcon>();

        /// <summary>
        /// Affiche une notification pour une alerte déclenchée
        /// </summary>
        /// <param name="alerte">Alerte déclenchée</param>
        /// <param name="parent">Formulaire parent</param>
        /// <returns>True si la notification a été affichée, sinon False</returns>
        public static bool ShowNotification(Alerte alerte, Form parent)
        {
            if (alerte == null || parent == null)
                return false;

            try
            {
                // Créer un NotifyIcon pour cette alerte si nécessaire
                if (!_notifyIcons.ContainsKey(alerte.Id))
                {
                    var notifyIcon = new NotifyIcon
                    {
                        Icon = SystemIcons.Warning,
                        Visible = true,
                        Text = "RecouvreX - Alerte"
                    };

                    // Ajouter un gestionnaire d'événements pour le clic sur la notification
                    notifyIcon.Click += (sender, e) => ShowAlerteDetails(alerte, parent);

                    _notifyIcons.Add(alerte.Id, notifyIcon);
                }

                // Récupérer le NotifyIcon pour cette alerte
                var icon = _notifyIcons[alerte.Id];

                // Construire le message de notification
                string title = $"Alerte : {alerte.Nom}";
                string message = GetNotificationMessage(alerte);

                // Afficher la notification
                icon.ShowBalloonTip(10000, title, message, ToolTipIcon.Warning);

                return true;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de l'affichage de la notification pour l'alerte {AlerteId}", alerte.Id);
                return false;
            }
        }

        /// <summary>
        /// Supprime une notification
        /// </summary>
        /// <param name="alerteId">Identifiant de l'alerte</param>
        /// <returns>True si la notification a été supprimée, sinon False</returns>
        public static bool RemoveNotification(int alerteId)
        {
            if (!_notifyIcons.ContainsKey(alerteId))
                return false;

            try
            {
                // Récupérer le NotifyIcon pour cette alerte
                var icon = _notifyIcons[alerteId];

                // Masquer et disposer le NotifyIcon
                icon.Visible = false;
                icon.Dispose();

                // Supprimer l'entrée du dictionnaire
                _notifyIcons.Remove(alerteId);

                return true;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de la suppression de la notification pour l'alerte {AlerteId}", alerteId);
                return false;
            }
        }

        /// <summary>
        /// Construit le message de notification en fonction du type d'alerte
        /// </summary>
        /// <param name="alerte">Alerte déclenchée</param>
        /// <returns>Message de notification</returns>
        private static string GetNotificationMessage(Alerte alerte)
        {
            string message = alerte.Description;

            if (string.IsNullOrEmpty(message))
            {
                switch (alerte.Type)
                {
                    case TypeAlerte.MontantFacturesEnRetard:
                        message = $"Le montant total des factures en retard est {GetConditionText(alerte.Condition)} {alerte.Seuil:C}.";
                        break;

                    case TypeAlerte.NombreFacturesEnRetard:
                        message = $"Le nombre de factures en retard est {GetConditionText(alerte.Condition)} {alerte.Seuil}.";
                        break;

                    case TypeAlerte.AncienneteCreances:
                        message = $"L'âge moyen des créances est {GetConditionText(alerte.Condition)} {alerte.Seuil} jours.";
                        break;

                    case TypeAlerte.TauxRecouvrement:
                        message = $"Le taux de recouvrement est {GetConditionText(alerte.Condition)} {alerte.Seuil}%.";
                        break;

                    case TypeAlerte.MontantCreancesClient:
                        message = $"Le montant des créances d'un client est {GetConditionText(alerte.Condition)} {alerte.Seuil:C}.";
                        break;

                    case TypeAlerte.FactureEcheance:
                        message = $"Des factures arrivent à échéance dans {alerte.Seuil} jours.";
                        break;

                    default:
                        message = "Une alerte a été déclenchée.";
                        break;
                }
            }

            return message;
        }

        /// <summary>
        /// Retourne le texte correspondant à une condition
        /// </summary>
        /// <param name="condition">Condition</param>
        /// <returns>Texte de la condition</returns>
        private static string GetConditionText(string condition)
        {
            return condition.ToLower();
        }

        /// <summary>
        /// Affiche les détails d'une alerte
        /// </summary>
        /// <param name="alerte">Alerte</param>
        /// <param name="parent">Formulaire parent</param>
        private static void ShowAlerteDetails(Alerte alerte, Form parent)
        {
            // Créer un formulaire pour afficher les détails de l'alerte
            var form = new Form
            {
                Text = $"Détails de l'alerte : {alerte.Nom}",
                Size = new Size(500, 300),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            // Créer un TableLayoutPanel pour organiser les contrôles
            var tableLayoutPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 2,
                RowCount = 7
            };

            // Configurer les colonnes
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30));
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70));

            // Ajouter les contrôles
            AddLabelAndValue(tableLayoutPanel, 0, "Nom :", alerte.Nom);
            AddLabelAndValue(tableLayoutPanel, 1, "Description :", alerte.Description);
            AddLabelAndValue(tableLayoutPanel, 2, "Type :", alerte.Type);
            AddLabelAndValue(tableLayoutPanel, 3, "Condition :", $"{alerte.Condition} {alerte.Seuil}");
            AddLabelAndValue(tableLayoutPanel, 4, "Statut :", alerte.EstActive ? "Active" : "Inactive");
            AddLabelAndValue(tableLayoutPanel, 5, "Notification par email :", alerte.EnvoyerEmail ? "Oui" : "Non");

            // Ajouter un bouton pour fermer le formulaire
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Bottom,
                FlowDirection = FlowDirection.RightToLeft,
                Height = 40,
                Padding = new Padding(10, 5, 10, 5)
            };

            var closeButton = new Button
            {
                Text = "Fermer",
                Width = 100
            };
            closeButton.Click += (sender, e) => form.Close();
            buttonPanel.Controls.Add(closeButton);

            // Ajouter les contrôles au formulaire
            form.Controls.Add(tableLayoutPanel);
            form.Controls.Add(buttonPanel);

            // Afficher le formulaire
            form.ShowDialog(parent);
        }

        /// <summary>
        /// Ajoute un label et une valeur à un TableLayoutPanel
        /// </summary>
        /// <param name="tableLayoutPanel">TableLayoutPanel</param>
        /// <param name="row">Numéro de ligne</param>
        /// <param name="labelText">Texte du label</param>
        /// <param name="value">Valeur</param>
        private static void AddLabelAndValue(TableLayoutPanel tableLayoutPanel, int row, string labelText, string value)
        {
            var label = new Label
            {
                Text = labelText,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold)
            };

            var valueLabel = new Label
            {
                Text = value,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft
            };

            tableLayoutPanel.Controls.Add(label, 0, row);
            tableLayoutPanel.Controls.Add(valueLabel, 1, row);
        }
    }
}
