using RecouvreX.Business.Interfaces;
using Serilog;
using System.ComponentModel;

namespace RecouvreX.WinForms.Forms.Rapports
{
    public partial class ReportGeneratorForm : Form
    {
        private readonly IReportService _reportService;
        private readonly int _currentUserId;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();
        private string _reportData = string.Empty;

        public ReportGeneratorForm(IReportService reportService, int currentUserId)
        {
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _currentUserId = currentUserId;

            InitializeComponent();

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;
        }

        private void ReportGeneratorForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialiser les contrôles de date
                UpdateDateControlsVisibility();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire de génération de rapports");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void TypeRapportComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateDateControlsVisibility();
        }

        private void UpdateDateControlsVisibility()
        {
            var typeRapportComboBox = this.Controls.Find("typeRapportComboBox", true).FirstOrDefault() as ComboBox;
            var dateDebutLabel = this.Controls.Find("dateDebutLabel", true).FirstOrDefault() as Label;
            var dateDebutPicker = this.Controls.Find("dateDebutPicker", true).FirstOrDefault() as DateTimePicker;
            var dateFinLabel = this.Controls.Find("dateFinLabel", true).FirstOrDefault() as Label;
            var dateFinPicker = this.Controls.Find("dateFinPicker", true).FirstOrDefault() as DateTimePicker;

            if (typeRapportComboBox != null && dateDebutLabel != null && dateDebutPicker != null &&
                dateFinLabel != null && dateFinPicker != null)
            {
                string selectedType = typeRapportComboBox.SelectedItem.ToString();
                bool needsDates = selectedType != "Factures en retard" && selectedType != "Clients avec factures en retard";

                dateDebutLabel.Visible = needsDates;
                dateDebutPicker.Visible = needsDates;
                dateFinLabel.Visible = needsDates;
                dateFinPicker.Visible = needsDates;
            }
        }

        private void TypeRapportComboBox_Validating(object sender, CancelEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                if (comboBox.SelectedIndex == -1) // Aucun élément sélectionné
                {
                    _errorProvider.SetError(comboBox, "Veuillez sélectionner un type de rapport.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(comboBox, "");
                }
            }
        }

        private void FormatComboBox_Validating(object sender, CancelEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                if (comboBox.SelectedIndex == -1) // Aucun élément sélectionné
                {
                    _errorProvider.SetError(comboBox, "Veuillez sélectionner un format d'export.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(comboBox, "");
                }
            }
        }

        private void BrowseButton_Click(object sender, EventArgs e)
        {
            try
            {
                var formatComboBox = this.Controls.Find("formatComboBox", true).FirstOrDefault() as ComboBox;
                var cheminTextBox = this.Controls.Find("cheminTextBox", true).FirstOrDefault() as TextBox;

                if (formatComboBox != null && cheminTextBox != null)
                {
                    string extension = ".json";
                    string filter = "Fichiers JSON (*.json)|*.json";

                    switch (formatComboBox.SelectedItem.ToString())
                    {
                        case "CSV":
                            extension = ".csv";
                            filter = "Fichiers CSV (*.csv)|*.csv";
                            break;
                        case "Excel":
                            extension = ".xlsx";
                            filter = "Fichiers Excel (*.xlsx)|*.xlsx";
                            break;
                        case "PDF":
                            extension = ".pdf";
                            filter = "Fichiers PDF (*.pdf)|*.pdf";
                            break;
                    }

                    using (SaveFileDialog saveFileDialog = new SaveFileDialog())
                    {
                        saveFileDialog.Filter = filter;
                        saveFileDialog.DefaultExt = extension;
                        saveFileDialog.AddExtension = true;
                        saveFileDialog.FileName = $"Rapport_{DateTime.Now:yyyyMMdd}{extension}";

                        if (saveFileDialog.ShowDialog() == DialogResult.OK)
                        {
                            cheminTextBox.Text = saveFileDialog.FileName;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la sélection du chemin d'export");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void GenerateButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant de générer le rapport.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer les valeurs du formulaire
                var typeRapportComboBox = this.Controls.Find("typeRapportComboBox", true).FirstOrDefault() as ComboBox;
                var formatComboBox = this.Controls.Find("formatComboBox", true).FirstOrDefault() as ComboBox;
                var dateDebutPicker = this.Controls.Find("dateDebutPicker", true).FirstOrDefault() as DateTimePicker;
                var dateFinPicker = this.Controls.Find("dateFinPicker", true).FirstOrDefault() as DateTimePicker;
                var cheminTextBox = this.Controls.Find("cheminTextBox", true).FirstOrDefault() as TextBox;
                var previewTextBox = this.Controls.Find("previewTextBox", true).FirstOrDefault() as TextBox;

                if (typeRapportComboBox == null || formatComboBox == null || dateDebutPicker == null ||
                    dateFinPicker == null || cheminTextBox == null || previewTextBox == null)
                {
                    throw new InvalidOperationException("Un ou plusieurs contrôles sont introuvables.");
                }

                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Générer le rapport
                string reportType = typeRapportComboBox.SelectedItem.ToString();
                DateTime dateDebut = dateDebutPicker.Value;
                DateTime dateFin = dateFinPicker.Value.AddDays(1).AddSeconds(-1); // Fin de journée
                string format = formatComboBox.SelectedItem.ToString();
                string filePath = cheminTextBox.Text;

                // Générer le rapport en fonction du type
                switch (reportType)
                {
                    case "Factures en retard":
                        _reportData = await _reportService.GenerateOverdueInvoicesReportAsync();
                        break;
                    case "Paiements par période":
                        _reportData = await _reportService.GeneratePaymentsByPeriodReportAsync(dateDebut, dateFin);
                        break;
                    case "Factures par période":
                        _reportData = await _reportService.GenerateInvoicesByPeriodReportAsync(dateDebut, dateFin);
                        break;
                    case "Clients avec factures en retard":
                        _reportData = await _reportService.GenerateClientsWithOverdueInvoicesReportAsync();
                        break;
                    case "Performances des commerciaux":
                        _reportData = await _reportService.GenerateSalesPerformanceReportAsync(dateDebut, dateFin);
                        break;
                    case "Modes de paiement":
                        _reportData = await _reportService.GeneratePaymentMethodsReportAsync(dateDebut, dateFin);
                        break;
                    case "Relances effectuées":
                        _reportData = await _reportService.GenerateRemindersReportAsync(dateDebut, dateFin);
                        break;
                    case "Taux de recouvrement":
                        _reportData = await _reportService.GenerateRecoveryRateReportAsync(dateDebut, dateFin);
                        break;
                    default:
                        throw new InvalidOperationException($"Type de rapport non pris en charge : {reportType}");
                }

                // Afficher l'aperçu
                previewTextBox.Text = _reportData;

                // Exporter le rapport si un chemin est spécifié
                if (!string.IsNullOrEmpty(filePath))
                {
                    bool success = false;
                    switch (format)
                    {
                        case "CSV":
                            success = await _reportService.ExportReportToCsvAsync(_reportData, filePath);
                            break;
                        case "Excel":
                            success = await _reportService.ExportReportToExcelAsync(_reportData, filePath);
                            break;
                        case "PDF":
                            success = await _reportService.ExportReportToPdfAsync(_reportData, filePath, reportType);
                            break;
                        case "JSON":
                            await File.WriteAllTextAsync(filePath, _reportData);
                            success = true;
                            break;
                    }

                    if (success)
                    {
                        MessageBox.Show($"Le rapport a été exporté avec succès vers {filePath}.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("Une erreur s'est produite lors de l'exportation du rapport.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération du rapport");
                MessageBox.Show($"Une erreur s'est produite lors de la génération du rapport : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }
    }
}
