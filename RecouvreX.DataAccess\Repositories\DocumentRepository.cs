using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les documents
    /// </summary>
    public class DocumentRepository : BaseRepository<Document>, IDocumentRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public DocumentRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "Documents")
        {
        }

        /// <summary>
        /// Récupère tous les documents associés à une entité
        /// </summary>
        /// <param name="typeEntite">Type d'entité (Facture, Paiement, Relance, Client)</param>
        /// <param name="entiteId">Identifiant de l'entité</param>
        /// <returns>Liste des documents associés à l'entité</returns>
        public async Task<IEnumerable<Document>> GetByEntityAsync(string typeEntite, int entiteId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT *
                    FROM Documents
                    WHERE TypeEntite = @TypeEntite AND EntiteId = @EntiteId AND EstActif = 1";

                return await connection.QueryAsync<Document>(query, new { TypeEntite = typeEntite, EntiteId = entiteId });
            }
        }

        /// <summary>
        /// Récupère les documents par type
        /// </summary>
        /// <param name="type">Type de document</param>
        /// <returns>Liste des documents du type spécifié</returns>
        public async Task<IEnumerable<Document>> GetByTypeAsync(string type)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Documents WHERE Type = @Type AND EstActif = 1";
                return await connection.QueryAsync<Document>(query, new { Type = type });
            }
        }

        /// <summary>
        /// Récupère un document avec son contenu
        /// </summary>
        /// <param name="documentId">Identifiant du document</param>
        /// <returns>Document avec son contenu</returns>
        public async Task<Document> GetWithContentAsync(int documentId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Documents WHERE Id = @Id AND EstActif = 1";
                return await connection.QueryFirstOrDefaultAsync<Document>(query, new { Id = documentId });
            }
        }

        /// <summary>
        /// Recherche des documents par nom
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des documents correspondant au terme de recherche</returns>
        public async Task<IEnumerable<Document>> SearchByNameAsync(string searchTerm)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT *
                    FROM Documents
                    WHERE Nom LIKE @SearchTerm AND EstActif = 1";

                return await connection.QueryAsync<Document>(query, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        /// <summary>
        /// Ajoute un nouveau document
        /// </summary>
        /// <param name="document">Document à ajouter</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Document ajouté avec son identifiant généré</returns>
        public override async Task<Document> AddAsync(Document document, int userId)
        {
            // Utiliser une requête SQL spécifique pour gérer le contenu binaire
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    INSERT INTO Documents (
                        Nom, Type, MimeType, Taille, Contenu, Description,
                        TypeEntite, EntiteId, DateCreation, CreePar, EstActif
                    )
                    VALUES (
                        @Nom, @Type, @MimeType, @Taille, @Contenu, @Description,
                        @TypeEntite, @EntiteId, @DateCreation, @CreePar, @EstActif
                    );
                    SELECT CAST(SCOPE_IDENTITY() as int)";

                document.DateCreation = System.DateTime.Now;
                document.CreePar = userId;
                document.EstActif = true;

                var id = await connection.QuerySingleAsync<int>(query, document);
                document.Id = id;

                return document;
            }
        }

        /// <summary>
        /// Met à jour un document existant
        /// </summary>
        /// <param name="document">Document à mettre à jour</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Document mis à jour</returns>
        public override async Task<Document> UpdateAsync(Document document, int userId)
        {
            // Utiliser une requête SQL spécifique pour gérer le contenu binaire
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE Documents
                    SET Nom = @Nom,
                        Type = @Type,
                        MimeType = @MimeType,
                        Taille = @Taille,
                        Contenu = @Contenu,
                        Description = @Description,
                        TypeEntite = @TypeEntite,
                        EntiteId = @EntiteId,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id";

                document.DateModification = System.DateTime.Now;
                document.ModifiePar = userId;

                await connection.ExecuteAsync(query, document);

                return document;
            }
        }
    }
}
