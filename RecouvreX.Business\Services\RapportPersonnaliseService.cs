using Newtonsoft.Json;
using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models.Reporting;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service pour la gestion des rapports personnalisés
    /// </summary>
    public class RapportPersonnaliseService : IRapportPersonnaliseService
    {
        private readonly IRapportPersonnaliseRepository _rapportPersonnaliseRepository;
        private readonly IFactureRepository _factureRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IPaiementRepository _paiementRepository;
        private readonly IRelanceRepository _relanceRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IReportService _reportService;
        private readonly IEmailService _emailService;

        public RapportPersonnaliseService(
            IRapportPersonnaliseRepository rapportPersonnaliseRepository,
            IFactureRepository factureRepository,
            IClientRepository clientRepository,
            IPaiementRepository paiementRepository,
            IRelanceRepository relanceRepository,
            IUtilisateurRepository utilisateurRepository,
            IReportService reportService,
            IEmailService emailService)
        {
            _rapportPersonnaliseRepository = rapportPersonnaliseRepository ?? throw new ArgumentNullException(nameof(rapportPersonnaliseRepository));
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _paiementRepository = paiementRepository ?? throw new ArgumentNullException(nameof(paiementRepository));
            _relanceRepository = relanceRepository ?? throw new ArgumentNullException(nameof(relanceRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
        }

        /// <summary>
        /// Récupère tous les rapports personnalisés
        /// </summary>
        /// <returns>Liste des rapports personnalisés</returns>
        public async Task<IEnumerable<RapportPersonnalise>> GetAllAsync()
        {
            try
            {
                // Utiliser un ID système (0) car cette méthode n'est pas liée à un utilisateur spécifique
                return await _rapportPersonnaliseRepository.GetAllAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération de tous les rapports personnalisés");
                throw;
            }
        }

        /// <summary>
        /// Récupère les rapports personnalisés d'un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des rapports personnalisés de l'utilisateur</returns>
        public async Task<IEnumerable<RapportPersonnalise>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            try
            {
                return await _rapportPersonnaliseRepository.GetByUtilisateurIdAsync(utilisateurId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des rapports personnalisés de l'utilisateur {UtilisateurId}", utilisateurId);
                throw;
            }
        }

        /// <summary>
        /// Récupère un rapport personnalisé par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du rapport</param>
        /// <returns>Rapport personnalisé</returns>
        public async Task<RapportPersonnalise> GetByIdAsync(int id)
        {
            try
            {
                return await _rapportPersonnaliseRepository.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération du rapport personnalisé {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Crée un nouveau rapport personnalisé
        /// </summary>
        /// <param name="rapport">Rapport personnalisé à créer</param>
        /// <param name="userId">Identifiant de l'utilisateur qui crée le rapport</param>
        /// <returns>Rapport personnalisé créé</returns>
        public async Task<RapportPersonnalise> CreateAsync(RapportPersonnalise rapport, int userId)
        {
            try
            {
                rapport.UtilisateurId = userId;
                rapport.DateCreation = DateTime.Now;
                rapport.DateModification = DateTime.Now;
                rapport.EstActif = true;

                return await _rapportPersonnaliseRepository.AddAsync(rapport, userId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la création du rapport personnalisé {Nom} par l'utilisateur {UserId}", rapport.Nom, userId);
                throw;
            }
        }

        /// <summary>
        /// Met à jour un rapport personnalisé
        /// </summary>
        /// <param name="rapport">Rapport personnalisé à mettre à jour</param>
        /// <param name="userId">Identifiant de l'utilisateur qui met à jour le rapport</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateAsync(RapportPersonnalise rapport, int userId)
        {
            try
            {
                var existingRapport = await _rapportPersonnaliseRepository.GetByIdAsync(rapport.Id);
                if (existingRapport == null)
                    return false;

                rapport.DateModification = DateTime.Now;
                rapport.UtilisateurId = existingRapport.UtilisateurId; // Conserver l'utilisateur d'origine

                var updatedRapport = await _rapportPersonnaliseRepository.UpdateAsync(rapport, userId);
                return updatedRapport != null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour du rapport personnalisé {Id} par l'utilisateur {UserId}", rapport.Id, userId);
                throw;
            }
        }

        /// <summary>
        /// Supprime un rapport personnalisé
        /// </summary>
        /// <param name="id">Identifiant du rapport à supprimer</param>
        /// <param name="userId">Identifiant de l'utilisateur qui supprime le rapport</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int userId)
        {
            try
            {
                return await _rapportPersonnaliseRepository.DeleteAsync(id, userId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression du rapport personnalisé {Id} par l'utilisateur {UserId}", id, userId);
                throw;
            }
        }

        /// <summary>
        /// Génère un rapport personnalisé
        /// </summary>
        /// <param name="rapportId">Identifiant du rapport à générer</param>
        /// <param name="userId">Identifiant de l'utilisateur qui génère le rapport</param>
        /// <returns>Données du rapport au format JSON</returns>
        public async Task<string> GenerateReportAsync(int rapportId, int userId)
        {
            try
            {
                var rapport = await _rapportPersonnaliseRepository.GetByIdAsync(rapportId);
                if (rapport == null)
                    throw new ArgumentException($"Le rapport personnalisé avec l'ID {rapportId} n'existe pas.");

                // Désérialiser la configuration du rapport
                var configuration = JsonConvert.DeserializeObject<ConfigurationRapport>(rapport.Configuration);
                if (configuration == null)
                    throw new InvalidOperationException("La configuration du rapport est invalide.");

                // Générer le rapport avec la configuration
                var result = await GenerateReportAsync(configuration, null, null, userId);

                // Mettre à jour la date de dernière génération
                await _rapportPersonnaliseRepository.UpdateLastGenerationDateAsync(rapportId, DateTime.Now, userId);

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération du rapport personnalisé {Id} par l'utilisateur {UserId}", rapportId, userId);
                throw;
            }
        }

        /// <summary>
        /// Génère un rapport personnalisé avec des paramètres spécifiques
        /// </summary>
        /// <param name="configuration">Configuration du rapport</param>
        /// <param name="dateDebut">Date de début (optionnelle)</param>
        /// <param name="dateFin">Date de fin (optionnelle)</param>
        /// <param name="userId">Identifiant de l'utilisateur qui génère le rapport</param>
        /// <returns>Données du rapport au format JSON</returns>
        public async Task<string> GenerateReportAsync(ConfigurationRapport configuration, DateTime? dateDebut, DateTime? dateFin, int userId)
        {
            try
            {
                // Implémenter la logique de génération de rapport en fonction du type
                switch (configuration.TypeRapport)
                {
                    case "Factures":
                        return await GenerateInvoiceReportAsync(configuration, dateDebut, dateFin);
                    case "Paiements":
                        return await GeneratePaymentReportAsync(configuration, dateDebut, dateFin);
                    case "Clients":
                        return await GenerateClientReportAsync(configuration, dateDebut, dateFin);
                    case "Relances":
                        return await GenerateReminderReportAsync(configuration, dateDebut, dateFin);
                    case "Utilisateurs":
                        return await GenerateUserReportAsync(configuration, dateDebut, dateFin);
                    case "Comparatif":
                        return await GenerateComparativeReportAsync(configuration, dateDebut, dateFin);
                    case "Prévisionnel":
                        return await GeneratePredictiveReportAsync(configuration, dateDebut, dateFin);
                    default:
                        throw new ArgumentException($"Type de rapport non pris en charge : {configuration.TypeRapport}");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération du rapport personnalisé avec la configuration {Configuration} par l'utilisateur {UserId}",
                    JsonConvert.SerializeObject(configuration), userId);
                throw;
            }
        }

        // Méthodes de génération de rapports spécifiques à implémenter
        private async Task<string> GenerateInvoiceReportAsync(ConfigurationRapport configuration, DateTime? dateDebut, DateTime? dateFin)
        {
            // Implémenter la génération de rapport sur les factures
            throw new NotImplementedException("La génération de rapports sur les factures n'est pas encore implémentée.");
        }

        private async Task<string> GeneratePaymentReportAsync(ConfigurationRapport configuration, DateTime? dateDebut, DateTime? dateFin)
        {
            // Implémenter la génération de rapport sur les paiements
            throw new NotImplementedException("La génération de rapports sur les paiements n'est pas encore implémentée.");
        }

        private async Task<string> GenerateClientReportAsync(ConfigurationRapport configuration, DateTime? dateDebut, DateTime? dateFin)
        {
            // Implémenter la génération de rapport sur les clients
            throw new NotImplementedException("La génération de rapports sur les clients n'est pas encore implémentée.");
        }

        private async Task<string> GenerateReminderReportAsync(ConfigurationRapport configuration, DateTime? dateDebut, DateTime? dateFin)
        {
            // Implémenter la génération de rapport sur les relances
            throw new NotImplementedException("La génération de rapports sur les relances n'est pas encore implémentée.");
        }

        private async Task<string> GenerateUserReportAsync(ConfigurationRapport configuration, DateTime? dateDebut, DateTime? dateFin)
        {
            // Implémenter la génération de rapport sur les utilisateurs
            throw new NotImplementedException("La génération de rapports sur les utilisateurs n'est pas encore implémentée.");
        }

        private async Task<string> GenerateComparativeReportAsync(ConfigurationRapport configuration, DateTime? dateDebut, DateTime? dateFin)
        {
            // Implémenter la génération de rapport comparatif
            throw new NotImplementedException("La génération de rapports comparatifs n'est pas encore implémentée.");
        }

        private async Task<string> GeneratePredictiveReportAsync(ConfigurationRapport configuration, DateTime? dateDebut, DateTime? dateFin)
        {
            // Implémenter la génération de rapport prévisionnel
            throw new NotImplementedException("La génération de rapports prévisionnels n'est pas encore implémentée.");
        }

        // Autres méthodes à implémenter
        /// <summary>
        /// Exporte un rapport personnalisé dans le format spécifié
        /// </summary>
        /// <param name="rapportId">Identifiant du rapport à exporter</param>
        /// <param name="format">Format d'export (PDF, Excel, CSV)</param>
        /// <param name="filePath">Chemin du fichier de destination</param>
        /// <param name="userId">Identifiant de l'utilisateur qui exporte le rapport</param>
        /// <returns>True si l'exportation a réussi, sinon False</returns>
        public async Task<bool> ExportReportAsync(int rapportId, string format, string filePath, int userId)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                    return false;

                // Récupérer le rapport
                var rapport = await _rapportPersonnaliseRepository.GetByIdAsync(rapportId);
                if (rapport == null)
                    return false;

                // Générer le rapport
                string reportData = await GenerateReportAsync(rapportId, userId);
                if (string.IsNullOrEmpty(reportData))
                    return false;

                // Exporter le rapport dans le format demandé
                bool exportSuccess = false;
                switch (format.ToUpper())
                {
                    case "PDF":
                        exportSuccess = await _reportService.ExportReportToPdfAsync(reportData, filePath, rapport.Nom);
                        break;
                    case "EXCEL":
                        exportSuccess = await _reportService.ExportReportToExcelAsync(reportData, filePath);
                        break;
                    case "CSV":
                        exportSuccess = await _reportService.ExportReportToCsvAsync(reportData, filePath);
                        break;
                    default:
                        // Format JSON par défaut
                        await File.WriteAllTextAsync(filePath, reportData);
                        exportSuccess = true;
                        break;
                }

                if (exportSuccess)
                {
                    // Mettre à jour la date de dernière génération du rapport
                    rapport.DerniereGeneration = DateTime.Now;
                    await _rapportPersonnaliseRepository.UpdateAsync(rapport, userId);
                }

                return exportSuccess;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de l'exportation du rapport {RapportId}", rapportId);
                return false;
            }
        }

        /// <summary>
        /// Envoie un rapport personnalisé par email
        /// </summary>
        /// <param name="rapportId">Identifiant du rapport à envoyer</param>
        /// <param name="destinataires">Liste des adresses email des destinataires</param>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="message">Corps de l'email</param>
        /// <param name="format">Format d'export (PDF, Excel, CSV)</param>
        /// <param name="userId">Identifiant de l'utilisateur qui envoie le rapport</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        public async Task<bool> SendReportByEmailAsync(int rapportId, List<string> destinataires, string objet, string message, string format, int userId)
        {
            try
            {
                if (destinataires == null || !destinataires.Any())
                    return false;

                // Récupérer le rapport
                var rapport = await _rapportPersonnaliseRepository.GetByIdAsync(rapportId);
                if (rapport == null)
                    return false;

                // Générer le rapport
                string reportData = await GenerateReportAsync(rapportId, userId);
                if (string.IsNullOrEmpty(reportData))
                    return false;

                // Créer un fichier temporaire pour le rapport
                string tempDir = Path.Combine(Path.GetTempPath(), "RecouvreX", "Rapports");
                Directory.CreateDirectory(tempDir);
                string fileName = $"Rapport_{rapport.Nom}_{DateTime.Now.ToString("yyyyMMddHHmmss")}";
                string extension = ".json";

                switch (format.ToUpper())
                {
                    case "PDF":
                        extension = ".pdf";
                        break;
                    case "EXCEL":
                        extension = ".xml"; // Format XML Excel
                        break;
                    case "CSV":
                        extension = ".csv";
                        break;
                }

                string filePath = Path.Combine(tempDir, fileName + extension);

                // Exporter le rapport dans le format demandé
                bool exportSuccess = false;
                switch (format.ToUpper())
                {
                    case "PDF":
                        exportSuccess = await _reportService.ExportReportToPdfAsync(reportData, filePath, rapport.Nom);
                        break;
                    case "EXCEL":
                        exportSuccess = await _reportService.ExportReportToExcelAsync(reportData, filePath);
                        break;
                    case "CSV":
                        exportSuccess = await _reportService.ExportReportToCsvAsync(reportData, filePath);
                        break;
                    default:
                        // Format JSON par défaut
                        await File.WriteAllTextAsync(filePath, reportData);
                        exportSuccess = true;
                        break;
                }

                if (!exportSuccess)
                    return false;

                // Préparer le corps de l'email
                string corps = message;
                if (string.IsNullOrEmpty(corps))
                {
                    corps = $"<p>Bonjour,</p>" +
                           $"<p>Veuillez trouver ci-joint le rapport \"{rapport.Nom}\" généré le {DateTime.Now.ToString("dd/MM/yyyy")} à {DateTime.Now.ToString("HH:mm")}.</p>" +
                           $"<p>Ce rapport a été généré automatiquement par l'application RecouvreX.</p>" +
                           $"<p>Cordialement,<br>L'équipe RecouvreX</p>";
                }

                // Envoyer l'email avec la pièce jointe
                return await _emailService.EnvoyerEmailAvecPieceJointeAsync(
                    destinataires,
                    objet,
                    corps,
                    filePath,
                    fileName + extension,
                    true // Corps HTML
                );
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de l'envoi du rapport {RapportId} par email", rapportId);
                return false;
            }
        }

        /// <summary>
        /// Planifie la génération récurrente d'un rapport
        /// </summary>
        /// <param name="rapportId">Identifiant du rapport à planifier</param>
        /// <param name="frequence">Fréquence de génération (Quotidien, Hebdomadaire, Mensuel, Trimestriel)</param>
        /// <param name="jour">Jour de génération (pour les fréquences hebdomadaire, mensuelle et trimestrielle)</param>
        /// <param name="heure">Heure de génération</param>
        /// <param name="destinataires">Liste des adresses email des destinataires</param>
        /// <param name="format">Format d'export (PDF, Excel, CSV)</param>
        /// <param name="userId">Identifiant de l'utilisateur qui planifie le rapport</param>
        /// <returns>True si la planification a réussi, sinon False</returns>
        public async Task<bool> ScheduleReportAsync(int rapportId, string frequence, int? jour, TimeSpan heure, List<string> destinataires, string format, int userId)
        {
            try
            {
                // Récupérer le rapport
                var rapport = await _rapportPersonnaliseRepository.GetByIdAsync(rapportId);
                if (rapport == null)
                    return false;

                // Valider les paramètres
                if (string.IsNullOrEmpty(frequence))
                    return false;

                // Valider le jour en fonction de la fréquence
                if (frequence != "Quotidien" && !jour.HasValue)
                    return false;

                if (frequence == "Hebdomadaire" && (jour < 1 || jour > 7))
                    return false;

                if ((frequence == "Mensuel" || frequence == "Trimestriel") && (jour < 1 || jour > 31))
                    return false;

                // Mettre à jour les paramètres de planification
                rapport.EstProgramme = true;
                rapport.FrequenceGeneration = frequence;
                rapport.JourGeneration = jour;
                rapport.HeureGeneration = heure;
                rapport.FormatExport = format;
                rapport.DestinatairesEmail = destinataires != null && destinataires.Any()
                    ? string.Join(";", destinataires)
                    : null;
                rapport.DateModification = DateTime.Now;

                // Enregistrer les modifications
                await _rapportPersonnaliseRepository.UpdateAsync(rapport, userId);

                return true;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de la planification du rapport {RapportId}", rapportId);
                return false;
            }
        }

        /// <summary>
        /// Annule la planification d'un rapport
        /// </summary>
        /// <param name="rapportId">Identifiant du rapport</param>
        /// <param name="userId">Identifiant de l'utilisateur qui annule la planification</param>
        /// <returns>True si l'annulation a réussi, sinon False</returns>
        public async Task<bool> UnscheduleReportAsync(int rapportId, int userId)
        {
            try
            {
                // Récupérer le rapport
                var rapport = await _rapportPersonnaliseRepository.GetByIdAsync(rapportId);
                if (rapport == null)
                    return false;

                // Vérifier que le rapport est bien programmé
                if (!rapport.EstProgramme)
                    return true; // Déjà non programmé, on considère que c'est un succès

                // Mettre à jour les paramètres de planification
                rapport.EstProgramme = false;
                rapport.FrequenceGeneration = null;
                rapport.JourGeneration = null;
                rapport.HeureGeneration = TimeSpan.Zero; // Utiliser TimeSpan.Zero au lieu de null car TimeSpan est un type valeur
                rapport.DateModification = DateTime.Now;

                // Enregistrer les modifications
                await _rapportPersonnaliseRepository.UpdateAsync(rapport, userId);

                return true;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de l'annulation de la planification du rapport {RapportId}", rapportId);
                return false;
            }
        }

        /// <summary>
        /// Récupère tous les rapports programmés
        /// </summary>
        /// <returns>Liste des rapports programmés</returns>
        public async Task<IEnumerable<RapportPersonnalise>> GetScheduledReportsAsync()
        {
            try
            {
                // Récupérer tous les rapports programmés
                var rapports = await _rapportPersonnaliseRepository.GetAllAsync();
                return rapports.Where(r => r.EstProgramme && r.EstActif);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de la récupération des rapports programmés");
                return Enumerable.Empty<RapportPersonnalise>();
            }
        }

        public async Task<IEnumerable<string>> GetAvailableReportTypesAsync()
        {
            // Retourner les types de rapports disponibles
            return new List<string>
            {
                "Factures",
                "Paiements",
                "Clients",
                "Relances",
                "Utilisateurs",
                "Comparatif",
                "Prévisionnel"
            };
        }

        /// <summary>
        /// Récupère les colonnes disponibles pour un type de rapport
        /// </summary>
        /// <param name="reportType">Type de rapport</param>
        /// <returns>Liste des colonnes disponibles</returns>
        public async Task<IEnumerable<ColonneRapport>> GetAvailableColumnsAsync(string reportType)
        {
            try
            {
                var colonnes = new List<ColonneRapport>();

                switch (reportType)
                {
                    case "Factures":
                        colonnes.Add(new ColonneRapport { Nom = "Numero", Titre = "Numéro", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "ClientId", Titre = "ID Client", TypeDonnees = "Integer", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "ClientName", Titre = "Client", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "DateEmission", Titre = "Date d'émission", TypeDonnees = "DateTime", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "DateEcheance", Titre = "Date d'échéance", TypeDonnees = "DateTime", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "MontantHT", Titre = "Montant HT", TypeDonnees = "Decimal", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "MontantTVA", Titre = "Montant TVA", TypeDonnees = "Decimal", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "MontantTTC", Titre = "Montant TTC", TypeDonnees = "Decimal", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "MontantPaye", Titre = "Montant payé", TypeDonnees = "Decimal", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "MontantRestant", Titre = "Montant restant", TypeDonnees = "Decimal", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Statut", Titre = "Statut", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "JoursRetard", Titre = "Jours de retard", TypeDonnees = "Integer", EstVisible = true });
                        break;

                    case "Paiements":
                        colonnes.Add(new ColonneRapport { Nom = "Reference", Titre = "Référence", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "ClientId", Titre = "ID Client", TypeDonnees = "Integer", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "ClientName", Titre = "Client", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "FactureId", Titre = "ID Facture", TypeDonnees = "Integer", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "InvoiceNumber", Titre = "Numéro de facture", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Date", Titre = "Date", TypeDonnees = "DateTime", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Amount", Titre = "Montant", TypeDonnees = "Decimal", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Method", Titre = "Mode de paiement", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Notes", Titre = "Notes", TypeDonnees = "String", EstVisible = true });
                        break;

                    case "Clients":
                        colonnes.Add(new ColonneRapport { Nom = "Id", Titre = "ID", TypeDonnees = "Integer", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Code", Titre = "Code", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "RaisonSociale", Titre = "Raison sociale", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Adresse", Titre = "Adresse", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "CodePostal", Titre = "Code postal", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Ville", Titre = "Ville", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Pays", Titre = "Pays", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Telephone", Titre = "Téléphone", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Email", Titre = "Email", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "SoldeActuel", Titre = "Solde actuel", TypeDonnees = "Decimal", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Segment", Titre = "Segment", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "ScoreRisque", Titre = "Score de risque", TypeDonnees = "Integer", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "OverdueInvoicesCount", Titre = "Nombre de factures en retard", TypeDonnees = "Integer", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "OverdueAmount", Titre = "Montant en retard", TypeDonnees = "Decimal", EstVisible = true });
                        break;

                    case "Relances":
                        colonnes.Add(new ColonneRapport { Nom = "Id", Titre = "ID", TypeDonnees = "Integer", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "FactureId", Titre = "ID Facture", TypeDonnees = "Integer", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "InvoiceNumber", Titre = "Numéro de facture", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "ClientName", Titre = "Client", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Type", Titre = "Type", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "DateRelance", Titre = "Date de relance", TypeDonnees = "DateTime", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Niveau", Titre = "Niveau", TypeDonnees = "Integer", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Statut", Titre = "Statut", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "UtilisateurId", Titre = "ID Utilisateur", TypeDonnees = "Integer", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "UserName", Titre = "Utilisateur", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "DateProchaineRelance", Titre = "Date prochaine relance", TypeDonnees = "DateTime", EstVisible = true });
                        break;

                    case "Utilisateurs":
                        colonnes.Add(new ColonneRapport { Nom = "Id", Titre = "ID", TypeDonnees = "Integer", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Nom", Titre = "Nom", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Prenom", Titre = "Prénom", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Email", Titre = "Email", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Role", Titre = "Rôle", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "EstActif", Titre = "Est actif", TypeDonnees = "Boolean", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "DateCreation", Titre = "Date de création", TypeDonnees = "DateTime", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "DerniereConnexion", Titre = "Dernière connexion", TypeDonnees = "DateTime", EstVisible = true });
                        break;

                    case "Comparatif":
                        colonnes.Add(new ColonneRapport { Nom = "Periode1", Titre = "Période 1", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Periode2", Titre = "Période 2", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Indicateur", Titre = "Indicateur", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "ValeurPeriode1", Titre = "Valeur période 1", TypeDonnees = "Decimal", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "ValeurPeriode2", Titre = "Valeur période 2", TypeDonnees = "Decimal", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Variation", Titre = "Variation", TypeDonnees = "Decimal", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "VariationPourcentage", Titre = "Variation (%)", TypeDonnees = "Decimal", EstVisible = true });
                        break;

                    case "Prévisionnel":
                        colonnes.Add(new ColonneRapport { Nom = "DatePrevision", Titre = "Date prévision", TypeDonnees = "DateTime", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Indicateur", Titre = "Indicateur", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "ValeurActuelle", Titre = "Valeur actuelle", TypeDonnees = "Decimal", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "ValeurPrevisionnelle", Titre = "Valeur prévisionnelle", TypeDonnees = "Decimal", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Tendance", Titre = "Tendance", TypeDonnees = "String", EstVisible = true });
                        colonnes.Add(new ColonneRapport { Nom = "Probabilite", Titre = "Probabilité", TypeDonnees = "Decimal", EstVisible = true });
                        break;

                    default:
                        throw new ArgumentException($"Type de rapport non pris en charge : {reportType}");
                }

                return colonnes;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de la récupération des colonnes disponibles pour le type de rapport {ReportType}", reportType);
                return Enumerable.Empty<ColonneRapport>();
            }
        }
    }
}
