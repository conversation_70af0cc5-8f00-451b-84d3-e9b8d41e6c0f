using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Admin
{
    partial class DatabaseConnectionForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles
        private Label titleLabel;
        private Label serverLabel;
        private TextBox serverTextBox;
        private Label databaseLabel;
        private TextBox databaseTextBox;
        private GroupBox authGroupBox;
        private RadioButton windowsAuthRadioButton;
        private RadioButton sqlAuthRadioButton;
        private Label userIdLabel;
        private TextBox userIdTextBox;
        private Label passwordLabel;
        private TextBox passwordTextBox;
        private GroupBox advancedGroupBox;
        private Label timeoutLabel;
        private NumericUpDown timeoutNumericUpDown;
        private CheckBox trustServerCertificateCheckBox;
        private CheckBox encryptCheckBox;
        private CheckBox encryptPasswordCheckBox;
        private Button testConnectionButton;
        private Button saveButton;
        private Button cancelButton;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            
            // Définir les propriétés du formulaire
            this.Text = "Configuration de la connexion à la base de données";
            this.ClientSize = new System.Drawing.Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Load += new EventHandler(this.DatabaseConnectionForm_Load);
            
            // Titre
            this.titleLabel = new Label();
            this.titleLabel.Text = "Configuration de la connexion à la base de données";
            this.titleLabel.Font = new Font(this.Font.FontFamily, 14, FontStyle.Bold);
            this.titleLabel.Location = new Point(20, 20);
            this.titleLabel.Size = new Size(560, 30);
            this.Controls.Add(this.titleLabel);
            
            // Serveur
            this.serverLabel = new Label();
            this.serverLabel.Text = "Serveur :";
            this.serverLabel.Location = new Point(20, 70);
            this.serverLabel.Size = new Size(120, 20);
            this.Controls.Add(this.serverLabel);
            
            this.serverTextBox = new TextBox();
            this.serverTextBox.Location = new Point(150, 70);
            this.serverTextBox.Size = new Size(400, 20);
            this.Controls.Add(this.serverTextBox);
            
            // Base de données
            this.databaseLabel = new Label();
            this.databaseLabel.Text = "Base de données :";
            this.databaseLabel.Location = new Point(20, 100);
            this.databaseLabel.Size = new Size(120, 20);
            this.Controls.Add(this.databaseLabel);
            
            this.databaseTextBox = new TextBox();
            this.databaseTextBox.Location = new Point(150, 100);
            this.databaseTextBox.Size = new Size(400, 20);
            this.Controls.Add(this.databaseTextBox);
            
            // Authentification
            this.authGroupBox = new GroupBox();
            this.authGroupBox.Text = "Authentification";
            this.authGroupBox.Location = new Point(20, 140);
            this.authGroupBox.Size = new Size(560, 120);
            this.Controls.Add(this.authGroupBox);
            
            this.windowsAuthRadioButton = new RadioButton();
            this.windowsAuthRadioButton.Text = "Authentification Windows";
            this.windowsAuthRadioButton.Location = new Point(20, 20);
            this.windowsAuthRadioButton.Size = new Size(200, 20);
            this.windowsAuthRadioButton.Checked = true;
            this.windowsAuthRadioButton.CheckedChanged += new EventHandler(this.AuthTypeRadioButton_CheckedChanged);
            this.authGroupBox.Controls.Add(this.windowsAuthRadioButton);
            
            this.sqlAuthRadioButton = new RadioButton();
            this.sqlAuthRadioButton.Text = "Authentification SQL Server";
            this.sqlAuthRadioButton.Location = new Point(20, 50);
            this.sqlAuthRadioButton.Size = new Size(200, 20);
            this.sqlAuthRadioButton.CheckedChanged += new EventHandler(this.AuthTypeRadioButton_CheckedChanged);
            this.authGroupBox.Controls.Add(this.sqlAuthRadioButton);
            
            this.userIdLabel = new Label();
            this.userIdLabel.Text = "Identifiant :";
            this.userIdLabel.Location = new Point(230, 50);
            this.userIdLabel.Size = new Size(80, 20);
            this.userIdLabel.Enabled = false;
            this.authGroupBox.Controls.Add(this.userIdLabel);
            
            this.userIdTextBox = new TextBox();
            this.userIdTextBox.Location = new Point(310, 50);
            this.userIdTextBox.Size = new Size(200, 20);
            this.userIdTextBox.Enabled = false;
            this.authGroupBox.Controls.Add(this.userIdTextBox);
            
            this.passwordLabel = new Label();
            this.passwordLabel.Text = "Mot de passe :";
            this.passwordLabel.Location = new Point(230, 80);
            this.passwordLabel.Size = new Size(80, 20);
            this.passwordLabel.Enabled = false;
            this.authGroupBox.Controls.Add(this.passwordLabel);
            
            this.passwordTextBox = new TextBox();
            this.passwordTextBox.Location = new Point(310, 80);
            this.passwordTextBox.Size = new Size(200, 20);
            this.passwordTextBox.PasswordChar = '*';
            this.passwordTextBox.Enabled = false;
            this.authGroupBox.Controls.Add(this.passwordTextBox);
            
            // Options avancées
            this.advancedGroupBox = new GroupBox();
            this.advancedGroupBox.Text = "Options avancées";
            this.advancedGroupBox.Location = new Point(20, 280);
            this.advancedGroupBox.Size = new Size(560, 120);
            this.Controls.Add(this.advancedGroupBox);
            
            this.timeoutLabel = new Label();
            this.timeoutLabel.Text = "Timeout (secondes) :";
            this.timeoutLabel.Location = new Point(20, 30);
            this.timeoutLabel.Size = new Size(120, 20);
            this.advancedGroupBox.Controls.Add(this.timeoutLabel);
            
            this.timeoutNumericUpDown = new NumericUpDown();
            this.timeoutNumericUpDown.Location = new Point(150, 30);
            this.timeoutNumericUpDown.Size = new Size(80, 20);
            this.timeoutNumericUpDown.Minimum = 5;
            this.timeoutNumericUpDown.Maximum = 300;
            this.timeoutNumericUpDown.Value = 30;
            this.advancedGroupBox.Controls.Add(this.timeoutNumericUpDown);
            
            this.trustServerCertificateCheckBox = new CheckBox();
            this.trustServerCertificateCheckBox.Text = "Trust Server Certificate";
            this.trustServerCertificateCheckBox.Location = new Point(20, 60);
            this.trustServerCertificateCheckBox.Size = new Size(200, 20);
            this.trustServerCertificateCheckBox.Checked = true;
            this.advancedGroupBox.Controls.Add(this.trustServerCertificateCheckBox);
            
            this.encryptCheckBox = new CheckBox();
            this.encryptCheckBox.Text = "Encrypt";
            this.encryptCheckBox.Location = new Point(230, 60);
            this.encryptCheckBox.Size = new Size(200, 20);
            this.encryptCheckBox.Checked = true;
            this.advancedGroupBox.Controls.Add(this.encryptCheckBox);
            
            this.encryptPasswordCheckBox = new CheckBox();
            this.encryptPasswordCheckBox.Text = "Chiffrer le mot de passe dans le fichier de configuration";
            this.encryptPasswordCheckBox.Location = new Point(20, 90);
            this.encryptPasswordCheckBox.Size = new Size(400, 20);
            this.encryptPasswordCheckBox.Checked = true;
            this.advancedGroupBox.Controls.Add(this.encryptPasswordCheckBox);
            
            // Boutons
            this.testConnectionButton = new Button();
            this.testConnectionButton.Text = "Tester la connexion";
            this.testConnectionButton.Location = new Point(20, 420);
            this.testConnectionButton.Size = new Size(150, 30);
            this.testConnectionButton.Click += new EventHandler(this.TestConnectionButton_Click);
            this.Controls.Add(this.testConnectionButton);
            
            this.saveButton = new Button();
            this.saveButton.Text = "Enregistrer";
            this.saveButton.Location = new Point(380, 420);
            this.saveButton.Size = new Size(100, 30);
            this.saveButton.Click += new EventHandler(this.SaveButton_Click);
            this.Controls.Add(this.saveButton);
            
            this.cancelButton = new Button();
            this.cancelButton.Text = "Annuler";
            this.cancelButton.Location = new Point(490, 420);
            this.cancelButton.Size = new Size(80, 30);
            this.cancelButton.Click += new EventHandler(this.CancelButton_Click);
            this.Controls.Add(this.cancelButton);
        }

        #endregion
    }
}
