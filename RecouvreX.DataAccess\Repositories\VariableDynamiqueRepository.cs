using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour la gestion des variables dynamiques
    /// </summary>
    public class VariableDynamiqueRepository : BaseRepository<VariableDynamique>, IVariableDynamiqueRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public VariableDynamiqueRepository(DatabaseConnection dbConnection) : base(dbConnection, "VariablesDynamiques")
        {
        }

        /// <summary>
        /// Récupère les variables dynamiques par catégorie
        /// </summary>
        /// <param name="categorie">Catégorie de variable</param>
        /// <returns>Liste des variables dynamiques de la catégorie spécifiée</returns>
        public async Task<IEnumerable<VariableDynamique>> GetByCategorieAsync(string categorie)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE Categorie = @Categorie AND EstActif = 1";
                return await connection.QueryAsync<VariableDynamique>(query, new { Categorie = categorie });
            }
        }
    }
}
