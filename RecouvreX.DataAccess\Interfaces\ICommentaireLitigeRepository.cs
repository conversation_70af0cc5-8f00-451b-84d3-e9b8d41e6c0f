using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des commentaires de litiges
    /// </summary>
    public interface ICommentaireLitigeRepository
    {
        /// <summary>
        /// Récupère tous les commentaires
        /// </summary>
        /// <returns>Liste des commentaires</returns>
        Task<IEnumerable<CommentaireLitige>> GetAllAsync();

        /// <summary>
        /// Récupère un commentaire par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du commentaire</param>
        /// <returns>Commentaire trouvé ou null</returns>
        Task<CommentaireLitige> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les commentaires pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des commentaires pour le litige</returns>
        Task<IEnumerable<CommentaireLitige>> GetByLitigeIdAsync(int litigeId);

        /// <summary>
        /// Ajoute un commentaire
        /// </summary>
        /// <param name="commentaire">Commentaire à ajouter</param>
        /// <returns>Commentaire ajouté</returns>
        Task<CommentaireLitige> AddAsync(CommentaireLitige commentaire);

        /// <summary>
        /// Met à jour un commentaire
        /// </summary>
        /// <param name="commentaire">Commentaire à mettre à jour</param>
        /// <returns>True si le commentaire a été mis à jour</returns>
        Task<bool> UpdateAsync(CommentaireLitige commentaire);

        /// <summary>
        /// Supprime un commentaire
        /// </summary>
        /// <param name="id">Identifiant du commentaire</param>
        /// <returns>True si le commentaire a été supprimé</returns>
        Task<bool> DeleteAsync(int id);
    }
}
