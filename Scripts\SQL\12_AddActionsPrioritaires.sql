-- Script pour ajouter la table ActionsPrioritaires
USE [RecouvreX]
GO

-- Vérifier si la table existe déjà
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[app].[ActionsPrioritaires]') AND type in (N'U'))
BEGIN
    -- Créer la table ActionsPrioritaires
    CREATE TABLE [app].[ActionsPrioritaires](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [ClientId] [int] NOT NULL,
        [FactureId] [int] NULL,
        [TypeAction] [int] NOT NULL,
        [NiveauPriorite] [int] NOT NULL,
        [Description] [nvarchar](500) NOT NULL,
        [DateEcheance] [datetime] NOT NULL,
        [UtilisateurAssigneId] [int] NULL,
        [EstCompletee] [bit] NOT NULL DEFAULT 0,
        [DateCompletion] [datetime] NULL,
        [CommentaireCompletion] [nvarchar](500) NULL,
        [ScorePriorite] [int] NOT NULL DEFAULT 0,
        [EstAutomatique] [bit] NOT NULL DEFAULT 0,
        [EstActif] [bit] NOT NULL DEFAULT 1,
        [DateCreation] [datetime] NOT NULL DEFAULT GETDATE(),
        [CreePar] [int] NOT NULL,
        [DateModification] [datetime] NULL,
        [ModifiePar] [int] NULL,
        CONSTRAINT [PK_ActionsPrioritaires] PRIMARY KEY CLUSTERED 
        (
            [Id] ASC
        ) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
    ) ON [PRIMARY]

    -- Ajouter les contraintes de clé étrangère
    ALTER TABLE [app].[ActionsPrioritaires] WITH CHECK ADD CONSTRAINT [FK_ActionsPrioritaires_Clients] FOREIGN KEY([ClientId])
    REFERENCES [app].[Clients] ([Id])

    ALTER TABLE [app].[ActionsPrioritaires] WITH CHECK ADD CONSTRAINT [FK_ActionsPrioritaires_Factures] FOREIGN KEY([FactureId])
    REFERENCES [app].[Factures] ([Id])

    ALTER TABLE [app].[ActionsPrioritaires] WITH CHECK ADD CONSTRAINT [FK_ActionsPrioritaires_Utilisateurs_Assigne] FOREIGN KEY([UtilisateurAssigneId])
    REFERENCES [app].[Utilisateurs] ([Id])

    ALTER TABLE [app].[ActionsPrioritaires] WITH CHECK ADD CONSTRAINT [FK_ActionsPrioritaires_Utilisateurs_Cree] FOREIGN KEY([CreePar])
    REFERENCES [app].[Utilisateurs] ([Id])

    ALTER TABLE [app].[ActionsPrioritaires] WITH CHECK ADD CONSTRAINT [FK_ActionsPrioritaires_Utilisateurs_Modifie] FOREIGN KEY([ModifiePar])
    REFERENCES [app].[Utilisateurs] ([Id])

    -- Créer des index pour améliorer les performances
    CREATE NONCLUSTERED INDEX [IX_ActionsPrioritaires_ClientId] ON [app].[ActionsPrioritaires]
    (
        [ClientId] ASC
    ) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

    CREATE NONCLUSTERED INDEX [IX_ActionsPrioritaires_FactureId] ON [app].[ActionsPrioritaires]
    (
        [FactureId] ASC
    ) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

    CREATE NONCLUSTERED INDEX [IX_ActionsPrioritaires_UtilisateurAssigneId] ON [app].[ActionsPrioritaires]
    (
        [UtilisateurAssigneId] ASC
    ) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

    CREATE NONCLUSTERED INDEX [IX_ActionsPrioritaires_NiveauPriorite] ON [app].[ActionsPrioritaires]
    (
        [NiveauPriorite] ASC
    ) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

    CREATE NONCLUSTERED INDEX [IX_ActionsPrioritaires_EstCompletee] ON [app].[ActionsPrioritaires]
    (
        [EstCompletee] ASC
    ) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

    PRINT 'Table ActionsPrioritaires créée avec succès.'
END
ELSE
BEGIN
    PRINT 'La table ActionsPrioritaires existe déjà.'
END
GO
