using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;

namespace RecouvreX.Business.Helpers
{
    /// <summary>
    /// Classe utilitaire pour l'envoi d'emails
    /// </summary>
    public static class EmailHelper
    {
        /// <summary>
        /// Envoie un email
        /// </summary>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="contenu">Contenu de l'email</param>
        /// <param name="destinataire">Adresse email du destinataire</param>
        /// <param name="configuration">Configuration de l'application</param>
        /// <param name="piecesJointes">Liste des pièces jointes (optionnel)</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        public static async Task<bool> SendEmailAsync(string objet, string contenu, string destinataire, IConfiguration configuration, List<string> piecesJointes = null)
        {
            try
            {
                // Récupérer les paramètres SMTP depuis la configuration
                var emailSettings = configuration.GetSection("EmailSettings");
                var smtpServer = emailSettings["SmtpServer"];
                var smtpPortStr = emailSettings["SmtpPort"];
                var smtpPort = !string.IsNullOrEmpty(smtpPortStr) && int.TryParse(smtpPortStr, out int port) ? port : 25;
                var smtpUsername = emailSettings["Username"];
                var smtpPassword = emailSettings["Password"];
                var emailFrom = emailSettings["FromEmail"];
                var emailFromName = emailSettings["FromName"];
                var enableSslStr = emailSettings["EnableSsl"];
                var enableSsl = !string.IsNullOrEmpty(enableSslStr) && bool.TryParse(enableSslStr, out bool ssl) ? ssl : false;

                // Vérifier que les paramètres sont valides
                if (string.IsNullOrEmpty(smtpServer) || string.IsNullOrEmpty(smtpUsername) || string.IsNullOrEmpty(smtpPassword) || string.IsNullOrEmpty(emailFrom))
                {
                    Serilog.Log.Error("Paramètres SMTP manquants dans la configuration");
                    return false;
                }

                // Créer le message
                var message = new MailMessage();
                message.From = new MailAddress(emailFrom, emailFromName);
                message.To.Add(new MailAddress(destinataire));
                message.Subject = objet;
                message.Body = contenu;
                message.IsBodyHtml = true;

                // Ajouter les pièces jointes
                if (piecesJointes != null)
                {
                    foreach (var pieceJointe in piecesJointes)
                    {
                        if (File.Exists(pieceJointe))
                        {
                            message.Attachments.Add(new Attachment(pieceJointe));
                        }
                    }
                }

                // Créer le client SMTP
                using (var client = new SmtpClient(smtpServer, smtpPort))
                {
                    client.EnableSsl = enableSsl;
                    client.Credentials = new NetworkCredential(smtpUsername, smtpPassword as string);
                    client.DeliveryMethod = SmtpDeliveryMethod.Network;

                    // Envoyer l'email
                    await client.SendMailAsync(message);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de l'envoi de l'email à {Destinataire}", destinataire);
                return false;
            }
        }
    }
}
