using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.ComponentModel;

namespace RecouvreX.WinForms.Forms.Relances
{
    public partial class RelanceEditForm : Form
    {
        private readonly IRelanceService _relanceService;
        private readonly IFactureService _factureService;
        private readonly IClientService _clientService;
        private readonly int _currentUserId;
        private readonly Relance? _relance;
        private readonly bool _isEditMode;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();
        private List<Facture> _factures = new List<Facture>();
        private Dictionary<int, string> _clientNames = new Dictionary<int, string>();

        public RelanceEditForm(IRelanceService relanceService, IFactureService factureService, IClientService clientService,
            int currentUserId, Relance? relance = null)
        {
            _relanceService = relanceService ?? throw new ArgumentNullException(nameof(relanceService));
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _currentUserId = currentUserId;
            _relance = relance;
            _isEditMode = relance != null;

            InitializeComponent();

            // Mettre à jour le titre du formulaire après l'initialisation des composants
            this.Text = _isEditMode ? "Modifier une relance" : "Ajouter une relance";

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;
        }

        private async void RelanceEditForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Charger les factures
                await LoadFacturesAsync();

                // Si en mode édition, remplir les champs avec les données de la relance
                if (_isEditMode && _relance != null)
                {
                    FillFormWithRelanceData(_relance);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'édition de relance");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadFacturesAsync()
        {
            try
            {
                // Récupérer les factures en retard ou non payées
                _factures = (await _factureService.GetOverdueInvoicesAsync()).ToList();
                var facturesNonPayees = await _factureService.GetByStatutAsync("En attente");
                _factures.AddRange(facturesNonPayees.Where(f => !_factures.Any(of => of.Id == f.Id)));

                // Récupérer les noms des clients
                await LoadClientNamesAsync();

                // Remplir la ComboBox
                var factureComboBox = this.Controls.Find("factureComboBox", true).FirstOrDefault() as ComboBox;
                if (factureComboBox != null)
                {
                    factureComboBox.Items.Clear();
                    factureComboBox.DisplayMember = "Text";
                    factureComboBox.ValueMember = "Value";

                    // Ajouter un élément vide
                    factureComboBox.Items.Add(new { Text = "-- Sélectionner une facture --", Value = 0 });

                    // Ajouter les factures
                    foreach (var facture in _factures)
                    {
                        string clientName = _clientNames.ContainsKey(facture.ClientId) ? _clientNames[facture.ClientId] : "Client inconnu";
                        factureComboBox.Items.Add(new { Text = $"{facture.Numero} - {clientName} ({facture.MontantRestant:C2})", Value = facture.Id });
                    }

                    factureComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des factures");
                throw;
            }
        }

        private async Task LoadClientNamesAsync()
        {
            try
            {
                // Récupérer les IDs des clients uniques
                var clientIds = _factures.Select(f => f.ClientId).Distinct().ToList();

                // Récupérer les noms des clients
                _clientNames.Clear();
                foreach (var clientId in clientIds)
                {
                    var client = await _clientService.GetByIdAsync(clientId);
                    if (client != null)
                    {
                        _clientNames[clientId] = client.RaisonSociale;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des noms des clients");
                throw;
            }
        }

        private void FillFormWithRelanceData(Relance relance)
        {
            var factureComboBox = this.Controls.Find("factureComboBox", true).FirstOrDefault() as ComboBox;
            var clientTextBox = this.Controls.Find("clientTextBox", true).FirstOrDefault() as TextBox;
            var typeComboBox = this.Controls.Find("typeComboBox", true).FirstOrDefault() as ComboBox;
            var dateRelancePicker = this.Controls.Find("dateRelancePicker", true).FirstOrDefault() as DateTimePicker;
            var niveauNumericUpDown = this.Controls.Find("niveauNumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var statutComboBox = this.Controls.Find("statutComboBox", true).FirstOrDefault() as ComboBox;
            var contenuTextBox = this.Controls.Find("contenuTextBox", true).FirstOrDefault() as TextBox;
            var dateProchaineRelancePicker = this.Controls.Find("dateProchaineRelancePicker", true).FirstOrDefault() as DateTimePicker;

            if (factureComboBox != null)
            {
                // Sélectionner la facture
                for (int i = 0; i < factureComboBox.Items.Count; i++)
                {
                    dynamic item = factureComboBox.Items[i];
                    if (item.Value == relance.FactureId)
                    {
                        factureComboBox.SelectedIndex = i;
                        break;
                    }
                }
            }

            if (typeComboBox != null)
            {
                // Sélectionner le type
                for (int i = 0; i < typeComboBox.Items.Count; i++)
                {
                    if (typeComboBox.Items[i].ToString() == relance.Type)
                    {
                        typeComboBox.SelectedIndex = i;
                        break;
                    }
                }
            }

            if (dateRelancePicker != null) dateRelancePicker.Value = relance.DateRelance;
            if (niveauNumericUpDown != null) niveauNumericUpDown.Value = relance.Niveau;

            if (statutComboBox != null)
            {
                // Sélectionner le statut
                for (int i = 0; i < statutComboBox.Items.Count; i++)
                {
                    if (statutComboBox.Items[i].ToString() == relance.Statut)
                    {
                        statutComboBox.SelectedIndex = i;
                        break;
                    }
                }
            }

            if (contenuTextBox != null) contenuTextBox.Text = relance.Contenu;
            if (dateProchaineRelancePicker != null && relance.DateProchaineRelance.HasValue)
                dateProchaineRelancePicker.Value = relance.DateProchaineRelance.Value;
        }

        private void FactureComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            var factureComboBox = sender as ComboBox;
            var clientTextBox = this.Controls.Find("clientTextBox", true).FirstOrDefault() as TextBox;

            if (factureComboBox != null && clientTextBox != null && factureComboBox.SelectedIndex > 0)
            {
                // Récupérer l'ID de la facture sélectionnée
                int factureId = ((dynamic)factureComboBox.SelectedItem).Value;

                // Trouver la facture correspondante
                var facture = _factures.FirstOrDefault(f => f.Id == factureId);
                if (facture != null && _clientNames.ContainsKey(facture.ClientId))
                {
                    // Afficher le nom du client
                    clientTextBox.Text = _clientNames[facture.ClientId];
                }
                else
                {
                    clientTextBox.Text = string.Empty;
                }
            }
            else if (clientTextBox != null)
            {
                clientTextBox.Text = string.Empty;
            }
        }

        private void FactureComboBox_Validating(object sender, CancelEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                if (comboBox.SelectedIndex == 0) // L'élément vide
                {
                    _errorProvider.SetError(comboBox, "Veuillez sélectionner une facture.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(comboBox, "");
                }
            }
        }

        private void TypeComboBox_Validating(object sender, CancelEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                if (comboBox.SelectedIndex == -1) // Aucun élément sélectionné
                {
                    _errorProvider.SetError(comboBox, "Veuillez sélectionner un type de relance.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(comboBox, "");
                }
            }
        }

        private void StatutComboBox_Validating(object sender, CancelEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                if (comboBox.SelectedIndex == -1) // Aucun élément sélectionné
                {
                    _errorProvider.SetError(comboBox, "Veuillez sélectionner un statut.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(comboBox, "");
                }
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant d'enregistrer.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer les valeurs du formulaire
                var factureComboBox = this.Controls.Find("factureComboBox", true).FirstOrDefault() as ComboBox;
                var typeComboBox = this.Controls.Find("typeComboBox", true).FirstOrDefault() as ComboBox;
                var dateRelancePicker = this.Controls.Find("dateRelancePicker", true).FirstOrDefault() as DateTimePicker;
                var niveauNumericUpDown = this.Controls.Find("niveauNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                var statutComboBox = this.Controls.Find("statutComboBox", true).FirstOrDefault() as ComboBox;
                var contenuTextBox = this.Controls.Find("contenuTextBox", true).FirstOrDefault() as TextBox;
                var dateProchaineRelancePicker = this.Controls.Find("dateProchaineRelancePicker", true).FirstOrDefault() as DateTimePicker;

                if (factureComboBox == null || typeComboBox == null || dateRelancePicker == null ||
                    niveauNumericUpDown == null || statutComboBox == null || contenuTextBox == null ||
                    dateProchaineRelancePicker == null)
                {
                    throw new InvalidOperationException("Un ou plusieurs champs sont introuvables.");
                }

                // Créer ou mettre à jour la relance
                Relance relance;
                if (_isEditMode && _relance != null)
                {
                    // Mode édition
                    relance = _relance;
                }
                else
                {
                    // Mode ajout
                    relance = new Relance();
                }

                // Remplir les propriétés de la relance
                relance.FactureId = ((dynamic)factureComboBox.SelectedItem).Value;
                relance.Type = typeComboBox.SelectedItem.ToString();
                relance.DateRelance = dateRelancePicker.Value;
                relance.Niveau = (int)niveauNumericUpDown.Value;
                relance.Statut = statutComboBox.SelectedItem.ToString();
                relance.Contenu = contenuTextBox.Text.Trim();
                relance.UtilisateurId = _currentUserId;
                relance.DateProchaineRelance = dateProchaineRelancePicker.Value;

                // Enregistrer la relance
                if (_isEditMode)
                {
                    // Mettre à jour la relance existante
                    await _relanceService.UpdateAsync(relance, _currentUserId);
                    MessageBox.Show("La relance a été mise à jour avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Créer une nouvelle relance
                    await _relanceService.CreateAsync(relance, _currentUserId);
                    MessageBox.Show("La relance a été créée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement de la relance");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement de la relance : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
