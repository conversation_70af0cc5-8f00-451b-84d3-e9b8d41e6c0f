using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Relances
{
    public partial class ModeleRelanceListForm : Form
    {
        private readonly IModeleRelanceService _modeleRelanceService;
        private readonly IAuthenticationService _authenticationService;
        private readonly int _currentUserId;
        private List<ModeleRelance> _modeles = new List<ModeleRelance>();
        private DataTable _dataTable = new DataTable();

        public ModeleRelanceListForm(IModeleRelanceService modeleRelanceService, IAuthenticationService authenticationService, int currentUserId)
        {
            _modeleRelanceService = modeleRelanceService ?? throw new ArgumentNullException(nameof(modeleRelanceService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }

        private async void ModeleRelanceListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Vérifier les permissions
                bool canView = await _authenticationService.HasPermissionAsync(_currentUserId, "MODELES_RELANCE_VIEW");
                bool canAdd = await _authenticationService.HasPermissionAsync(_currentUserId, "MODELES_RELANCE_ADD");
                bool canEdit = await _authenticationService.HasPermissionAsync(_currentUserId, "MODELES_RELANCE_EDIT");
                bool canDelete = await _authenticationService.HasPermissionAsync(_currentUserId, "MODELES_RELANCE_DELETE");

                if (!canView)
                {
                    MessageBox.Show("Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.",
                        "Accès refusé", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return;
                }

                // Configurer les boutons en fonction des permissions
                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                var addButton = toolStrip?.Items.Find("addButton", false).FirstOrDefault() as ToolStripButton;
                var editButton = toolStrip?.Items.Find("editButton", false).FirstOrDefault() as ToolStripButton;
                var deleteButton = toolStrip?.Items.Find("deleteButton", false).FirstOrDefault() as ToolStripButton;

                if (addButton != null) addButton.Enabled = canAdd;
                if (editButton != null) editButton.Enabled = canEdit;
                if (deleteButton != null) deleteButton.Enabled = canDelete;

                // Initialiser la table de données
                InitializeDataTable();

                // Initialiser les filtres
                var typeFilterComboBox = this.Controls.Find("typeFilterComboBox", true).FirstOrDefault() as ComboBox;
                if (typeFilterComboBox != null)
                {
                    typeFilterComboBox.Items.Clear();
                    typeFilterComboBox.Items.Add("Tous les types");
                    typeFilterComboBox.Items.Add(TypeModeleRelance.Email);
                    typeFilterComboBox.Items.Add(TypeModeleRelance.Lettre);
                    typeFilterComboBox.Items.Add(TypeModeleRelance.SMS);
                    typeFilterComboBox.SelectedIndex = 0;
                }

                var niveauFilterComboBox = this.Controls.Find("niveauFilterComboBox", true).FirstOrDefault() as ComboBox;
                if (niveauFilterComboBox != null)
                {
                    niveauFilterComboBox.Items.Clear();
                    niveauFilterComboBox.Items.Add("Tous les niveaux");
                    niveauFilterComboBox.Items.Add("Niveau 1");
                    niveauFilterComboBox.Items.Add("Niveau 2");
                    niveauFilterComboBox.Items.Add("Niveau 3");
                    niveauFilterComboBox.SelectedIndex = 0;
                }

                // Charger les modèles de relance
                await LoadModelesAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire de liste des modèles de relance");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Nom", typeof(string));
            _dataTable.Columns.Add("Type", typeof(string));
            _dataTable.Columns.Add("Niveau", typeof(string));
            _dataTable.Columns.Add("Par défaut", typeof(bool));
            _dataTable.Columns.Add("Actif", typeof(bool));
            _dataTable.Columns.Add("Créé par", typeof(string));
            _dataTable.Columns.Add("Date création", typeof(DateTime));

            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (dataGridView.Columns["Date création"] != null)
                    dataGridView.Columns["Date création"].DefaultCellStyle.Format = "dd/MM/yyyy HH:mm";
            }
        }

        private async Task LoadModelesAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer les modèles de relance
                _modeles = (await _modeleRelanceService.GetAllAsync()).ToList();

                // Appliquer les filtres
                ApplyFilters();

                // Mettre à jour le compteur
                var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
                var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
                if (countLabel != null)
                {
                    countLabel.Text = $"Nombre de modèles : {_modeles.Count}";
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des modèles de relance");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des modèles de relance : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void ApplyFilters()
        {
            try
            {
                // Récupérer les valeurs des filtres
                var typeFilterComboBox = this.Controls.Find("typeFilterComboBox", true).FirstOrDefault() as ComboBox;
                var niveauFilterComboBox = this.Controls.Find("niveauFilterComboBox", true).FirstOrDefault() as ComboBox;
                var searchTextBox = this.Controls.Find("searchTextBox", true).FirstOrDefault() as TextBox;

                string typeFilter = typeFilterComboBox?.SelectedIndex > 0 ? typeFilterComboBox.SelectedItem.ToString() : null;
                int niveauFilter = niveauFilterComboBox?.SelectedIndex > 0 ? niveauFilterComboBox.SelectedIndex : 0;
                string searchText = searchTextBox?.Text?.Trim().ToLower() ?? string.Empty;

                // Filtrer les modèles
                var filteredModeles = _modeles.AsEnumerable();

                if (!string.IsNullOrEmpty(typeFilter))
                {
                    filteredModeles = filteredModeles.Where(m => m.Type == typeFilter);
                }

                if (niveauFilter > 0)
                {
                    filteredModeles = filteredModeles.Where(m => m.NiveauFermete == niveauFilter);
                }

                if (!string.IsNullOrEmpty(searchText))
                {
                    filteredModeles = filteredModeles.Where(m =>
                        m.Nom.ToLower().Contains(searchText) ||
                        m.Description.ToLower().Contains(searchText) ||
                        m.Objet?.ToLower().Contains(searchText) == true);
                }

                // Mettre à jour la table de données
                UpdateDataTable(filteredModeles.ToList());
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'application des filtres");
                MessageBox.Show($"Une erreur s'est produite lors de l'application des filtres : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateDataTable(List<ModeleRelance> modeles)
        {
            _dataTable.Clear();

            foreach (var modele in modeles)
            {
                _dataTable.Rows.Add(
                    modele.Id,
                    modele.Nom,
                    modele.Type,
                    $"Niveau {modele.NiveauFermete}",
                    modele.EstParDefaut,
                    modele.EstActif,
                    modele.UtilisateurId, // À remplacer par le nom de l'utilisateur
                    modele.DateCreation
                );
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            // Réinitialiser les filtres
            var typeFilterComboBox = this.Controls.Find("typeFilterComboBox", true).FirstOrDefault() as ComboBox;
            var niveauFilterComboBox = this.Controls.Find("niveauFilterComboBox", true).FirstOrDefault() as ComboBox;
            var searchTextBox = this.Controls.Find("searchTextBox", true).FirstOrDefault() as TextBox;

            if (typeFilterComboBox != null) typeFilterComboBox.SelectedIndex = 0;
            if (niveauFilterComboBox != null) niveauFilterComboBox.SelectedIndex = 0;
            if (searchTextBox != null) searchTextBox.Text = string.Empty;

            // Recharger les modèles de relance
            await LoadModelesAsync();
        }

        private void ApplyFilterButton_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                ApplyFilters();
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir le formulaire d'ajout de modèle de relance
                using (var form = new ModeleRelanceEditForm(_modeleRelanceService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les modèles de relance
                        LoadModelesAsync().Wait();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout de modèle de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer le modèle de relance sélectionné
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int modeleId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    EditModele(modeleId);
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un modèle de relance à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de modification de modèle de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int modeleId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    EditModele(modeleId);
                }
            }
        }

        private async void EditModele(int modeleId)
        {
            try
            {
                // Récupérer le modèle de relance
                var modele = await _modeleRelanceService.GetByIdAsync(modeleId);
                if (modele == null)
                {
                    MessageBox.Show("Le modèle de relance demandé n'existe pas ou a été supprimé.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Ouvrir le formulaire de modification de modèle de relance
                using (var form = new ModeleRelanceEditForm(_modeleRelanceService, _currentUserId, modele))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les modèles de relance
                        await LoadModelesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de modification de modèle de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer le modèle de relance sélectionné
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int modeleId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string modeleName = dataGridView.SelectedRows[0].Cells["Nom"].Value.ToString();

                    // Demander confirmation
                    var result = MessageBox.Show($"Êtes-vous sûr de vouloir supprimer le modèle de relance '{modeleName}' ?\n\nCette action est irréversible.",
                        "Confirmation de suppression", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        // Supprimer le modèle de relance
                        bool success = await _modeleRelanceService.DeleteAsync(modeleId, _currentUserId);
                        if (success)
                        {
                            // Recharger les modèles de relance
                            await LoadModelesAsync();
                            MessageBox.Show("Le modèle de relance a été supprimé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("Une erreur s'est produite lors de la suppression du modèle de relance.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un modèle de relance à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression d'un modèle de relance");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SetDefaultButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer le modèle de relance sélectionné
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int modeleId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string modeleName = dataGridView.SelectedRows[0].Cells["Nom"].Value.ToString();

                    // Demander confirmation
                    var result = MessageBox.Show($"Voulez-vous définir le modèle '{modeleName}' comme modèle par défaut pour son type et niveau de fermeté ?",
                        "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Définir le modèle de relance comme modèle par défaut
                        bool success = await _modeleRelanceService.SetAsDefaultAsync(modeleId, _currentUserId);
                        if (success)
                        {
                            // Recharger les modèles de relance
                            await LoadModelesAsync();
                            MessageBox.Show("Le modèle de relance a été défini comme modèle par défaut avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("Une erreur s'est produite lors de la définition du modèle de relance comme modèle par défaut.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un modèle de relance à définir comme modèle par défaut.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la définition d'un modèle de relance comme modèle par défaut");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
