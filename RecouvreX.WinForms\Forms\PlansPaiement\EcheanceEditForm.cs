using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.ComponentModel;

namespace RecouvreX.WinForms.Forms.PlansPaiement
{
    public partial class EcheanceEditForm : Form
    {
        private readonly IPlanPaiementService _planPaiementService;
        private readonly EcheancePaiement _echeance;
        private readonly int _userId;
        private TableLayoutPanel mainPanel;
        private NumericUpDown montantPrevuNumeric;
        private Label titleLabel;
        private NumericUpDown numeroOrdreNumeric;
        private DateTimePicker dateEcheancePicker;
        private TableLayoutPanel buttonsPanel;
        private Button saveButton;
        private Button cancelButton;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();

        public EcheanceEditForm(
            IPlanPaiementService planPaiementService,
            EcheancePaiement echeance,
            int userId)
        {
            _planPaiementService = planPaiementService ?? throw new ArgumentNullException(nameof(planPaiementService));
            _echeance = echeance ?? throw new ArgumentNullException(nameof(echeance));
            _userId = userId;

            InitializeComponent();
            InitializeData();
        }

        private void InitializeComponent()
        {
            mainPanel = new TableLayoutPanel();
            titleLabel = new Label();
            numeroOrdreNumeric = new NumericUpDown();
            dateEcheancePicker = new DateTimePicker();
            montantPrevuNumeric = new NumericUpDown();
            buttonsPanel = new TableLayoutPanel();
            saveButton = new Button();
            cancelButton = new Button();
            mainPanel.SuspendLayout();
            ((ISupportInitialize)numeroOrdreNumeric).BeginInit();
            ((ISupportInitialize)montantPrevuNumeric).BeginInit();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 79F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 105F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 89F));
            mainPanel.Controls.Add(montantPrevuNumeric, 1, 3);
            mainPanel.Controls.Add(titleLabel, 1, 0);
            mainPanel.Controls.Add(numeroOrdreNumeric, 1, 2);
            mainPanel.Controls.Add(dateEcheancePicker, 1, 1);
            mainPanel.Location = new Point(0, 0);
            mainPanel.Name = "mainPanel";
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 32F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 43F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 18F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            mainPanel.Size = new Size(372, 215);
            mainPanel.TabIndex = 0;
            // 
            // titleLabel
            // 
            mainPanel.SetColumnSpan(titleLabel, 2);
            titleLabel.Location = new Point(82, 0);
            titleLabel.Name = "titleLabel";
            titleLabel.Size = new Size(287, 32);
            titleLabel.TabIndex = 0;
            // 
            // numeroOrdreNumeric
            // 
            numeroOrdreNumeric.Location = new Point(82, 70);
            numeroOrdreNumeric.Name = "numeroOrdreNumeric";
            numeroOrdreNumeric.Size = new Size(99, 23);
            numeroOrdreNumeric.TabIndex = 2;
            // 
            // dateEcheancePicker
            // 
            dateEcheancePicker.Location = new Point(82, 35);
            dateEcheancePicker.Name = "dateEcheancePicker";
            dateEcheancePicker.Size = new Size(99, 23);
            dateEcheancePicker.TabIndex = 4;
            // 
            // montantPrevuNumeric
            // 
            montantPrevuNumeric.Location = new Point(82, 113);
            montantPrevuNumeric.Name = "montantPrevuNumeric";
            montantPrevuNumeric.Size = new Size(99, 23);
            montantPrevuNumeric.TabIndex = 6;
            montantPrevuNumeric.Validating += MontantPrevuNumeric_Validating;
            // 
            // buttonsPanel
            // 
            buttonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 107F));
            buttonsPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 8F));
            buttonsPanel.Controls.Add(saveButton, 0, 0);
            buttonsPanel.Controls.Add(cancelButton, 1, 0);
            buttonsPanel.Location = new Point(173, 221);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            buttonsPanel.Size = new Size(199, 42);
            buttonsPanel.TabIndex = 7;
            // 
            // saveButton
            // 
            saveButton.Location = new Point(3, 3);
            saveButton.Name = "saveButton";
            saveButton.Size = new Size(75, 23);
            saveButton.TabIndex = 0;
            saveButton.Click += SaveButton_Click;
            // 
            // cancelButton
            // 
            cancelButton.Location = new Point(110, 3);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(75, 23);
            cancelButton.TabIndex = 1;
            cancelButton.Click += CancelButton_Click;
            // 
            // EcheanceEditForm
            // 
            ClientSize = new Size(384, 261);
            Controls.Add(mainPanel);
            Controls.Add(buttonsPanel);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "EcheanceEditForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "Modifier l'échéance";
            mainPanel.ResumeLayout(false);
            ((ISupportInitialize)numeroOrdreNumeric).EndInit();
            ((ISupportInitialize)montantPrevuNumeric).EndInit();
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
        }

        private void InitializeData()
        {
            // Remplir les champs avec les données de l'échéance
            var numeroOrdreNumeric = Controls.Find("numeroOrdreNumeric", true).FirstOrDefault() as NumericUpDown;
            if (numeroOrdreNumeric != null)
            {
                numeroOrdreNumeric.Value = _echeance.NumeroOrdre;
            }

            var dateEcheancePicker = Controls.Find("dateEcheancePicker", true).FirstOrDefault() as DateTimePicker;
            if (dateEcheancePicker != null)
            {
                dateEcheancePicker.Value = _echeance.DateEcheance;
            }

            var montantPrevuNumeric = Controls.Find("montantPrevuNumeric", true).FirstOrDefault() as NumericUpDown;
            if (montantPrevuNumeric != null)
            {
                montantPrevuNumeric.Value = _echeance.MontantPrevu;
            }
        }

        private void MontantPrevuNumeric_Validating(object sender, CancelEventArgs e)
        {
            var montantPrevuNumeric = sender as NumericUpDown;
            if (montantPrevuNumeric == null)
                return;

            if (montantPrevuNumeric.Value <= 0)
            {
                _errorProvider.SetError(montantPrevuNumeric, "Le montant prévu doit être supérieur à zéro.");
                e.Cancel = true;
            }
            else
            {
                _errorProvider.SetError(montantPrevuNumeric, "");
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            if (!ValidateChildren())
                return;

            try
            {
                var dateEcheancePicker = Controls.Find("dateEcheancePicker", true).FirstOrDefault() as DateTimePicker;
                var montantPrevuNumeric = Controls.Find("montantPrevuNumeric", true).FirstOrDefault() as NumericUpDown;

                if (dateEcheancePicker == null || montantPrevuNumeric == null)
                    return;

                // Mettre à jour l'échéance
                _echeance.DateEcheance = dateEcheancePicker.Value;
                _echeance.MontantPrevu = montantPrevuNumeric.Value;

                // Enregistrer les modifications
                var success = await _planPaiementService.UpdateEcheanceAsync(_echeance, _userId);

                if (success)
                {
                    MessageBox.Show("L'échéance a été modifiée avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    DialogResult = DialogResult.OK;
                    Close();
                }
                else
                {
                    MessageBox.Show("Erreur lors de la modification de l'échéance.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la modification de l'échéance");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
}
