using RecouvreX.Models;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service d'authentification
    /// </summary>
    public interface IAuthenticationService
    {
        /// <summary>
        /// Authentifie un utilisateur avec son nom d'utilisateur et son mot de passe
        /// </summary>
        /// <param name="nomUtilisateur">Nom d'utilisateur</param>
        /// <param name="motDePasse">Mot de passe en clair</param>
        /// <returns>Utilisateur authentifié ou null si l'authentification échoue</returns>
        Task<Utilisateur> AuthenticateAsync(string nomUtilisateur, string motDePasse);

        /// <summary>
        /// Vérifie si un utilisateur possède une permission spécifique
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="permissionCode">Code de la permission</param>
        /// <returns>True si l'utilisateur possède la permission, sinon False</returns>
        Task<bool> HasPermissionAsync(int utilisateurId, string permissionCode);

        /// <summary>
        /// Déconnecte un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>True si la déconnexion a réussi, sinon False</returns>
        Task<bool> LogoutAsync(int utilisateurId);

        /// <summary>
        /// Change le mot de passe d'un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="ancienMotDePasse">Ancien mot de passe en clair</param>
        /// <param name="nouveauMotDePasse">Nouveau mot de passe en clair</param>
        /// <returns>True si le changement a réussi, sinon False</returns>
        Task<bool> ChangePasswordAsync(int utilisateurId, string ancienMotDePasse, string nouveauMotDePasse);

        /// <summary>
        /// Réinitialise le mot de passe d'un utilisateur (par un administrateur)
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="nouveauMotDePasse">Nouveau mot de passe en clair</param>
        /// <param name="administrateurId">Identifiant de l'administrateur qui effectue l'action</param>
        /// <returns>True si la réinitialisation a réussi, sinon False</returns>
        Task<bool> ResetPasswordAsync(int utilisateurId, string nouveauMotDePasse, int administrateurId);

        /// <summary>
        /// Verrouille ou déverrouille un compte utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="estVerrouille">True pour verrouiller, False pour déverrouiller</param>
        /// <param name="administrateurId">Identifiant de l'administrateur qui effectue l'action</param>
        /// <returns>True si l'opération a réussi, sinon False</returns>
        Task<bool> SetAccountLockedStatusAsync(int utilisateurId, bool estVerrouille, int administrateurId);
    }
}
