using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des planifications de relance
    /// </summary>
    public interface IPlanificationRelanceService
    {
        /// <summary>
        /// Récupère toutes les planifications de relance
        /// </summary>
        /// <returns>Liste des planifications de relance</returns>
        Task<IEnumerable<PlanificationRelance>> GetAllAsync();

        /// <summary>
        /// Récupère une planification de relance par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance</param>
        /// <returns>Planification de relance trouvée ou null</returns>
        Task<PlanificationRelance> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les planifications de relance par facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des planifications de relance pour la facture spécifiée</returns>
        Task<IEnumerable<PlanificationRelance>> GetByFactureIdAsync(int factureId);

        /// <summary>
        /// Récupère les planifications de relance par statut
        /// </summary>
        /// <param name="statut">Statut des planifications</param>
        /// <returns>Liste des planifications de relance avec le statut spécifié</returns>
        Task<IEnumerable<PlanificationRelance>> GetByStatutAsync(string statut);

        /// <summary>
        /// Récupère les planifications de relance prévues pour une date
        /// </summary>
        /// <param name="date">Date prévue</param>
        /// <returns>Liste des planifications de relance prévues pour la date spécifiée</returns>
        Task<IEnumerable<PlanificationRelance>> GetPlannedForDateAsync(DateTime date);

        /// <summary>
        /// Récupère les planifications de relance en attente de validation
        /// </summary>
        /// <returns>Liste des planifications de relance en attente de validation</returns>
        Task<IEnumerable<PlanificationRelance>> GetPendingValidationAsync();

        /// <summary>
        /// Crée une nouvelle planification de relance
        /// </summary>
        /// <param name="planificationRelance">Planification de relance à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Planification de relance créée avec son identifiant généré</returns>
        Task<PlanificationRelance> CreateAsync(PlanificationRelance planificationRelance, int creePar);

        /// <summary>
        /// Met à jour une planification de relance existante
        /// </summary>
        /// <param name="planificationRelance">Planification de relance à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Planification de relance mise à jour</returns>
        Task<PlanificationRelance> UpdateAsync(PlanificationRelance planificationRelance, int modifiePar);

        /// <summary>
        /// Supprime une planification de relance
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Valide une planification de relance
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance</param>
        /// <param name="validePar">Identifiant de l'utilisateur qui effectue la validation</param>
        /// <param name="commentaire">Commentaire de validation (optionnel)</param>
        /// <returns>True si la validation a réussi, sinon False</returns>
        Task<bool> ValidateAsync(int id, int validePar, string commentaire = null);

        /// <summary>
        /// Annule une planification de relance
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance</param>
        /// <param name="annulePar">Identifiant de l'utilisateur qui effectue l'annulation</param>
        /// <param name="commentaire">Commentaire d'annulation (optionnel)</param>
        /// <returns>True si l'annulation a réussi, sinon False</returns>
        Task<bool> CancelAsync(int id, int annulePar, string commentaire = null);

        /// <summary>
        /// Marque une planification de relance comme envoyée
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance</param>
        /// <param name="envoyePar">Identifiant de l'utilisateur qui effectue l'envoi</param>
        /// <param name="dateEnvoi">Date d'envoi (par défaut, la date actuelle)</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> MarkAsSentAsync(int id, int envoyePar, DateTime? dateEnvoi = null);

        /// <summary>
        /// Exécute les planifications de relance prévues pour aujourd'hui
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre de relances envoyées</returns>
        Task<int> ExecutePlannedRemindersAsync(int utilisateurId);
    }
}
