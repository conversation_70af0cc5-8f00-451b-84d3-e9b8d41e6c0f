namespace RecouvreX.WinForms.Forms.Administration
{
    partial class RolePermissionsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            mainPanel = new TableLayoutPanel();
            toolStrip = new ToolStrip();
            selectAllButton = new ToolStripButton();
            deselectAllButton = new ToolStripButton();
            moduleLabel = new ToolStripLabel();
            moduleComboBox = new ToolStripComboBox();
            permissionsTreeView = new TreeView();
            buttonsPanel = new FlowLayoutPanel();
            cancelButton = new Button();
            saveButton = new Button();
            mainPanel.SuspendLayout();
            toolStrip.SuspendLayout();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.ColumnCount = 1;
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            mainPanel.Controls.Add(toolStrip, 0, 0);
            mainPanel.Controls.Add(permissionsTreeView, 0, 1);
            mainPanel.Controls.Add(buttonsPanel, 0, 2);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Location = new Point(0, 0);
            mainPanel.Name = "mainPanel";
            mainPanel.Padding = new Padding(10);
            mainPanel.RowCount = 3;
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 42F));
            mainPanel.Size = new Size(700, 500);
            mainPanel.TabIndex = 0;
            // 
            // toolStrip
            // 
            toolStrip.GripStyle = ToolStripGripStyle.Hidden;
            toolStrip.Items.AddRange(new ToolStripItem[] { selectAllButton, deselectAllButton, moduleLabel, moduleComboBox });
            toolStrip.Location = new Point(10, 10);
            toolStrip.Name = "toolStrip";
            toolStrip.Size = new Size(680, 25);
            toolStrip.TabIndex = 0;
            // 
            // selectAllButton
            // 
            selectAllButton.DisplayStyle = ToolStripItemDisplayStyle.Text;
            selectAllButton.Name = "selectAllButton";
            selectAllButton.Size = new Size(101, 22);
            selectAllButton.Text = "Tout sélectionner";
            selectAllButton.Click += SelectAllButton_Click;
            // 
            // deselectAllButton
            // 
            deselectAllButton.DisplayStyle = ToolStripItemDisplayStyle.Text;
            deselectAllButton.Name = "deselectAllButton";
            deselectAllButton.Size = new Size(114, 22);
            deselectAllButton.Text = "Tout désélectionner";
            deselectAllButton.Click += DeselectAllButton_Click;
            // 
            // moduleLabel
            // 
            moduleLabel.Name = "moduleLabel";
            moduleLabel.Size = new Size(54, 22);
            moduleLabel.Text = "Module :";
            // 
            // moduleComboBox
            // 
            moduleComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            moduleComboBox.Name = "moduleComboBox";
            moduleComboBox.Size = new Size(121, 25);
            moduleComboBox.SelectedIndexChanged += ModuleComboBox_SelectedIndexChanged;
            // 
            // permissionsTreeView
            // 
            permissionsTreeView.CheckBoxes = true;
            permissionsTreeView.Dock = DockStyle.Fill;
            permissionsTreeView.Location = new Point(13, 53);
            permissionsTreeView.Name = "permissionsTreeView";
            permissionsTreeView.Size = new Size(674, 392);
            permissionsTreeView.TabIndex = 1;
            permissionsTreeView.AfterCheck += PermissionsTreeView_AfterCheck;
            // 
            // buttonsPanel
            // 
            buttonsPanel.Anchor = AnchorStyles.Right;
            buttonsPanel.Controls.Add(cancelButton);
            buttonsPanel.Controls.Add(saveButton);
            buttonsPanel.FlowDirection = FlowDirection.RightToLeft;
            buttonsPanel.Location = new Point(458, 451);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.Size = new Size(229, 36);
            buttonsPanel.TabIndex = 2;
            // 
            // cancelButton
            // 
            cancelButton.Location = new Point(126, 3);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(100, 30);
            cancelButton.TabIndex = 0;
            cancelButton.Text = "Annuler";
            cancelButton.Click += CancelButton_Click;
            // 
            // saveButton
            // 
            saveButton.Location = new Point(20, 3);
            saveButton.Name = "saveButton";
            saveButton.Size = new Size(100, 30);
            saveButton.TabIndex = 1;
            saveButton.Text = "Enregistrer";
            saveButton.Click += SaveButton_Click;
            // 
            // RolePermissionsForm
            // 
            ClientSize = new Size(700, 500);
            Controls.Add(mainPanel);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "RolePermissionsForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Permissions du rôle";
            Load += RolePermissionsForm_Load;
            mainPanel.ResumeLayout(false);
            mainPanel.PerformLayout();
            toolStrip.ResumeLayout(false);
            toolStrip.PerformLayout();
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private TableLayoutPanel mainPanel;
        private ToolStrip toolStrip;
        private ToolStripButton selectAllButton;
        private ToolStripButton deselectAllButton;
        private ToolStripLabel moduleLabel;
        private ToolStripComboBox moduleComboBox;
        private TreeView permissionsTreeView;
        private FlowLayoutPanel buttonsPanel;
        private Button cancelButton;
        private Button saveButton;
    }
}
