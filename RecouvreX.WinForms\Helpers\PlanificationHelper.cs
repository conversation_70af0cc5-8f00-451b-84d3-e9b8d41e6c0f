using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Helpers
{
    /// <summary>
    /// Classe utilitaire pour la gestion des planifications de relance
    /// </summary>
    public static class PlanificationHelper
    {
        /// <summary>
        /// Retourne la couleur associée à un statut de planification
        /// </summary>
        /// <param name="statut">Statut de la planification</param>
        /// <returns>Couleur associée au statut</returns>
        public static Color GetStatusColor(string statut)
        {
            switch (statut)
            {
                case StatutPlanificationRelance.Planifiee:
                    return Color.DodgerBlue;
                case StatutPlanificationRelance.EnAttenteValidation:
                    return Color.Orange;
                case StatutPlanificationRelance.Validee:
                    return Color.Green;
                case StatutPlanificationRelance.Envoyee:
                    return Color.DarkGreen;
                case StatutPlanificationRelance.Annulee:
                    return Color.Red;
                default:
                    return Color.Gray;
            }
        }

        /// <summary>
        /// Retourne l'icône associée à un statut de planification
        /// </summary>
        /// <param name="statut">Statut de la planification</param>
        /// <returns>Icône associée au statut</returns>
        public static Icon GetStatusIcon(string statut)
        {
            switch (statut)
            {
                case StatutPlanificationRelance.Planifiee:
                    return SystemIcons.Information;
                case StatutPlanificationRelance.EnAttenteValidation:
                    return SystemIcons.Question;
                case StatutPlanificationRelance.Validee:
                    return SystemIcons.Shield;
                case StatutPlanificationRelance.Envoyee:
                    return SystemIcons.Application;
                case StatutPlanificationRelance.Annulee:
                    return SystemIcons.Error;
                default:
                    return SystemIcons.WinLogo;
            }
        }

        /// <summary>
        /// Retourne la couleur associée à un niveau de relance
        /// </summary>
        /// <param name="niveau">Niveau de relance</param>
        /// <returns>Couleur associée au niveau</returns>
        public static Color GetLevelColor(int niveau)
        {
            switch (niveau)
            {
                case 1:
                    return Color.DodgerBlue;
                case 2:
                    return Color.Orange;
                case 3:
                    return Color.Red;
                default:
                    return Color.Gray;
            }
        }

        /// <summary>
        /// Retourne le texte descriptif d'un niveau de relance
        /// </summary>
        /// <param name="niveau">Niveau de relance</param>
        /// <returns>Texte descriptif du niveau</returns>
        public static string GetLevelDescription(int niveau)
        {
            switch (niveau)
            {
                case 1:
                    return "Niveau 1 (Courtois)";
                case 2:
                    return "Niveau 2 (Ferme)";
                case 3:
                    return "Niveau 3 (Mise en demeure)";
                default:
                    return $"Niveau {niveau}";
            }
        }

        /// <summary>
        /// Affiche un aperçu d'une planification de relance
        /// </summary>
        /// <param name="planification">Planification de relance</param>
        /// <param name="facture">Facture concernée</param>
        /// <param name="client">Client concerné</param>
        /// <param name="modele">Modèle de relance</param>
        /// <param name="contenu">Contenu de la relance avec les variables remplacées</param>
        public static void ShowPreview(PlanificationRelance planification, Facture facture, Client client, ModeleRelance modele, string contenu)
        {
            // Créer un formulaire pour l'aperçu
            var form = new Form
            {
                Text = $"Aperçu de la relance - {client.RaisonSociale} - Facture {facture.Numero}",
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterScreen,
                MinimizeBox = false,
                MaximizeBox = true,
                ShowIcon = false,
                ShowInTaskbar = false
            };

            // Créer un TableLayoutPanel pour organiser les contrôles
            var tableLayoutPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3,
                Padding = new Padding(10)
            };
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 100));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            form.Controls.Add(tableLayoutPanel);

            // Panneau d'informations
            var infoPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle
            };
            tableLayoutPanel.Controls.Add(infoPanel, 0, 0);

            // Ajouter les informations
            var infoTable = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 5,
                Padding = new Padding(5)
            };
            infoTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30));
            infoTable.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70));
            infoPanel.Controls.Add(infoTable);

            // Client
            infoTable.Controls.Add(new Label { Text = "Client :", Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold), TextAlign = ContentAlignment.MiddleRight, Dock = DockStyle.Fill }, 0, 0);
            infoTable.Controls.Add(new Label { Text = client.RaisonSociale, TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 1, 0);

            // Facture
            infoTable.Controls.Add(new Label { Text = "Facture :", Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold), TextAlign = ContentAlignment.MiddleRight, Dock = DockStyle.Fill }, 0, 1);
            infoTable.Controls.Add(new Label { Text = $"{facture.Numero} - {facture.MontantRestant:C}", TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 1, 1);

            // Date d'échéance
            infoTable.Controls.Add(new Label { Text = "Date d'échéance :", Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold), TextAlign = ContentAlignment.MiddleRight, Dock = DockStyle.Fill }, 0, 2);
            infoTable.Controls.Add(new Label { Text = facture.DateEcheance.ToString("dd/MM/yyyy"), TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 1, 2);

            // Niveau de relance
            infoTable.Controls.Add(new Label { Text = "Niveau de relance :", Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold), TextAlign = ContentAlignment.MiddleRight, Dock = DockStyle.Fill }, 0, 3);
            infoTable.Controls.Add(new Label { Text = GetLevelDescription(planification.NiveauRelance), ForeColor = GetLevelColor(planification.NiveauRelance), TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 1, 3);

            // Date prévue
            infoTable.Controls.Add(new Label { Text = "Date prévue :", Font = new Font(SystemFonts.DefaultFont, FontStyle.Bold), TextAlign = ContentAlignment.MiddleRight, Dock = DockStyle.Fill }, 0, 4);
            infoTable.Controls.Add(new Label { Text = planification.DatePrevue.ToString("dd/MM/yyyy"), TextAlign = ContentAlignment.MiddleLeft, Dock = DockStyle.Fill }, 1, 4);

            // Contenu de la relance
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle
            };
            tableLayoutPanel.Controls.Add(contentPanel, 0, 1);

            // Ajouter le contenu
            var richTextBox = new RichTextBox
            {
                Dock = DockStyle.Fill,
                ReadOnly = true,
                BackColor = Color.White,
                Font = new Font("Arial", 11),
                Text = contenu
            };
            contentPanel.Controls.Add(richTextBox);

            // Boutons
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft
            };
            tableLayoutPanel.Controls.Add(buttonPanel, 0, 2);

            // Bouton Fermer
            var closeButton = new Button
            {
                Text = "Fermer",
                Width = 100,
                Height = 30
            };
            closeButton.Click += (sender, e) => form.Close();
            buttonPanel.Controls.Add(closeButton);

            // Afficher le formulaire
            form.ShowDialog();
        }
    }
}
