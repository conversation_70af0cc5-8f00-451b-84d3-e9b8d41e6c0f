using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.DataAccess.Repositories;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des contacts
    /// </summary>
    public class ContactService : IContactService
    {
        private readonly IContactRepository _contactRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="contactRepository">Repository des contacts</param>
        /// <param name="clientRepository">Repository des clients</param>
        /// <param name="utilisateurRepository">Repository des utilisateurs</param>
        /// <param name="journalAuditRepository">Repository du journal d'audit</param>
        public ContactService(
            IContactRepository contactRepository,
            IClientRepository clientRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _contactRepository = contactRepository ?? throw new ArgumentNullException(nameof(contactRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère tous les contacts
        /// </summary>
        /// <returns>Liste des contacts</returns>
        public async Task<IEnumerable<Contact>> GetAllAsync()
        {
            return await _contactRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère un contact par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du contact</param>
        /// <returns>Contact trouvé ou null</returns>
        public async Task<Contact> GetByIdAsync(int id)
        {
            return await _contactRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère tous les contacts d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des contacts du client</returns>
        public async Task<IEnumerable<Contact>> GetByClientIdAsync(int clientId)
        {
            return await _contactRepository.GetByClientIdAsync(clientId);
        }

        /// <summary>
        /// Récupère le contact principal d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Contact principal du client ou null si aucun contact principal n'est défini</returns>
        public async Task<Contact> GetPrincipalContactAsync(int clientId)
        {
            return await _contactRepository.GetPrincipalContactAsync(clientId);
        }

        /// <summary>
        /// Ajoute un nouveau contact
        /// </summary>
        /// <param name="contact">Contact à ajouter</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact ajouté avec son identifiant généré</returns>
        public async Task<Contact> AddAsync(Contact contact, int creePar)
        {
            if (contact == null || contact.ClientId <= 0 || creePar <= 0)
                throw new ArgumentException("Paramètres invalides pour l'ajout d'un contact");

            // Vérifier si le client existe
            var client = await _clientRepository.GetByIdAsync(contact.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {contact.ClientId} n'existe pas");

            // Ajouter le contact
            var addedContact = await _contactRepository.AddAsync(contact, creePar);

            // Journaliser l'ajout du contact
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = creePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(creePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Creation,
                TypeEntite = "Contact",
                EntiteId = addedContact.Id,
                Description = $"Ajout du contact {addedContact.Nom} {addedContact.Prenom} pour le client {client.RaisonSociale}",
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(addedContact)
            });

            return addedContact;
        }

        /// <summary>
        /// Met à jour un contact existant
        /// </summary>
        /// <param name="contact">Contact à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact mis à jour</returns>
        public async Task<Contact> UpdateAsync(Contact contact, int modifiePar)
        {
            if (contact == null || contact.Id <= 0 || contact.ClientId <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la mise à jour d'un contact");

            // Vérifier si le client existe
            var client = await _clientRepository.GetByIdAsync(contact.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {contact.ClientId} n'existe pas");

            // Récupérer le contact existant
            var existingContact = await _contactRepository.GetByIdAsync(contact.Id);
            if (existingContact == null)
                throw new InvalidOperationException($"Le contact avec l'ID {contact.Id} n'existe pas");

            // Journaliser la modification
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Contact",
                EntiteId = contact.Id,
                Description = $"Modification du contact {contact.Nom} {contact.Prenom} pour le client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(existingContact),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(contact)
            });

            // Mettre à jour le contact
            await _contactRepository.UpdateAsync(contact, modifiePar);
            return contact;
        }

        /// <summary>
        /// Supprime un contact
        /// </summary>
        /// <param name="id">Identifiant du contact à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0 || supprimePar <= 0)
                return false;

            // Récupérer le contact à supprimer
            var contact = await _contactRepository.GetByIdAsync(id);
            if (contact == null)
                return false;

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(contact.ClientId);
            if (client == null)
                return false;

            // Journaliser la suppression
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = supprimePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(supprimePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Suppression,
                TypeEntite = "Contact",
                EntiteId = id,
                Description = $"Suppression du contact {contact.Nom} {contact.Prenom} pour le client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(contact)
            });

            // Supprimer le contact
            return await _contactRepository.DeleteAsync(id, supprimePar);
        }
    }
}
