using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Communications
{
    partial class WhatsAppForm
    {
        /// <summary>
        /// Variable nécessaire au concepteur.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private TableLayoutPanel tableLayoutPanel;
        private Label factureLabel;
        private Label factureValueLabel;
        private Label clientLabel;
        private Label clientValueLabel;
        private Label destinataireLabel;
        private ComboBox destinataireComboBox;
        private Label niveauLabel;
        private ComboBox niveauComboBox;
        private Label messageLabel;
        private Button previewButton;
        private Label suiviLabel;
        private CheckBox suiviNecessaireCheckBox;
        private Panel buttonsPanel;
        private Button cancelButton;
        private Button sendButton;
        private Label noteLabel;

        /// <summary>
        /// Nettoyage des ressources utilisées.
        /// </summary>
        /// <param name="disposing">true si les ressources managées doivent être supprimées ; sinon, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Code généré par le Concepteur Windows Form

        /// <summary>
        /// Méthode requise pour la prise en charge du concepteur - ne modifiez pas
        /// le contenu de cette méthode avec l'éditeur de code.
        /// </summary>
        private void InitializeComponent()
        {
            mainPanel = new Panel();
            tableLayoutPanel = new TableLayoutPanel();
            factureLabel = new Label();
            factureValueLabel = new Label();
            clientLabel = new Label();
            clientValueLabel = new Label();
            destinataireLabel = new Label();
            destinataireComboBox = new ComboBox();
            niveauLabel = new Label();
            niveauComboBox = new ComboBox();
            messageLabel = new Label();
            previewButton = new Button();
            suiviLabel = new Label();
            suiviNecessaireCheckBox = new CheckBox();
            buttonsPanel = new Panel();
            cancelButton = new Button();
            sendButton = new Button();
            noteLabel = new Label();
            mainPanel.SuspendLayout();
            tableLayoutPanel.SuspendLayout();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.Controls.Add(tableLayoutPanel);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Location = new Point(0, 0);
            mainPanel.Name = "mainPanel";
            mainPanel.Padding = new Padding(10);
            mainPanel.Size = new Size(600, 450);
            mainPanel.TabIndex = 0;
            // 
            // tableLayoutPanel
            // 
            tableLayoutPanel.ColumnCount = 2;
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            tableLayoutPanel.Controls.Add(factureLabel, 0, 0);
            tableLayoutPanel.Controls.Add(factureValueLabel, 1, 0);
            tableLayoutPanel.Controls.Add(clientLabel, 0, 1);
            tableLayoutPanel.Controls.Add(clientValueLabel, 1, 1);
            tableLayoutPanel.Controls.Add(destinataireLabel, 0, 2);
            tableLayoutPanel.Controls.Add(destinataireComboBox, 1, 2);
            tableLayoutPanel.Controls.Add(niveauLabel, 0, 3);
            tableLayoutPanel.Controls.Add(niveauComboBox, 1, 3);
            tableLayoutPanel.Controls.Add(messageLabel, 0, 4);
            tableLayoutPanel.Controls.Add(previewButton, 1, 4);
            tableLayoutPanel.Controls.Add(suiviLabel, 0, 5);
            tableLayoutPanel.Controls.Add(suiviNecessaireCheckBox, 1, 5);
            tableLayoutPanel.Dock = DockStyle.Fill;
            tableLayoutPanel.Location = new Point(10, 10);
            tableLayoutPanel.Name = "tableLayoutPanel";
            tableLayoutPanel.RowCount = 6;
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel.Size = new Size(580, 430);
            tableLayoutPanel.TabIndex = 0;
            // 
            // factureLabel
            // 
            factureLabel.Dock = DockStyle.Fill;
            factureLabel.Location = new Point(3, 0);
            factureLabel.Name = "factureLabel";
            factureLabel.Size = new Size(168, 40);
            factureLabel.TabIndex = 0;
            factureLabel.Text = "Facture :";
            factureLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // factureValueLabel
            // 
            factureValueLabel.Dock = DockStyle.Fill;
            factureValueLabel.Location = new Point(177, 0);
            factureValueLabel.Name = "factureValueLabel";
            factureValueLabel.Size = new Size(400, 40);
            factureValueLabel.TabIndex = 1;
            factureValueLabel.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // clientLabel
            // 
            clientLabel.Dock = DockStyle.Fill;
            clientLabel.Location = new Point(3, 40);
            clientLabel.Name = "clientLabel";
            clientLabel.Size = new Size(168, 40);
            clientLabel.TabIndex = 2;
            clientLabel.Text = "Client :";
            clientLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // clientValueLabel
            // 
            clientValueLabel.Dock = DockStyle.Fill;
            clientValueLabel.Location = new Point(177, 40);
            clientValueLabel.Name = "clientValueLabel";
            clientValueLabel.Size = new Size(400, 40);
            clientValueLabel.TabIndex = 3;
            clientValueLabel.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // destinataireLabel
            // 
            destinataireLabel.Dock = DockStyle.Fill;
            destinataireLabel.Location = new Point(3, 80);
            destinataireLabel.Name = "destinataireLabel";
            destinataireLabel.Size = new Size(168, 40);
            destinataireLabel.TabIndex = 4;
            destinataireLabel.Text = "Destinataire * :";
            destinataireLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // destinataireComboBox
            // 
            destinataireComboBox.Dock = DockStyle.Fill;
            destinataireComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            destinataireComboBox.Location = new Point(177, 83);
            destinataireComboBox.Name = "destinataireComboBox";
            destinataireComboBox.Size = new Size(400, 23);
            destinataireComboBox.TabIndex = 5;
            destinataireComboBox.Validating += DestinataireComboBox_Validating;
            // 
            // niveauLabel
            // 
            niveauLabel.Dock = DockStyle.Fill;
            niveauLabel.Location = new Point(3, 120);
            niveauLabel.Name = "niveauLabel";
            niveauLabel.Size = new Size(168, 40);
            niveauLabel.TabIndex = 6;
            niveauLabel.Text = "Niveau de relance :";
            niveauLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // niveauComboBox
            // 
            niveauComboBox.Dock = DockStyle.Fill;
            niveauComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            niveauComboBox.Items.AddRange(new object[] { "Niveau 1 - Rappel", "Niveau 2 - Deuxième rappel", "Niveau 3 - Mise en demeure" });
            niveauComboBox.Location = new Point(177, 123);
            niveauComboBox.Name = "niveauComboBox";
            niveauComboBox.Size = new Size(400, 23);
            niveauComboBox.TabIndex = 7;
            // 
            // messageLabel
            // 
            messageLabel.Dock = DockStyle.Fill;
            messageLabel.Location = new Point(3, 160);
            messageLabel.Name = "messageLabel";
            messageLabel.Size = new Size(168, 40);
            messageLabel.TabIndex = 8;
            messageLabel.Text = "Aperçu du message :";
            messageLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // previewButton
            // 
            previewButton.Location = new Point(177, 163);
            previewButton.Name = "previewButton";
            previewButton.Size = new Size(100, 30);
            previewButton.TabIndex = 9;
            previewButton.Text = "Aperçu";
            previewButton.Click += PreviewButton_Click;
            // 
            // suiviLabel
            // 
            suiviLabel.Dock = DockStyle.Fill;
            suiviLabel.Location = new Point(3, 200);
            suiviLabel.Name = "suiviLabel";
            suiviLabel.Size = new Size(168, 230);
            suiviLabel.TabIndex = 10;
            suiviLabel.Text = "Suivi nécessaire :";
            suiviLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // suiviNecessaireCheckBox
            // 
            suiviNecessaireCheckBox.Dock = DockStyle.Fill;
            suiviNecessaireCheckBox.Location = new Point(177, 203);
            suiviNecessaireCheckBox.Name = "suiviNecessaireCheckBox";
            suiviNecessaireCheckBox.Size = new Size(400, 224);
            suiviNecessaireCheckBox.TabIndex = 11;
            suiviNecessaireCheckBox.Text = "Planifier un suivi";
            // 
            // buttonsPanel
            // 
            buttonsPanel.Controls.Add(cancelButton);
            buttonsPanel.Controls.Add(sendButton);
            buttonsPanel.Dock = DockStyle.Bottom;
            buttonsPanel.Location = new Point(0, 450);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.Padding = new Padding(10);
            buttonsPanel.Size = new Size(600, 50);
            buttonsPanel.TabIndex = 1;
            // 
            // cancelButton
            // 
            cancelButton.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            cancelButton.Location = new Point(684, 10);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(100, 30);
            cancelButton.TabIndex = 0;
            cancelButton.Text = "Annuler";
            cancelButton.Click += CancelButton_Click;
            // 
            // sendButton
            // 
            sendButton.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            sendButton.Location = new Point(684, 10);
            sendButton.Name = "sendButton";
            sendButton.Size = new Size(100, 30);
            sendButton.TabIndex = 1;
            sendButton.Text = "Envoyer";
            sendButton.Click += SendButton_Click;
            // 
            // noteLabel
            // 
            noteLabel.AutoSize = true;
            noteLabel.ForeColor = Color.Red;
            noteLabel.Location = new Point(10, 261);
            noteLabel.Name = "noteLabel";
            noteLabel.Size = new Size(124, 15);
            noteLabel.TabIndex = 2;
            noteLabel.Text = "* Champs obligatoires";
            // 
            // WhatsAppForm
            // 
            ClientSize = new Size(600, 500);
            Controls.Add(mainPanel);
            Controls.Add(buttonsPanel);
            Controls.Add(noteLabel);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "WhatsAppForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Envoyer un message WhatsApp";
            Load += WhatsAppForm_Load;
            mainPanel.ResumeLayout(false);
            tableLayoutPanel.ResumeLayout(false);
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion
    }
}
