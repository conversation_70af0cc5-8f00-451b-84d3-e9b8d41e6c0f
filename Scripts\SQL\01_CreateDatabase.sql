-- Script de création de la base de données RecouvreX
-- Ce script crée la base de données pour l'application de gestion de recouvrement des créances clients

USE master;
GO

-- Vérifier si la base de données existe déjà et la supprimer si nécessaire
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'RecouvreX')
BEGIN
    ALTER DATABASE RecouvreX SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE RecouvreX;
END
GO

-- Créer la base de données
CREATE DATABASE RecouvreX
COLLATE French_CI_AS;
GO

-- Utiliser la base de données
USE RecouvreX;
GO

-- Créer un schéma pour l'application
CREATE SCHEMA app;
GO

PRINT 'Base de données RecouvreX créée avec succès.';
GO
