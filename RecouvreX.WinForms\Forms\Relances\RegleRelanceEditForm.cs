using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Relances
{
    public partial class RegleRelanceEditForm : Form
    {
        private readonly IRegleRelanceService _regleRelanceService;
        private readonly IModeleRelanceService _modeleRelanceService;
        private readonly int _currentUserId;
        private readonly RegleRelance _regle;
        private readonly bool _isEditMode;
        private List<ModeleRelance> _modelesNiveau1;
        private List<ModeleRelance> _modelesNiveau2;
        private List<ModeleRelance> _modelesNiveau3;

        public RegleRelanceEditForm(IRegleRelanceService regleRelanceService, int currentUserId, RegleRelance regle = null)
        {
            _regleRelanceService = regleRelanceService ?? throw new ArgumentNullException(nameof(regleRelanceService));
            _currentUserId = currentUserId;
            _regle = regle ?? new RegleRelance { UtilisateurId = currentUserId, EstActive = true, Priorite = 1 };
            _isEditMode = regle != null;

            InitializeComponent();
        }

        private async void RegleRelanceEditForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Configurer le titre du formulaire
                this.Text = _isEditMode ? "Modifier une règle de relance" : "Ajouter une règle de relance";

                // Charger les modèles de relance
                await LoadModelesRelanceAsync();

                // Remplir les champs avec les valeurs de la règle
                if (_isEditMode)
                {
                    var nomTextBox = this.Controls.Find("nomTextBox", true)[0] as TextBox;
                    var descriptionTextBox = this.Controls.Find("descriptionTextBox", true)[0] as TextBox;
                    var typeClientComboBox = this.Controls.Find("typeClientComboBox", true)[0] as ComboBox;
                    var montantMinimumNumericUpDown = this.Controls.Find("montantMinimumNumericUpDown", true)[0] as NumericUpDown;
                    var joursApresPremiere = this.Controls.Find("joursApresPremiere", true)[0] as NumericUpDown;
                    var joursEntrePremiereDeuxieme = this.Controls.Find("joursEntrePremiereDeuxieme", true)[0] as NumericUpDown;
                    var joursEntreDeuxiemeTroisieme = this.Controls.Find("joursEntreDeuxiemeTroisieme", true)[0] as NumericUpDown;
                    var modeleNiveau1ComboBox = this.Controls.Find("modeleNiveau1ComboBox", true)[0] as ComboBox;
                    var modeleNiveau2ComboBox = this.Controls.Find("modeleNiveau2ComboBox", true)[0] as ComboBox;
                    var modeleNiveau3ComboBox = this.Controls.Find("modeleNiveau3ComboBox", true)[0] as ComboBox;
                    var prioriteNumericUpDown = this.Controls.Find("prioriteNumericUpDown", true)[0] as NumericUpDown;
                    var requiertValidationCheckBox = this.Controls.Find("requiertValidationCheckBox", true)[0] as CheckBox;
                    var estActiveCheckBox = this.Controls.Find("estActiveCheckBox", true)[0] as CheckBox;

                    if (nomTextBox != null) nomTextBox.Text = _regle.Nom;
                    if (descriptionTextBox != null) descriptionTextBox.Text = _regle.Description;
                    if (typeClientComboBox != null) typeClientComboBox.Text = _regle.TypeClient;
                    if (montantMinimumNumericUpDown != null) montantMinimumNumericUpDown.Value = _regle.MontantMinimum ?? 0;
                    if (joursApresPremiere != null) joursApresPremiere.Value = _regle.JoursApresPremiere;
                    if (joursEntrePremiereDeuxieme != null) joursEntrePremiereDeuxieme.Value = _regle.JoursEntrePremiereDeuxieme;
                    if (joursEntreDeuxiemeTroisieme != null) joursEntreDeuxiemeTroisieme.Value = _regle.JoursEntreDeuxiemeTroisieme;
                    if (prioriteNumericUpDown != null) prioriteNumericUpDown.Value = _regle.Priorite;
                    if (requiertValidationCheckBox != null) requiertValidationCheckBox.Checked = _regle.RequiertValidation;
                    if (estActiveCheckBox != null) estActiveCheckBox.Checked = _regle.EstActive;

                    // Sélectionner les modèles de relance
                    if (modeleNiveau1ComboBox != null && _regle.ModeleRelanceNiveau1Id.HasValue)
                    {
                        for (int i = 0; i < modeleNiveau1ComboBox.Items.Count; i++)
                        {
                            var item = modeleNiveau1ComboBox.Items[i] as ComboBoxItem;
                            if (item != null && item.Value == _regle.ModeleRelanceNiveau1Id.Value)
                            {
                                modeleNiveau1ComboBox.SelectedIndex = i;
                                break;
                            }
                        }
                    }

                    if (modeleNiveau2ComboBox != null && _regle.ModeleRelanceNiveau2Id.HasValue)
                    {
                        for (int i = 0; i < modeleNiveau2ComboBox.Items.Count; i++)
                        {
                            var item = modeleNiveau2ComboBox.Items[i] as ComboBoxItem;
                            if (item != null && item.Value == _regle.ModeleRelanceNiveau2Id.Value)
                            {
                                modeleNiveau2ComboBox.SelectedIndex = i;
                                break;
                            }
                        }
                    }

                    if (modeleNiveau3ComboBox != null && _regle.ModeleRelanceNiveau3Id.HasValue)
                    {
                        for (int i = 0; i < modeleNiveau3ComboBox.Items.Count; i++)
                        {
                            var item = modeleNiveau3ComboBox.Items[i] as ComboBoxItem;
                            if (item != null && item.Value == _regle.ModeleRelanceNiveau3Id.Value)
                            {
                                modeleNiveau3ComboBox.SelectedIndex = i;
                                break;
                            }
                        }
                    }
                }
                else
                {
                    // Valeurs par défaut pour une nouvelle règle
                    var joursApresPremiere = this.Controls.Find("joursApresPremiere", true)[0] as NumericUpDown;
                    var joursEntrePremiereDeuxieme = this.Controls.Find("joursEntrePremiereDeuxieme", true)[0] as NumericUpDown;
                    var joursEntreDeuxiemeTroisieme = this.Controls.Find("joursEntreDeuxiemeTroisieme", true)[0] as NumericUpDown;
                    var prioriteNumericUpDown = this.Controls.Find("prioriteNumericUpDown", true)[0] as NumericUpDown;
                    var estActiveCheckBox = this.Controls.Find("estActiveCheckBox", true)[0] as CheckBox;

                    if (joursApresPremiere != null) joursApresPremiere.Value = 7;
                    if (joursEntrePremiereDeuxieme != null) joursEntrePremiereDeuxieme.Value = 7;
                    if (joursEntreDeuxiemeTroisieme != null) joursEntreDeuxiemeTroisieme.Value = 7;
                    if (prioriteNumericUpDown != null) prioriteNumericUpDown.Value = 1;
                    if (estActiveCheckBox != null) estActiveCheckBox.Checked = true;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'édition de règle de relance");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadModelesRelanceAsync()
        {
            try
            {
                // Récupérer tous les modèles de relance
                var modeles = await _modeleRelanceService.GetAllAsync();

                // Filtrer les modèles par niveau
                _modelesNiveau1 = new List<ModeleRelance>();
                _modelesNiveau2 = new List<ModeleRelance>();
                _modelesNiveau3 = new List<ModeleRelance>();

                foreach (var modele in modeles)
                {
                    if (modele.NiveauFermete == 1)
                        _modelesNiveau1.Add(modele);
                    else if (modele.NiveauFermete == 2)
                        _modelesNiveau2.Add(modele);
                    else if (modele.NiveauFermete == 3)
                        _modelesNiveau3.Add(modele);
                }

                // Remplir les combobox
                var modeleNiveau1ComboBox = this.Controls.Find("modeleNiveau1ComboBox", true)[0] as ComboBox;
                var modeleNiveau2ComboBox = this.Controls.Find("modeleNiveau2ComboBox", true)[0] as ComboBox;
                var modeleNiveau3ComboBox = this.Controls.Find("modeleNiveau3ComboBox", true)[0] as ComboBox;

                if (modeleNiveau1ComboBox != null)
                {
                    modeleNiveau1ComboBox.Items.Clear();
                    modeleNiveau1ComboBox.Items.Add(new ComboBoxItem { Text = "-- Sélectionner un modèle --", Value = 0 });
                    foreach (var modele in _modelesNiveau1)
                    {
                        modeleNiveau1ComboBox.Items.Add(new ComboBoxItem { Text = modele.Nom, Value = modele.Id });
                    }
                    modeleNiveau1ComboBox.SelectedIndex = 0;
                }

                if (modeleNiveau2ComboBox != null)
                {
                    modeleNiveau2ComboBox.Items.Clear();
                    modeleNiveau2ComboBox.Items.Add(new ComboBoxItem { Text = "-- Sélectionner un modèle --", Value = 0 });
                    foreach (var modele in _modelesNiveau2)
                    {
                        modeleNiveau2ComboBox.Items.Add(new ComboBoxItem { Text = modele.Nom, Value = modele.Id });
                    }
                    modeleNiveau2ComboBox.SelectedIndex = 0;
                }

                if (modeleNiveau3ComboBox != null)
                {
                    modeleNiveau3ComboBox.Items.Clear();
                    modeleNiveau3ComboBox.Items.Add(new ComboBoxItem { Text = "-- Sélectionner un modèle --", Value = 0 });
                    foreach (var modele in _modelesNiveau3)
                    {
                        modeleNiveau3ComboBox.Items.Add(new ComboBoxItem { Text = modele.Nom, Value = modele.Id });
                    }
                    modeleNiveau3ComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des modèles de relance");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des modèles de relance : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer les valeurs des champs
                var nomTextBox = this.Controls.Find("nomTextBox", true)[0] as TextBox;
                var descriptionTextBox = this.Controls.Find("descriptionTextBox", true)[0] as TextBox;
                var typeClientComboBox = this.Controls.Find("typeClientComboBox", true)[0] as ComboBox;
                var montantMinimumNumericUpDown = this.Controls.Find("montantMinimumNumericUpDown", true)[0] as NumericUpDown;
                var joursApresPremiere = this.Controls.Find("joursApresPremiere", true)[0] as NumericUpDown;
                var joursEntrePremiereDeuxieme = this.Controls.Find("joursEntrePremiereDeuxieme", true)[0] as NumericUpDown;
                var joursEntreDeuxiemeTroisieme = this.Controls.Find("joursEntreDeuxiemeTroisieme", true)[0] as NumericUpDown;
                var modeleNiveau1ComboBox = this.Controls.Find("modeleNiveau1ComboBox", true)[0] as ComboBox;
                var modeleNiveau2ComboBox = this.Controls.Find("modeleNiveau2ComboBox", true)[0] as ComboBox;
                var modeleNiveau3ComboBox = this.Controls.Find("modeleNiveau3ComboBox", true)[0] as ComboBox;
                var prioriteNumericUpDown = this.Controls.Find("prioriteNumericUpDown", true)[0] as NumericUpDown;
                var requiertValidationCheckBox = this.Controls.Find("requiertValidationCheckBox", true)[0] as CheckBox;
                var estActiveCheckBox = this.Controls.Find("estActiveCheckBox", true)[0] as CheckBox;

                // Valider les champs obligatoires
                if (string.IsNullOrWhiteSpace(nomTextBox?.Text))
                {
                    MessageBox.Show("Le nom de la règle est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    nomTextBox?.Focus();
                    return;
                }

                if (modeleNiveau1ComboBox?.SelectedIndex <= 0)
                {
                    MessageBox.Show("Le modèle de relance niveau 1 est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    modeleNiveau1ComboBox?.Focus();
                    return;
                }

                // Mettre à jour l'objet règle
                _regle.Nom = nomTextBox?.Text;
                _regle.Description = descriptionTextBox?.Text;
                _regle.TypeClient = typeClientComboBox?.Text;
                _regle.MontantMinimum = montantMinimumNumericUpDown?.Value > 0 ? montantMinimumNumericUpDown.Value : (decimal?)null;
                _regle.JoursApresPremiere = (int)(joursApresPremiere?.Value ?? 7);
                _regle.JoursEntrePremiereDeuxieme = (int)(joursEntrePremiereDeuxieme?.Value ?? 7);
                _regle.JoursEntreDeuxiemeTroisieme = (int)(joursEntreDeuxiemeTroisieme?.Value ?? 7);
                _regle.Priorite = (int)(prioriteNumericUpDown?.Value ?? 1);
                _regle.RequiertValidation = requiertValidationCheckBox?.Checked ?? false;
                _regle.EstActive = estActiveCheckBox?.Checked ?? true;

                // Récupérer les identifiants des modèles de relance
                var modeleNiveau1Item = modeleNiveau1ComboBox?.SelectedItem as ComboBoxItem;
                var modeleNiveau2Item = modeleNiveau2ComboBox?.SelectedItem as ComboBoxItem;
                var modeleNiveau3Item = modeleNiveau3ComboBox?.SelectedItem as ComboBoxItem;

                _regle.ModeleRelanceNiveau1Id = modeleNiveau1Item?.Value > 0 ? modeleNiveau1Item.Value : (int?)null;
                _regle.ModeleRelanceNiveau2Id = modeleNiveau2Item?.Value > 0 ? modeleNiveau2Item.Value : (int?)null;
                _regle.ModeleRelanceNiveau3Id = modeleNiveau3Item?.Value > 0 ? modeleNiveau3Item.Value : (int?)null;

                // Enregistrer la règle
                if (_isEditMode)
                {
                    await _regleRelanceService.UpdateAsync(_regle, _currentUserId);
                    MessageBox.Show("La règle de relance a été modifiée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _regleRelanceService.CreateAsync(_regle, _currentUserId);
                    MessageBox.Show("La règle de relance a été créée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Fermer le formulaire
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement de la règle de relance");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement de la règle de relance : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// Classe pour les éléments de la combobox
        /// </summary>
        private class ComboBoxItem
        {
            public string Text { get; set; }
            public int Value { get; set; }

            public override string ToString()
            {
                return Text;
            }
        }
    }
}
