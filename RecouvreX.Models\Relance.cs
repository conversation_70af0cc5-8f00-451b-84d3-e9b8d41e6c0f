using System;
using System.Collections.Generic;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une relance de paiement dans le système
    /// </summary>
    public class Relance : BaseEntity
    {
        /// <summary>
        /// Identifiant de la facture concernée
        /// </summary>
        public int FactureId { get; set; }

        /// <summary>
        /// Facture associée (navigation property)
        /// </summary>
        public Facture Facture { get; set; }

        /// <summary>
        /// Type de relance (Téléphonique, Email, Courrier, etc.)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Date de la relance
        /// </summary>
        public DateTime DateRelance { get; set; }

        /// <summary>
        /// Niveau de la relance (1, 2, 3, etc.)
        /// </summary>
        public int Niveau { get; set; }

        /// <summary>
        /// Statut de la relance (Planifiée, Effectuée, Annulée, etc.)
        /// </summary>
        public string Statut { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a effectué la relance
        /// </summary>
        public int? UtilisateurId { get; set; }

        /// <summary>
        /// Utilisateur qui a effectué la relance (navigation property)
        /// </summary>
        public Utilisateur Utilisateur { get; set; }

        /// <summary>
        /// Contenu ou message de la relance
        /// </summary>
        public string Contenu { get; set; }

        /// <summary>
        /// Réponse du client suite à la relance
        /// </summary>
        public string ReponseClient { get; set; }

        /// <summary>
        /// Date de la prochaine relance prévue
        /// </summary>
        public DateTime? DateProchaineRelance { get; set; }

        /// <summary>
        /// Notes ou commentaires sur la relance
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Liste des documents associés à cette relance (navigation property)
        /// </summary>
        public ICollection<Document> Documents { get; set; }
    }
}
