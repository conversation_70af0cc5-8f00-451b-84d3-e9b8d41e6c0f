namespace RecouvreX.WinForms.Forms.Administration
{
    partial class ResetPasswordForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // ResetPasswordForm
            // 
            this.ClientSize = new System.Drawing.Size(400, 200);
            this.Name = "ResetPasswordForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Réinitialiser le mot de passe";
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ResumeLayout(false);

            // Panneau principal
            System.Windows.Forms.TableLayoutPanel mainPanel = new System.Windows.Forms.TableLayoutPanel();
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Padding = new System.Windows.Forms.Padding(10);
            mainPanel.ColumnCount = 2;
            mainPanel.RowCount = 3;
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 40F));
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 60F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 50F));
            this.Controls.Add(mainPanel);

            // Nouveau mot de passe
            System.Windows.Forms.Label newPasswordLabel = new System.Windows.Forms.Label();
            newPasswordLabel.Text = "Nouveau mot de passe* :";
            newPasswordLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(newPasswordLabel, 0, 0);

            System.Windows.Forms.TextBox newPasswordTextBox = new System.Windows.Forms.TextBox();
            newPasswordTextBox.Name = "newPasswordTextBox";
            newPasswordTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            newPasswordTextBox.PasswordChar = '•';
            newPasswordTextBox.Validating += new System.ComponentModel.CancelEventHandler(this.NewPasswordTextBox_Validating);
            mainPanel.Controls.Add(newPasswordTextBox, 1, 0);

            // Confirmation du nouveau mot de passe
            System.Windows.Forms.Label confirmPasswordLabel = new System.Windows.Forms.Label();
            confirmPasswordLabel.Text = "Confirmation* :";
            confirmPasswordLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(confirmPasswordLabel, 0, 1);

            System.Windows.Forms.TextBox confirmPasswordTextBox = new System.Windows.Forms.TextBox();
            confirmPasswordTextBox.Name = "confirmPasswordTextBox";
            confirmPasswordTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            confirmPasswordTextBox.PasswordChar = '•';
            confirmPasswordTextBox.Validating += new System.ComponentModel.CancelEventHandler(this.ConfirmPasswordTextBox_Validating);
            mainPanel.Controls.Add(confirmPasswordTextBox, 1, 1);

            // Boutons
            System.Windows.Forms.FlowLayoutPanel buttonsPanel = new System.Windows.Forms.FlowLayoutPanel();
            buttonsPanel.FlowDirection = System.Windows.Forms.FlowDirection.RightToLeft;
            buttonsPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            buttonsPanel.Anchor = System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(buttonsPanel, 1, 2);

            System.Windows.Forms.Button cancelButton = new System.Windows.Forms.Button();
            cancelButton.Name = "cancelButton";
            cancelButton.Text = "Annuler";
            cancelButton.Size = new System.Drawing.Size(100, 30);
            cancelButton.Click += new System.EventHandler(this.CancelButton_Click);
            buttonsPanel.Controls.Add(cancelButton);

            System.Windows.Forms.Button okButton = new System.Windows.Forms.Button();
            okButton.Name = "okButton";
            okButton.Text = "OK";
            okButton.Size = new System.Drawing.Size(100, 30);
            okButton.Click += new System.EventHandler(this.OkButton_Click);
            buttonsPanel.Controls.Add(okButton);

            // Note sur les champs obligatoires
            System.Windows.Forms.Label noteLabel = new System.Windows.Forms.Label();
            noteLabel.Text = "* Champs obligatoires";
            noteLabel.AutoSize = true;
            noteLabel.ForeColor = System.Drawing.Color.Red;
            noteLabel.Location = new System.Drawing.Point(10, this.ClientSize.Height - 30);
            this.Controls.Add(noteLabel);
        }

        #endregion
    }
}
