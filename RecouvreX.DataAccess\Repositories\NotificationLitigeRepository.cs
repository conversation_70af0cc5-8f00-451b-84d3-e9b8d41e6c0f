using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les notifications de litiges
    /// </summary>
    public class NotificationLitigeRepository : INotificationLitigeRepository
    {
        private readonly DatabaseConnection _dbConnection;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public NotificationLitigeRepository(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection ?? throw new ArgumentNullException(nameof(dbConnection));
        }

        /// <summary>
        /// Récupère toutes les notifications
        /// </summary>
        /// <returns>Liste des notifications</returns>
        public async Task<IEnumerable<NotificationLitige>> GetAllAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT n.*, l.*, u.*
                    FROM NotificationsLitige n
                    LEFT JOIN Litiges l ON n.LitigeId = l.Id
                    LEFT JOIN Utilisateurs u ON n.UtilisateurId = u.Id
                    WHERE n.EstActif = 1
                    ORDER BY n.DateNotification DESC";

                var notificationsDictionary = new Dictionary<int, NotificationLitige>();
                var result = await connection.QueryAsync<NotificationLitige, Litige, Utilisateur, NotificationLitige>(
                    query,
                    (notification, litige, utilisateur) =>
                    {
                        if (!notificationsDictionary.TryGetValue(notification.Id, out var notificationEntry))
                        {
                            notificationEntry = notification;
                            notificationEntry.Litige = litige;
                            notificationEntry.Utilisateur = utilisateur;
                            notificationsDictionary.Add(notification.Id, notificationEntry);
                        }
                        return notificationEntry;
                    },
                    splitOn: "Id,Id");

                return notificationsDictionary.Values;
            }
        }

        /// <summary>
        /// Récupère une notification par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la notification</param>
        /// <returns>Notification</returns>
        public async Task<NotificationLitige> GetByIdAsync(int id)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT n.*, l.*, u.*
                    FROM NotificationsLitige n
                    LEFT JOIN Litiges l ON n.LitigeId = l.Id
                    LEFT JOIN Utilisateurs u ON n.UtilisateurId = u.Id
                    WHERE n.Id = @Id AND n.EstActif = 1";

                var notificationsDictionary = new Dictionary<int, NotificationLitige>();
                var result = await connection.QueryAsync<NotificationLitige, Litige, Utilisateur, NotificationLitige>(
                    query,
                    (notification, litige, utilisateur) =>
                    {
                        if (!notificationsDictionary.TryGetValue(notification.Id, out var notificationEntry))
                        {
                            notificationEntry = notification;
                            notificationEntry.Litige = litige;
                            notificationEntry.Utilisateur = utilisateur;
                            notificationsDictionary.Add(notification.Id, notificationEntry);
                        }
                        return notificationEntry;
                    },
                    new { Id = id },
                    splitOn: "Id,Id");

                return notificationsDictionary.Values.FirstOrDefault();
            }
        }

        /// <summary>
        /// Récupère les notifications pour un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des notifications</returns>
        public async Task<IEnumerable<NotificationLitige>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT n.*, l.*, u.*
                    FROM NotificationsLitige n
                    LEFT JOIN Litiges l ON n.LitigeId = l.Id
                    LEFT JOIN Utilisateurs u ON n.UtilisateurId = u.Id
                    WHERE n.UtilisateurId = @UtilisateurId AND n.EstActif = 1
                    ORDER BY n.DateNotification DESC";

                var notificationsDictionary = new Dictionary<int, NotificationLitige>();
                var result = await connection.QueryAsync<NotificationLitige, Litige, Utilisateur, NotificationLitige>(
                    query,
                    (notification, litige, utilisateur) =>
                    {
                        if (!notificationsDictionary.TryGetValue(notification.Id, out var notificationEntry))
                        {
                            notificationEntry = notification;
                            notificationEntry.Litige = litige;
                            notificationEntry.Utilisateur = utilisateur;
                            notificationsDictionary.Add(notification.Id, notificationEntry);
                        }
                        return notificationEntry;
                    },
                    new { UtilisateurId = utilisateurId },
                    splitOn: "Id,Id");

                return notificationsDictionary.Values;
            }
        }

        /// <summary>
        /// Récupère les notifications non lues pour un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des notifications non lues</returns>
        public async Task<IEnumerable<NotificationLitige>> GetUnreadByUtilisateurIdAsync(int utilisateurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT n.*, l.*, u.*
                    FROM NotificationsLitige n
                    LEFT JOIN Litiges l ON n.LitigeId = l.Id
                    LEFT JOIN Utilisateurs u ON n.UtilisateurId = u.Id
                    WHERE n.UtilisateurId = @UtilisateurId AND n.EstLue = 0 AND n.EstActif = 1
                    ORDER BY n.DateNotification DESC";

                var notificationsDictionary = new Dictionary<int, NotificationLitige>();
                var result = await connection.QueryAsync<NotificationLitige, Litige, Utilisateur, NotificationLitige>(
                    query,
                    (notification, litige, utilisateur) =>
                    {
                        if (!notificationsDictionary.TryGetValue(notification.Id, out var notificationEntry))
                        {
                            notificationEntry = notification;
                            notificationEntry.Litige = litige;
                            notificationEntry.Utilisateur = utilisateur;
                            notificationsDictionary.Add(notification.Id, notificationEntry);
                        }
                        return notificationEntry;
                    },
                    new { UtilisateurId = utilisateurId },
                    splitOn: "Id,Id");

                return notificationsDictionary.Values;
            }
        }

        /// <summary>
        /// Récupère les notifications pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des notifications</returns>
        public async Task<IEnumerable<NotificationLitige>> GetByLitigeIdAsync(int litigeId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT n.*, l.*, u.*
                    FROM NotificationsLitige n
                    LEFT JOIN Litiges l ON n.LitigeId = l.Id
                    LEFT JOIN Utilisateurs u ON n.UtilisateurId = u.Id
                    WHERE n.LitigeId = @LitigeId AND n.EstActif = 1
                    ORDER BY n.DateNotification DESC";

                var notificationsDictionary = new Dictionary<int, NotificationLitige>();
                var result = await connection.QueryAsync<NotificationLitige, Litige, Utilisateur, NotificationLitige>(
                    query,
                    (notification, litige, utilisateur) =>
                    {
                        if (!notificationsDictionary.TryGetValue(notification.Id, out var notificationEntry))
                        {
                            notificationEntry = notification;
                            notificationEntry.Litige = litige;
                            notificationEntry.Utilisateur = utilisateur;
                            notificationsDictionary.Add(notification.Id, notificationEntry);
                        }
                        return notificationEntry;
                    },
                    new { LitigeId = litigeId },
                    splitOn: "Id,Id");

                return notificationsDictionary.Values;
            }
        }

        /// <summary>
        /// Ajoute une notification
        /// </summary>
        /// <param name="notification">Notification à ajouter</param>
        /// <returns>Notification ajoutée</returns>
        public async Task<NotificationLitige> AddAsync(NotificationLitige notification)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    INSERT INTO NotificationsLitige (
                        LitigeId, UtilisateurId, Type, Message, DateNotification, EstLue, DateLecture, EstActif
                    ) VALUES (
                        @LitigeId, @UtilisateurId, @Type, @Message, @DateNotification, @EstLue, @DateLecture, @EstActif
                    );
                    SELECT CAST(SCOPE_IDENTITY() as int)";

                var id = await connection.QuerySingleAsync<int>(query, notification);
                notification.Id = id;
                return notification;
            }
        }

        /// <summary>
        /// Marque une notification comme lue
        /// </summary>
        /// <param name="id">Identifiant de la notification</param>
        /// <returns>True si la notification a été marquée comme lue</returns>
        public async Task<bool> MarkAsReadAsync(int id)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE NotificationsLitige
                    SET EstLue = 1, DateLecture = GETDATE()
                    WHERE Id = @Id";

                var result = await connection.ExecuteAsync(query, new { Id = id });
                return result > 0;
            }
        }

        /// <summary>
        /// Marque toutes les notifications d'un utilisateur comme lues
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Nombre de notifications marquées comme lues</returns>
        public async Task<int> MarkAllAsReadAsync(int utilisateurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE NotificationsLitige
                    SET EstLue = 1, DateLecture = GETDATE()
                    WHERE UtilisateurId = @UtilisateurId AND EstLue = 0 AND EstActif = 1";

                return await connection.ExecuteAsync(query, new { UtilisateurId = utilisateurId });
            }
        }

        /// <summary>
        /// Supprime une notification
        /// </summary>
        /// <param name="id">Identifiant de la notification</param>
        /// <returns>True si la notification a été supprimée</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE NotificationsLitige
                    SET EstActif = 0
                    WHERE Id = @Id";

                var result = await connection.ExecuteAsync(query, new { Id = id });
                return result > 0;
            }
        }
    }
}
