namespace RecouvreX.WinForms.Forms.Relances
{
    partial class RelanceEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // RelanceEditForm
            //
            this.ClientSize = new System.Drawing.Size(600, 500);
            this.Name = "RelanceEditForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Relance"; // Titre par défaut, sera mis à jour dans le constructeur
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Load += new System.EventHandler(this.RelanceEditForm_Load);
            this.ResumeLayout(false);

            // Panneau principal
            System.Windows.Forms.TableLayoutPanel mainPanel = new System.Windows.Forms.TableLayoutPanel();
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Padding = new System.Windows.Forms.Padding(10);
            mainPanel.ColumnCount = 2;
            mainPanel.RowCount = 10;
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 30F));
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 70F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            this.Controls.Add(mainPanel);

            // Facture
            System.Windows.Forms.Label factureLabel = new System.Windows.Forms.Label();
            factureLabel.Text = "Facture* :";
            factureLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(factureLabel, 0, 0);

            System.Windows.Forms.ComboBox factureComboBox = new System.Windows.Forms.ComboBox();
            factureComboBox.Name = "factureComboBox";
            factureComboBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            factureComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            factureComboBox.Validating += new System.ComponentModel.CancelEventHandler(this.FactureComboBox_Validating);
            factureComboBox.SelectedIndexChanged += new System.EventHandler(this.FactureComboBox_SelectedIndexChanged);
            mainPanel.Controls.Add(factureComboBox, 1, 0);

            // Client (en lecture seule)
            System.Windows.Forms.Label clientLabel = new System.Windows.Forms.Label();
            clientLabel.Text = "Client :";
            clientLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(clientLabel, 0, 1);

            System.Windows.Forms.TextBox clientTextBox = new System.Windows.Forms.TextBox();
            clientTextBox.Name = "clientTextBox";
            clientTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            clientTextBox.ReadOnly = true;
            clientTextBox.BackColor = System.Drawing.SystemColors.Control;
            mainPanel.Controls.Add(clientTextBox, 1, 1);

            // Type de relance
            System.Windows.Forms.Label typeLabel = new System.Windows.Forms.Label();
            typeLabel.Text = "Type* :";
            typeLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(typeLabel, 0, 2);

            System.Windows.Forms.ComboBox typeComboBox = new System.Windows.Forms.ComboBox();
            typeComboBox.Name = "typeComboBox";
            typeComboBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            typeComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            typeComboBox.Items.Add("Email");
            typeComboBox.Items.Add("Téléphone");
            typeComboBox.Items.Add("Courrier");
            typeComboBox.SelectedIndex = 0;
            typeComboBox.Validating += new System.ComponentModel.CancelEventHandler(this.TypeComboBox_Validating);
            mainPanel.Controls.Add(typeComboBox, 1, 2);

            // Date de relance
            System.Windows.Forms.Label dateRelanceLabel = new System.Windows.Forms.Label();
            dateRelanceLabel.Text = "Date de relance* :";
            dateRelanceLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(dateRelanceLabel, 0, 3);

            System.Windows.Forms.DateTimePicker dateRelancePicker = new System.Windows.Forms.DateTimePicker();
            dateRelancePicker.Name = "dateRelancePicker";
            dateRelancePicker.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            dateRelancePicker.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            dateRelancePicker.Value = System.DateTime.Today;
            mainPanel.Controls.Add(dateRelancePicker, 1, 3);

            // Niveau
            System.Windows.Forms.Label niveauLabel = new System.Windows.Forms.Label();
            niveauLabel.Text = "Niveau* :";
            niveauLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(niveauLabel, 0, 4);

            System.Windows.Forms.NumericUpDown niveauNumericUpDown = new System.Windows.Forms.NumericUpDown();
            niveauNumericUpDown.Name = "niveauNumericUpDown";
            niveauNumericUpDown.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            niveauNumericUpDown.Minimum = 1;
            niveauNumericUpDown.Maximum = 10;
            niveauNumericUpDown.Value = 1;
            mainPanel.Controls.Add(niveauNumericUpDown, 1, 4);

            // Statut
            System.Windows.Forms.Label statutLabel = new System.Windows.Forms.Label();
            statutLabel.Text = "Statut* :";
            statutLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(statutLabel, 0, 5);

            System.Windows.Forms.ComboBox statutComboBox = new System.Windows.Forms.ComboBox();
            statutComboBox.Name = "statutComboBox";
            statutComboBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            statutComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            statutComboBox.Items.Add("Planifiée");
            statutComboBox.Items.Add("En cours");
            statutComboBox.Items.Add("Terminée");
            statutComboBox.Items.Add("Annulée");
            statutComboBox.SelectedIndex = 0;
            statutComboBox.Validating += new System.ComponentModel.CancelEventHandler(this.StatutComboBox_Validating);
            mainPanel.Controls.Add(statutComboBox, 1, 5);

            // Contenu
            System.Windows.Forms.Label contenuLabel = new System.Windows.Forms.Label();
            contenuLabel.Text = "Contenu :";
            contenuLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(contenuLabel, 0, 6);

            System.Windows.Forms.TextBox contenuTextBox = new System.Windows.Forms.TextBox();
            contenuTextBox.Name = "contenuTextBox";
            contenuTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            contenuTextBox.Multiline = true;
            contenuTextBox.Height = 60;
            contenuTextBox.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            mainPanel.Controls.Add(contenuTextBox, 1, 6);
            mainPanel.SetRowSpan(contenuTextBox, 2);

            // Date de prochaine relance
            System.Windows.Forms.Label dateProchaineRelanceLabel = new System.Windows.Forms.Label();
            dateProchaineRelanceLabel.Text = "Date prochaine relance :";
            dateProchaineRelanceLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(dateProchaineRelanceLabel, 0, 8);

            System.Windows.Forms.DateTimePicker dateProchaineRelancePicker = new System.Windows.Forms.DateTimePicker();
            dateProchaineRelancePicker.Name = "dateProchaineRelancePicker";
            dateProchaineRelancePicker.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            dateProchaineRelancePicker.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            dateProchaineRelancePicker.Value = System.DateTime.Today.AddDays(7);
            mainPanel.Controls.Add(dateProchaineRelancePicker, 1, 8);

            // Boutons
            System.Windows.Forms.FlowLayoutPanel buttonsPanel = new System.Windows.Forms.FlowLayoutPanel();
            buttonsPanel.FlowDirection = System.Windows.Forms.FlowDirection.RightToLeft;
            buttonsPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            buttonsPanel.Anchor = System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(buttonsPanel, 1, 9);
            mainPanel.SetColumnSpan(buttonsPanel, 2);

            System.Windows.Forms.Button cancelButton = new System.Windows.Forms.Button();
            cancelButton.Name = "cancelButton";
            cancelButton.Text = "Annuler";
            cancelButton.Size = new System.Drawing.Size(100, 30);
            cancelButton.Click += new System.EventHandler(this.CancelButton_Click);
            buttonsPanel.Controls.Add(cancelButton);

            System.Windows.Forms.Button saveButton = new System.Windows.Forms.Button();
            saveButton.Name = "saveButton";
            saveButton.Text = "Enregistrer";
            saveButton.Size = new System.Drawing.Size(100, 30);
            saveButton.Click += new System.EventHandler(this.SaveButton_Click);
            buttonsPanel.Controls.Add(saveButton);

            // Note sur les champs obligatoires
            System.Windows.Forms.Label noteLabel = new System.Windows.Forms.Label();
            noteLabel.Text = "* Champs obligatoires";
            noteLabel.AutoSize = true;
            noteLabel.ForeColor = System.Drawing.Color.Red;
            noteLabel.Location = new System.Drawing.Point(10, this.ClientSize.Height - 30);
            this.Controls.Add(noteLabel);
        }

        #endregion
    }
}
