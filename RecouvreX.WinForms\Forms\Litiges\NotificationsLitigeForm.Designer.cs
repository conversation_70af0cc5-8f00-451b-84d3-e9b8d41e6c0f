using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.IO;
using System.Data;

namespace RecouvreX.WinForms.Forms.Litiges
{
    partial class NotificationsLitigeForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private Label titleLabel;
        private Panel filtersPanel;
        private CheckBox showAllCheckBox;
        private Button markAllAsReadButton;
        private DataGridView notificationsDataGridView;
        private Panel buttonsPanel;
        private Button viewLitigeButton;
        private Button markAsReadButton;
        private Button refreshButton;
        private Button closeButton;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            
            // Définir les propriétés du formulaire
            this.Text = "Notifications de litiges";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimizeBox = false;
            this.MaximizeBox = false;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            
            // Créer les contrôles
            this.mainPanel = new Panel();
            this.mainPanel.Dock = DockStyle.Fill;
            this.Controls.Add(this.mainPanel);

            // Titre
            this.titleLabel = new Label();
            this.titleLabel.Text = "Notifications de litiges";
            this.titleLabel.Font = new Font(this.Font.FontFamily, 14, FontStyle.Bold);
            this.titleLabel.AutoSize = true;
            this.titleLabel.Location = new Point(20, 20);
            this.mainPanel.Controls.Add(this.titleLabel);

            // Filtres
            this.filtersPanel = new Panel();
            this.filtersPanel.Size = new Size(740, 40);
            this.filtersPanel.Location = new Point(20, 60);
            this.mainPanel.Controls.Add(this.filtersPanel);

            this.showAllCheckBox = new CheckBox();
            this.showAllCheckBox.Name = "showAllCheckBox";
            this.showAllCheckBox.Text = "Afficher toutes les notifications (y compris les lues)";
            this.showAllCheckBox.AutoSize = true;
            this.showAllCheckBox.Location = new Point(10, 10);
            this.showAllCheckBox.CheckedChanged += new EventHandler(this.ShowAllCheckBox_CheckedChanged);
            this.filtersPanel.Controls.Add(this.showAllCheckBox);

            this.markAllAsReadButton = new Button();
            this.markAllAsReadButton.Text = "Marquer tout comme lu";
            this.markAllAsReadButton.Size = new Size(150, 30);
            this.markAllAsReadButton.Location = new Point(580, 5);
            this.markAllAsReadButton.Click += new EventHandler(this.MarkAllAsReadButton_Click);
            this.filtersPanel.Controls.Add(this.markAllAsReadButton);

            // Liste des notifications
            this.notificationsDataGridView = new DataGridView();
            this.notificationsDataGridView.Name = "notificationsDataGridView";
            this.notificationsDataGridView.Location = new Point(20, 110);
            this.notificationsDataGridView.Size = new Size(740, 400);
            this.notificationsDataGridView.AllowUserToAddRows = false;
            this.notificationsDataGridView.AllowUserToDeleteRows = false;
            this.notificationsDataGridView.AllowUserToResizeRows = false;
            this.notificationsDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.notificationsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.notificationsDataGridView.MultiSelect = false;
            this.notificationsDataGridView.ReadOnly = true;
            this.notificationsDataGridView.RowHeadersVisible = false;
            this.notificationsDataGridView.BackgroundColor = Color.White;
            this.notificationsDataGridView.BorderStyle = BorderStyle.None;
            this.notificationsDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.notificationsDataGridView.CellDoubleClick += new DataGridViewCellEventHandler(this.NotificationsDataGridView_CellDoubleClick);
            this.mainPanel.Controls.Add(this.notificationsDataGridView);

            // Configurer les colonnes du DataGridView
            ConfigureDataGridView(this.notificationsDataGridView);

            // Boutons d'action
            this.buttonsPanel = new Panel();
            this.buttonsPanel.Size = new Size(740, 50);
            this.buttonsPanel.Location = new Point(20, 520);
            this.mainPanel.Controls.Add(this.buttonsPanel);

            this.viewLitigeButton = new Button();
            this.viewLitigeButton.Text = "Voir le litige";
            this.viewLitigeButton.Size = new Size(120, 30);
            this.viewLitigeButton.Location = new Point(0, 10);
            this.viewLitigeButton.Click += new EventHandler(this.ViewLitigeButton_Click);
            this.buttonsPanel.Controls.Add(this.viewLitigeButton);

            this.markAsReadButton = new Button();
            this.markAsReadButton.Text = "Marquer comme lu";
            this.markAsReadButton.Size = new Size(150, 30);
            this.markAsReadButton.Location = new Point(130, 10);
            this.markAsReadButton.Click += new EventHandler(this.MarkAsReadButton_Click);
            this.buttonsPanel.Controls.Add(this.markAsReadButton);

            this.refreshButton = new Button();
            this.refreshButton.Text = "Actualiser";
            this.refreshButton.Size = new Size(100, 30);
            this.refreshButton.Location = new Point(290, 10);
            this.refreshButton.Click += new EventHandler(this.RefreshButton_Click);
            this.buttonsPanel.Controls.Add(this.refreshButton);

            this.closeButton = new Button();
            this.closeButton.Text = "Fermer";
            this.closeButton.Size = new Size(100, 30);
            this.closeButton.Location = new Point(640, 10);
            this.closeButton.Click += new EventHandler(this.CloseButton_Click);
            this.buttonsPanel.Controls.Add(this.closeButton);

            // Charger le formulaire
            this.Load += new EventHandler(this.NotificationsLitigeForm_Load);
        }

        /// <summary>
        /// Configure les colonnes du DataGridView
        /// </summary>
        private void ConfigureDataGridView(DataGridView dataGridView)
        {
            // Ajouter les colonnes
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "ID",
                DataPropertyName = "Id",
                Visible = false
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Type",
                HeaderText = "Type",
                DataPropertyName = "Type",
                Width = 120
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Message",
                HeaderText = "Message",
                DataPropertyName = "Message",
                Width = 250
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Litige",
                HeaderText = "Litige",
                DataPropertyName = "Litige",
                Width = 120
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DateNotification",
                HeaderText = "Date",
                DataPropertyName = "DateNotification",
                Width = 120
            });

            dataGridView.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "EstLue",
                HeaderText = "Lue",
                DataPropertyName = "EstLue",
                Visible = false
            });
        }

        #endregion
    }
}
