using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des règles de relance
    /// </summary>
    public interface IRegleRelanceService
    {
        /// <summary>
        /// Récupère toutes les règles de relance
        /// </summary>
        /// <returns>Liste des règles de relance</returns>
        Task<IEnumerable<RegleRelance>> GetAllAsync();

        /// <summary>
        /// Récupère une règle de relance par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la règle de relance</param>
        /// <returns>Règle de relance trouvée ou null</returns>
        Task<RegleRelance> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les règles de relance actives
        /// </summary>
        /// <returns>Liste des règles de relance actives</returns>
        Task<IEnumerable<RegleRelance>> GetActiveAsync();

        /// <summary>
        /// Récupère les règles de relance par type de client
        /// </summary>
        /// <param name="typeClient">Type de client</param>
        /// <returns>Liste des règles de relance pour le type de client spécifié</returns>
        Task<IEnumerable<RegleRelance>> GetByTypeClientAsync(string typeClient);

        /// <summary>
        /// Crée une nouvelle règle de relance
        /// </summary>
        /// <param name="regleRelance">Règle de relance à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Règle de relance créée avec son identifiant généré</returns>
        Task<RegleRelance> CreateAsync(RegleRelance regleRelance, int creePar);

        /// <summary>
        /// Met à jour une règle de relance existante
        /// </summary>
        /// <param name="regleRelance">Règle de relance à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Règle de relance mise à jour</returns>
        Task<RegleRelance> UpdateAsync(RegleRelance regleRelance, int modifiePar);

        /// <summary>
        /// Supprime une règle de relance
        /// </summary>
        /// <param name="id">Identifiant de la règle de relance à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Active ou désactive une règle de relance
        /// </summary>
        /// <param name="id">Identifiant de la règle de relance</param>
        /// <param name="estActive">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> SetActiveStatusAsync(int id, bool estActive, int modifiePar);

        /// <summary>
        /// Trouve la règle de relance applicable à une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Règle de relance applicable ou null</returns>
        Task<RegleRelance> FindApplicableRuleAsync(int factureId);

        /// <summary>
        /// Planifie les relances pour une facture selon la règle applicable
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Liste des planifications de relance créées</returns>
        Task<IEnumerable<PlanificationRelance>> PlanifierRelancesAsync(int factureId, int utilisateurId);

        /// <summary>
        /// Planifie les relances pour toutes les factures en retard
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre de planifications de relance créées</returns>
        Task<int> PlanifierRelancesAutomatiquesAsync(int utilisateurId);
    }
}
