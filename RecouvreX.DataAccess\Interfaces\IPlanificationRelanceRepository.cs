using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des planifications de relance
    /// </summary>
    public interface IPlanificationRelanceRepository : IRepository<PlanificationRelance>
    {
        /// <summary>
        /// Récupère les planifications de relance par facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des planifications de relance pour la facture spécifiée</returns>
        Task<IEnumerable<PlanificationRelance>> GetByFactureIdAsync(int factureId);

        /// <summary>
        /// Récupère les planifications de relance par statut
        /// </summary>
        /// <param name="statut">Statut des planifications</param>
        /// <returns>Liste des planifications de relance avec le statut spécifié</returns>
        Task<IEnumerable<PlanificationRelance>> GetByStatutAsync(string statut);

        /// <summary>
        /// Récupère les planifications de relance prévues pour une date
        /// </summary>
        /// <param name="date">Date prévue</param>
        /// <returns>Liste des planifications de relance prévues pour la date spécifiée</returns>
        Task<IEnumerable<PlanificationRelance>> GetPlannedForDateAsync(DateTime date);

        /// <summary>
        /// Récupère les planifications de relance avec leurs relations (facture, modèle, etc.)
        /// </summary>
        /// <returns>Liste des planifications de relance avec leurs relations</returns>
        Task<IEnumerable<PlanificationRelance>> GetWithRelationsAsync();

        /// <summary>
        /// Met à jour le statut d'une planification de relance
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <param name="commentaire">Commentaire (optionnel)</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateStatutAsync(int id, string statut, int modifiePar, string commentaire = null);

        /// <summary>
        /// Marque une planification de relance comme envoyée
        /// </summary>
        /// <param name="id">Identifiant de la planification de relance</param>
        /// <param name="dateEnvoi">Date d'envoi</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> MarkAsSentAsync(int id, DateTime dateEnvoi, int modifiePar);
    }
}
