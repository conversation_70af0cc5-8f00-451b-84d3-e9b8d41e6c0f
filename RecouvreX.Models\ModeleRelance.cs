using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un modèle de relance dans le système
    /// </summary>
    public class ModeleRelance : BaseEntity
    {
        /// <summary>
        /// Nom du modèle de relance
        /// </summary>
        public string Nom { get; set; } = string.Empty;

        /// <summary>
        /// Description du modèle de relance
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Type de modèle (Email, Lettre, SMS)
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Niveau de fermeté (1, 2, 3)
        /// </summary>
        public int NiveauFermete { get; set; }

        /// <summary>
        /// Objet du message (pour les emails)
        /// </summary>
        public string Objet { get; set; } = string.Empty;

        /// <summary>
        /// Contenu du modèle
        /// </summary>
        public string Contenu { get; set; } = string.Empty;

        /// <summary>
        /// Indique si le modèle est un modèle par défaut
        /// </summary>
        public bool EstParDefaut { get; set; }

        /// <summary>
        /// Indique si le modèle est actif
        /// </summary>
        public new bool EstActif { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a créé le modèle
        /// </summary>
        public int UtilisateurId { get; set; }

        /// <summary>
        /// Utilisateur qui a créé le modèle (navigation property)
        /// </summary>
        public Utilisateur Utilisateur { get; set; } = null!;
    }
}
