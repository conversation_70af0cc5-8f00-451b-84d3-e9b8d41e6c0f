using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les factures
    /// </summary>
    public class FactureRepository : BaseRepository<Facture>, IFactureRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public FactureRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "Factures")
        {
        }

        /// <summary>
        /// Récupère une facture par son numéro
        /// </summary>
        /// <param name="numero">Numéro de la facture</param>
        /// <returns>Facture trouvée ou null</returns>
        public async Task<Facture> GetByNumeroAsync(string numero)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Factures WHERE Numero = @Numero AND EstActif = 1";
                return await connection.QueryFirstOrDefaultAsync<Facture>(query, new { Numero = numero });
            }
        }

        /// <summary>
        /// Récupère une facture avec ses paiements
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Facture avec ses paiements</returns>
        public async Task<Facture> GetWithPaiementsAsync(int factureId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT f.*, fp.*, p.*
                    FROM Factures f
                    LEFT JOIN FacturePaiements fp ON f.Id = fp.FactureId
                    LEFT JOIN Paiements p ON fp.PaiementId = p.Id
                    WHERE f.Id = @FactureId AND f.EstActif = 1";

                Facture facture = null;
                var facturePaiements = new Dictionary<int, FacturePaiement>();

                var results = await connection.QueryAsync<Facture, FacturePaiement, Paiement, Facture>(
                    query,
                    (f, fp, p) =>
                    {
                        if (facture == null)
                        {
                            facture = f;
                            facture.FacturePaiements = new List<FacturePaiement>();
                        }

                        if (fp != null && p != null)
                        {
                            fp.Facture = facture;
                            fp.Paiement = p;
                            facture.FacturePaiements.Add(fp);
                        }

                        return facture;
                    },
                    new { FactureId = factureId },
                    splitOn: "Id,Id");

                return results.FirstOrDefault();
            }
        }

        /// <summary>
        /// Récupère une facture avec ses relances
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Facture avec ses relances</returns>
        public async Task<Facture> GetWithRelancesAsync(int factureId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT f.*, r.*
                    FROM Factures f
                    LEFT JOIN Relances r ON f.Id = r.FactureId
                    WHERE f.Id = @FactureId AND f.EstActif = 1";

                Facture facture = null;
                var relances = new List<Relance>();

                var results = await connection.QueryAsync<Facture, Relance, Facture>(
                    query,
                    (f, r) =>
                    {
                        if (facture == null)
                        {
                            facture = f;
                            facture.Relances = new List<Relance>();
                        }

                        if (r != null && r.Id != 0)
                        {
                            facture.Relances.Add(r);
                        }

                        return facture;
                    },
                    new { FactureId = factureId },
                    splitOn: "Id");

                return results.FirstOrDefault();
            }
        }

        /// <summary>
        /// Récupère une facture avec ses documents
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Facture avec ses documents</returns>
        public async Task<Facture> GetWithDocumentsAsync(int factureId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT f.*, d.*
                    FROM Factures f
                    LEFT JOIN Documents d ON f.Id = d.EntiteId AND d.TypeEntite = 'Facture'
                    WHERE f.Id = @FactureId AND f.EstActif = 1";

                Facture facture = null;
                var documents = new List<Document>();

                var results = await connection.QueryAsync<Facture, Document, Facture>(
                    query,
                    (f, d) =>
                    {
                        if (facture == null)
                        {
                            facture = f;
                            facture.Documents = new List<Document>();
                        }

                        if (d != null && d.Id != 0)
                        {
                            facture.Documents.Add(d);
                        }

                        return facture;
                    },
                    new { FactureId = factureId },
                    splitOn: "Id");

                return results.FirstOrDefault();
            }
        }

        /// <summary>
        /// Récupère toutes les factures d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des factures du client</returns>
        public async Task<IEnumerable<Facture>> GetByClientIdAsync(int clientId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Factures WHERE ClientId = @ClientId AND EstActif = 1";
                return await connection.QueryAsync<Facture>(query, new { ClientId = clientId });
            }
        }

        /// <summary>
        /// Récupère toutes les factures d'un commercial
        /// </summary>
        /// <param name="commercialId">Identifiant du commercial</param>
        /// <returns>Liste des factures du commercial</returns>
        public async Task<IEnumerable<Facture>> GetByCommercialIdAsync(int commercialId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Factures WHERE CommercialId = @CommercialId AND EstActif = 1";
                return await connection.QueryAsync<Facture>(query, new { CommercialId = commercialId });
            }
        }

        /// <summary>
        /// Récupère toutes les factures d'un livreur
        /// </summary>
        /// <param name="livreurId">Identifiant du livreur</param>
        /// <returns>Liste des factures du livreur</returns>
        public async Task<IEnumerable<Facture>> GetByLivreurIdAsync(int livreurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Factures WHERE LivreurId = @LivreurId AND EstActif = 1";
                return await connection.QueryAsync<Facture>(query, new { LivreurId = livreurId });
            }
        }

        /// <summary>
        /// Récupère les factures par statut
        /// </summary>
        /// <param name="statut">Statut des factures</param>
        /// <returns>Liste des factures ayant le statut spécifié</returns>
        public async Task<IEnumerable<Facture>> GetByStatutAsync(string statut)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Factures WHERE Statut = @Statut AND EstActif = 1";
                return await connection.QueryAsync<Facture>(query, new { Statut = statut });
            }
        }

        /// <summary>
        /// Récupère les factures par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des factures émises dans la période spécifiée</returns>
        public async Task<IEnumerable<Facture>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT *
                    FROM Factures
                    WHERE DateEmission BETWEEN @DateDebut AND @DateFin
                        AND EstActif = 1";

                return await connection.QueryAsync<Facture>(query, new { DateDebut = dateDebut, DateFin = dateFin });
            }
        }

        /// <summary>
        /// Récupère les factures en retard de paiement
        /// </summary>
        /// <returns>Liste des factures en retard</returns>
        public async Task<IEnumerable<Facture>> GetOverdueInvoicesAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT *
                    FROM Factures
                    WHERE (Statut = @StatutEnAttente OR Statut = @StatutPayeePartiellement)
                        AND DateEcheance < @DateAujourdhui
                        AND EstActif = 1";

                var parameters = new
                {
                    StatutEnAttente = StatutFacture.EnAttente,
                    StatutPayeePartiellement = StatutFacture.PayeePartiellement,
                    DateAujourdhui = DateTime.Today
                };

                return await connection.QueryAsync<Facture>(query, parameters);
            }
        }

        /// <summary>
        /// Met à jour le statut d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateStatutAsync(int factureId, string statut, int userId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE Factures
                    SET Statut = @Statut,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id";

                var parameters = new
                {
                    Id = factureId,
                    Statut = statut,
                    DateModification = DateTime.Now,
                    ModifiePar = userId
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }

        /// <summary>
        /// Met à jour les montants payés d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="montantPaye">Montant payé</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateMontantPayeAsync(int factureId, decimal montantPaye, int userId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Récupérer la facture
                var getQuery = "SELECT * FROM Factures WHERE Id = @Id";
                var facture = await connection.QueryFirstOrDefaultAsync<Facture>(getQuery, new { Id = factureId });

                if (facture == null)
                    return false;

                // Calculer le nouveau montant payé et le montant restant
                var nouveauMontantPaye = montantPaye;
                var nouveauMontantRestant = facture.MontantTTC - nouveauMontantPaye;

                // Déterminer le nouveau statut
                string nouveauStatut;
                if (nouveauMontantRestant <= 0)
                {
                    nouveauStatut = StatutFacture.Payee;
                    nouveauMontantRestant = 0;
                }
                else if (nouveauMontantPaye > 0)
                {
                    nouveauStatut = StatutFacture.PayeePartiellement;
                }
                else
                {
                    nouveauStatut = facture.DateEcheance < DateTime.Today ? StatutFacture.EnRetard : StatutFacture.EnAttente;
                }

                // Mettre à jour la facture
                var updateQuery = @"
                    UPDATE Factures
                    SET MontantPaye = @MontantPaye,
                        MontantRestant = @MontantRestant,
                        Statut = @Statut,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id";

                var parameters = new
                {
                    Id = factureId,
                    MontantPaye = nouveauMontantPaye,
                    MontantRestant = nouveauMontantRestant,
                    Statut = nouveauStatut,
                    DateModification = DateTime.Now,
                    ModifiePar = userId
                };

                var result = await connection.ExecuteAsync(updateQuery, parameters);
                return result > 0;
            }
        }
    }
}
