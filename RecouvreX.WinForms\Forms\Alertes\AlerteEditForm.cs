using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using Serilog;
using System;
using System.Collections.Generic;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Alertes
{
    public partial class AlerteEditForm : Form
    {
        private readonly IAlerteService _alerteService;
        private readonly int _currentUserId;
        private readonly Alerte _alerte;
        private readonly bool _isEditMode;

        public AlerteEditForm(IAlerteService alerteService, int currentUserId, Alerte alerte = null)
        {
            _alerteService = alerteService ?? throw new ArgumentNullException(nameof(alerteService));
            _currentUserId = currentUserId;
            _alerte = alerte ?? new Alerte { UtilisateurId = currentUserId, EstActive = true, FrequenceNotificationHeures = 24 };
            _isEditMode = alerte != null;

            InitializeComponent();
        }

        private void AlerteEditForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Configurer le titre du formulaire
                this.Text = _isEditMode ? "Modifier une alerte" : "Ajouter une alerte";

                // Remplir les combobox
                var typeComboBox = this.Controls.Find("typeComboBox", true)[0] as ComboBox;
                var conditionComboBox = this.Controls.Find("conditionComboBox", true)[0] as ComboBox;

                if (typeComboBox != null)
                {
                    typeComboBox.Items.Clear();
                    typeComboBox.Items.Add(TypeAlerte.MontantFacturesEnRetard);
                    typeComboBox.Items.Add(TypeAlerte.NombreFacturesEnRetard);
                    typeComboBox.Items.Add(TypeAlerte.AncienneteCreances);
                    typeComboBox.Items.Add(TypeAlerte.TauxRecouvrement);
                    typeComboBox.Items.Add(TypeAlerte.MontantCreancesClient);
                    typeComboBox.Items.Add(TypeAlerte.FactureEcheance);
                }

                if (conditionComboBox != null)
                {
                    conditionComboBox.Items.Clear();
                    conditionComboBox.Items.Add(ConditionAlerte.Superieur);
                    conditionComboBox.Items.Add(ConditionAlerte.Inferieur);
                    conditionComboBox.Items.Add(ConditionAlerte.Egal);
                    conditionComboBox.Items.Add(ConditionAlerte.SuperieurOuEgal);
                    conditionComboBox.Items.Add(ConditionAlerte.InferieurOuEgal);
                }

                // Remplir les champs avec les valeurs de l'alerte
                if (_isEditMode)
                {
                    var nomTextBox = this.Controls.Find("nomTextBox", true)[0] as TextBox;
                    var descriptionTextBox = this.Controls.Find("descriptionTextBox", true)[0] as TextBox;
                    var seuilNumericUpDown = this.Controls.Find("seuilNumericUpDown", true)[0] as NumericUpDown;
                    var estActiveCheckBox = this.Controls.Find("estActiveCheckBox", true)[0] as CheckBox;
                    var envoyerEmailCheckBox = this.Controls.Find("envoyerEmailCheckBox", true)[0] as CheckBox;
                    var frequenceNumericUpDown = this.Controls.Find("frequenceNumericUpDown", true)[0] as NumericUpDown;

                    if (nomTextBox != null) nomTextBox.Text = _alerte.Nom;
                    if (descriptionTextBox != null) descriptionTextBox.Text = _alerte.Description;
                    if (typeComboBox != null) typeComboBox.SelectedItem = _alerte.Type;
                    if (conditionComboBox != null) conditionComboBox.SelectedItem = _alerte.Condition;
                    if (seuilNumericUpDown != null) seuilNumericUpDown.Value = _alerte.Seuil;
                    if (estActiveCheckBox != null) estActiveCheckBox.Checked = _alerte.EstActive;
                    if (envoyerEmailCheckBox != null) envoyerEmailCheckBox.Checked = _alerte.EnvoyerEmail;
                    if (frequenceNumericUpDown != null) frequenceNumericUpDown.Value = _alerte.FrequenceNotificationHeures;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'édition d'alerte");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer les valeurs des champs
                var nomTextBox = this.Controls.Find("nomTextBox", true)[0] as TextBox;
                var descriptionTextBox = this.Controls.Find("descriptionTextBox", true)[0] as TextBox;
                var typeComboBox = this.Controls.Find("typeComboBox", true)[0] as ComboBox;
                var conditionComboBox = this.Controls.Find("conditionComboBox", true)[0] as ComboBox;
                var seuilNumericUpDown = this.Controls.Find("seuilNumericUpDown", true)[0] as NumericUpDown;
                var estActiveCheckBox = this.Controls.Find("estActiveCheckBox", true)[0] as CheckBox;
                var envoyerEmailCheckBox = this.Controls.Find("envoyerEmailCheckBox", true)[0] as CheckBox;
                var frequenceNumericUpDown = this.Controls.Find("frequenceNumericUpDown", true)[0] as NumericUpDown;

                // Valider les champs obligatoires
                if (string.IsNullOrWhiteSpace(nomTextBox?.Text))
                {
                    MessageBox.Show("Le nom de l'alerte est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    nomTextBox?.Focus();
                    return;
                }

                if (typeComboBox?.SelectedItem == null)
                {
                    MessageBox.Show("Le type d'alerte est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    typeComboBox?.Focus();
                    return;
                }

                if (conditionComboBox?.SelectedItem == null)
                {
                    MessageBox.Show("La condition de l'alerte est obligatoire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    conditionComboBox?.Focus();
                    return;
                }

                // Mettre à jour l'objet alerte
                _alerte.Nom = nomTextBox?.Text;
                _alerte.Description = descriptionTextBox?.Text;
                _alerte.Type = typeComboBox?.SelectedItem.ToString();
                _alerte.Condition = conditionComboBox?.SelectedItem.ToString();
                _alerte.Seuil = seuilNumericUpDown?.Value ?? 0;
                _alerte.EstActive = estActiveCheckBox?.Checked ?? true;
                _alerte.EnvoyerEmail = envoyerEmailCheckBox?.Checked ?? false;
                _alerte.FrequenceNotificationHeures = (int)(frequenceNumericUpDown?.Value ?? 24);

                // Enregistrer l'alerte
                if (_isEditMode)
                {
                    await _alerteService.UpdateAsync(_alerte, _currentUserId);
                    MessageBox.Show("L'alerte a été modifiée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _alerteService.CreateAsync(_alerte, _currentUserId);
                    MessageBox.Show("L'alerte a été créée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Fermer le formulaire
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement de l'alerte");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement de l'alerte : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void TypeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                var typeComboBox = sender as ComboBox;
                var seuilNumericUpDown = this.Controls.Find("seuilNumericUpDown", true)[0] as NumericUpDown;
                var seuilLabel = this.Controls.Find("seuilLabel", true)[0] as Label;

                if (typeComboBox != null && seuilNumericUpDown != null && seuilLabel != null)
                {
                    string selectedType = typeComboBox.SelectedItem?.ToString();

                    switch (selectedType)
                    {
                        case TypeAlerte.MontantFacturesEnRetard:
                            seuilLabel.Text = "Seuil (€) :";
                            seuilNumericUpDown.DecimalPlaces = 2;
                            seuilNumericUpDown.Increment = 100;
                            seuilNumericUpDown.Maximum = 1000000;
                            seuilNumericUpDown.Value = 1000;
                            break;

                        case TypeAlerte.NombreFacturesEnRetard:
                            seuilLabel.Text = "Seuil (nombre) :";
                            seuilNumericUpDown.DecimalPlaces = 0;
                            seuilNumericUpDown.Increment = 1;
                            seuilNumericUpDown.Maximum = 1000;
                            seuilNumericUpDown.Value = 5;
                            break;

                        case TypeAlerte.AncienneteCreances:
                            seuilLabel.Text = "Seuil (jours) :";
                            seuilNumericUpDown.DecimalPlaces = 0;
                            seuilNumericUpDown.Increment = 1;
                            seuilNumericUpDown.Maximum = 365;
                            seuilNumericUpDown.Value = 30;
                            break;

                        case TypeAlerte.TauxRecouvrement:
                            seuilLabel.Text = "Seuil (%) :";
                            seuilNumericUpDown.DecimalPlaces = 2;
                            seuilNumericUpDown.Increment = 1;
                            seuilNumericUpDown.Maximum = 100;
                            seuilNumericUpDown.Value = 80;
                            break;

                        case TypeAlerte.MontantCreancesClient:
                            seuilLabel.Text = "Seuil (€) :";
                            seuilNumericUpDown.DecimalPlaces = 2;
                            seuilNumericUpDown.Increment = 100;
                            seuilNumericUpDown.Maximum = 1000000;
                            seuilNumericUpDown.Value = 5000;
                            break;

                        case TypeAlerte.FactureEcheance:
                            seuilLabel.Text = "Jours avant échéance :";
                            seuilNumericUpDown.DecimalPlaces = 0;
                            seuilNumericUpDown.Increment = 1;
                            seuilNumericUpDown.Maximum = 30;
                            seuilNumericUpDown.Value = 7;
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du changement de type d'alerte");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
