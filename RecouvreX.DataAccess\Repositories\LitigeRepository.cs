using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les litiges
    /// </summary>
    public class LitigeRepository : BaseRepository<Litige>, ILitigeRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public LitigeRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "Litiges")
        {
        }

        /// <summary>
        /// Récupère un litige avec toutes ses informations associées
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Litige avec ses informations associées</returns>
        public async Task<Litige> GetWithDetailsAsync(int litigeId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT l.*, f.*, c.*, cl.*, el.*, u.*,
                           com.*, comu.*,
                           h.*, he.*, hu.*
                    FROM Litiges l
                    LEFT JOIN Factures f ON l.FactureId = f.Id
                    LEFT JOIN Clients c ON f.ClientId = c.Id
                    LEFT JOIN CategoriesLitige cl ON l.CategorieLitigeId = cl.Id
                    LEFT JOIN EtapesLitige el ON l.EtapeLitigeId = el.Id
                    LEFT JOIN Utilisateurs u ON l.ResponsableId = u.Id
                    LEFT JOIN CommentairesLitige com ON l.Id = com.LitigeId
                    LEFT JOIN Utilisateurs comu ON com.UtilisateurId = comu.Id
                    LEFT JOIN HistoriqueEtapesLitige h ON l.Id = h.LitigeId
                    LEFT JOIN EtapesLitige he ON h.EtapeLitigeId = he.Id
                    LEFT JOIN Utilisateurs hu ON h.UtilisateurId = hu.Id
                    WHERE l.Id = @LitigeId AND l.EstActif = 1";

                var litigeDictionary = new Dictionary<int, Litige>();
                var commentaireDictionary = new Dictionary<int, CommentaireLitige>();
                var historiqueDictionary = new Dictionary<int, HistoriqueEtapeLitige>();

                // Exécuter la requête de manière simplifiée
                var litige = await connection.QueryFirstOrDefaultAsync<Litige>(
                    "SELECT * FROM Litiges WHERE Id = @LitigeId AND EstActif = 1",
                    new { LitigeId = litigeId });

                if (litige != null)
                {
                    // Charger la facture et le client
                    var facture = await connection.QueryFirstOrDefaultAsync<Facture>(
                        "SELECT * FROM Factures WHERE Id = @FactureId AND EstActif = 1",
                        new { FactureId = litige.FactureId });

                    if (facture != null)
                    {
                        litige.Facture = facture;

                        var client = await connection.QueryFirstOrDefaultAsync<Client>(
                            "SELECT * FROM Clients WHERE Id = @ClientId AND EstActif = 1",
                            new { ClientId = facture.ClientId });

                        facture.Client = client;
                    }

                    // Charger la catégorie
                    var categorie = await connection.QueryFirstOrDefaultAsync<CategorieLitige>(
                        "SELECT * FROM CategoriesLitige WHERE Id = @CategorieId AND EstActif = 1",
                        new { CategorieId = litige.CategorieLitigeId });

                    litige.CategorieLitige = categorie;

                    // Charger l'étape
                    var etape = await connection.QueryFirstOrDefaultAsync<EtapeLitige>(
                        "SELECT * FROM EtapesLitige WHERE Id = @EtapeId AND EstActif = 1",
                        new { EtapeId = litige.EtapeLitigeId });

                    litige.EtapeLitige = etape;

                    // Charger le responsable
                    var responsable = await connection.QueryFirstOrDefaultAsync<Utilisateur>(
                        "SELECT * FROM Utilisateurs WHERE Id = @ResponsableId AND EstActif = 1",
                        new { ResponsableId = litige.ResponsableId });

                    litige.Responsable = responsable;

                    // Charger les commentaires
                    var commentaires = await connection.QueryAsync<CommentaireLitige, Utilisateur, CommentaireLitige>(
                        "SELECT c.*, u.* FROM CommentairesLitige c " +
                        "LEFT JOIN Utilisateurs u ON c.UtilisateurId = u.Id " +
                        "WHERE c.LitigeId = @LitigeId AND c.EstActif = 1",
                        (commentaire, utilisateur) => {
                            commentaire.Utilisateur = utilisateur;
                            return commentaire;
                        },
                        new { LitigeId = litigeId },
                        splitOn: "Id");

                    litige.Commentaires = commentaires.ToList();

                    // Charger l'historique des étapes
                    var historique = await connection.QueryAsync<HistoriqueEtapeLitige, EtapeLitige, Utilisateur, HistoriqueEtapeLitige>(
                        "SELECT h.*, e.*, u.* FROM HistoriqueEtapesLitige h " +
                        "LEFT JOIN EtapesLitige e ON h.EtapeLitigeId = e.Id " +
                        "LEFT JOIN Utilisateurs u ON h.UtilisateurId = u.Id " +
                        "WHERE h.LitigeId = @LitigeId AND h.EstActif = 1",
                        (historique, etape, utilisateur) => {
                            historique.EtapeLitige = etape;
                            historique.Utilisateur = utilisateur;
                            return historique;
                        },
                        new { LitigeId = litigeId },
                        splitOn: "Id,Id");

                    litige.HistoriqueEtapes = historique.ToList();
                }

                return litige;
            }
        }

        /// <summary>
        /// Récupère tous les litiges d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des litiges de la facture</returns>
        public async Task<IEnumerable<Litige>> GetByFactureIdAsync(int factureId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT l.*, cl.*, el.*, u.*
                    FROM Litiges l
                    LEFT JOIN CategoriesLitige cl ON l.CategorieLitigeId = cl.Id
                    LEFT JOIN EtapesLitige el ON l.EtapeLitigeId = el.Id
                    LEFT JOIN Utilisateurs u ON l.ResponsableId = u.Id
                    WHERE l.FactureId = @FactureId AND l.EstActif = 1
                    ORDER BY l.DateCreation DESC";

                var litiges = await connection.QueryAsync<Litige, CategorieLitige, EtapeLitige, Utilisateur, Litige>(
                    query,
                    (litige, categorie, etape, responsable) =>
                    {
                        litige.CategorieLitige = categorie;
                        litige.EtapeLitige = etape;
                        litige.Responsable = responsable;
                        return litige;
                    },
                    new { FactureId = factureId },
                    splitOn: "Id,Id,Id");

                return litiges;
            }
        }

        /// <summary>
        /// Récupère tous les litiges d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des litiges du client</returns>
        public async Task<IEnumerable<Litige>> GetByClientIdAsync(int clientId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT l.*, f.*, cl.*, el.*, u.*
                    FROM Litiges l
                    INNER JOIN Factures f ON l.FactureId = f.Id
                    LEFT JOIN CategoriesLitige cl ON l.CategorieLitigeId = cl.Id
                    LEFT JOIN EtapesLitige el ON l.EtapeLitigeId = el.Id
                    LEFT JOIN Utilisateurs u ON l.ResponsableId = u.Id
                    WHERE f.ClientId = @ClientId AND l.EstActif = 1
                    ORDER BY l.DateCreation DESC";

                var litiges = await connection.QueryAsync<Litige, Facture, CategorieLitige, EtapeLitige, Utilisateur, Litige>(
                    query,
                    (litige, facture, categorie, etape, responsable) =>
                    {
                        litige.Facture = facture;
                        litige.CategorieLitige = categorie;
                        litige.EtapeLitige = etape;
                        litige.Responsable = responsable;
                        return litige;
                    },
                    new { ClientId = clientId },
                    splitOn: "Id,Id,Id,Id");

                return litiges;
            }
        }

        /// <summary>
        /// Récupère tous les litiges par statut
        /// </summary>
        /// <param name="statut">Statut des litiges</param>
        /// <returns>Liste des litiges ayant le statut spécifié</returns>
        public async Task<IEnumerable<Litige>> GetByStatutAsync(string statut)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT l.*, f.*, cl.*, el.*, u.*
                    FROM Litiges l
                    LEFT JOIN Factures f ON l.FactureId = f.Id
                    LEFT JOIN CategoriesLitige cl ON l.CategorieLitigeId = cl.Id
                    LEFT JOIN EtapesLitige el ON l.EtapeLitigeId = el.Id
                    LEFT JOIN Utilisateurs u ON l.ResponsableId = u.Id
                    WHERE l.Statut = @Statut AND l.EstActif = 1
                    ORDER BY l.DateCreation DESC";

                var litiges = await connection.QueryAsync<Litige, Facture, CategorieLitige, EtapeLitige, Utilisateur, Litige>(
                    query,
                    (litige, facture, categorie, etape, responsable) =>
                    {
                        litige.Facture = facture;
                        litige.CategorieLitige = categorie;
                        litige.EtapeLitige = etape;
                        litige.Responsable = responsable;
                        return litige;
                    },
                    new { Statut = statut },
                    splitOn: "Id,Id,Id,Id");

                return litiges;
            }
        }

        /// <summary>
        /// Récupère tous les litiges par catégorie
        /// </summary>
        /// <param name="categorieId">Identifiant de la catégorie</param>
        /// <returns>Liste des litiges de la catégorie spécifiée</returns>
        public async Task<IEnumerable<Litige>> GetByCategorieIdAsync(int categorieId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT l.*, f.*, cl.*, el.*, u.*
                    FROM Litiges l
                    LEFT JOIN Factures f ON l.FactureId = f.Id
                    LEFT JOIN CategoriesLitige cl ON l.CategorieLitigeId = cl.Id
                    LEFT JOIN EtapesLitige el ON l.EtapeLitigeId = el.Id
                    LEFT JOIN Utilisateurs u ON l.ResponsableId = u.Id
                    WHERE l.CategorieLitigeId = @CategorieId AND l.EstActif = 1
                    ORDER BY l.DateCreation DESC";

                var litiges = await connection.QueryAsync<Litige, Facture, CategorieLitige, EtapeLitige, Utilisateur, Litige>(
                    query,
                    (litige, facture, categorie, etape, responsable) =>
                    {
                        litige.Facture = facture;
                        litige.CategorieLitige = categorie;
                        litige.EtapeLitige = etape;
                        litige.Responsable = responsable;
                        return litige;
                    },
                    new { CategorieId = categorieId },
                    splitOn: "Id,Id,Id,Id");

                return litiges;
            }
        }

        /// <summary>
        /// Récupère tous les litiges par étape
        /// </summary>
        /// <param name="etapeId">Identifiant de l'étape</param>
        /// <returns>Liste des litiges à l'étape spécifiée</returns>
        public async Task<IEnumerable<Litige>> GetByEtapeIdAsync(int etapeId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT l.*, f.*, cl.*, el.*, u.*
                    FROM Litiges l
                    LEFT JOIN Factures f ON l.FactureId = f.Id
                    LEFT JOIN CategoriesLitige cl ON l.CategorieLitigeId = cl.Id
                    LEFT JOIN EtapesLitige el ON l.EtapeLitigeId = el.Id
                    LEFT JOIN Utilisateurs u ON l.ResponsableId = u.Id
                    WHERE l.EtapeLitigeId = @EtapeId AND l.EstActif = 1
                    ORDER BY l.DateCreation DESC";

                var litiges = await connection.QueryAsync<Litige, Facture, CategorieLitige, EtapeLitige, Utilisateur, Litige>(
                    query,
                    (litige, facture, categorie, etape, responsable) =>
                    {
                        litige.Facture = facture;
                        litige.CategorieLitige = categorie;
                        litige.EtapeLitige = etape;
                        litige.Responsable = responsable;
                        return litige;
                    },
                    new { EtapeId = etapeId },
                    splitOn: "Id,Id,Id,Id");

                return litiges;
            }
        }

        /// <summary>
        /// Récupère tous les litiges assignés à un responsable
        /// </summary>
        /// <param name="responsableId">Identifiant du responsable</param>
        /// <returns>Liste des litiges assignés au responsable spécifié</returns>
        public async Task<IEnumerable<Litige>> GetByResponsableIdAsync(int responsableId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT l.*, f.*, cl.*, el.*, u.*
                    FROM Litiges l
                    LEFT JOIN Factures f ON l.FactureId = f.Id
                    LEFT JOIN CategoriesLitige cl ON l.CategorieLitigeId = cl.Id
                    LEFT JOIN EtapesLitige el ON l.EtapeLitigeId = el.Id
                    LEFT JOIN Utilisateurs u ON l.ResponsableId = u.Id
                    WHERE l.ResponsableId = @ResponsableId AND l.EstActif = 1
                    ORDER BY l.DateCreation DESC";

                var litiges = await connection.QueryAsync<Litige, Facture, CategorieLitige, EtapeLitige, Utilisateur, Litige>(
                    query,
                    (litige, facture, categorie, etape, responsable) =>
                    {
                        litige.Facture = facture;
                        litige.CategorieLitige = categorie;
                        litige.EtapeLitige = etape;
                        litige.Responsable = responsable;
                        return litige;
                    },
                    new { ResponsableId = responsableId },
                    splitOn: "Id,Id,Id,Id");

                return litiges;
            }
        }

        /// <summary>
        /// Récupère tous les litiges dont la date d'échéance est dépassée
        /// </summary>
        /// <returns>Liste des litiges en retard</returns>
        public async Task<IEnumerable<Litige>> GetOverdueAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT l.*, f.*, cl.*, el.*, u.*
                    FROM Litiges l
                    LEFT JOIN Factures f ON l.FactureId = f.Id
                    LEFT JOIN CategoriesLitige cl ON l.CategorieLitigeId = cl.Id
                    LEFT JOIN EtapesLitige el ON l.EtapeLitigeId = el.Id
                    LEFT JOIN Utilisateurs u ON l.ResponsableId = u.Id
                    WHERE l.DateEcheance < GETDATE() AND l.DateResolution IS NULL AND l.EstActif = 1
                    ORDER BY l.DateEcheance ASC";

                var litiges = await connection.QueryAsync<Litige, Facture, CategorieLitige, EtapeLitige, Utilisateur, Litige>(
                    query,
                    (litige, facture, categorie, etape, responsable) =>
                    {
                        litige.Facture = facture;
                        litige.CategorieLitige = categorie;
                        litige.EtapeLitige = etape;
                        litige.Responsable = responsable;
                        return litige;
                    },
                    splitOn: "Id,Id,Id,Id");

                return litiges;
            }
        }

        /// <summary>
        /// Récupère tous les litiges créés ou modifiés dans une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des litiges créés ou modifiés dans la période spécifiée</returns>
        public async Task<IEnumerable<Litige>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT l.*, f.*, cl.*, el.*, u.*
                    FROM Litiges l
                    LEFT JOIN Factures f ON l.FactureId = f.Id
                    LEFT JOIN CategoriesLitige cl ON l.CategorieLitigeId = cl.Id
                    LEFT JOIN EtapesLitige el ON l.EtapeLitigeId = el.Id
                    LEFT JOIN Utilisateurs u ON l.ResponsableId = u.Id
                    WHERE (l.DateCreation BETWEEN @DateDebut AND @DateFin OR l.DateModification BETWEEN @DateDebut AND @DateFin) AND l.EstActif = 1
                    ORDER BY l.DateCreation DESC";

                var litiges = await connection.QueryAsync<Litige, Facture, CategorieLitige, EtapeLitige, Utilisateur, Litige>(
                    query,
                    (litige, facture, categorie, etape, responsable) =>
                    {
                        litige.Facture = facture;
                        litige.CategorieLitige = categorie;
                        litige.EtapeLitige = etape;
                        litige.Responsable = responsable;
                        return litige;
                    },
                    new { DateDebut = dateDebut, DateFin = dateFin },
                    splitOn: "Id,Id,Id,Id");

                return litiges;
            }
        }

        /// <summary>
        /// Ajoute un commentaire à un litige
        /// </summary>
        /// <param name="commentaire">Commentaire à ajouter</param>
        /// <returns>Commentaire ajouté</returns>
        public async Task<CommentaireLitige> AddCommentaireAsync(CommentaireLitige commentaire)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    INSERT INTO CommentairesLitige (LitigeId, Contenu, DateCommentaire, UtilisateurId, EstInterne, DateCreation, CreePar, EstActif)
                    VALUES (@LitigeId, @Contenu, @DateCommentaire, @UtilisateurId, @EstInterne, GETDATE(), @CreePar, 1);
                    SELECT CAST(SCOPE_IDENTITY() as int)";

                var id = await connection.QuerySingleAsync<int>(query, new
                {
                    commentaire.LitigeId,
                    commentaire.Contenu,
                    commentaire.DateCommentaire,
                    commentaire.UtilisateurId,
                    commentaire.EstInterne,
                    CreePar = commentaire.UtilisateurId
                });

                commentaire.Id = id;
                commentaire.DateCreation = DateTime.Now;
                commentaire.CreePar = commentaire.UtilisateurId;
                commentaire.EstActif = true;

                return commentaire;
            }
        }

        /// <summary>
        /// Récupère tous les commentaires d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des commentaires du litige</returns>
        public async Task<IEnumerable<CommentaireLitige>> GetCommentairesAsync(int litigeId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT c.*, u.*
                    FROM CommentairesLitige c
                    LEFT JOIN Utilisateurs u ON c.UtilisateurId = u.Id
                    WHERE c.LitigeId = @LitigeId AND c.EstActif = 1
                    ORDER BY c.DateCommentaire DESC";

                var commentaires = await connection.QueryAsync<CommentaireLitige, Utilisateur, CommentaireLitige>(
                    query,
                    (commentaire, utilisateur) =>
                    {
                        commentaire.Utilisateur = utilisateur;
                        return commentaire;
                    },
                    new { LitigeId = litigeId },
                    splitOn: "Id");

                return commentaires;
            }
        }

        /// <summary>
        /// Ajoute une entrée dans l'historique des étapes d'un litige
        /// </summary>
        /// <param name="historiqueEtape">Historique d'étape à ajouter</param>
        /// <returns>Historique d'étape ajouté</returns>
        public async Task<HistoriqueEtapeLitige> AddHistoriqueEtapeAsync(HistoriqueEtapeLitige historiqueEtape)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    INSERT INTO HistoriqueEtapesLitige (LitigeId, EtapeLitigeId, DateDebut, DateFin, UtilisateurId, Commentaire, DateCreation, CreePar, EstActif)
                    VALUES (@LitigeId, @EtapeLitigeId, @DateDebut, @DateFin, @UtilisateurId, @Commentaire, GETDATE(), @CreePar, 1);
                    SELECT CAST(SCOPE_IDENTITY() as int)";

                var id = await connection.QuerySingleAsync<int>(query, new
                {
                    historiqueEtape.LitigeId,
                    historiqueEtape.EtapeLitigeId,
                    historiqueEtape.DateDebut,
                    historiqueEtape.DateFin,
                    historiqueEtape.UtilisateurId,
                    historiqueEtape.Commentaire,
                    CreePar = historiqueEtape.UtilisateurId
                });

                historiqueEtape.Id = id;
                historiqueEtape.DateCreation = DateTime.Now;
                historiqueEtape.CreePar = historiqueEtape.UtilisateurId;
                historiqueEtape.EstActif = true;

                return historiqueEtape;
            }
        }

        /// <summary>
        /// Récupère l'historique des étapes d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des historiques d'étapes du litige</returns>
        public async Task<IEnumerable<HistoriqueEtapeLitige>> GetHistoriqueEtapesAsync(int litigeId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT h.*, e.*, u.*
                    FROM HistoriqueEtapesLitige h
                    LEFT JOIN EtapesLitige e ON h.EtapeLitigeId = e.Id
                    LEFT JOIN Utilisateurs u ON h.UtilisateurId = u.Id
                    WHERE h.LitigeId = @LitigeId AND h.EstActif = 1
                    ORDER BY h.DateDebut DESC";

                var historiques = await connection.QueryAsync<HistoriqueEtapeLitige, EtapeLitige, Utilisateur, HistoriqueEtapeLitige>(
                    query,
                    (historique, etape, utilisateur) =>
                    {
                        historique.EtapeLitige = etape;
                        historique.Utilisateur = utilisateur;
                        return historique;
                    },
                    new { LitigeId = litigeId },
                    splitOn: "Id,Id");

                return historiques;
            }
        }

        /// <summary>
        /// Met à jour le statut d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateStatutAsync(int litigeId, string statut, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE Litiges
                    SET Statut = @Statut, DateModification = GETDATE(), ModifiePar = @ModifiePar
                    WHERE Id = @LitigeId AND EstActif = 1";

                var result = await connection.ExecuteAsync(query, new
                {
                    LitigeId = litigeId,
                    Statut = statut,
                    ModifiePar = modifiePar
                });

                return result > 0;
            }
        }

        /// <summary>
        /// Met à jour l'étape d'un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <param name="etapeId">Identifiant de la nouvelle étape</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <param name="commentaire">Commentaire associé au changement d'étape</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateEtapeAsync(int litigeId, int etapeId, int modifiePar, string commentaire)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Commencer une transaction
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Mettre à jour l'étape du litige
                        var updateQuery = @"
                            UPDATE Litiges
                            SET EtapeLitigeId = @EtapeId, DateModification = GETDATE(), ModifiePar = @ModifiePar
                            WHERE Id = @LitigeId AND EstActif = 1";

                        await connection.ExecuteAsync(updateQuery, new
                        {
                            LitigeId = litigeId,
                            EtapeId = etapeId,
                            ModifiePar = modifiePar
                        }, transaction);

                        // Fermer l'étape précédente dans l'historique
                        var closeQuery = @"
                            UPDATE HistoriqueEtapesLitige
                            SET DateFin = GETDATE(), DateModification = GETDATE(), ModifiePar = @ModifiePar
                            WHERE LitigeId = @LitigeId AND DateFin IS NULL AND EstActif = 1";

                        await connection.ExecuteAsync(closeQuery, new
                        {
                            LitigeId = litigeId,
                            ModifiePar = modifiePar
                        }, transaction);

                        // Ajouter une nouvelle entrée dans l'historique
                        var insertQuery = @"
                            INSERT INTO HistoriqueEtapesLitige (LitigeId, EtapeLitigeId, DateDebut, UtilisateurId, Commentaire, DateCreation, CreePar, EstActif)
                            VALUES (@LitigeId, @EtapeId, GETDATE(), @UtilisateurId, @Commentaire, GETDATE(), @CreePar, 1)";

                        await connection.ExecuteAsync(insertQuery, new
                        {
                            LitigeId = litigeId,
                            EtapeId = etapeId,
                            UtilisateurId = modifiePar,
                            Commentaire = commentaire,
                            CreePar = modifiePar
                        }, transaction);

                        // Valider la transaction
                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        // Annuler la transaction en cas d'erreur
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }
    }
}
