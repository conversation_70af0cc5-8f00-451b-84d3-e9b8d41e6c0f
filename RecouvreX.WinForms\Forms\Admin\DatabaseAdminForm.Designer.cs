using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Admin
{
    partial class DatabaseAdminForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles
        private TabControl tabControl;
        private TabPage infoTabPage;
        private TabPage backupTabPage;
        private TabPage sqlTabPage;
        private Label titleLabel;
        private Label databaseNameLabel;
        private Label createDateLabel;
        private Label sizeLabel;
        private Label tableCountLabel;
        private Label rowCountLabel;
        private ListBox tablesListBox;
        private Button backupButton;
        private Button restoreButton;
        private Button checkIntegrityButton;
        private ComboBox scheduleComboBox;
        private DateTimePicker backupTimeTimePicker;
        private Button scheduleBackupButton;
        private TextBox sqlTextBox;
        private Button executeQueryButton;
        private DataGridView resultDataGridView;
        private TextBox resultTextBox;
        private Button closeButton;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            
            // Définir les propriétés du formulaire
            this.Text = "Administration de la base de données";
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Load += new EventHandler(this.DatabaseAdminForm_Load);
            
            // Titre
            this.titleLabel = new Label();
            this.titleLabel.Text = "Administration de la base de données";
            this.titleLabel.Font = new Font(this.Font.FontFamily, 14, FontStyle.Bold);
            this.titleLabel.Location = new Point(20, 20);
            this.titleLabel.Size = new Size(760, 30);
            this.Controls.Add(this.titleLabel);
            
            // TabControl
            this.tabControl = new TabControl();
            this.tabControl.Location = new Point(20, 60);
            this.tabControl.Size = new Size(760, 480);
            this.Controls.Add(this.tabControl);
            
            // Onglet Informations
            this.infoTabPage = new TabPage();
            this.infoTabPage.Text = "Informations";
            this.tabControl.TabPages.Add(this.infoTabPage);
            
            // Informations sur la base de données
            Label databaseNameTitleLabel = new Label();
            databaseNameTitleLabel.Text = "Nom de la base de données :";
            databaseNameTitleLabel.Location = new Point(20, 20);
            databaseNameTitleLabel.Size = new Size(200, 20);
            this.infoTabPage.Controls.Add(databaseNameTitleLabel);
            
            this.databaseNameLabel = new Label();
            this.databaseNameLabel.Location = new Point(230, 20);
            this.databaseNameLabel.Size = new Size(300, 20);
            this.infoTabPage.Controls.Add(this.databaseNameLabel);
            
            Label createDateTitleLabel = new Label();
            createDateTitleLabel.Text = "Date de création :";
            createDateTitleLabel.Location = new Point(20, 50);
            createDateTitleLabel.Size = new Size(200, 20);
            this.infoTabPage.Controls.Add(createDateTitleLabel);
            
            this.createDateLabel = new Label();
            this.createDateLabel.Location = new Point(230, 50);
            this.createDateLabel.Size = new Size(300, 20);
            this.infoTabPage.Controls.Add(this.createDateLabel);
            
            Label sizeTitleLabel = new Label();
            sizeTitleLabel.Text = "Taille :";
            sizeTitleLabel.Location = new Point(20, 80);
            sizeTitleLabel.Size = new Size(200, 20);
            this.infoTabPage.Controls.Add(sizeTitleLabel);
            
            this.sizeLabel = new Label();
            this.sizeLabel.Location = new Point(230, 80);
            this.sizeLabel.Size = new Size(300, 20);
            this.infoTabPage.Controls.Add(this.sizeLabel);
            
            Label tableCountTitleLabel = new Label();
            tableCountTitleLabel.Text = "Nombre de tables :";
            tableCountTitleLabel.Location = new Point(20, 110);
            tableCountTitleLabel.Size = new Size(200, 20);
            this.infoTabPage.Controls.Add(tableCountTitleLabel);
            
            this.tableCountLabel = new Label();
            this.tableCountLabel.Location = new Point(230, 110);
            this.tableCountLabel.Size = new Size(300, 20);
            this.infoTabPage.Controls.Add(this.tableCountLabel);
            
            Label rowCountTitleLabel = new Label();
            rowCountTitleLabel.Text = "Nombre total de lignes :";
            rowCountTitleLabel.Location = new Point(20, 140);
            rowCountTitleLabel.Size = new Size(200, 20);
            this.infoTabPage.Controls.Add(rowCountTitleLabel);
            
            this.rowCountLabel = new Label();
            this.rowCountLabel.Location = new Point(230, 140);
            this.rowCountLabel.Size = new Size(300, 20);
            this.infoTabPage.Controls.Add(this.rowCountLabel);
            
            Label tablesListTitleLabel = new Label();
            tablesListTitleLabel.Text = "Liste des tables :";
            tablesListTitleLabel.Location = new Point(20, 180);
            tablesListTitleLabel.Size = new Size(200, 20);
            this.infoTabPage.Controls.Add(tablesListTitleLabel);
            
            this.tablesListBox = new ListBox();
            this.tablesListBox.Location = new Point(20, 210);
            this.tablesListBox.Size = new Size(700, 200);
            this.infoTabPage.Controls.Add(this.tablesListBox);
            
            // Onglet Sauvegarde et restauration
            this.backupTabPage = new TabPage();
            this.backupTabPage.Text = "Sauvegarde et restauration";
            this.tabControl.TabPages.Add(this.backupTabPage);
            
            // Sauvegarde manuelle
            GroupBox manualBackupGroupBox = new GroupBox();
            manualBackupGroupBox.Text = "Sauvegarde et restauration manuelles";
            manualBackupGroupBox.Location = new Point(20, 20);
            manualBackupGroupBox.Size = new Size(700, 100);
            this.backupTabPage.Controls.Add(manualBackupGroupBox);
            
            this.backupButton = new Button();
            this.backupButton.Text = "Sauvegarder la base de données";
            this.backupButton.Location = new Point(20, 30);
            this.backupButton.Size = new Size(200, 30);
            this.backupButton.Click += new EventHandler(this.BackupButton_Click);
            manualBackupGroupBox.Controls.Add(this.backupButton);
            
            this.restoreButton = new Button();
            this.restoreButton.Text = "Restaurer une sauvegarde";
            this.restoreButton.Location = new Point(240, 30);
            this.restoreButton.Size = new Size(200, 30);
            this.restoreButton.Click += new EventHandler(this.RestoreButton_Click);
            manualBackupGroupBox.Controls.Add(this.restoreButton);
            
            this.checkIntegrityButton = new Button();
            this.checkIntegrityButton.Text = "Vérifier l'intégrité";
            this.checkIntegrityButton.Location = new Point(460, 30);
            this.checkIntegrityButton.Size = new Size(200, 30);
            this.checkIntegrityButton.Click += new EventHandler(this.CheckIntegrityButton_Click);
            manualBackupGroupBox.Controls.Add(this.checkIntegrityButton);
            
            // Sauvegarde automatique
            GroupBox autoBackupGroupBox = new GroupBox();
            autoBackupGroupBox.Text = "Sauvegarde automatique";
            autoBackupGroupBox.Location = new Point(20, 140);
            autoBackupGroupBox.Size = new Size(700, 150);
            this.backupTabPage.Controls.Add(autoBackupGroupBox);
            
            Label scheduleLabel = new Label();
            scheduleLabel.Text = "Fréquence :";
            scheduleLabel.Location = new Point(20, 30);
            scheduleLabel.Size = new Size(100, 20);
            autoBackupGroupBox.Controls.Add(scheduleLabel);
            
            this.scheduleComboBox = new ComboBox();
            this.scheduleComboBox.Location = new Point(130, 30);
            this.scheduleComboBox.Size = new Size(200, 20);
            this.scheduleComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.scheduleComboBox.Items.AddRange(new object[] { "Quotidienne", "Hebdomadaire", "Mensuelle" });
            this.scheduleComboBox.SelectedIndex = 0;
            autoBackupGroupBox.Controls.Add(this.scheduleComboBox);
            
            Label timeLabel = new Label();
            timeLabel.Text = "Heure :";
            timeLabel.Location = new Point(20, 70);
            timeLabel.Size = new Size(100, 20);
            autoBackupGroupBox.Controls.Add(timeLabel);
            
            this.backupTimeTimePicker = new DateTimePicker();
            this.backupTimeTimePicker.Location = new Point(130, 70);
            this.backupTimeTimePicker.Size = new Size(200, 20);
            this.backupTimeTimePicker.Format = DateTimePickerFormat.Time;
            this.backupTimeTimePicker.ShowUpDown = true;
            this.backupTimeTimePicker.Value = DateTime.Today.AddHours(23);
            autoBackupGroupBox.Controls.Add(this.backupTimeTimePicker);
            
            this.scheduleBackupButton = new Button();
            this.scheduleBackupButton.Text = "Planifier une sauvegarde automatique";
            this.scheduleBackupButton.Location = new Point(20, 110);
            this.scheduleBackupButton.Size = new Size(250, 30);
            this.scheduleBackupButton.Click += new EventHandler(this.ScheduleBackupButton_Click);
            autoBackupGroupBox.Controls.Add(this.scheduleBackupButton);
            
            // Onglet SQL
            this.sqlTabPage = new TabPage();
            this.sqlTabPage.Text = "Requêtes SQL";
            this.tabControl.TabPages.Add(this.sqlTabPage);
            
            Label sqlLabel = new Label();
            sqlLabel.Text = "Requête SQL :";
            sqlLabel.Location = new Point(20, 20);
            sqlLabel.Size = new Size(100, 20);
            this.sqlTabPage.Controls.Add(sqlLabel);
            
            this.sqlTextBox = new TextBox();
            this.sqlTextBox.Location = new Point(20, 50);
            this.sqlTextBox.Size = new Size(700, 100);
            this.sqlTextBox.Multiline = true;
            this.sqlTextBox.ScrollBars = ScrollBars.Vertical;
            this.sqlTabPage.Controls.Add(this.sqlTextBox);
            
            this.executeQueryButton = new Button();
            this.executeQueryButton.Text = "Exécuter";
            this.executeQueryButton.Location = new Point(620, 160);
            this.executeQueryButton.Size = new Size(100, 30);
            this.executeQueryButton.Click += new EventHandler(this.ExecuteQueryButton_Click);
            this.sqlTabPage.Controls.Add(this.executeQueryButton);
            
            Label resultLabel = new Label();
            resultLabel.Text = "Résultat :";
            resultLabel.Location = new Point(20, 200);
            resultLabel.Size = new Size(100, 20);
            this.sqlTabPage.Controls.Add(resultLabel);
            
            this.resultDataGridView = new DataGridView();
            this.resultDataGridView.Location = new Point(20, 230);
            this.resultDataGridView.Size = new Size(700, 150);
            this.resultDataGridView.AllowUserToAddRows = false;
            this.resultDataGridView.AllowUserToDeleteRows = false;
            this.resultDataGridView.ReadOnly = true;
            this.resultDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.sqlTabPage.Controls.Add(this.resultDataGridView);
            
            this.resultTextBox = new TextBox();
            this.resultTextBox.Location = new Point(20, 390);
            this.resultTextBox.Size = new Size(700, 50);
            this.resultTextBox.Multiline = true;
            this.resultTextBox.ScrollBars = ScrollBars.Vertical;
            this.resultTextBox.ReadOnly = true;
            this.sqlTabPage.Controls.Add(this.resultTextBox);
            
            // Bouton Fermer
            this.closeButton = new Button();
            this.closeButton.Text = "Fermer";
            this.closeButton.Location = new Point(680, 550);
            this.closeButton.Size = new Size(100, 30);
            this.closeButton.Click += new EventHandler(this.CloseButton_Click);
            this.Controls.Add(this.closeButton);
        }

        #endregion
    }
}
