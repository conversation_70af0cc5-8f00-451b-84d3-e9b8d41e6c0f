using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Drawing.Design;

namespace RecouvreX.WinForms.Forms.Litiges
{
    /// <summary>
    /// Formulaire d'édition d'une catégorie de litige
    /// </summary>
    public partial class CategorieLitigeEditForm : Form
    {
        private readonly ILitigeService _litigeService;
        private readonly int _currentUserId;
        private CategorieLitige? _categorie;
        private bool _isNewCategorie = true;
        private ColorDialog _colorDialog = new ColorDialog();

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="litigeService">Service de gestion des litiges</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        /// <param name="categorie">Catégorie à éditer (null pour une nouvelle catégorie)</param>
        public CategorieLitigeEditForm(ILitigeService litigeService, int currentUserId, CategorieLitige? categorie = null)
        {
            _litigeService = litigeService ?? throw new ArgumentNullException(nameof(litigeService));
            _currentUserId = currentUserId;
            _categorie = categorie;
            _isNewCategorie = categorie == null;

            InitializeComponent();
        }

        // La méthode InitializeComponent() a été déplacée dans le fichier CategorieLitigeEditForm.Designer.cs

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private void CategorieLitigeEditForm_Load(object sender, EventArgs e)
        {
            // Définir le titre du formulaire et du label de titre
            this.Text = _isNewCategorie ? "Nouvelle catégorie de litige" : "Modifier une catégorie de litige";
            if (titleLabel != null)
            {
                titleLabel.Text = _isNewCategorie ? "Nouvelle catégorie de litige" : "Modifier une catégorie de litige";
            }

            // Initialiser le ColorDialog
            _colorDialog.AnyColor = true;
            _colorDialog.FullOpen = true;
            _colorDialog.Color = Color.LightGray;

            // Si c'est une modification, remplir les champs avec les données de la catégorie
            if (!_isNewCategorie && _categorie != null)
            {
                if (nomTextBox != null)
                    nomTextBox.Text = _categorie.Nom;

                if (descriptionTextBox != null)
                    descriptionTextBox.Text = _categorie.Description;

                if (delaiNumericUpDown != null)
                    delaiNumericUpDown.Value = _categorie.DelaiResolutionJours;

                if (!string.IsNullOrEmpty(_categorie.Couleur) && couleurPanel != null && couleurTextBox != null)
                {
                    try
                    {
                        Color couleur = ColorTranslator.FromHtml(_categorie.Couleur);
                        couleurPanel.BackColor = couleur;
                        couleurTextBox.Text = _categorie.Couleur;
                        _colorDialog.Color = couleur;
                    }
                    catch
                    {
                        // En cas d'erreur, utiliser la couleur par défaut
                    }
                }
            }

            // Mettre le focus sur le champ Nom
            if (nomTextBox != null)
            {
                nomTextBox.Focus();
            }
        }

        /// <summary>
        /// Événement de clic sur le panel de couleur
        /// </summary>
        private void CouleurPanel_Click(object sender, EventArgs e)
        {
            // Afficher le sélecteur de couleur
            if (_colorDialog.ShowDialog() == DialogResult.OK)
            {
                // Mettre à jour la couleur du panel
                var couleurPanel = this.Controls.Find("couleurPanel", true).FirstOrDefault() as Panel;
                var couleurTextBox = this.Controls.Find("couleurTextBox", true).FirstOrDefault() as TextBox;

                if (couleurPanel != null && couleurTextBox != null)
                {
                    couleurPanel.BackColor = _colorDialog.Color;

                    // Convertir la couleur en format hexadécimal
                    string couleurHex = $"#{_colorDialog.Color.R:X2}{_colorDialog.Color.G:X2}{_colorDialog.Color.B:X2}";
                    couleurTextBox.Text = couleurHex;
                }
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Enregistrer
        /// </summary>
        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer les valeurs des champs
                var nomTextBox = this.Controls.Find("nomTextBox", true).FirstOrDefault() as TextBox;
                var descriptionTextBox = this.Controls.Find("descriptionTextBox", true).FirstOrDefault() as TextBox;
                var delaiNumericUpDown = this.Controls.Find("delaiNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                var couleurTextBox = this.Controls.Find("couleurTextBox", true).FirstOrDefault() as TextBox;

                // Valider les champs obligatoires
                if (string.IsNullOrWhiteSpace(nomTextBox?.Text))
                {
                    MessageBox.Show("Le nom de la catégorie est obligatoire.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Créer ou mettre à jour la catégorie
                if (_isNewCategorie)
                {
                    // Créer une nouvelle catégorie
                    var categorie = new CategorieLitige
                    {
                        Nom = nomTextBox.Text,
                        Description = descriptionTextBox?.Text ?? string.Empty,
                        DelaiResolutionJours = (int)(delaiNumericUpDown?.Value ?? 7),
                        Couleur = couleurTextBox?.Text ?? "#CCCCCC"
                    };

                    // Enregistrer la catégorie
                    await _litigeService.CreateCategorieAsync(categorie, _currentUserId);
                }
                else
                {
                    // Mettre à jour la catégorie existante
                    _categorie.Nom = nomTextBox.Text;
                    _categorie.Description = descriptionTextBox?.Text ?? string.Empty;
                    _categorie.DelaiResolutionJours = (int)(delaiNumericUpDown?.Value ?? 7);
                    _categorie.Couleur = couleurTextBox?.Text ?? "#CCCCCC";

                    // Enregistrer les modifications
                    await _litigeService.UpdateCategorieAsync(_categorie, _currentUserId);
                }

                // Fermer le formulaire avec succès
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement de la catégorie de litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Annuler
        /// </summary>
        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
