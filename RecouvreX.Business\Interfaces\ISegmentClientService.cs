using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des segments client
    /// </summary>
    public interface ISegmentClientService
    {
        /// <summary>
        /// Récupère les clients par segment
        /// </summary>
        /// <param name="segment">Segment de client</param>
        /// <returns>Liste des clients du segment spécifié</returns>
        Task<IEnumerable<Client>> GetClientsBySegmentAsync(SegmentClient segment);

        /// <summary>
        /// Met à jour le segment d'un client manuellement
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="segment">Nouveau segment</param>
        /// <param name="commentaire">Commentaire sur la segmentation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Client mis à jour</returns>
        Task<Client> UpdateClientSegmentAsync(int clientId, SegmentClient segment, string commentaire, int modifiePar);

        /// <summary>
        /// Segmente automatiquement tous les clients
        /// </summary>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre de clients segmentés</returns>
        Task<int> SegmenterClientsAutomatiquementAsync(int modifiePar);

        /// <summary>
        /// Récupère la répartition des clients par segment
        /// </summary>
        /// <returns>Dictionnaire avec le segment comme clé et le nombre de clients comme valeur</returns>
        Task<Dictionary<SegmentClient, int>> GetClientDistributionBySegmentAsync();

        /// <summary>
        /// Calcule le segment d'un client en fonction de critères prédéfinis
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Segment calculé</returns>
        Task<SegmentClient> CalculerSegmentClientAsync(int clientId);
    }
}
