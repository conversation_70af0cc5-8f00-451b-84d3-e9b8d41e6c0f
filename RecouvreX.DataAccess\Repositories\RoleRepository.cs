using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les rôles
    /// </summary>
    public class RoleRepository : BaseRepository<Role>, IRoleRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public RoleRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "Roles")
        {
        }

        /// <summary>
        /// Récupère un rôle par son nom
        /// </summary>
        /// <param name="nom">Nom du rôle</param>
        /// <returns>Rôle trouvé ou null</returns>
        public async Task<Role> GetByNomAsync(string nom)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Roles WHERE Nom = @Nom AND EstActif = 1";
                return await connection.QueryFirstOrDefaultAsync<Role>(query, new { Nom = nom });
            }
        }

        /// <summary>
        /// Récupère un rôle avec ses permissions
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Rôle avec ses permissions</returns>
        public async Task<Role> GetWithPermissionsAsync(int roleId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT r.*, p.*
                    FROM Roles r
                    LEFT JOIN RolePermissions rp ON r.Id = rp.RoleId
                    LEFT JOIN Permissions p ON rp.PermissionId = p.Id
                    WHERE r.Id = @RoleId AND r.EstActif = 1";

                Role role = null;
                var rolePermissions = new Dictionary<int, List<Permission>>();

                var results = await connection.QueryAsync<Role, Permission, Role>(
                    query,
                    (r, p) =>
                    {
                        if (role == null)
                        {
                            role = r;
                            role.RolePermissions = new List<RolePermission>();
                        }

                        if (p != null)
                        {
                            var rolePermission = new RolePermission
                            {
                                RoleId = r.Id,
                                Role = r,
                                PermissionId = p.Id,
                                Permission = p
                            };

                            role.RolePermissions.Add(rolePermission);
                        }

                        return role;
                    },
                    new { RoleId = roleId },
                    splitOn: "Id");

                return results.FirstOrDefault();
            }
        }

        /// <summary>
        /// Récupère tous les rôles avec leurs permissions
        /// </summary>
        /// <returns>Liste des rôles avec leurs permissions</returns>
        public async Task<IEnumerable<Role>> GetAllWithPermissionsAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT r.*, p.*
                    FROM Roles r
                    LEFT JOIN RolePermissions rp ON r.Id = rp.RoleId
                    LEFT JOIN Permissions p ON rp.PermissionId = p.Id
                    WHERE r.EstActif = 1
                    ORDER BY r.Id";

                var roleDict = new Dictionary<int, Role>();

                var results = await connection.QueryAsync<Role, Permission, Role>(
                    query,
                    (role, permission) =>
                    {
                        if (!roleDict.TryGetValue(role.Id, out var existingRole))
                        {
                            existingRole = role;
                            existingRole.RolePermissions = new List<RolePermission>();
                            roleDict.Add(role.Id, existingRole);
                        }

                        if (permission != null)
                        {
                            var rolePermission = new RolePermission
                            {
                                RoleId = role.Id,
                                Role = role,
                                PermissionId = permission.Id,
                                Permission = permission
                            };

                            existingRole.RolePermissions.Add(rolePermission);
                        }

                        return existingRole;
                    },
                    splitOn: "Id");

                return roleDict.Values;
            }
        }

        /// <summary>
        /// Ajoute une permission à un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <param name="permissionId">Identifiant de la permission</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'ajout a réussi, sinon False</returns>
        public async Task<bool> AddPermissionAsync(int roleId, int permissionId, int userId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Vérifier si l'association existe déjà
                var checkQuery = @"
                    SELECT COUNT(*)
                    FROM RolePermissions
                    WHERE RoleId = @RoleId AND PermissionId = @PermissionId";

                var exists = await connection.ExecuteScalarAsync<int>(checkQuery, new { RoleId = roleId, PermissionId = permissionId }) > 0;

                if (exists)
                    return true; // L'association existe déjà

                // Ajouter l'association
                var insertQuery = @"
                    INSERT INTO RolePermissions (RoleId, PermissionId, DateCreation, CreePar, EstActif)
                    VALUES (@RoleId, @PermissionId, @DateCreation, @CreePar, 1)";

                var parameters = new
                {
                    RoleId = roleId,
                    PermissionId = permissionId,
                    DateCreation = DateTime.Now,
                    CreePar = userId
                };

                var result = await connection.ExecuteAsync(insertQuery, parameters);
                return result > 0;
            }
        }

        /// <summary>
        /// Supprime une permission d'un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <param name="permissionId">Identifiant de la permission</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> RemovePermissionAsync(int roleId, int permissionId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    DELETE FROM RolePermissions
                    WHERE RoleId = @RoleId AND PermissionId = @PermissionId";

                var parameters = new
                {
                    RoleId = roleId,
                    PermissionId = permissionId
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }

        /// <summary>
        /// Récupère le nombre d'utilisateurs associés à un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Nombre d'utilisateurs</returns>
        public async Task<int> GetUserCountByRoleIdAsync(int roleId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT COUNT(*)
                    FROM Utilisateurs
                    WHERE RoleId = @RoleId AND EstActif = 1";

                return await connection.ExecuteScalarAsync<int>(query, new { RoleId = roleId });
            }
        }

        /// <summary>
        /// Récupère le nombre de permissions associées à un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Nombre de permissions</returns>
        public async Task<int> GetPermissionCountByRoleIdAsync(int roleId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT COUNT(*)
                    FROM RolePermissions
                    WHERE RoleId = @RoleId AND EstActif = 1";

                return await connection.ExecuteScalarAsync<int>(query, new { RoleId = roleId });
            }
        }

        /// <summary>
        /// Met à jour les permissions d'un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <param name="permissionIds">Liste des identifiants des permissions à associer au rôle</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdatePermissionsAsync(int roleId, List<int> permissionIds, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Commencer une transaction
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Supprimer toutes les permissions existantes du rôle
                        var deleteQuery = @"
                            DELETE FROM RolePermissions
                            WHERE RoleId = @RoleId";

                        await connection.ExecuteAsync(deleteQuery, new { RoleId = roleId }, transaction);

                        // Ajouter les nouvelles permissions
                        if (permissionIds != null && permissionIds.Count > 0)
                        {
                            var insertQuery = @"
                                INSERT INTO RolePermissions (RoleId, PermissionId, DateCreation, CreePar, EstActif)
                                VALUES (@RoleId, @PermissionId, @DateCreation, @CreePar, 1)";

                            foreach (var permissionId in permissionIds)
                            {
                                var parameters = new
                                {
                                    RoleId = roleId,
                                    PermissionId = permissionId,
                                    DateCreation = DateTime.Now,
                                    CreePar = modifiePar
                                };

                                await connection.ExecuteAsync(insertQuery, parameters, transaction);
                            }
                        }

                        // Mettre à jour la date de modification du rôle
                        var updateRoleQuery = @"
                            UPDATE Roles
                            SET DateModification = @DateModification,
                                ModifiePar = @ModifiePar
                            WHERE Id = @RoleId";

                        await connection.ExecuteAsync(updateRoleQuery, new
                        {
                            RoleId = roleId,
                            DateModification = DateTime.Now,
                            ModifiePar = modifiePar
                        }, transaction);

                        // Valider la transaction
                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        // Annuler la transaction en cas d'erreur
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }
    }
}
