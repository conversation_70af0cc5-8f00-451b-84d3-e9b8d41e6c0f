using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les plans de paiement
    /// </summary>
    public class PlanPaiementRepository : BaseRepository<PlanPaiement>, IPlanPaiementRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public PlanPaiementRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "PlansPaiement")
        {
        }

        /// <summary>
        /// Récupère un plan de paiement par sa référence
        /// </summary>
        /// <param name="reference">Référence du plan de paiement</param>
        /// <returns>Plan de paiement trouvé ou null</returns>
        public async Task<PlanPaiement> GetByReferenceAsync(string reference)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = $"SELECT * FROM {_tableName} WHERE Reference = @Reference AND EstActif = 1";
                    return await connection.QueryFirstOrDefaultAsync<PlanPaiement>(query, new { Reference = reference });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération du plan de paiement par référence {Reference}", reference);
                throw;
            }
        }

        /// <summary>
        /// Récupère un plan de paiement avec ses échéances
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement avec ses échéances</returns>
        public async Task<PlanPaiement> GetWithEcheancesAsync(int planPaiementId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        SELECT p.*, e.*
                        FROM PlansPaiement p
                        LEFT JOIN EcheancesPaiement e ON p.Id = e.PlanPaiementId
                        WHERE p.Id = @PlanPaiementId AND p.EstActif = 1
                        ORDER BY e.NumeroOrdre";

                    PlanPaiement planPaiement = null;
                    var echeances = new List<EcheancePaiement>();

                    await connection.QueryAsync<PlanPaiement, EcheancePaiement, PlanPaiement>(
                        query,
                        (plan, echeance) =>
                        {
                            if (planPaiement == null)
                            {
                                planPaiement = plan;
                                planPaiement.Echeances = new List<EcheancePaiement>();
                            }

                            if (echeance != null && echeance.Id != 0)
                            {
                                echeance.PlanPaiement = planPaiement;
                                planPaiement.Echeances.Add(echeance);
                            }

                            return planPaiement;
                        },
                        new { PlanPaiementId = planPaiementId },
                        splitOn: "Id");

                    return planPaiement;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération du plan de paiement avec ses échéances {PlanPaiementId}", planPaiementId);
                throw;
            }
        }

        /// <summary>
        /// Récupère un plan de paiement avec ses factures associées
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement avec ses factures</returns>
        public async Task<PlanPaiement> GetWithFacturesAsync(int planPaiementId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        SELECT p.*, pf.*, f.*
                        FROM PlansPaiement p
                        LEFT JOIN PlanPaiementFactures pf ON p.Id = pf.PlanPaiementId
                        LEFT JOIN Factures f ON pf.FactureId = f.Id
                        WHERE p.Id = @PlanPaiementId AND p.EstActif = 1";

                    PlanPaiement planPaiement = null;
                    var factures = new Dictionary<int, Facture>();

                    await connection.QueryAsync<PlanPaiement, dynamic, Facture, PlanPaiement>(
                        query,
                        (plan, pf, facture) =>
                        {
                            if (planPaiement == null)
                            {
                                planPaiement = plan;
                                planPaiement.Factures = new List<Facture>();
                            }

                            if (facture != null && facture.Id != 0 && !factures.ContainsKey(facture.Id))
                            {
                                factures.Add(facture.Id, facture);
                                planPaiement.Factures.Add(facture);
                            }

                            return planPaiement;
                        },
                        new { PlanPaiementId = planPaiementId },
                        splitOn: "Id,Id");

                    return planPaiement;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération du plan de paiement avec ses factures {PlanPaiementId}", planPaiementId);
                throw;
            }
        }

        /// <summary>
        /// Récupère un plan de paiement avec ses documents associés
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement avec ses documents</returns>
        public async Task<PlanPaiement> GetWithDocumentsAsync(int planPaiementId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        SELECT p.*, pd.*, d.*
                        FROM PlansPaiement p
                        LEFT JOIN PlanPaiementDocuments pd ON p.Id = pd.PlanPaiementId
                        LEFT JOIN Documents d ON pd.DocumentId = d.Id
                        WHERE p.Id = @PlanPaiementId AND p.EstActif = 1";

                    PlanPaiement planPaiement = null;
                    var documents = new Dictionary<int, Document>();

                    await connection.QueryAsync<PlanPaiement, dynamic, Document, PlanPaiement>(
                        query,
                        (plan, pd, document) =>
                        {
                            if (planPaiement == null)
                            {
                                planPaiement = plan;
                                planPaiement.Documents = new List<Document>();
                            }

                            if (document != null && document.Id != 0 && !documents.ContainsKey(document.Id))
                            {
                                documents.Add(document.Id, document);
                                planPaiement.Documents.Add(document);
                            }

                            return planPaiement;
                        },
                        new { PlanPaiementId = planPaiementId },
                        splitOn: "Id,Id");

                    return planPaiement;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération du plan de paiement avec ses documents {PlanPaiementId}", planPaiementId);
                throw;
            }
        }

        /// <summary>
        /// Récupère un plan de paiement complet avec toutes ses relations
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement complet</returns>
        public async Task<PlanPaiement> GetCompleteAsync(int planPaiementId)
        {
            try
            {
                var planPaiement = await GetWithEcheancesAsync(planPaiementId);
                if (planPaiement == null)
                    return null;

                var planWithFactures = await GetWithFacturesAsync(planPaiementId);
                if (planWithFactures != null)
                    planPaiement.Factures = planWithFactures.Factures;

                var planWithDocuments = await GetWithDocumentsAsync(planPaiementId);
                if (planWithDocuments != null)
                    planPaiement.Documents = planWithDocuments.Documents;

                return planPaiement;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération du plan de paiement complet {PlanPaiementId}", planPaiementId);
                throw;
            }
        }

        /// <summary>
        /// Récupère tous les plans de paiement d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des plans de paiement du client</returns>
        public async Task<IEnumerable<PlanPaiement>> GetByClientIdAsync(int clientId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = $"SELECT * FROM {_tableName} WHERE ClientId = @ClientId AND EstActif = 1 ORDER BY DateCreationPlan DESC";
                    return await connection.QueryAsync<PlanPaiement>(query, new { ClientId = clientId });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des plans de paiement du client {ClientId}", clientId);
                throw;
            }
        }

        /// <summary>
        /// Récupère tous les plans de paiement associés à une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des plans de paiement associés à la facture</returns>
        public async Task<IEnumerable<PlanPaiement>> GetByFactureIdAsync(int factureId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        SELECT p.*
                        FROM PlansPaiement p
                        INNER JOIN PlanPaiementFactures pf ON p.Id = pf.PlanPaiementId
                        WHERE pf.FactureId = @FactureId AND p.EstActif = 1 AND pf.EstActif = 1
                        ORDER BY p.DateCreationPlan DESC";
                    return await connection.QueryAsync<PlanPaiement>(query, new { FactureId = factureId });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des plans de paiement associés à la facture {FactureId}", factureId);
                throw;
            }
        }

        /// <summary>
        /// Récupère les plans de paiement par statut
        /// </summary>
        /// <param name="statut">Statut des plans de paiement</param>
        /// <returns>Liste des plans de paiement ayant le statut spécifié</returns>
        public async Task<IEnumerable<PlanPaiement>> GetByStatutAsync(string statut)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = $"SELECT * FROM {_tableName} WHERE Statut = @Statut AND EstActif = 1 ORDER BY DateCreationPlan DESC";
                    return await connection.QueryAsync<PlanPaiement>(query, new { Statut = statut });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des plans de paiement par statut {Statut}", statut);
                throw;
            }
        }

        /// <summary>
        /// Récupère les plans de paiement par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des plans de paiement créés dans la période spécifiée</returns>
        public async Task<IEnumerable<PlanPaiement>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = $"SELECT * FROM {_tableName} WHERE DateCreationPlan BETWEEN @DateDebut AND @DateFin AND EstActif = 1 ORDER BY DateCreationPlan DESC";
                    return await connection.QueryAsync<PlanPaiement>(query, new { DateDebut = dateDebut, DateFin = dateFin });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des plans de paiement par période");
                throw;
            }
        }

        /// <summary>
        /// Récupère les plans de paiement avec des échéances à venir
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des plans de paiement avec des échéances dans la période spécifiée</returns>
        public async Task<IEnumerable<PlanPaiement>> GetWithUpcomingEcheancesAsync(DateTime dateDebut, DateTime dateFin)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        SELECT DISTINCT p.*
                        FROM PlansPaiement p
                        INNER JOIN EcheancesPaiement e ON p.Id = e.PlanPaiementId
                        WHERE e.DateEcheance BETWEEN @DateDebut AND @DateFin
                        AND e.EstPayee = 0
                        AND p.EstActif = 1
                        AND e.EstActif = 1
                        ORDER BY p.DateCreationPlan DESC";
                    return await connection.QueryAsync<PlanPaiement>(query, new { DateDebut = dateDebut, DateFin = dateFin });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des plans de paiement avec des échéances à venir");
                throw;
            }
        }

        /// <summary>
        /// Récupère les plans de paiement avec des échéances en retard
        /// </summary>
        /// <returns>Liste des plans de paiement avec des échéances en retard</returns>
        public async Task<IEnumerable<PlanPaiement>> GetWithLateEcheancesAsync()
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        SELECT DISTINCT p.*
                        FROM PlansPaiement p
                        INNER JOIN EcheancesPaiement e ON p.Id = e.PlanPaiementId
                        WHERE e.DateEcheance < GETDATE()
                        AND e.EstPayee = 0
                        AND p.EstActif = 1
                        AND e.EstActif = 1
                        ORDER BY p.DateCreationPlan DESC";
                    return await connection.QueryAsync<PlanPaiement>(query);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des plans de paiement avec des échéances en retard");
                throw;
            }
        }

        /// <summary>
        /// Associe un plan de paiement à une facture
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="montantCouvert">Montant de la facture couvert par le plan</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'association a réussi, sinon False</returns>
        public async Task<bool> AssociateToFactureAsync(int planPaiementId, int factureId, decimal montantCouvert, int userId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    // Vérifier si l'association existe déjà
                    var existingQuery = "SELECT Id FROM PlanPaiementFactures WHERE PlanPaiementId = @PlanPaiementId AND FactureId = @FactureId AND EstActif = 1";
                    var existingId = await connection.QueryFirstOrDefaultAsync<int?>(existingQuery, new { PlanPaiementId = planPaiementId, FactureId = factureId });

                    if (existingId.HasValue)
                    {
                        // Mettre à jour l'association existante
                        var updateQuery = @"
                            UPDATE PlanPaiementFactures
                            SET MontantCouvert = @MontantCouvert,
                                DateModification = GETDATE(),
                                ModifiePar = @UserId
                            WHERE Id = @Id";

                        await connection.ExecuteAsync(updateQuery, new
                        {
                            Id = existingId.Value,
                            MontantCouvert = montantCouvert,
                            UserId = userId
                        });
                    }
                    else
                    {
                        // Créer une nouvelle association
                        var insertQuery = @"
                            INSERT INTO PlanPaiementFactures (PlanPaiementId, FactureId, MontantCouvert, DateCreation, CreePar, EstActif)
                            VALUES (@PlanPaiementId, @FactureId, @MontantCouvert, GETDATE(), @UserId, 1)";

                        await connection.ExecuteAsync(insertQuery, new
                        {
                            PlanPaiementId = planPaiementId,
                            FactureId = factureId,
                            MontantCouvert = montantCouvert,
                            UserId = userId
                        });
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'association du plan de paiement {PlanPaiementId} à la facture {FactureId}", planPaiementId, factureId);
                throw;
            }
        }

        /// <summary>
        /// Dissocie un plan de paiement d'une facture
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la dissociation a réussi, sinon False</returns>
        public async Task<bool> DissociateFromFactureAsync(int planPaiementId, int factureId, int userId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        UPDATE PlanPaiementFactures
                        SET EstActif = 0,
                            DateModification = GETDATE(),
                            ModifiePar = @UserId
                        WHERE PlanPaiementId = @PlanPaiementId AND FactureId = @FactureId";

                    var result = await connection.ExecuteAsync(query, new
                    {
                        PlanPaiementId = planPaiementId,
                        FactureId = factureId,
                        UserId = userId
                    });

                    return result > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la dissociation du plan de paiement {PlanPaiementId} de la facture {FactureId}", planPaiementId, factureId);
                throw;
            }
        }

        /// <summary>
        /// Associe un document à un plan de paiement
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="documentId">Identifiant du document</param>
        /// <param name="typeDocument">Type de document</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'association a réussi, sinon False</returns>
        public async Task<bool> AssociateDocumentAsync(int planPaiementId, int documentId, string typeDocument, int userId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    // Vérifier si l'association existe déjà
                    var existingQuery = "SELECT Id FROM PlanPaiementDocuments WHERE PlanPaiementId = @PlanPaiementId AND DocumentId = @DocumentId AND EstActif = 1";
                    var existingId = await connection.QueryFirstOrDefaultAsync<int?>(existingQuery, new { PlanPaiementId = planPaiementId, DocumentId = documentId });

                    if (existingId.HasValue)
                    {
                        // Mettre à jour l'association existante
                        var updateQuery = @"
                            UPDATE PlanPaiementDocuments
                            SET TypeDocument = @TypeDocument,
                                DateModification = GETDATE(),
                                ModifiePar = @UserId
                            WHERE Id = @Id";

                        await connection.ExecuteAsync(updateQuery, new
                        {
                            Id = existingId.Value,
                            TypeDocument = typeDocument,
                            UserId = userId
                        });
                    }
                    else
                    {
                        // Créer une nouvelle association
                        var insertQuery = @"
                            INSERT INTO PlanPaiementDocuments (PlanPaiementId, DocumentId, TypeDocument, DateCreation, CreePar, EstActif)
                            VALUES (@PlanPaiementId, @DocumentId, @TypeDocument, GETDATE(), @UserId, 1)";

                        await connection.ExecuteAsync(insertQuery, new
                        {
                            PlanPaiementId = planPaiementId,
                            DocumentId = documentId,
                            TypeDocument = typeDocument,
                            UserId = userId
                        });
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'association du document {DocumentId} au plan de paiement {PlanPaiementId}", documentId, planPaiementId);
                throw;
            }
        }

        /// <summary>
        /// Met à jour le statut d'un plan de paiement
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateStatutAsync(int planPaiementId, string statut, int userId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        UPDATE PlansPaiement
                        SET Statut = @Statut,
                            DateModification = GETDATE(),
                            ModifiePar = @UserId
                        WHERE Id = @PlanPaiementId";

                    var result = await connection.ExecuteAsync(query, new
                    {
                        PlanPaiementId = planPaiementId,
                        Statut = statut,
                        UserId = userId
                    });

                    return result > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour du statut du plan de paiement {PlanPaiementId}", planPaiementId);
                throw;
            }
        }
    }
}
