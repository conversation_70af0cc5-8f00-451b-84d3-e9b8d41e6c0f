using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des factures
    /// </summary>
    public interface IFactureRepository : IRepository<Facture>
    {
        /// <summary>
        /// Récupère une facture par son numéro
        /// </summary>
        /// <param name="numero">Numéro de la facture</param>
        /// <returns>Facture trouvée ou null</returns>
        Task<Facture> GetByNumeroAsync(string numero);

        /// <summary>
        /// Récupère une facture avec ses paiements
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Facture avec ses paiements</returns>
        Task<Facture> GetWithPaiementsAsync(int factureId);

        /// <summary>
        /// Récupère une facture avec ses relances
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Facture avec ses relances</returns>
        Task<Facture> GetWithRelancesAsync(int factureId);

        /// <summary>
        /// Récupère une facture avec ses documents
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Facture avec ses documents</returns>
        Task<Facture> GetWithDocumentsAsync(int factureId);

        /// <summary>
        /// Récupère toutes les factures d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des factures du client</returns>
        Task<IEnumerable<Facture>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère toutes les factures d'un commercial
        /// </summary>
        /// <param name="commercialId">Identifiant du commercial</param>
        /// <returns>Liste des factures du commercial</returns>
        Task<IEnumerable<Facture>> GetByCommercialIdAsync(int commercialId);

        /// <summary>
        /// Récupère toutes les factures d'un livreur
        /// </summary>
        /// <param name="livreurId">Identifiant du livreur</param>
        /// <returns>Liste des factures du livreur</returns>
        Task<IEnumerable<Facture>> GetByLivreurIdAsync(int livreurId);

        /// <summary>
        /// Récupère les factures par statut
        /// </summary>
        /// <param name="statut">Statut des factures</param>
        /// <returns>Liste des factures ayant le statut spécifié</returns>
        Task<IEnumerable<Facture>> GetByStatutAsync(string statut);

        /// <summary>
        /// Récupère les factures par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des factures émises dans la période spécifiée</returns>
        Task<IEnumerable<Facture>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère les factures en retard de paiement
        /// </summary>
        /// <returns>Liste des factures en retard</returns>
        Task<IEnumerable<Facture>> GetOverdueInvoicesAsync();

        /// <summary>
        /// Met à jour le statut d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateStatutAsync(int factureId, string statut, int userId);

        /// <summary>
        /// Met à jour les montants payés d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="montantPaye">Montant payé</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateMontantPayeAsync(int factureId, decimal montantPaye, int userId);
    }
}
