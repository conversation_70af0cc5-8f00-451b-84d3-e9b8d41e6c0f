using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service d'administration de la base de données
    /// </summary>
    public interface IDatabaseAdminService
    {
        /// <summary>
        /// Teste la connexion à la base de données avec les paramètres fournis
        /// </summary>
        /// <param name="server">Nom ou adresse IP du serveur</param>
        /// <param name="database">Nom de la base de données</param>
        /// <param name="userId">Identifiant utilisateur (si authentification SQL)</param>
        /// <param name="password">Mot de passe (si authentification SQL)</param>
        /// <param name="integratedSecurity">True pour utiliser l'authentification Windows, sinon False</param>
        /// <returns>True si la connexion est établie avec succès, sinon False</returns>
        Task<bool> TestConnectionAsync(string server, string database, string userId = null, string password = null, bool integratedSecurity = false);

        /// <summary>
        /// Sauvegarde la chaîne de connexion dans le fichier de configuration
        /// </summary>
        /// <param name="connectionString">Chaîne de connexion à sauvegarder</param>
        /// <param name="encrypt">True pour chiffrer les informations sensibles, sinon False</param>
        /// <returns>True si la sauvegarde est réussie, sinon False</returns>
        Task<bool> SaveConnectionStringAsync(string connectionString, bool encrypt = true);

        /// <summary>
        /// Effectue une sauvegarde de la base de données
        /// </summary>
        /// <param name="backupPath">Chemin du fichier de sauvegarde</param>
        /// <param name="backupType">Type de sauvegarde (complète, différentielle, etc.)</param>
        /// <param name="description">Description de la sauvegarde</param>
        /// <returns>True si la sauvegarde est réussie, sinon False</returns>
        Task<bool> BackupDatabaseAsync(string backupPath, string backupType = "FULL", string description = null);

        /// <summary>
        /// Restaure une base de données à partir d'une sauvegarde
        /// </summary>
        /// <param name="backupPath">Chemin du fichier de sauvegarde</param>
        /// <param name="newDatabaseName">Nouveau nom de la base de données (optionnel)</param>
        /// <param name="replaceExisting">True pour remplacer la base de données existante, sinon False</param>
        /// <returns>True si la restauration est réussie, sinon False</returns>
        Task<bool> RestoreDatabaseAsync(string backupPath, string newDatabaseName = null, bool replaceExisting = false);

        /// <summary>
        /// Vérifie l'intégrité de la base de données
        /// </summary>
        /// <param name="repairOption">Option de réparation (NONE, REPAIR_ALLOW_DATA_LOSS, etc.)</param>
        /// <returns>Résultat de la vérification</returns>
        Task<string> CheckDatabaseIntegrityAsync(string repairOption = "NONE");

        /// <summary>
        /// Exécute une requête SQL personnalisée
        /// </summary>
        /// <param name="sqlQuery">Requête SQL à exécuter</param>
        /// <returns>Résultat de la requête sous forme de DataTable</returns>
        Task<DataTable> ExecuteCustomQueryAsync(string sqlQuery);

        /// <summary>
        /// Exécute une commande SQL personnalisée (non-query)
        /// </summary>
        /// <param name="sqlCommand">Commande SQL à exécuter</param>
        /// <returns>Nombre de lignes affectées</returns>
        Task<int> ExecuteCustomCommandAsync(string sqlCommand);

        /// <summary>
        /// Obtient la liste des bases de données sur le serveur
        /// </summary>
        /// <returns>Liste des noms de bases de données</returns>
        Task<List<string>> GetDatabasesAsync();

        /// <summary>
        /// Obtient la liste des tables dans la base de données
        /// </summary>
        /// <returns>Liste des noms de tables</returns>
        Task<List<string>> GetTablesAsync();

        /// <summary>
        /// Obtient des informations sur la taille et l'utilisation de la base de données
        /// </summary>
        /// <returns>Informations sur la base de données</returns>
        Task<Dictionary<string, object>> GetDatabaseInfoAsync();

        /// <summary>
        /// Planifie une sauvegarde automatique
        /// </summary>
        /// <param name="backupPath">Chemin du fichier de sauvegarde</param>
        /// <param name="schedule">Planification (quotidienne, hebdomadaire, etc.)</param>
        /// <param name="time">Heure de la sauvegarde</param>
        /// <returns>True si la planification est réussie, sinon False</returns>
        Task<bool> ScheduleBackupAsync(string backupPath, string schedule, TimeSpan time);
    }
}
