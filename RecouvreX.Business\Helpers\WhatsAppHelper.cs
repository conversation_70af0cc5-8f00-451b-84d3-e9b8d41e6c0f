using System;
using System.Diagnostics;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using RecouvreX.Models;
using Serilog;

namespace RecouvreX.Business.Helpers
{
    /// <summary>
    /// Classe utilitaire pour l'envoi de messages WhatsApp
    /// </summary>
    public static class WhatsAppHelper
    {
        /// <summary>
        /// Envoie un message WhatsApp en ouvrant le navigateur avec l'URL WhatsApp Web
        /// </summary>
        /// <param name="numeroTelephone">Numéro de téléphone du destinataire (format international sans le +, ex: ***********)</param>
        /// <param name="message">Message à envoyer</param>
        /// <returns>True si l'URL a été ouverte avec succès, sinon False</returns>
        public static bool SendWhatsAppMessage(string numeroTelephone, string message)
        {
            try
            {
                // Vérifier les paramètres
                if (string.IsNullOrEmpty(numeroTelephone))
                    throw new ArgumentException("Le numéro de téléphone est obligatoire");

                if (string.IsNullOrEmpty(message))
                    throw new ArgumentException("Le message est obligatoire");

                // Nettoyer le numéro de téléphone (supprimer les espaces, tirets, etc.)
                numeroTelephone = CleanPhoneNumber(numeroTelephone);

                // Encoder le message pour l'URL
                string encodedMessage = WebUtility.UrlEncode(message);

                // Construire l'URL WhatsApp
                string url = $"https://wa.me/{numeroTelephone}?text={encodedMessage}";

                // Ouvrir l'URL dans le navigateur par défaut
                Process.Start(new ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                });

                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'envoi du message WhatsApp au numéro {NumeroTelephone}", numeroTelephone);
                return false;
            }
        }

        /// <summary>
        /// Envoie un message de relance via WhatsApp
        /// </summary>
        /// <param name="facture">Facture concernée</param>
        /// <param name="client">Client concerné</param>
        /// <param name="niveau">Niveau de relance</param>
        /// <param name="numeroTelephone">Numéro de téléphone du destinataire</param>
        /// <returns>True si l'URL a été ouverte avec succès, sinon False</returns>
        public static bool EnvoyerRelanceWhatsApp(Facture facture, Client client, int niveau, string numeroTelephone)
        {
            try
            {
                // Vérifier les paramètres
                if (facture == null)
                    throw new ArgumentNullException(nameof(facture));

                if (client == null)
                    throw new ArgumentNullException(nameof(client));

                if (string.IsNullOrEmpty(numeroTelephone))
                    throw new ArgumentException("Le numéro de téléphone est obligatoire");

                // Générer le message en fonction du niveau de relance
                string message = string.Empty;

                switch (niveau)
                {
                    case 1:
                        message = $"Bonjour, nous constatons que votre facture n°{facture.Numero} d'un montant de {facture.MontantTotal:C2} émise le {facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {facture.DateEcheance:dd/MM/yyyy} n'a pas encore été réglée. Nous vous prions de bien vouloir procéder au règlement de cette facture dans les plus brefs délais. Si votre paiement est en cours, nous vous prions de ne pas tenir compte de ce rappel. Cordialement, Service Recouvrement";
                        break;

                    case 2:
                        message = $"Bonjour, malgré notre précédent rappel, nous constatons que votre facture n°{facture.Numero} d'un montant de {facture.MontantTotal:C2} émise le {facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {facture.DateEcheance:dd/MM/yyyy} n'a toujours pas été réglée. Nous vous prions de bien vouloir procéder au règlement de cette facture sous 8 jours. Si votre paiement est en cours, nous vous prions de ne pas tenir compte de ce rappel. Cordialement, Service Recouvrement";
                        break;

                    case 3:
                        message = $"Bonjour, malgré nos précédents rappels, nous constatons que votre facture n°{facture.Numero} d'un montant de {facture.MontantTotal:C2} émise le {facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {facture.DateEcheance:dd/MM/yyyy} n'a toujours pas été réglée. Nous vous mettons en demeure de régler cette facture sous 48 heures. À défaut, nous nous verrons dans l'obligation d'engager une procédure de recouvrement judiciaire. Si votre paiement est en cours, nous vous prions de nous en informer immédiatement. Cordialement, Service Recouvrement";
                        break;

                    default:
                        message = $"Bonjour, nous constatons que votre facture n°{facture.Numero} d'un montant de {facture.MontantTotal:C2} émise le {facture.DateEmission:dd/MM/yyyy} et arrivée à échéance le {facture.DateEcheance:dd/MM/yyyy} n'a pas encore été réglée. Nous vous prions de bien vouloir procéder au règlement de cette facture dans les plus brefs délais. Cordialement, Service Recouvrement";
                        break;
                }

                // Envoyer le message WhatsApp
                return SendWhatsAppMessage(numeroTelephone, message);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'envoi de la relance WhatsApp pour la facture {FactureId}", facture?.Id);
                return false;
            }
        }

        /// <summary>
        /// Nettoie un numéro de téléphone en supprimant les caractères non numériques
        /// </summary>
        /// <param name="phoneNumber">Numéro de téléphone à nettoyer</param>
        /// <returns>Numéro de téléphone nettoyé</returns>
        private static string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber))
                return string.Empty;

            // Supprimer tous les caractères non numériques
            StringBuilder sb = new StringBuilder();
            foreach (char c in phoneNumber)
            {
                if (char.IsDigit(c))
                {
                    sb.Append(c);
                }
            }

            // Si le numéro commence par un 0, le remplacer par 33 (code pays France)
            string cleanedNumber = sb.ToString();
            if (cleanedNumber.StartsWith("0"))
            {
                cleanedNumber = "33" + cleanedNumber.Substring(1);
            }

            return cleanedNumber;
        }
    }
}
