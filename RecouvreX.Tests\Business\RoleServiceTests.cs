using Moq;
using RecouvreX.Business.Interfaces;
using RecouvreX.Business.Services;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Xunit;

namespace RecouvreX.Tests.Business
{
    public class RoleServiceTests
    {
        private readonly Mock<IRoleRepository> _mockRoleRepository;
        private readonly Mock<IAuditService> _mockAuditService;
        private readonly IRoleService _roleService;

        public RoleServiceTests()
        {
            _mockRoleRepository = new Mock<IRoleRepository>();
            _mockAuditService = new Mock<IAuditService>();
            _roleService = new RoleService(_mockRoleRepository.Object, _mockAuditService.Object);
        }

        [Fact]
        public async Task GetAllAsync_ShouldReturnAllRoles()
        {
            // Arrange
            var expectedRoles = new List<Role>
            {
                new Role { Id = 1, Nom = "Administrateur", Description = "Accès complet" },
                new Role { Id = 2, Nom = "Utilisateur", Description = "Accès limité" },
                new Role { Id = 3, Nom = "Comptable", Description = "Accès aux factures et paiements" }
            };

            _mockRoleRepository.Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(expectedRoles);

            // Act
            var result = await _roleService.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedRoles.Count, result.Count());
            Assert.Equal(expectedRoles, result);
            _mockRoleRepository.Verify(repo => repo.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetAllWithStatsAsync_ShouldReturnRolesWithStats()
        {
            // Arrange
            var roles = new List<Role>
            {
                new Role { Id = 1, Nom = "Administrateur", Description = "Accès complet" },
                new Role { Id = 2, Nom = "Utilisateur", Description = "Accès limité" }
            };

            var utilisateurs = new List<Utilisateur>
            {
                new Utilisateur { Id = 1, RoleId = 1 },
                new Utilisateur { Id = 2, RoleId = 1 },
                new Utilisateur { Id = 3, RoleId = 2 }
            };

            var permissions = new List<Permission>
            {
                new Permission { Id = 1, Code = "ADMIN_ACCESS" },
                new Permission { Id = 2, Code = "USER_ACCESS" }
            };

            var rolePermissions = new Dictionary<int, List<Permission>>
            {
                { 1, new List<Permission> { permissions[0], permissions[1] } },
                { 2, new List<Permission> { permissions[1] } }
            };

            _mockRoleRepository.Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(roles);

            _mockRoleRepository.Setup(repo => repo.GetUtilisateurCountByRoleAsync(1))
                .ReturnsAsync(2);

            _mockRoleRepository.Setup(repo => repo.GetUtilisateurCountByRoleAsync(2))
                .ReturnsAsync(1);

            _mockRoleRepository.Setup(repo => repo.GetPermissionsByRoleIdAsync(1))
                .ReturnsAsync(rolePermissions[1]);

            _mockRoleRepository.Setup(repo => repo.GetPermissionsByRoleIdAsync(2))
                .ReturnsAsync(rolePermissions[2]);

            // Act
            var result = await _roleService.GetAllWithStatsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(roles.Count, result.Count());
            
            var adminRole = result.FirstOrDefault(r => r.Id == 1);
            Assert.NotNull(adminRole);
            Assert.Equal(2, adminRole.NombreUtilisateurs);
            Assert.Equal(2, adminRole.NombrePermissions);
            
            var userRole = result.FirstOrDefault(r => r.Id == 2);
            Assert.NotNull(userRole);
            Assert.Equal(1, userRole.NombreUtilisateurs);
            Assert.Equal(1, userRole.NombrePermissions);
            
            _mockRoleRepository.Verify(repo => repo.GetAllAsync(), Times.Once);
            _mockRoleRepository.Verify(repo => repo.GetUtilisateurCountByRoleAsync(It.IsAny<int>()), Times.Exactly(2));
            _mockRoleRepository.Verify(repo => repo.GetPermissionsByRoleIdAsync(It.IsAny<int>()), Times.Exactly(2));
        }

        [Fact]
        public async Task GetByIdAsync_WithValidId_ShouldReturnRole()
        {
            // Arrange
            int roleId = 1;
            var expectedRole = new Role { Id = roleId, Nom = "Administrateur", Description = "Accès complet" };

            _mockRoleRepository.Setup(repo => repo.GetByIdAsync(roleId))
                .ReturnsAsync(expectedRole);

            // Act
            var result = await _roleService.GetByIdAsync(roleId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedRole, result);
            _mockRoleRepository.Verify(repo => repo.GetByIdAsync(roleId), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            int roleId = 999;

            _mockRoleRepository.Setup(repo => repo.GetByIdAsync(roleId))
                .ReturnsAsync((Role)null);

            // Act
            var result = await _roleService.GetByIdAsync(roleId);

            // Assert
            Assert.Null(result);
            _mockRoleRepository.Verify(repo => repo.GetByIdAsync(roleId), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_ShouldCreateRoleAndReturnId()
        {
            // Arrange
            int userId = 1;
            int expectedRoleId = 4;
            var role = new Role
            {
                Nom = "Nouveau Rôle",
                Description = "Description du nouveau rôle"
            };

            _mockRoleRepository.Setup(repo => repo.GetByNameAsync(role.Nom))
                .ReturnsAsync((Role)null);

            _mockRoleRepository.Setup(repo => repo.CreateAsync(It.IsAny<Role>()))
                .ReturnsAsync(expectedRoleId);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _roleService.CreateAsync(role, userId);

            // Assert
            Assert.Equal(expectedRoleId, result);
            Assert.Equal(expectedRoleId, role.Id);
            _mockRoleRepository.Verify(repo => repo.GetByNameAsync(role.Nom), Times.Once);
            _mockRoleRepository.Verify(repo => repo.CreateAsync(role), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Rôle",
                It.IsAny<string>(),
                expectedRoleId,
                userId), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_WithExistingName_ShouldThrowException()
        {
            // Arrange
            int userId = 1;
            var role = new Role
            {
                Nom = "Administrateur", // Nom existant
                Description = "Description du nouveau rôle"
            };

            var existingRole = new Role
            {
                Id = 1,
                Nom = "Administrateur",
                Description = "Accès complet"
            };

            _mockRoleRepository.Setup(repo => repo.GetByNameAsync(role.Nom))
                .ReturnsAsync(existingRole);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _roleService.CreateAsync(role, userId));
            Assert.Contains("Ce nom de rôle existe déjà", exception.Message);
            
            _mockRoleRepository.Verify(repo => repo.GetByNameAsync(role.Nom), Times.Once);
            _mockRoleRepository.Verify(repo => repo.CreateAsync(It.IsAny<Role>()), Times.Never);
        }

        [Fact]
        public async Task UpdateAsync_ShouldUpdateRoleAndReturnTrue()
        {
            // Arrange
            int userId = 1;
            var role = new Role
            {
                Id = 2,
                Nom = "Utilisateur Modifié",
                Description = "Description modifiée"
            };

            var existingRole = new Role
            {
                Id = 2,
                Nom = "Utilisateur",
                Description = "Accès limité"
            };

            _mockRoleRepository.Setup(repo => repo.GetByIdAsync(role.Id))
                .ReturnsAsync(existingRole);

            _mockRoleRepository.Setup(repo => repo.GetByNameAsync(role.Nom))
                .ReturnsAsync((Role)null);

            _mockRoleRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Role>()))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _roleService.UpdateAsync(role, userId);

            // Assert
            Assert.True(result);
            _mockRoleRepository.Verify(repo => repo.GetByIdAsync(role.Id), Times.Once);
            _mockRoleRepository.Verify(repo => repo.GetByNameAsync(role.Nom), Times.Once);
            _mockRoleRepository.Verify(repo => repo.UpdateAsync(role), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Rôle",
                It.IsAny<string>(),
                role.Id,
                userId), Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_WithExistingName_ShouldThrowException()
        {
            // Arrange
            int userId = 1;
            var role = new Role
            {
                Id = 2,
                Nom = "Comptable", // Nom d'un autre rôle
                Description = "Description modifiée"
            };

            var existingRole = new Role
            {
                Id = 2,
                Nom = "Utilisateur",
                Description = "Accès limité"
            };

            var otherRole = new Role
            {
                Id = 3,
                Nom = "Comptable",
                Description = "Accès aux factures et paiements"
            };

            _mockRoleRepository.Setup(repo => repo.GetByIdAsync(role.Id))
                .ReturnsAsync(existingRole);

            _mockRoleRepository.Setup(repo => repo.GetByNameAsync(role.Nom))
                .ReturnsAsync(otherRole);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _roleService.UpdateAsync(role, userId));
            Assert.Contains("Ce nom de rôle est déjà utilisé par un autre rôle", exception.Message);
            
            _mockRoleRepository.Verify(repo => repo.GetByIdAsync(role.Id), Times.Once);
            _mockRoleRepository.Verify(repo => repo.GetByNameAsync(role.Nom), Times.Once);
            _mockRoleRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Role>()), Times.Never);
        }

        [Fact]
        public async Task DeleteAsync_ShouldDeleteRoleAndReturnTrue()
        {
            // Arrange
            int roleId = 3;
            int userId = 1;

            _mockRoleRepository.Setup(repo => repo.GetUtilisateurCountByRoleAsync(roleId))
                .ReturnsAsync(0);

            _mockRoleRepository.Setup(repo => repo.DeleteAsync(roleId))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _roleService.DeleteAsync(roleId, userId);

            // Assert
            Assert.True(result);
            _mockRoleRepository.Verify(repo => repo.GetUtilisateurCountByRoleAsync(roleId), Times.Once);
            _mockRoleRepository.Verify(repo => repo.DeleteAsync(roleId), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Rôle",
                It.IsAny<string>(),
                roleId,
                userId), Times.Once);
        }

        [Fact]
        public async Task DeleteAsync_WithAssignedUsers_ShouldThrowException()
        {
            // Arrange
            int roleId = 2;
            int userId = 1;

            _mockRoleRepository.Setup(repo => repo.GetUtilisateurCountByRoleAsync(roleId))
                .ReturnsAsync(3); // 3 utilisateurs ont ce rôle

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _roleService.DeleteAsync(roleId, userId));
            Assert.Contains("Ce rôle est attribué à des utilisateurs", exception.Message);
            
            _mockRoleRepository.Verify(repo => repo.GetUtilisateurCountByRoleAsync(roleId), Times.Once);
            _mockRoleRepository.Verify(repo => repo.DeleteAsync(roleId), Times.Never);
        }

        [Fact]
        public async Task GetAllPermissionsAsync_ShouldReturnAllPermissions()
        {
            // Arrange
            var expectedPermissions = new List<Permission>
            {
                new Permission { Id = 1, Code = "ADMIN_ACCESS", Description = "Accès administrateur", Module = "Administration" },
                new Permission { Id = 2, Code = "USER_ACCESS", Description = "Accès utilisateur", Module = "Général" }
            };

            _mockRoleRepository.Setup(repo => repo.GetAllPermissionsAsync())
                .ReturnsAsync(expectedPermissions);

            // Act
            var result = await _roleService.GetAllPermissionsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedPermissions.Count, result.Count());
            Assert.Equal(expectedPermissions, result);
            _mockRoleRepository.Verify(repo => repo.GetAllPermissionsAsync(), Times.Once);
        }

        [Fact]
        public async Task GetPermissionsByRoleIdAsync_ShouldReturnRolePermissions()
        {
            // Arrange
            int roleId = 1;
            var expectedPermissions = new List<Permission>
            {
                new Permission { Id = 1, Code = "ADMIN_ACCESS", Description = "Accès administrateur", Module = "Administration" },
                new Permission { Id = 2, Code = "USER_ACCESS", Description = "Accès utilisateur", Module = "Général" }
            };

            _mockRoleRepository.Setup(repo => repo.GetPermissionsByRoleIdAsync(roleId))
                .ReturnsAsync(expectedPermissions);

            // Act
            var result = await _roleService.GetPermissionsByRoleIdAsync(roleId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedPermissions.Count, result.Count());
            Assert.Equal(expectedPermissions, result);
            _mockRoleRepository.Verify(repo => repo.GetPermissionsByRoleIdAsync(roleId), Times.Once);
        }

        [Fact]
        public async Task UpdatePermissionsAsync_ShouldUpdatePermissionsAndReturnTrue()
        {
            // Arrange
            int roleId = 2;
            int userId = 1;
            var permissionIds = new List<int> { 2, 3, 4 };

            _mockRoleRepository.Setup(repo => repo.UpdatePermissionsAsync(roleId, permissionIds))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _roleService.UpdatePermissionsAsync(roleId, permissionIds, userId);

            // Assert
            Assert.True(result);
            _mockRoleRepository.Verify(repo => repo.UpdatePermissionsAsync(roleId, permissionIds), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Rôle",
                It.IsAny<string>(),
                roleId,
                userId), Times.Once);
        }
    }
}
