using System;

namespace RecouvreX.Models.Reporting
{
    /// <summary>
    /// Représente des statistiques de recouvrement pour un client
    /// </summary>
    public class StatistiqueClient
    {
        /// <summary>
        /// Identifiant du client
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// Raison sociale du client
        /// </summary>
        public string RaisonSociale { get; set; }

        /// <summary>
        /// Type de client
        /// </summary>
        public string TypeClient { get; set; }

        /// <summary>
        /// Montant total des factures
        /// </summary>
        public decimal MontantTotal { get; set; }

        /// <summary>
        /// Montant total recouvré
        /// </summary>
        public decimal MontantRecouvre { get; set; }

        /// <summary>
        /// Montant restant à recouvrer
        /// </summary>
        public decimal MontantRestant { get; set; }

        /// <summary>
        /// Nombre total de factures
        /// </summary>
        public int NombreFactures { get; set; }

        /// <summary>
        /// Nombre de factures payées
        /// </summary>
        public int NombreFacturesPayees { get; set; }

        /// <summary>
        /// Nombre de factures en retard
        /// </summary>
        public int NombreFacturesEnRetard { get; set; }

        /// <summary>
        /// Délai moyen de paiement (en jours)
        /// </summary>
        public double DelaiMoyenPaiement { get; set; }

        /// <summary>
        /// Nombre de relances envoyées
        /// </summary>
        public int NombreRelances { get; set; }

        /// <summary>
        /// Date de la dernière facture
        /// </summary>
        public DateTime? DateDerniereFacture { get; set; }

        /// <summary>
        /// Date du dernier paiement
        /// </summary>
        public DateTime? DateDernierPaiement { get; set; }

        /// <summary>
        /// Taux de recouvrement (pourcentage)
        /// </summary>
        public decimal TauxRecouvrement => MontantTotal != 0 ? (MontantRecouvre / MontantTotal) * 100 : 0;

        /// <summary>
        /// Taux de factures payées (pourcentage)
        /// </summary>
        public decimal TauxFacturesPayees => NombreFactures != 0 ? ((decimal)NombreFacturesPayees / NombreFactures) * 100 : 0;

        /// <summary>
        /// Taux de factures en retard (pourcentage)
        /// </summary>
        public decimal TauxFacturesEnRetard => NombreFactures != 0 ? ((decimal)NombreFacturesEnRetard / NombreFactures) * 100 : 0;

        /// <summary>
        /// Score de fiabilité (0-100)
        /// </summary>
        public int ScoreFiabilite { get; set; }
    }
}
