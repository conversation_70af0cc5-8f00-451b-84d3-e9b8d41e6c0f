using iText.Kernel.Pdf;
using iText.Layout;
using iText.Layout.Element;
using iText.Layout.Properties;
using Microsoft.Extensions.Configuration;
using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.Business.Helpers
{
    /// <summary>
    /// Classe utilitaire pour la génération d'accords de paiement
    /// </summary>
    public static class AccordPaiementHelper
    {
        /// <summary>
        /// Génère un accord de paiement au format PDF
        /// </summary>
        /// <param name="planPaiement">Plan de paiement</param>
        /// <param name="client">Client concerné</param>
        /// <param name="factures">Liste des factures concernées</param>
        /// <param name="echeances">Liste des échéances</param>
        /// <param name="responsable">Utilisateur responsable</param>
        /// <param name="cheminFichier">Chemin du fichier PDF à générer</param>
        /// <returns>True si la génération a réussi, sinon False</returns>
        public static bool GenererAccordPaiement(
            PlanPaiement planPaiement,
            Client client,
            List<Facture> factures,
            List<EcheancePaiement> echeances,
            Utilisateur responsable,
            string cheminFichier)
        {
            try
            {
                // Créer le répertoire si nécessaire
                var directory = Path.GetDirectoryName(cheminFichier);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Créer le document PDF
                using (var writer = new PdfWriter(cheminFichier))
                {
                    using (var pdf = new PdfDocument(writer))
                    {
                        using (var document = new iText.Layout.Document(pdf))
                        {
                            // Ajouter l'en-tête
                            document.Add(new Paragraph("ACCORD DE PAIEMENT")
                                .SetTextAlignment(TextAlignment.CENTER)
                                .SetFontSize(18)
                                .SetBold());

                            document.Add(new Paragraph($"Référence : {planPaiement.Reference}")
                                .SetTextAlignment(TextAlignment.RIGHT)
                                .SetFontSize(10));

                            document.Add(new Paragraph($"Date : {DateTime.Now:dd/MM/yyyy}")
                                .SetTextAlignment(TextAlignment.RIGHT)
                                .SetFontSize(10));

                            // Ajouter les informations du client
                            document.Add(new Paragraph("ENTRE LES SOUSSIGNÉS :")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(12)
                                .SetBold());

                            document.Add(new Paragraph("La société [Nom de votre société], [forme juridique], au capital de [montant] euros, immatriculée au RCS de [ville] sous le numéro [numéro], dont le siège social est situé [adresse], représentée par [nom du représentant] en sa qualité de [fonction],")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("Ci-après dénommée « le Créancier »,")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10)
                                .SetBold());

                            document.Add(new Paragraph("ET")
                                .SetTextAlignment(TextAlignment.CENTER)
                                .SetFontSize(10)
                                .SetBold());

                            document.Add(new Paragraph($"La société {client.RaisonSociale}, dont le siège social est situé {client.Adresse}, {client.CodePostal} {client.Ville}, {client.Pays}, numéro fiscal {client.NumeroFiscal},")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("Ci-après dénommée « le Débiteur »,")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10)
                                .SetBold());

                            // Préambule
                            document.Add(new Paragraph("PRÉAMBULE")
                                .SetTextAlignment(TextAlignment.CENTER)
                                .SetFontSize(12)
                                .SetBold());

                            document.Add(new Paragraph("Le Débiteur est redevable envers le Créancier des sommes suivantes :")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            // Tableau des factures
                            Table tableFactures = new Table(4).UseAllAvailableWidth();
                            tableFactures.AddHeaderCell(new Cell().Add(new Paragraph("Facture").SetBold()));
                            tableFactures.AddHeaderCell(new Cell().Add(new Paragraph("Date").SetBold()));
                            tableFactures.AddHeaderCell(new Cell().Add(new Paragraph("Montant TTC").SetBold()));
                            tableFactures.AddHeaderCell(new Cell().Add(new Paragraph("Montant couvert").SetBold()));

                            decimal totalFactures = 0;
                            foreach (var facture in factures)
                            {
                                tableFactures.AddCell(new Cell().Add(new Paragraph(facture.Numero)));
                                tableFactures.AddCell(new Cell().Add(new Paragraph(facture.DateEmission.ToString("dd/MM/yyyy"))));
                                tableFactures.AddCell(new Cell().Add(new Paragraph($"{facture.MontantTTC:C2}")));
                                tableFactures.AddCell(new Cell().Add(new Paragraph($"{facture.MontantRestant:C2}")));
                                totalFactures += facture.MontantRestant;
                            }

                            document.Add(tableFactures);

                            document.Add(new Paragraph($"Montant total des factures : {totalFactures:C2}")
                                .SetTextAlignment(TextAlignment.RIGHT)
                                .SetFontSize(10)
                                .SetBold());

                            // Accord
                            document.Add(new Paragraph("IL A ÉTÉ CONVENU CE QUI SUIT :")
                                .SetTextAlignment(TextAlignment.CENTER)
                                .SetFontSize(12)
                                .SetBold());

                            document.Add(new Paragraph("Article 1 - Objet")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(11)
                                .SetBold());

                            document.Add(new Paragraph("Le présent accord a pour objet de définir les modalités de règlement des sommes dues par le Débiteur au Créancier.")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("Article 2 - Reconnaissance de dette")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(11)
                                .SetBold());

                            document.Add(new Paragraph($"Le Débiteur reconnaît devoir au Créancier la somme de {planPaiement.MontantTotal:C2}, correspondant au montant total des factures impayées, majoré des intérêts et frais convenus.")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("Article 3 - Modalités de paiement")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(11)
                                .SetBold());

                            document.Add(new Paragraph($"Le Débiteur s'engage à régler la somme de {planPaiement.MontantTotal:C2} selon l'échéancier suivant :")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            // Tableau des échéances
                            Table tableEcheances = new Table(3).UseAllAvailableWidth();
                            tableEcheances.AddHeaderCell(new Cell().Add(new Paragraph("Échéance").SetBold()));
                            tableEcheances.AddHeaderCell(new Cell().Add(new Paragraph("Date").SetBold()));
                            tableEcheances.AddHeaderCell(new Cell().Add(new Paragraph("Montant").SetBold()));

                            foreach (var echeance in echeances.OrderBy(e => e.NumeroOrdre))
                            {
                                tableEcheances.AddCell(new Cell().Add(new Paragraph($"N°{echeance.NumeroOrdre}")));
                                tableEcheances.AddCell(new Cell().Add(new Paragraph(echeance.DateEcheance.ToString("dd/MM/yyyy"))));
                                tableEcheances.AddCell(new Cell().Add(new Paragraph($"{echeance.MontantPrevu:C2}")));
                            }

                            document.Add(tableEcheances);

                            document.Add(new Paragraph("Les paiements seront effectués par virement bancaire sur le compte du Créancier dont les coordonnées sont les suivantes :")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("[COORDONNÉES BANCAIRES DU CRÉANCIER]")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("Article 4 - Clause résolutoire")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(11)
                                .SetBold());

                            document.Add(new Paragraph("En cas de non-paiement d'une seule échéance à sa date d'exigibilité, et après mise en demeure restée sans effet pendant 15 jours, la totalité des sommes restant dues deviendra immédiatement exigible et le Créancier pourra engager toutes les procédures judiciaires nécessaires au recouvrement de sa créance.")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("Article 5 - Frais et intérêts")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(11)
                                .SetBold());

                            document.Add(new Paragraph($"Le présent accord inclut des frais de gestion de {planPaiement.MontantFrais:C2} et des intérêts de {planPaiement.MontantInterets:C2}, déjà intégrés dans le montant total à régler.")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            // Signatures
                            document.Add(new Paragraph("Fait à _________________, le _________________")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("En deux exemplaires originaux.")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            Table tableSignatures = new Table(2).UseAllAvailableWidth();
                            tableSignatures.AddCell(new Cell().Add(new Paragraph("Pour le Créancier")
                                .SetTextAlignment(TextAlignment.CENTER)
                                .SetFontSize(10)
                                .SetBold()));
                            tableSignatures.AddCell(new Cell().Add(new Paragraph("Pour le Débiteur")
                                .SetTextAlignment(TextAlignment.CENTER)
                                .SetFontSize(10)
                                .SetBold()));

                            tableSignatures.AddCell(new Cell().Add(new Paragraph($"Nom : {responsable.NomComplet}")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10)));
                            tableSignatures.AddCell(new Cell().Add(new Paragraph($"Nom : {client.RaisonSociale}")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10)));

                            tableSignatures.AddCell(new Cell().Add(new Paragraph("Signature :")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10)));
                            tableSignatures.AddCell(new Cell().Add(new Paragraph("Signature :")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10)));

                            tableSignatures.AddCell(new Cell().SetHeight(50));
                            tableSignatures.AddCell(new Cell().SetHeight(50));

                            document.Add(tableSignatures);
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de la génération de l'accord de paiement pour le plan {PlanPaiementId}", planPaiement.Id);
                return false;
            }
        }

        /// <summary>
        /// Envoie un accord de paiement par email
        /// </summary>
        /// <param name="planPaiement">Plan de paiement</param>
        /// <param name="client">Client concerné</param>
        /// <param name="contact">Contact client</param>
        /// <param name="cheminFichier">Chemin du fichier PDF à envoyer</param>
        /// <param name="utilisateurId">ID de l'utilisateur qui envoie l'email</param>
        /// <param name="configuration">Configuration de l'application</param>
        /// <param name="communicationService">Service de communication</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        public static async Task<bool> EnvoyerAccordPaiementAsync(
            PlanPaiement planPaiement,
            Client client,
            ContactClient contact,
            string cheminFichier,
            int utilisateurId,
            IConfiguration configuration,
            ICommunicationService communicationService)
        {
            try
            {
                // Vérifier les paramètres
                if (planPaiement == null || client == null || contact == null || string.IsNullOrEmpty(contact.Email))
                    return false;

                // Préparer l'email
                string objet = $"Accord de paiement - {planPaiement.Reference}";
                string contenu = $@"
                    <html>
                    <body>
                        <p>Bonjour {contact.Prenom} {contact.Nom},</p>
                        <p>Veuillez trouver ci-joint l'accord de paiement concernant vos factures impayées.</p>
                        <p>Cet accord détaille l'échéancier de paiement que nous avons convenu ensemble.</p>
                        <p>Nous vous remercions de bien vouloir nous retourner ce document signé dans les meilleurs délais.</p>
                        <p>Cordialement,</p>
                        <p>Le service recouvrement</p>
                    </body>
                    </html>";

                // Envoyer l'email avec la pièce jointe
                var result = await communicationService.EnvoyerEtEnregistrerEmailAsync(
                    planPaiement.Factures.First().Id, // Utiliser la première facture associée
                    contact.Id,
                    contact.Email,
                    objet,
                    contenu,
                    new List<string> { cheminFichier },
                    true,
                    DateTime.Now.AddDays(7),
                    utilisateurId,
                    configuration);

                return result.EmailEnvoye;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Erreur lors de l'envoi de l'accord de paiement pour le plan {PlanPaiementId}", planPaiement.Id);
                return false;
            }
        }
    }
}
