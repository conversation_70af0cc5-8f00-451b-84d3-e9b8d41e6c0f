namespace RecouvreX.WinForms.Forms.Relances
{
    partial class ModeleRelanceListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // ModeleRelanceListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(900, 600);
            this.Name = "ModeleRelanceListForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Gestion des modèles de relance";
            this.Load += new System.EventHandler(this.ModeleRelanceListForm_Load);
            this.ResumeLayout(false);

            // Barre d'outils
            System.Windows.Forms.ToolStrip toolStrip = new System.Windows.Forms.ToolStrip();
            toolStrip.Name = "toolStrip";
            toolStrip.Dock = System.Windows.Forms.DockStyle.Top;
            toolStrip.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
            this.Controls.Add(toolStrip);

            // Bouton Ajouter
            System.Windows.Forms.ToolStripButton addButton = new System.Windows.Forms.ToolStripButton();
            addButton.Name = "addButton";
            addButton.Text = "Ajouter";
            addButton.Image = null; // Ajouter une icône
            addButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            addButton.Click += new System.EventHandler(this.AddButton_Click);
            toolStrip.Items.Add(addButton);

            // Bouton Modifier
            System.Windows.Forms.ToolStripButton editButton = new System.Windows.Forms.ToolStripButton();
            editButton.Name = "editButton";
            editButton.Text = "Modifier";
            editButton.Image = null; // Ajouter une icône
            editButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            editButton.Click += new System.EventHandler(this.EditButton_Click);
            toolStrip.Items.Add(editButton);

            // Bouton Supprimer
            System.Windows.Forms.ToolStripButton deleteButton = new System.Windows.Forms.ToolStripButton();
            deleteButton.Name = "deleteButton";
            deleteButton.Text = "Supprimer";
            deleteButton.Image = null; // Ajouter une icône
            deleteButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            deleteButton.Click += new System.EventHandler(this.DeleteButton_Click);
            toolStrip.Items.Add(deleteButton);

            // Séparateur
            toolStrip.Items.Add(new System.Windows.Forms.ToolStripSeparator());

            // Bouton Définir comme modèle par défaut
            System.Windows.Forms.ToolStripButton setDefaultButton = new System.Windows.Forms.ToolStripButton();
            setDefaultButton.Name = "setDefaultButton";
            setDefaultButton.Text = "Définir comme modèle par défaut";
            setDefaultButton.Image = null; // Ajouter une icône
            setDefaultButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            setDefaultButton.Click += new System.EventHandler(this.SetDefaultButton_Click);
            toolStrip.Items.Add(setDefaultButton);

            // Séparateur
            toolStrip.Items.Add(new System.Windows.Forms.ToolStripSeparator());

            // Bouton Rafraîchir
            System.Windows.Forms.ToolStripButton refreshButton = new System.Windows.Forms.ToolStripButton();
            refreshButton.Name = "refreshButton";
            refreshButton.Text = "Rafraîchir";
            refreshButton.Image = null; // Ajouter une icône
            refreshButton.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.ImageAndText;
            refreshButton.Click += new System.EventHandler(this.RefreshButton_Click);
            toolStrip.Items.Add(refreshButton);

            // Panneau de filtres
            System.Windows.Forms.Panel filterPanel = new System.Windows.Forms.Panel();
            filterPanel.Dock = System.Windows.Forms.DockStyle.Top;
            filterPanel.Height = 40;
            filterPanel.Padding = new System.Windows.Forms.Padding(5);
            this.Controls.Add(filterPanel);

            // Filtre par type
            System.Windows.Forms.Label typeFilterLabel = new System.Windows.Forms.Label();
            typeFilterLabel.Text = "Type :";
            typeFilterLabel.AutoSize = true;
            typeFilterLabel.Location = new System.Drawing.Point(10, 10);
            filterPanel.Controls.Add(typeFilterLabel);

            System.Windows.Forms.ComboBox typeFilterComboBox = new System.Windows.Forms.ComboBox();
            typeFilterComboBox.Name = "typeFilterComboBox";
            typeFilterComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            typeFilterComboBox.Width = 150;
            typeFilterComboBox.Location = new System.Drawing.Point(50, 7);
            filterPanel.Controls.Add(typeFilterComboBox);

            // Filtre par niveau
            System.Windows.Forms.Label niveauFilterLabel = new System.Windows.Forms.Label();
            niveauFilterLabel.Text = "Niveau :";
            niveauFilterLabel.AutoSize = true;
            niveauFilterLabel.Location = new System.Drawing.Point(220, 10);
            filterPanel.Controls.Add(niveauFilterLabel);

            System.Windows.Forms.ComboBox niveauFilterComboBox = new System.Windows.Forms.ComboBox();
            niveauFilterComboBox.Name = "niveauFilterComboBox";
            niveauFilterComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            niveauFilterComboBox.Width = 150;
            niveauFilterComboBox.Location = new System.Drawing.Point(270, 7);
            filterPanel.Controls.Add(niveauFilterComboBox);

            // Recherche
            System.Windows.Forms.Label searchLabel = new System.Windows.Forms.Label();
            searchLabel.Text = "Rechercher :";
            searchLabel.AutoSize = true;
            searchLabel.Location = new System.Drawing.Point(440, 10);
            filterPanel.Controls.Add(searchLabel);

            System.Windows.Forms.TextBox searchTextBox = new System.Windows.Forms.TextBox();
            searchTextBox.Name = "searchTextBox";
            searchTextBox.Width = 200;
            searchTextBox.Location = new System.Drawing.Point(510, 7);
            searchTextBox.KeyDown += new System.Windows.Forms.KeyEventHandler(this.SearchTextBox_KeyDown);
            filterPanel.Controls.Add(searchTextBox);

            System.Windows.Forms.Button applyFilterButton = new System.Windows.Forms.Button();
            applyFilterButton.Text = "Appliquer";
            applyFilterButton.Size = new System.Drawing.Size(80, 25);
            applyFilterButton.Location = new System.Drawing.Point(720, 7);
            applyFilterButton.Click += new System.EventHandler(this.ApplyFilterButton_Click);
            filterPanel.Controls.Add(applyFilterButton);

            // DataGridView
            System.Windows.Forms.DataGridView dataGridView = new System.Windows.Forms.DataGridView();
            dataGridView.Name = "dataGridView";
            dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.ReadOnly = true;
            dataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;
            dataGridView.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView.RowHeadersVisible = false;
            dataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.DataGridView_CellDoubleClick);
            this.Controls.Add(dataGridView);

            // Barre de statut
            System.Windows.Forms.StatusStrip statusStrip = new System.Windows.Forms.StatusStrip();
            statusStrip.Name = "statusStrip";
            statusStrip.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Controls.Add(statusStrip);

            System.Windows.Forms.ToolStripStatusLabel countLabel = new System.Windows.Forms.ToolStripStatusLabel();
            countLabel.Name = "countLabel";
            countLabel.Text = "Nombre de modèles : 0";
            statusStrip.Items.Add(countLabel);
        }

        #endregion
    }
}
