using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des contacts client
    /// </summary>
    public interface IContactClientService
    {
        /// <summary>
        /// Récupère tous les contacts client
        /// </summary>
        /// <returns>Liste des contacts client</returns>
        Task<IEnumerable<ContactClient>> GetAllAsync();

        /// <summary>
        /// Récupère un contact client par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du contact client</param>
        /// <returns>Contact client trouvé ou null</returns>
        Task<ContactClient> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les contacts par client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des contacts pour le client spécifié</returns>
        Task<IEnumerable<ContactClient>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère le contact principal d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Contact principal du client ou null</returns>
        Task<ContactClient> GetPrincipalContactAsync(int clientId);

        /// <summary>
        /// Récupère le contact responsable des paiements d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Contact responsable des paiements du client ou null</returns>
        Task<ContactClient> GetPaymentContactAsync(int clientId);

        /// <summary>
        /// Crée un nouveau contact client
        /// </summary>
        /// <param name="contactClient">Contact client à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact client créé avec son identifiant généré</returns>
        Task<ContactClient> CreateAsync(ContactClient contactClient, int creePar);

        /// <summary>
        /// Met à jour un contact client existant
        /// </summary>
        /// <param name="contactClient">Contact client à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact client mis à jour</returns>
        Task<ContactClient> UpdateAsync(ContactClient contactClient, int modifiePar);

        /// <summary>
        /// Supprime un contact client
        /// </summary>
        /// <param name="id">Identifiant du contact client à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Définit un contact comme contact principal
        /// </summary>
        /// <param name="contactId">Identifiant du contact</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> SetAsPrincipalAsync(int contactId, int modifiePar);

        /// <summary>
        /// Définit un contact comme responsable des paiements
        /// </summary>
        /// <param name="contactId">Identifiant du contact</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> SetAsPaymentResponsibleAsync(int contactId, int modifiePar);
    }
}
