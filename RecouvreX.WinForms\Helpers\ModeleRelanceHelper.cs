using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using iText.Kernel.Pdf;
using iText.Layout;
using iText.Layout.Element;
using iText.Layout.Properties;
using System.Net.Mail;
using System.Net;
using Microsoft.Extensions.Configuration;
using Serilog;

namespace RecouvreX.WinForms.Helpers
{
    /// <summary>
    /// Classe utilitaire pour la gestion des modèles de relance
    /// </summary>
    public static class ModeleRelanceHelper
    {
        /// <summary>
        /// Génère un document PDF à partir d'un modèle de relance
        /// </summary>
        /// <param name="contenu">Contenu du modèle avec les variables remplacées</param>
        /// <param name="facture">Facture concernée</param>
        /// <param name="client">Client concerné</param>
        /// <param name="cheminFichier">Chemin du fichier PDF à générer</param>
        /// <returns>True si la génération a réussi, sinon False</returns>
        public static bool GeneratePdf(string contenu, Facture facture, Client client, string cheminFichier)
        {
            try
            {
                // Créer le répertoire si nécessaire
                var directory = Path.GetDirectoryName(cheminFichier);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Créer le document PDF
                using (var writer = new PdfWriter(cheminFichier))
                {
                    using (var pdf = new PdfDocument(writer))
                    {
                        using (var document = new iText.Layout.Document(pdf))
                        {
                            // Ajouter l'en-tête
                            document.Add(new Paragraph("RELANCE DE PAIEMENT")
                                .SetTextAlignment(TextAlignment.CENTER)
                                .SetFontSize(16)
                                .SetBold());

                            document.Add(new Paragraph($"Référence : {facture.Numero}")
                                .SetTextAlignment(TextAlignment.RIGHT)
                                .SetFontSize(10));

                            document.Add(new Paragraph($"Date : {DateTime.Now:dd/MM/yyyy}")
                                .SetTextAlignment(TextAlignment.RIGHT)
                                .SetFontSize(10));

                            // Ajouter les informations du client
                            document.Add(new Paragraph($"{client.RaisonSociale}")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(12)
                                .SetBold());

                            document.Add(new Paragraph($"{client.Adresse}")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph($"{client.CodePostal} {client.Ville}")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph($"{client.Pays}")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter le contenu
                            string[] paragraphs = contenu.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.None);
                            foreach (var paragraph in paragraphs)
                            {
                                if (!string.IsNullOrWhiteSpace(paragraph))
                                {
                                    document.Add(new Paragraph(paragraph)
                                        .SetTextAlignment(TextAlignment.JUSTIFIED)
                                        .SetFontSize(11));
                                }
                                else
                                {
                                    document.Add(new Paragraph(" "));
                                }
                            }

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter les informations de la facture
                            document.Add(new Paragraph("Détails de la facture")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(12)
                                .SetBold());

                            // Créer un tableau pour les détails de la facture
                            Table table = new Table(new float[] { 1, 1, 1, 1 })
                                .SetWidth(UnitValue.CreatePercentValue(100));

                            // Ajouter les en-têtes du tableau
                            table.AddHeaderCell(new Cell().Add(new Paragraph("Numéro").SetBold()));
                            table.AddHeaderCell(new Cell().Add(new Paragraph("Date").SetBold()));
                            table.AddHeaderCell(new Cell().Add(new Paragraph("Montant TTC").SetBold()));
                            table.AddHeaderCell(new Cell().Add(new Paragraph("Montant restant").SetBold()));

                            // Ajouter les données du tableau
                            table.AddCell(new Cell().Add(new Paragraph(facture.Numero)));
                            table.AddCell(new Cell().Add(new Paragraph(facture.DateEmission.ToString("dd/MM/yyyy"))));
                            table.AddCell(new Cell().Add(new Paragraph(facture.MontantTTC.ToString("C"))));
                            table.AddCell(new Cell().Add(new Paragraph(facture.MontantRestant.ToString("C"))));

                            document.Add(table);

                            // Ajouter un espace
                            document.Add(new Paragraph("\n"));

                            // Ajouter le pied de page
                            document.Add(new Paragraph("Nous vous remercions de votre attention.")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("Cordialement,")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10));

                            document.Add(new Paragraph("Service Recouvrement")
                                .SetTextAlignment(TextAlignment.LEFT)
                                .SetFontSize(10)
                                .SetBold());
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération du PDF pour la facture {FactureNumero}", facture.Numero);
                return false;
            }
        }

        /// <summary>
        /// Envoie un email à partir d'un modèle de relance
        /// </summary>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="contenu">Contenu de l'email avec les variables remplacées</param>
        /// <param name="destinataire">Adresse email du destinataire</param>
        /// <param name="configuration">Configuration de l'application</param>
        /// <param name="piecesJointes">Liste des pièces jointes (optionnel)</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        public static async Task<bool> SendEmailAsync(string objet, string contenu, string destinataire, IConfiguration configuration, List<string> piecesJointes = null)
        {
            try
            {
                // Récupérer les paramètres SMTP depuis la configuration
                var smtpServer = configuration.GetValue<string>("EmailSettings:SmtpServer");
                var smtpPort = configuration.GetValue<int>("EmailSettings:SmtpPort");
                var smtpUsername = configuration.GetValue<string>("EmailSettings:Username");
                var smtpPassword = configuration.GetValue<string>("EmailSettings:Password");
                var emailFrom = configuration.GetValue<string>("EmailSettings:FromEmail");
                var emailFromName = configuration.GetValue<string>("EmailSettings:FromName");
                var enableSsl = configuration.GetValue<bool>("EmailSettings:EnableSsl");

                // Vérifier que les paramètres sont valides
                if (string.IsNullOrEmpty(smtpServer) || string.IsNullOrEmpty(smtpUsername) || string.IsNullOrEmpty(smtpPassword) || string.IsNullOrEmpty(emailFrom))
                {
                    Log.Error("Paramètres SMTP manquants dans la configuration");
                    return false;
                }

                // Créer le message
                var message = new MailMessage();
                message.From = new MailAddress(emailFrom, emailFromName);
                message.To.Add(new MailAddress(destinataire));
                message.Subject = objet;
                message.Body = contenu;
                message.IsBodyHtml = true;

                // Ajouter les pièces jointes
                if (piecesJointes != null)
                {
                    foreach (var pieceJointe in piecesJointes)
                    {
                        if (File.Exists(pieceJointe))
                        {
                            message.Attachments.Add(new Attachment(pieceJointe));
                        }
                    }
                }

                // Configurer le client SMTP
                var client = new SmtpClient(smtpServer, smtpPort);
                client.EnableSsl = enableSsl;
                client.Credentials = new NetworkCredential(smtpUsername, smtpPassword);

                // Envoyer l'email
                await client.SendMailAsync(message);

                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'envoi de l'email à {Destinataire}", destinataire);
                return false;
            }
        }

        /// <summary>
        /// Affiche un aperçu du modèle de relance
        /// </summary>
        /// <param name="contenu">Contenu du modèle avec les variables remplacées</param>
        /// <param name="titre">Titre de la fenêtre d'aperçu</param>
        public static void ShowPreview(string contenu, string titre)
        {
            // Créer un formulaire pour l'aperçu
            var form = new Form
            {
                Text = titre,
                Size = new System.Drawing.Size(800, 600),
                StartPosition = FormStartPosition.CenterScreen,
                MinimizeBox = false,
                MaximizeBox = true,
                ShowIcon = false,
                ShowInTaskbar = false
            };

            // Créer un contrôle RichTextBox pour afficher le contenu
            var richTextBox = new RichTextBox
            {
                Dock = DockStyle.Fill,
                ReadOnly = true,
                BackColor = System.Drawing.Color.White,
                Font = new System.Drawing.Font("Arial", 11),
                Text = contenu
            };

            // Ajouter le contrôle au formulaire
            form.Controls.Add(richTextBox);

            // Ajouter un bouton pour fermer l'aperçu
            var closeButton = new Button
            {
                Text = "Fermer",
                Dock = DockStyle.Bottom,
                Height = 30
            };
            closeButton.Click += (sender, e) => form.Close();
            form.Controls.Add(closeButton);

            // Afficher le formulaire
            form.ShowDialog();
        }
    }
}
