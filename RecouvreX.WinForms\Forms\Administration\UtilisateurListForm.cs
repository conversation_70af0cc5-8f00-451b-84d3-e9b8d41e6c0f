using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Data;

namespace RecouvreX.WinForms.Forms.Administration
{
    public partial class UtilisateurListForm : Form
    {
        private readonly IUtilisateurService _utilisateurService;
        private readonly IRoleService _roleService;
        private readonly IAuthenticationService _authenticationService;
        private readonly int _currentUserId;
        private List<Utilisateur> _utilisateurs = new List<Utilisateur>();
        private DataTable _dataTable = new DataTable();
        private Dictionary<int, string> _roleNames = new Dictionary<int, string>();

        public UtilisateurListForm(IUtilisateurService utilisateurService, IRoleService roleService,
            IAuthenticationService authenticationService, int currentUserId)
        {
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _roleService = roleService ?? throw new ArgumentNullException(nameof(roleService));
            _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }



        private async void UtilisateurListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Vérifier les permissions
                bool canManageUsers = await _authenticationService.HasPermissionAsync(_currentUserId, "USERS_MANAGE");
                if (!canManageUsers)
                {
                    MessageBox.Show("Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.",
                        "Accès refusé", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return;
                }

                // Initialiser la table de données
                InitializeDataTable();

                // Charger les rôles
                await LoadRolesAsync();

                // Charger les utilisateurs
                await LoadUtilisateursAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement de la liste des utilisateurs");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des utilisateurs : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeDataTable()
        {
            _dataTable = new DataTable();
            _dataTable.Columns.Add("Id", typeof(int));
            _dataTable.Columns.Add("Nom d'utilisateur", typeof(string));
            _dataTable.Columns.Add("Nom", typeof(string));
            _dataTable.Columns.Add("Prénom", typeof(string));
            _dataTable.Columns.Add("Email", typeof(string));
            _dataTable.Columns.Add("Rôle", typeof(string));
            _dataTable.Columns.Add("Actif", typeof(bool));
            _dataTable.Columns.Add("Dernière connexion", typeof(DateTime));

            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                    dataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (dataGridView.Columns["Dernière connexion"] != null)
                    dataGridView.Columns["Dernière connexion"].DefaultCellStyle.Format = "dd/MM/yyyy HH:mm";
                if (dataGridView.Columns["Actif"] != null)
                    dataGridView.Columns["Actif"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            }
        }

        private async Task LoadRolesAsync()
        {
            try
            {
                // Récupérer les rôles
                var roles = await _roleService.GetAllAsync();

                // Remplir le dictionnaire des noms de rôles
                _roleNames.Clear();
                foreach (var role in roles)
                {
                    _roleNames[role.Id] = role.Nom;
                }

                // Remplir la ComboBox des rôles
                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                var roleComboBox = toolStrip?.Items.Find("roleComboBox", false).FirstOrDefault() as ToolStripComboBox;
                if (roleComboBox != null)
                {
                    roleComboBox.Items.Clear();
                    roleComboBox.Items.Add("Tous");
                    foreach (var role in roles)
                    {
                        roleComboBox.Items.Add(role.Nom);
                    }
                    roleComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des rôles");
                throw;
            }
        }

        private async Task LoadUtilisateursAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer les utilisateurs
                _utilisateurs = (await _utilisateurService.GetAllAsync()).ToList();

                // Appliquer le filtre de rôle
                ApplyRoleFilter();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des utilisateurs");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des utilisateurs : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void ApplyRoleFilter()
        {
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var roleComboBox = toolStrip?.Items.Find("roleComboBox", false).FirstOrDefault() as ToolStripComboBox;
            if (roleComboBox != null)
            {
                string selectedRole = roleComboBox.SelectedItem?.ToString() ?? "Tous";

                // Filtrer les utilisateurs par rôle
                List<Utilisateur> filteredUtilisateurs;
                if (selectedRole == "Tous")
                {
                    filteredUtilisateurs = _utilisateurs;
                }
                else
                {
                    // Trouver l'ID du rôle sélectionné
                    int roleId = _roleNames.FirstOrDefault(r => r.Value == selectedRole).Key;
                    filteredUtilisateurs = _utilisateurs.Where(u => u.RoleId == roleId).ToList();
                }

                // Mettre à jour la table de données
                UpdateDataTable(filteredUtilisateurs);

                // Mettre à jour le compteur
                UpdateCounter(filteredUtilisateurs);
            }
        }

        private void UpdateDataTable(List<Utilisateur> utilisateurs)
        {
            _dataTable.Clear();

            foreach (var utilisateur in utilisateurs)
            {
                string roleName = _roleNames.ContainsKey(utilisateur.RoleId)
                    ? _roleNames[utilisateur.RoleId]
                    : "Non défini";

                _dataTable.Rows.Add(
                    utilisateur.Id,
                    utilisateur.NomUtilisateur,
                    utilisateur.NomComplet?.Split(new char[] { ' ' }, 2).FirstOrDefault() ?? "",
                    utilisateur.NomComplet?.Split(new char[] { ' ' }, 2).Skip(1).FirstOrDefault() ?? "",
                    utilisateur.Email,
                    roleName,
                    utilisateur.EstActif,
                    utilisateur.DerniereConnexion
                );
            }
        }

        private void UpdateCounter(List<Utilisateur> utilisateurs)
        {
            var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
            var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
            if (countLabel != null)
            {
                countLabel.Text = $"Nombre d'utilisateurs : {utilisateurs.Count}";
            }
        }

        private void RoleComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyRoleFilter();
        }

        private async void SearchButton_Click(object sender, EventArgs e)
        {
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var searchTextBox = toolStrip?.Items.Find("searchTextBox", false).FirstOrDefault() as ToolStripTextBox;
            if (searchTextBox != null)
            {
                string searchTerm = searchTextBox.Text.Trim();
                await SearchUtilisateursAsync(searchTerm);
            }
        }

        private async void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                e.SuppressKeyPress = true;

                var searchTextBox = sender as ToolStripTextBox;
                if (searchTextBox != null)
                {
                    string searchTerm = searchTextBox.Text.Trim();
                    await SearchUtilisateursAsync(searchTerm);
                }
            }
        }

        private async Task SearchUtilisateursAsync(string searchTerm)
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                if (string.IsNullOrEmpty(searchTerm))
                {
                    // Si la recherche est vide, charger tous les utilisateurs
                    await LoadUtilisateursAsync();
                }
                else
                {
                    // Rechercher les utilisateurs
                    _utilisateurs = (await _utilisateurService.SearchAsync(searchTerm)).ToList();

                    // Appliquer le filtre de rôle
                    ApplyRoleFilter();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la recherche des utilisateurs");
                MessageBox.Show($"Une erreur s'est produite lors de la recherche des utilisateurs : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            // Réinitialiser le champ de recherche
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var searchTextBox = toolStrip?.Items.Find("searchTextBox", false).FirstOrDefault() as ToolStripTextBox;
            if (searchTextBox != null)
            {
                searchTextBox.Text = string.Empty;
            }

            // Recharger les utilisateurs
            await LoadUtilisateursAsync();
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir le formulaire d'ajout d'utilisateur
                using (var form = new UtilisateurEditForm(_utilisateurService, _roleService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les utilisateurs
                        LoadUtilisateursAsync().Wait();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire d'ajout d'utilisateur");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
            {
                int utilisateurId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                EditUtilisateur(utilisateurId);
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un utilisateur à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int utilisateurId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    EditUtilisateur(utilisateurId);
                }
            }
        }

        private void EditUtilisateur(int utilisateurId)
        {
            try
            {
                // Vérifier si l'utilisateur essaie de modifier son propre compte
                if (utilisateurId == _currentUserId)
                {
                    MessageBox.Show("Vous ne pouvez pas modifier votre propre compte depuis cette interface.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Récupérer l'utilisateur
                var utilisateur = _utilisateurs.FirstOrDefault(u => u.Id == utilisateurId);
                if (utilisateur != null)
                {
                    // Ouvrir le formulaire de modification d'utilisateur
                    using (var form = new UtilisateurEditForm(_utilisateurService, _roleService, _currentUserId, utilisateur))
                    {
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            // Recharger les utilisateurs
                            LoadUtilisateursAsync().Wait();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de modification d'utilisateur");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer l'utilisateur sélectionné
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int utilisateurId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string utilisateurNom = dataGridView.SelectedRows[0].Cells["Nom d'utilisateur"].Value?.ToString() ?? "";

                    // Vérifier si l'utilisateur essaie de supprimer son propre compte
                    if (utilisateurId == _currentUserId)
                    {
                        MessageBox.Show("Vous ne pouvez pas supprimer votre propre compte.",
                            "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // Demander confirmation
                    var result = MessageBox.Show($"Êtes-vous sûr de vouloir supprimer l'utilisateur '{utilisateurNom}' ?\n\nCette action est irréversible.",
                        "Confirmation de suppression", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        // Supprimer l'utilisateur
                        bool success = await _utilisateurService.DeleteAsync(utilisateurId, _currentUserId);
                        if (success)
                        {
                            MessageBox.Show("L'utilisateur a été supprimé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            // Recharger les utilisateurs
                            await LoadUtilisateursAsync();
                        }
                        else
                        {
                            MessageBox.Show("Impossible de supprimer l'utilisateur. Il est peut-être référencé par d'autres éléments.",
                                "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un utilisateur à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression de l'utilisateur");
                MessageBox.Show($"Une erreur s'est produite lors de la suppression de l'utilisateur : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void ResetPasswordButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer l'utilisateur sélectionné
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int utilisateurId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string utilisateurNom = dataGridView.SelectedRows[0].Cells["Nom d'utilisateur"].Value?.ToString() ?? "";

                    // Vérifier si l'utilisateur essaie de réinitialiser son propre mot de passe
                    if (utilisateurId == _currentUserId)
                    {
                        MessageBox.Show("Vous ne pouvez pas réinitialiser votre propre mot de passe depuis cette interface.",
                            "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // Demander confirmation
                    var result = MessageBox.Show($"Êtes-vous sûr de vouloir réinitialiser le mot de passe de l'utilisateur '{utilisateurNom}' ?",
                        "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Demander le nouveau mot de passe
                        using (var form = new ResetPasswordForm())
                        {
                            if (form.ShowDialog() == DialogResult.OK)
                            {
                                // Réinitialiser le mot de passe
                                bool success = await _utilisateurService.ResetPasswordAsync(utilisateurId, form.NewPassword, _currentUserId);
                                if (success)
                                {
                                    MessageBox.Show("Le mot de passe a été réinitialisé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                }
                                else
                                {
                                    MessageBox.Show("Impossible de réinitialiser le mot de passe.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un utilisateur pour réinitialiser son mot de passe.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la réinitialisation du mot de passe");
                MessageBox.Show($"Une erreur s'est produite lors de la réinitialisation du mot de passe : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void ToggleActiveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer l'utilisateur sélectionné
                var dataGridView = this.Controls.Find("dataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int utilisateurId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    string utilisateurNom = dataGridView.SelectedRows[0].Cells["Nom d'utilisateur"].Value?.ToString() ?? "";
                    bool estActif = Convert.ToBoolean(dataGridView.SelectedRows[0].Cells["Actif"].Value);

                    // Vérifier si l'utilisateur essaie de désactiver son propre compte
                    if (utilisateurId == _currentUserId)
                    {
                        MessageBox.Show("Vous ne pouvez pas désactiver votre propre compte.",
                            "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // Demander confirmation
                    string action = estActif ? "désactiver" : "activer";
                    var result = MessageBox.Show($"Êtes-vous sûr de vouloir {action} l'utilisateur '{utilisateurNom}' ?",
                        "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Activer/désactiver l'utilisateur
                        bool success = await _utilisateurService.SetActiveStatusAsync(utilisateurId, !estActif, _currentUserId);
                        if (success)
                        {
                            MessageBox.Show($"L'utilisateur a été {(estActif ? "désactivé" : "activé")} avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            // Recharger les utilisateurs
                            await LoadUtilisateursAsync();
                        }
                        else
                        {
                            MessageBox.Show($"Impossible de {action} l'utilisateur.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un utilisateur à activer/désactiver.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'activation/désactivation de l'utilisateur");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
