using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.IO;

namespace RecouvreX.WinForms.Forms.Litiges
{
    partial class EtapeLitigeEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private Label titleLabel;
        private Label nomLabel;
        private TextBox nomTextBox;
        private Label descriptionLabel;
        private TextBox descriptionTextBox;
        private Label ordreLabel;
        private NumericUpDown ordreNumericUpDown;
        private Label delaiLabel;
        private NumericUpDown delaiNumericUpDown;
        private CheckBox etapeFinaleCheckBox;
        private CheckBox necessiteValidationCheckBox;
        private CheckBox escaladeAutomatiqueCheckBox;
        private Label etapeEscaladeLabel;
        private ComboBox etapeEscaladeComboBox;
        private Panel buttonsPanel;
        private Button saveButton;
        private Button cancelButton;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();

            // Définir les propriétés du formulaire
            this.Size = new Size(500, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimizeBox = false;
            this.MaximizeBox = false;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;

            // Créer les contrôles
            this.mainPanel = new Panel();
            this.mainPanel.Dock = DockStyle.Fill;
            this.Controls.Add(this.mainPanel);

            // Titre
            this.titleLabel = new Label();
            this.titleLabel.Font = new Font(this.Font.FontFamily, 14, FontStyle.Bold);
            this.titleLabel.AutoSize = true;
            this.titleLabel.Location = new Point(20, 20);
            this.mainPanel.Controls.Add(this.titleLabel);

            // Nom
            this.nomLabel = new Label();
            this.nomLabel.Text = "Nom :";
            this.nomLabel.AutoSize = true;
            this.nomLabel.Location = new Point(20, 70);
            this.mainPanel.Controls.Add(this.nomLabel);

            this.nomTextBox = new TextBox();
            this.nomTextBox.Name = "nomTextBox";
            this.nomTextBox.Location = new Point(150, 67);
            this.nomTextBox.Size = new Size(300, 25);
            this.mainPanel.Controls.Add(this.nomTextBox);

            // Description
            this.descriptionLabel = new Label();
            this.descriptionLabel.Text = "Description :";
            this.descriptionLabel.AutoSize = true;
            this.descriptionLabel.Location = new Point(20, 110);
            this.mainPanel.Controls.Add(this.descriptionLabel);

            this.descriptionTextBox = new TextBox();
            this.descriptionTextBox.Name = "descriptionTextBox";
            this.descriptionTextBox.Location = new Point(150, 107);
            this.descriptionTextBox.Size = new Size(300, 100);
            this.descriptionTextBox.Multiline = true;
            this.descriptionTextBox.ScrollBars = ScrollBars.Vertical;
            this.mainPanel.Controls.Add(this.descriptionTextBox);

            // Ordre
            this.ordreLabel = new Label();
            this.ordreLabel.Text = "Ordre :";
            this.ordreLabel.AutoSize = true;
            this.ordreLabel.Location = new Point(20, 220);
            this.mainPanel.Controls.Add(this.ordreLabel);

            this.ordreNumericUpDown = new NumericUpDown();
            this.ordreNumericUpDown.Name = "ordreNumericUpDown";
            this.ordreNumericUpDown.Location = new Point(150, 217);
            this.ordreNumericUpDown.Size = new Size(100, 25);
            this.ordreNumericUpDown.Minimum = 1;
            this.ordreNumericUpDown.Maximum = 100;
            this.ordreNumericUpDown.Value = 1;
            this.mainPanel.Controls.Add(this.ordreNumericUpDown);

            // Délai
            this.delaiLabel = new Label();
            this.delaiLabel.Text = "Délai (jours) :";
            this.delaiLabel.AutoSize = true;
            this.delaiLabel.Location = new Point(20, 260);
            this.mainPanel.Controls.Add(this.delaiLabel);

            this.delaiNumericUpDown = new NumericUpDown();
            this.delaiNumericUpDown.Name = "delaiNumericUpDown";
            this.delaiNumericUpDown.Location = new Point(150, 257);
            this.delaiNumericUpDown.Size = new Size(100, 25);
            this.delaiNumericUpDown.Minimum = 1;
            this.delaiNumericUpDown.Maximum = 365;
            this.delaiNumericUpDown.Value = 7;
            this.mainPanel.Controls.Add(this.delaiNumericUpDown);

            // Étape finale
            this.etapeFinaleCheckBox = new CheckBox();
            this.etapeFinaleCheckBox.Name = "etapeFinaleCheckBox";
            this.etapeFinaleCheckBox.Text = "Étape finale (résolution)";
            this.etapeFinaleCheckBox.AutoSize = true;
            this.etapeFinaleCheckBox.Location = new Point(150, 300);
            this.mainPanel.Controls.Add(this.etapeFinaleCheckBox);

            // Nécessite validation
            this.necessiteValidationCheckBox = new CheckBox();
            this.necessiteValidationCheckBox.Name = "necessiteValidationCheckBox";
            this.necessiteValidationCheckBox.Text = "Nécessite validation";
            this.necessiteValidationCheckBox.AutoSize = true;
            this.necessiteValidationCheckBox.Location = new Point(150, 330);
            this.mainPanel.Controls.Add(this.necessiteValidationCheckBox);

            // Escalade automatique
            this.escaladeAutomatiqueCheckBox = new CheckBox();
            this.escaladeAutomatiqueCheckBox.Name = "escaladeAutomatiqueCheckBox";
            this.escaladeAutomatiqueCheckBox.Text = "Escalade automatique";
            this.escaladeAutomatiqueCheckBox.AutoSize = true;
            this.escaladeAutomatiqueCheckBox.Location = new Point(150, 360);
            this.escaladeAutomatiqueCheckBox.CheckedChanged += new EventHandler(this.EscaladeAutomatiqueCheckBox_CheckedChanged);
            this.mainPanel.Controls.Add(this.escaladeAutomatiqueCheckBox);

            // Étape d'escalade
            this.etapeEscaladeLabel = new Label();
            this.etapeEscaladeLabel.Text = "Étape d'escalade :";
            this.etapeEscaladeLabel.AutoSize = true;
            this.etapeEscaladeLabel.Location = new Point(20, 390);
            this.mainPanel.Controls.Add(this.etapeEscaladeLabel);

            this.etapeEscaladeComboBox = new ComboBox();
            this.etapeEscaladeComboBox.Name = "etapeEscaladeComboBox";
            this.etapeEscaladeComboBox.Location = new Point(150, 387);
            this.etapeEscaladeComboBox.Size = new Size(300, 25);
            this.etapeEscaladeComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.etapeEscaladeComboBox.Enabled = false;
            this.mainPanel.Controls.Add(this.etapeEscaladeComboBox);

            // Boutons d'action
            this.buttonsPanel = new Panel();
            this.buttonsPanel.Size = new Size(460, 50);
            this.buttonsPanel.Location = new Point(20, 430);
            this.mainPanel.Controls.Add(this.buttonsPanel);

            this.saveButton = new Button();
            this.saveButton.Text = "Enregistrer";
            this.saveButton.Size = new Size(100, 30);
            this.saveButton.Location = new Point(250, 10);
            this.saveButton.Click += new EventHandler(this.SaveButton_Click);
            this.buttonsPanel.Controls.Add(this.saveButton);

            this.cancelButton = new Button();
            this.cancelButton.Text = "Annuler";
            this.cancelButton.Size = new Size(100, 30);
            this.cancelButton.Location = new Point(360, 10);
            this.cancelButton.Click += new EventHandler(this.CancelButton_Click);
            this.buttonsPanel.Controls.Add(this.cancelButton);

            // Charger le formulaire
            this.Load += new EventHandler(this.EtapeLitigeEditForm_Load);
        }

        #endregion
    }
}
