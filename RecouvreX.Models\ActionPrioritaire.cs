using RecouvreX.Models.Enums;
using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une action prioritaire à effectuer
    /// </summary>
    public class ActionPrioritaire : BaseEntity
    {
        /// <summary>
        /// Identifiant du client concerné
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// Client concerné (navigation property)
        /// </summary>
        public Client Client { get; set; } = null!;

        /// <summary>
        /// Identifiant de la facture concernée (optionnel)
        /// </summary>
        public int? FactureId { get; set; }

        /// <summary>
        /// Facture concernée (navigation property)
        /// </summary>
        public Facture Facture { get; set; } = null!;

        /// <summary>
        /// Type d'action prioritaire
        /// </summary>
        public TypeActionPrioritaire TypeAction { get; set; }

        /// <summary>
        /// Niveau de priorité
        /// </summary>
        public NiveauPriorite NiveauPriorite { get; set; }

        /// <summary>
        /// Description de l'action à effectuer
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Date d'échéance pour effectuer l'action
        /// </summary>
        public DateTime DateEcheance { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur assigné à l'action
        /// </summary>
        public int? UtilisateurAssigneId { get; set; }

        /// <summary>
        /// Utilisateur assigné à l'action (navigation property)
        /// </summary>
        public Utilisateur UtilisateurAssigne { get; set; } = null!;

        /// <summary>
        /// Indique si l'action a été complétée
        /// </summary>
        public bool EstCompletee { get; set; }

        /// <summary>
        /// Date de complétion de l'action
        /// </summary>
        public DateTime? DateCompletion { get; set; }

        /// <summary>
        /// Commentaire sur la complétion de l'action
        /// </summary>
        public string CommentaireCompletion { get; set; } = string.Empty;

        /// <summary>
        /// Score de priorité calculé
        /// </summary>
        public int ScorePriorite { get; set; }

        /// <summary>
        /// Indique si l'action a été créée automatiquement
        /// </summary>
        public bool EstAutomatique { get; set; }
    }
}
