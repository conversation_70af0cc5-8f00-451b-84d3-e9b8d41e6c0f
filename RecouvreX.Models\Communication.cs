using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une communication avec un client
    /// </summary>
    public class Communication : BaseEntity
    {
        /// <summary>
        /// Identifiant de la facture concernée
        /// </summary>
        public int FactureId { get; set; }

        /// <summary>
        /// Facture concernée (navigation property)
        /// </summary>
        public Facture Facture { get; set; }

        /// <summary>
        /// Type de communication (Appel, Email, Courrier, SMS, Note)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Direction de la communication (Entrant, Sortant)
        /// </summary>
        public string Direction { get; set; }

        /// <summary>
        /// Date et heure de la communication
        /// </summary>
        public DateTime DateCommunication { get; set; }

        /// <summary>
        /// Durée de la communication en secondes (pour les appels)
        /// </summary>
        public int? Duree { get; set; }

        /// <summary>
        /// Objet de la communication
        /// </summary>
        public string Objet { get; set; }

        /// <summary>
        /// Contenu de la communication
        /// </summary>
        public string Contenu { get; set; }

        /// <summary>
        /// Résultat de la communication (Réussi, Échec, Rappel demandé, etc.)
        /// </summary>
        public string Resultat { get; set; }

        /// <summary>
        /// Identifiant de la relance associée (si applicable)
        /// </summary>
        public int? RelanceId { get; set; }

        /// <summary>
        /// Relance associée (navigation property)
        /// </summary>
        public Relance Relance { get; set; }

        /// <summary>
        /// Identifiant de la planification de relance associée (si applicable)
        /// </summary>
        public int? PlanificationRelanceId { get; set; }

        /// <summary>
        /// Planification de relance associée (navigation property)
        /// </summary>
        public PlanificationRelance PlanificationRelance { get; set; }

        /// <summary>
        /// Identifiant du contact client (si applicable)
        /// </summary>
        public int? ContactClientId { get; set; }

        /// <summary>
        /// Contact client (navigation property)
        /// </summary>
        public ContactClient ContactClient { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a effectué la communication
        /// </summary>
        public int UtilisateurId { get; set; }

        /// <summary>
        /// Utilisateur qui a effectué la communication (navigation property)
        /// </summary>
        public Utilisateur Utilisateur { get; set; }

        /// <summary>
        /// Chemin vers le fichier attaché (si applicable)
        /// </summary>
        public string CheminFichier { get; set; }

        /// <summary>
        /// Indique si la communication a été lue/ouverte par le destinataire
        /// </summary>
        public bool? EstLu { get; set; }

        /// <summary>
        /// Date et heure de lecture par le destinataire
        /// </summary>
        public DateTime? DateLecture { get; set; }

        /// <summary>
        /// Indique si un suivi est nécessaire
        /// </summary>
        public bool SuiviNecessaire { get; set; }

        /// <summary>
        /// Date de suivi prévue
        /// </summary>
        public DateTime? DateSuivi { get; set; }

        /// <summary>
        /// Notes de suivi
        /// </summary>
        public string NotesSuivi { get; set; }

        /// <summary>
        /// Identifiant de la communication parente (pour les réponses)
        /// </summary>
        public int? CommunicationParentId { get; set; }

        /// <summary>
        /// Communication parente (navigation property)
        /// </summary>
        public Communication CommunicationParent { get; set; }
    }
}
