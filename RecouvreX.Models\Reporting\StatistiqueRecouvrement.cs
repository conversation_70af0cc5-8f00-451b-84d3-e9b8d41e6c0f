using System;

namespace RecouvreX.Models.Reporting
{
    /// <summary>
    /// Représente des statistiques de recouvrement pour une période donnée
    /// </summary>
    public class StatistiqueRecouvrement
    {
        /// <summary>
        /// Date de début de la période
        /// </summary>
        public DateTime DateDebut { get; set; }

        /// <summary>
        /// Date de fin de la période
        /// </summary>
        public DateTime DateFin { get; set; }

        /// <summary>
        /// Montant total des factures
        /// </summary>
        public decimal MontantTotal { get; set; }

        /// <summary>
        /// Montant total recouvré
        /// </summary>
        public decimal MontantRecouvre { get; set; }

        /// <summary>
        /// Montant restant à recouvrer
        /// </summary>
        public decimal MontantRestant { get; set; }

        /// <summary>
        /// Nombre total de factures
        /// </summary>
        public int NombreFactures { get; set; }

        /// <summary>
        /// Nombre de factures payées
        /// </summary>
        public int NombreFacturesPayees { get; set; }

        /// <summary>
        /// Nombre de factures en retard
        /// </summary>
        public int NombreFacturesEnRetard { get; set; }

        /// <summary>
        /// Nombre de relances envoyées
        /// </summary>
        public int NombreRelances { get; set; }

        /// <summary>
        /// Délai moyen de paiement (en jours)
        /// </summary>
        public double DelaiMoyenPaiement { get; set; }

        /// <summary>
        /// Taux de recouvrement (pourcentage)
        /// </summary>
        public decimal TauxRecouvrement => MontantTotal != 0 ? (MontantRecouvre / MontantTotal) * 100 : 0;

        /// <summary>
        /// Taux de factures payées (pourcentage)
        /// </summary>
        public decimal TauxFacturesPayees => NombreFactures != 0 ? ((decimal)NombreFacturesPayees / NombreFactures) * 100 : 0;

        /// <summary>
        /// Taux de factures en retard (pourcentage)
        /// </summary>
        public decimal TauxFacturesEnRetard => NombreFactures != 0 ? ((decimal)NombreFacturesEnRetard / NombreFactures) * 100 : 0;

        /// <summary>
        /// Montant moyen par facture
        /// </summary>
        public decimal MontantMoyenFacture => NombreFactures != 0 ? MontantTotal / NombreFactures : 0;

        /// <summary>
        /// Nombre moyen de relances par facture
        /// </summary>
        public decimal NombreMoyenRelancesParFacture => NombreFactures != 0 ? (decimal)NombreRelances / NombreFactures : 0;
    }
}
