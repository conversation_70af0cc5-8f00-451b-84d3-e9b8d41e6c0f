using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des plans de paiement
    /// </summary>
    public interface IPlanPaiementService
    {
        #region Plans de paiement

        /// <summary>
        /// Récupère tous les plans de paiement
        /// </summary>
        /// <returns>Liste des plans de paiement</returns>
        Task<IEnumerable<PlanPaiement>> GetAllPlansAsync();

        /// <summary>
        /// Récupère un plan de paiement par son identifiant
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement trouvé ou null</returns>
        Task<PlanPaiement> GetPlanByIdAsync(int planPaiementId);

        /// <summary>
        /// Récupère un plan de paiement par sa référence
        /// </summary>
        /// <param name="reference">Référence du plan de paiement</param>
        /// <returns>Plan de paiement trouvé ou null</returns>
        Task<PlanPaiement> GetPlanByReferenceAsync(string reference);

        /// <summary>
        /// Récupère un plan de paiement avec ses échéances
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement avec ses échéances</returns>
        Task<PlanPaiement> GetPlanWithEcheancesAsync(int planPaiementId);

        /// <summary>
        /// Récupère un plan de paiement complet avec toutes ses relations
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement complet</returns>
        Task<PlanPaiement> GetPlanCompleteAsync(int planPaiementId);

        /// <summary>
        /// Récupère tous les plans de paiement d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des plans de paiement du client</returns>
        Task<IEnumerable<PlanPaiement>> GetPlansByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère les plans de paiement par statut
        /// </summary>
        /// <param name="statut">Statut des plans de paiement</param>
        /// <returns>Liste des plans de paiement ayant le statut spécifié</returns>
        Task<IEnumerable<PlanPaiement>> GetPlansByStatutAsync(string statut);

        /// <summary>
        /// Récupère les plans de paiement avec des échéances en retard
        /// </summary>
        /// <returns>Liste des plans de paiement avec des échéances en retard</returns>
        Task<IEnumerable<PlanPaiement>> GetPlansWithLateEcheancesAsync();

        /// <summary>
        /// Crée un nouveau plan de paiement
        /// </summary>
        /// <param name="planPaiement">Plan de paiement à créer</param>
        /// <param name="echeances">Liste des échéances du plan</param>
        /// <param name="factureIds">Liste des identifiants des factures à associer</param>
        /// <param name="montantsCouvert">Liste des montants couverts pour chaque facture</param>
        /// <param name="userId">Identifiant de l'utilisateur qui crée le plan</param>
        /// <returns>Plan de paiement créé</returns>
        Task<PlanPaiement> CreatePlanAsync(PlanPaiement planPaiement, List<EcheancePaiement> echeances, List<int> factureIds, List<decimal> montantsCouvert, int userId);

        /// <summary>
        /// Met à jour un plan de paiement
        /// </summary>
        /// <param name="planPaiement">Plan de paiement à mettre à jour</param>
        /// <param name="userId">Identifiant de l'utilisateur qui met à jour le plan</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdatePlanAsync(PlanPaiement planPaiement, int userId);

        /// <summary>
        /// Supprime un plan de paiement
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="userId">Identifiant de l'utilisateur qui supprime le plan</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeletePlanAsync(int planPaiementId, int userId);

        /// <summary>
        /// Met à jour le statut d'un plan de paiement
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdatePlanStatutAsync(int planPaiementId, string statut, int userId);

        /// <summary>
        /// Associe un plan de paiement à une facture
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="montantCouvert">Montant de la facture couvert par le plan</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'association a réussi, sinon False</returns>
        Task<bool> AssociatePlanToFactureAsync(int planPaiementId, int factureId, decimal montantCouvert, int userId);

        /// <summary>
        /// Dissocie un plan de paiement d'une facture
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la dissociation a réussi, sinon False</returns>
        Task<bool> DissociatePlanFromFactureAsync(int planPaiementId, int factureId, int userId);

        /// <summary>
        /// Génère un plan de paiement automatique
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="factureIds">Liste des identifiants des factures à inclure</param>
        /// <param name="montantTotal">Montant total du plan (si différent de la somme des factures)</param>
        /// <param name="dateDebut">Date de début du plan</param>
        /// <param name="nombreEcheances">Nombre d'échéances</param>
        /// <param name="frequence">Fréquence des échéances (en jours)</param>
        /// <param name="tauxInteret">Taux d'intérêt annuel (en pourcentage)</param>
        /// <param name="frais">Frais additionnels</param>
        /// <param name="responsableId">Identifiant du responsable du plan</param>
        /// <param name="userId">Identifiant de l'utilisateur qui crée le plan</param>
        /// <returns>Plan de paiement généré</returns>
        Task<PlanPaiement> GeneratePlanAutomatiqueAsync(int clientId, List<int> factureIds, decimal? montantTotal, DateTime dateDebut, int nombreEcheances, int frequence, decimal tauxInteret, decimal frais, int responsableId, int userId);

        #endregion

        #region Échéances

        /// <summary>
        /// Récupère toutes les échéances d'un plan de paiement
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Liste des échéances du plan de paiement</returns>
        Task<IEnumerable<EcheancePaiement>> GetEcheancesByPlanIdAsync(int planPaiementId);

        /// <summary>
        /// Récupère une échéance par son identifiant
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <returns>Échéance trouvée ou null</returns>
        Task<EcheancePaiement> GetEcheanceByIdAsync(int echeanceId);

        /// <summary>
        /// Récupère les échéances à venir pour une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des échéances à venir dans la période spécifiée</returns>
        Task<IEnumerable<EcheancePaiement>> GetUpcomingEcheancesAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère les échéances en retard
        /// </summary>
        /// <returns>Liste des échéances en retard</returns>
        Task<IEnumerable<EcheancePaiement>> GetLateEcheancesAsync();

        /// <summary>
        /// Récupère les échéances en retard pour un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des échéances en retard pour le client</returns>
        Task<IEnumerable<EcheancePaiement>> GetLateEcheancesByClientIdAsync(int clientId);

        /// <summary>
        /// Ajoute une échéance à un plan de paiement
        /// </summary>
        /// <param name="echeance">Échéance à ajouter</param>
        /// <param name="userId">Identifiant de l'utilisateur qui ajoute l'échéance</param>
        /// <returns>Échéance ajoutée</returns>
        Task<EcheancePaiement> AddEcheanceAsync(EcheancePaiement echeance, int userId);

        /// <summary>
        /// Met à jour une échéance
        /// </summary>
        /// <param name="echeance">Échéance à mettre à jour</param>
        /// <param name="userId">Identifiant de l'utilisateur qui met à jour l'échéance</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateEcheanceAsync(EcheancePaiement echeance, int userId);

        /// <summary>
        /// Supprime une échéance
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="userId">Identifiant de l'utilisateur qui supprime l'échéance</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteEcheanceAsync(int echeanceId, int userId);

        /// <summary>
        /// Marque une échéance comme payée
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="paiementId">Identifiant du paiement associé</param>
        /// <param name="montantPaye">Montant effectivement payé</param>
        /// <param name="datePaiement">Date du paiement</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> MarkEcheanceAsPaidAsync(int echeanceId, int paiementId, decimal montantPaye, DateTime datePaiement, int userId);

        /// <summary>
        /// Marque une échéance comme non payée (annulation d'un paiement)
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> MarkEcheanceAsUnpaidAsync(int echeanceId, int userId);

        /// <summary>
        /// Envoie une notification pour une échéance
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="userId">Identifiant de l'utilisateur qui envoie la notification</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        Task<bool> SendEcheanceNotificationAsync(int echeanceId, int userId);

        /// <summary>
        /// Récupère les statistiques de respect des échéances pour un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Statistiques de respect des échéances</returns>
        Task<(int Total, int Payees, int EnRetard, decimal TauxRespect)> GetClientEcheanceStatisticsAsync(int clientId);

        #endregion

        #region Rapports et statistiques

        /// <summary>
        /// Récupère les statistiques globales des plans de paiement
        /// </summary>
        /// <returns>Statistiques globales</returns>
        Task<(int NombrePlans, decimal MontantTotal, decimal MontantPaye, decimal MontantRestant, int NombreEcheances, int EcheancesPaye, int EcheancesRetard)> GetGlobalStatisticsAsync();

        /// <summary>
        /// Récupère les statistiques des plans de paiement pour une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Statistiques pour la période</returns>
        Task<(int NombrePlans, decimal MontantTotal, decimal MontantPaye, int NombreEcheances, int EcheancesPaye)> GetStatisticsByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        #endregion
    }
}
