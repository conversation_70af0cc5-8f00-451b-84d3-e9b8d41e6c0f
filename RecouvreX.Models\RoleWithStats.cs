namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un rôle avec des statistiques supplémentaires
    /// </summary>
    public class RoleWithStats : Role
    {
        /// <summary>
        /// Nombre d'utilisateurs ayant ce rôle
        /// </summary>
        public int NombreUtilisateurs { get; set; }

        /// <summary>
        /// Nombre de permissions associées à ce rôle
        /// </summary>
        public int NombrePermissions { get; set; }
    }
}
