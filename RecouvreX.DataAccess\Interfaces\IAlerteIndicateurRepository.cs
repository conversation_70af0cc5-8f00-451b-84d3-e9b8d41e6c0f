using RecouvreX.Models.Reporting;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des alertes sur indicateurs
    /// </summary>
    public interface IAlerteIndicateurRepository : IRepository<AlerteIndicateur>
    {
        /// <summary>
        /// Récupère les alertes par indicateur
        /// </summary>
        /// <param name="indicateur">Nom de l'indicateur</param>
        /// <returns>Liste des alertes pour l'indicateur spécifié</returns>
        Task<IEnumerable<AlerteIndicateur>> GetByIndicateurAsync(string indicateur);

        /// <summary>
        /// Récupère les alertes par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des alertes pour l'utilisateur spécifié</returns>
        Task<IEnumerable<AlerteIndicateur>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les alertes actives
        /// </summary>
        /// <returns>Liste des alertes actives</returns>
        Task<IEnumerable<AlerteIndicateur>> GetActiveAsync();

        /// <summary>
        /// Récupère les alertes par sévérité
        /// </summary>
        /// <param name="severite">Niveau de sévérité</param>
        /// <returns>Liste des alertes avec le niveau de sévérité spécifié</returns>
        Task<IEnumerable<AlerteIndicateur>> GetBySeveriteAsync(string severite);

        /// <summary>
        /// Met à jour le statut de déclenchement d'une alerte
        /// </summary>
        /// <param name="id">Identifiant de l'alerte</param>
        /// <param name="dateDeclenchement">Date de déclenchement</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateTriggerStatusAsync(int id, DateTime dateDeclenchement, int modifiePar);

        /// <summary>
        /// Active ou désactive une alerte
        /// </summary>
        /// <param name="id">Identifiant de l'alerte</param>
        /// <param name="estActive">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> SetActiveStatusAsync(int id, bool estActive, int modifiePar);
    }
}
