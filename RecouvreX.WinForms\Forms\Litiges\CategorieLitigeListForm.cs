using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.Litiges
{
    /// <summary>
    /// Formulaire de liste des catégories de litiges
    /// </summary>
    public partial class CategorieLitigeListForm : Form
    {
        private readonly ILitigeService _litigeService;
        private readonly int _currentUserId;
        private List<CategorieLitige> _categories = new List<CategorieLitige>();

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="litigeService">Service de gestion des litiges</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        public CategorieLitigeListForm(ILitigeService litigeService, int currentUserId)
        {
            _litigeService = litigeService ?? throw new ArgumentNullException(nameof(litigeService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }

        /// <summary>
        /// Initialisation des composants
        /// </summary>
        private void InitializeComponent()
        {
            // Définir les propriétés du formulaire
            this.Text = "Gestion des catégories de litiges";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimizeBox = false;
            this.MaximizeBox = true;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.Icon = new Icon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "dispute.ico"));

            // Créer les contrôles
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill
            };
            this.Controls.Add(mainPanel);

            // Titre
            var titleLabel = new Label
            {
                Text = "Gestion des catégories de litiges",
                Font = new Font(this.Font.FontFamily, 16, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(20, 20)
            };
            mainPanel.Controls.Add(titleLabel);

            // DataGridView pour afficher les catégories
            var categoriesDataGridView = new DataGridView
            {
                Name = "categoriesDataGridView",
                Location = new Point(20, 60),
                Size = new Size(850, 450),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                RowHeadersVisible = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };
            categoriesDataGridView.CellDoubleClick += new DataGridViewCellEventHandler(this.CategoriesDataGridView_CellDoubleClick);
            mainPanel.Controls.Add(categoriesDataGridView);

            // Configurer les colonnes du DataGridView
            ConfigureDataGridView(categoriesDataGridView);

            // Boutons d'action
            var buttonsPanel = new Panel
            {
                Size = new Size(850, 50),
                Location = new Point(20, 520)
            };
            mainPanel.Controls.Add(buttonsPanel);

            var newButton = new Button
            {
                Text = "Nouvelle catégorie",
                Size = new Size(150, 30),
                Location = new Point(0, 10)
            };
            newButton.Click += new EventHandler(this.NewButton_Click);
            buttonsPanel.Controls.Add(newButton);

            var editButton = new Button
            {
                Text = "Modifier",
                Size = new Size(100, 30),
                Location = new Point(160, 10)
            };
            editButton.Click += new EventHandler(this.EditButton_Click);
            buttonsPanel.Controls.Add(editButton);

            var deleteButton = new Button
            {
                Text = "Supprimer",
                Size = new Size(100, 30),
                Location = new Point(270, 10)
            };
            deleteButton.Click += new EventHandler(this.DeleteButton_Click);
            buttonsPanel.Controls.Add(deleteButton);

            var refreshButton = new Button
            {
                Text = "Rafraîchir",
                Size = new Size(100, 30),
                Location = new Point(380, 10)
            };
            refreshButton.Click += new EventHandler(this.RefreshButton_Click);
            buttonsPanel.Controls.Add(refreshButton);

            var closeButton = new Button
            {
                Text = "Fermer",
                Size = new Size(100, 30),
                Location = new Point(750, 10)
            };
            closeButton.Click += new EventHandler(this.CloseButton_Click);
            buttonsPanel.Controls.Add(closeButton);

            // Charger les données au chargement du formulaire
            this.Load += new EventHandler(this.CategorieLitigeListForm_Load);
        }

        /// <summary>
        /// Configure les colonnes du DataGridView
        /// </summary>
        /// <param name="dataGridView">DataGridView à configurer</param>
        private void ConfigureDataGridView(DataGridView dataGridView)
        {
            dataGridView.Columns.Clear();

            // Ajouter les colonnes
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "ID",
                DataPropertyName = "Id",
                Visible = false
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Nom",
                HeaderText = "Nom",
                DataPropertyName = "Nom",
                Width = 150
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "Description",
                DataPropertyName = "Description",
                Width = 300
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DelaiResolutionJours",
                HeaderText = "Délai (jours)",
                DataPropertyName = "DelaiResolutionJours",
                Width = 80
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "NombreLitiges",
                HeaderText = "Nombre de litiges",
                DataPropertyName = "NombreLitiges",
                Width = 100
            });

            // Colonne pour afficher la couleur
            var couleurColumn = new DataGridViewTextBoxColumn
            {
                Name = "Couleur",
                HeaderText = "Couleur",
                DataPropertyName = "Couleur",
                Width = 80
            };
            dataGridView.Columns.Add(couleurColumn);

            // Événement pour afficher la couleur dans la cellule
            dataGridView.CellFormatting += new DataGridViewCellFormattingEventHandler(this.DataGridView_CellFormatting);
        }

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void CategorieLitigeListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Charger les catégories
                await LoadCategoriesAsync();

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors du chargement de la liste des catégories de litiges");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Charge les catégories de litiges
        /// </summary>
        private async Task LoadCategoriesAsync()
        {
            try
            {
                // Récupérer les catégories avec le nombre de litiges
                var categories = await _litigeService.GetAllCategoriesWithCountAsync();
                _categories = categories.ToList();

                // Afficher les catégories dans le DataGridView
                DisplayCategories();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des catégories de litiges");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Affiche les catégories dans le DataGridView
        /// </summary>
        private void DisplayCategories()
        {
            var dataGridView = this.Controls.Find("categoriesDataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                // Créer une table de données
                var dataTable = new DataTable();
                dataTable.Columns.Add("Id", typeof(int));
                dataTable.Columns.Add("Nom", typeof(string));
                dataTable.Columns.Add("Description", typeof(string));
                dataTable.Columns.Add("DelaiResolutionJours", typeof(int));
                dataTable.Columns.Add("Couleur", typeof(string));
                dataTable.Columns.Add("NombreLitiges", typeof(int));

                // Remplir la table avec les données des catégories
                foreach (var categorie in _categories)
                {
                    dataTable.Rows.Add(
                        categorie.Id,
                        categorie.Nom,
                        categorie.Description,
                        categorie.DelaiResolutionJours,
                        categorie.Couleur,
                        categorie.Litiges?.Count ?? 0
                    );
                }

                // Assigner la table au DataGridView
                dataGridView.DataSource = dataTable;
            }
        }

        /// <summary>
        /// Formatage des cellules du DataGridView
        /// </summary>
        private void DataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            var dataGridView = sender as DataGridView;
            if (dataGridView != null && e.ColumnIndex >= 0 && e.RowIndex >= 0)
            {
                if (dataGridView.Columns[e.ColumnIndex].Name == "Couleur" && e.Value != null)
                {
                    string couleurHex = e.Value.ToString();
                    if (!string.IsNullOrEmpty(couleurHex))
                    {
                        try
                        {
                            // Convertir la valeur hexadécimale en couleur
                            Color couleur = ColorTranslator.FromHtml(couleurHex);

                            // Appliquer la couleur à la cellule
                            e.CellStyle.BackColor = couleur;
                            e.CellStyle.ForeColor = GetContrastColor(couleur);
                            e.CellStyle.SelectionBackColor = couleur;
                            e.CellStyle.SelectionForeColor = GetContrastColor(couleur);
                        }
                        catch
                        {
                            // En cas d'erreur, ne pas modifier la couleur
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Détermine la couleur de texte contrastante (noir ou blanc) en fonction de la couleur de fond
        /// </summary>
        private Color GetContrastColor(Color color)
        {
            // Formule pour déterminer la luminosité (0-255)
            double luminance = (0.299 * color.R + 0.587 * color.G + 0.114 * color.B);

            // Si la luminosité est élevée (couleur claire), utiliser du texte noir, sinon blanc
            return luminance > 128 ? Color.Black : Color.White;
        }

        /// <summary>
        /// Événement de double-clic sur une cellule du DataGridView
        /// </summary>
        private void CategoriesDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditSelectedCategory();
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Nouvelle catégorie
        /// </summary>
        private void NewButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Créer un formulaire d'édition de catégorie
                using (var form = new CategorieLitigeEditForm(_litigeService, _currentUserId))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les catégories
                        _ = LoadCategoriesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la création d'une nouvelle catégorie de litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Modifier
        /// </summary>
        private void EditButton_Click(object sender, EventArgs e)
        {
            EditSelectedCategory();
        }

        /// <summary>
        /// Édite la catégorie sélectionnée
        /// </summary>
        private void EditSelectedCategory()
        {
            try
            {
                var dataGridView = this.Controls.Find("categoriesDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    // Récupérer l'ID de la catégorie sélectionnée
                    int categorieId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);

                    // Récupérer la catégorie correspondante
                    var categorie = _categories.FirstOrDefault(c => c.Id == categorieId);
                    if (categorie != null)
                    {
                        // Créer un formulaire d'édition de catégorie
                        using (var form = new CategorieLitigeEditForm(_litigeService, _currentUserId, categorie))
                        {
                            if (form.ShowDialog() == DialogResult.OK)
                            {
                                // Recharger les catégories
                                _ = LoadCategoriesAsync();
                            }
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une catégorie à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la modification d'une catégorie de litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Supprimer
        /// </summary>
        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = this.Controls.Find("categoriesDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    // Récupérer l'ID de la catégorie sélectionnée
                    int categorieId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    int nombreLitiges = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["NombreLitiges"].Value);

                    // Vérifier si la catégorie est utilisée
                    if (nombreLitiges > 0)
                    {
                        MessageBox.Show(
                            "Cette catégorie est utilisée par des litiges et ne peut pas être supprimée.\nVeuillez d'abord modifier les litiges associés.",
                            "Suppression impossible",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Warning);
                        return;
                    }

                    // Demander confirmation
                    var result = MessageBox.Show(
                        "Êtes-vous sûr de vouloir supprimer cette catégorie ?",
                        "Confirmation",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Supprimer la catégorie
                        await _litigeService.DeleteCategorieAsync(categorieId, _currentUserId);

                        // Recharger les catégories
                        await LoadCategoriesAsync();

                        MessageBox.Show("La catégorie a été supprimée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner une catégorie à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression d'une catégorie de litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Rafraîchir
        /// </summary>
        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            await LoadCategoriesAsync();
        }

        /// <summary>
        /// Événement de clic sur le bouton Fermer
        /// </summary>
        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
