using Dapper;
using Microsoft.Extensions.Configuration;
using RecouvreX.Data.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;
using System.Data.SqlClient;

namespace RecouvreX.Data.Repositories
{
    /// <summary>
    /// Repository pour les documents de litiges
    /// </summary>
    public class DocumentLitigeRepository : IDocumentLitigeRepository
    {
        private readonly string _connectionString;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="configuration">Configuration de l'application</param>
        public DocumentLitigeRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        /// <summary>
        /// Récupère tous les documents
        /// </summary>
        /// <returns>Liste de tous les documents</returns>
        public async Task<IEnumerable<DocumentLitige>> GetAllAsync()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT d.*, l.*, u.*
                        FROM DocumentsLitige d
                        LEFT JOIN Litiges l ON d.LitigeId = l.Id
                        LEFT JOIN Utilisateurs u ON d.UtilisateurId = u.Id
                        WHERE d.EstActif = 1
                        ORDER BY d.DateAjout DESC";

                    var documentsDict = new Dictionary<int, DocumentLitige>();

                    var documents = await connection.QueryAsync<DocumentLitige, Litige, Utilisateur, DocumentLitige>(
                        query,
                        (document, litige, utilisateur) =>
                        {
                            if (!documentsDict.TryGetValue(document.Id, out var existingDocument))
                            {
                                existingDocument = document;
                                existingDocument.Litige = litige;
                                existingDocument.Utilisateur = utilisateur;
                                documentsDict.Add(existingDocument.Id, existingDocument);
                            }

                            return existingDocument;
                        },
                        splitOn: "Id,Id"
                    );

                    return documentsDict.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération de tous les documents");
                throw;
            }
        }

        /// <summary>
        /// Récupère un document par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du document</param>
        /// <returns>Document trouvé ou null</returns>
        public async Task<DocumentLitige> GetByIdAsync(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT d.*, l.*, u.*
                        FROM DocumentsLitige d
                        LEFT JOIN Litiges l ON d.LitigeId = l.Id
                        LEFT JOIN Utilisateurs u ON d.UtilisateurId = u.Id
                        WHERE d.Id = @Id AND d.EstActif = 1";

                    var documents = await connection.QueryAsync<DocumentLitige, Litige, Utilisateur, DocumentLitige>(
                        query,
                        (document, litige, utilisateur) =>
                        {
                            document.Litige = litige;
                            document.Utilisateur = utilisateur;
                            return document;
                        },
                        new { Id = id },
                        splitOn: "Id,Id"
                    );

                    return documents.FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération du document {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// Récupère tous les documents pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des documents du litige</returns>
        public async Task<IEnumerable<DocumentLitige>> GetByLitigeIdAsync(int litigeId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT d.*, l.*, u.*
                        FROM DocumentsLitige d
                        LEFT JOIN Litiges l ON d.LitigeId = l.Id
                        LEFT JOIN Utilisateurs u ON d.UtilisateurId = u.Id
                        WHERE d.LitigeId = @LitigeId AND d.EstActif = 1
                        ORDER BY d.DateAjout DESC";

                    var documentsDict = new Dictionary<int, DocumentLitige>();

                    var documents = await connection.QueryAsync<DocumentLitige, Litige, Utilisateur, DocumentLitige>(
                        query,
                        (document, litige, utilisateur) =>
                        {
                            if (!documentsDict.TryGetValue(document.Id, out var existingDocument))
                            {
                                existingDocument = document;
                                existingDocument.Litige = litige;
                                existingDocument.Utilisateur = utilisateur;
                                documentsDict.Add(existingDocument.Id, existingDocument);
                            }

                            return existingDocument;
                        },
                        new { LitigeId = litigeId },
                        splitOn: "Id,Id"
                    );

                    return documentsDict.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des documents pour le litige {LitigeId}", litigeId);
                throw;
            }
        }

        /// <summary>
        /// Ajoute un document
        /// </summary>
        /// <param name="document">Document à ajouter</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui ajoute le document</param>
        /// <returns>Document ajouté</returns>
        public async Task<DocumentLitige> AddAsync(DocumentLitige document, int creePar)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        INSERT INTO DocumentsLitige (LitigeId, Nom, Description, CheminFichier, TypeFichier, TailleFichier, DateAjout, UtilisateurId, EstActif, CreePar, DateCreation)
                        VALUES (@LitigeId, @Nom, @Description, @CheminFichier, @TypeFichier, @TailleFichier, @DateAjout, @UtilisateurId, 1, @CreePar, @DateCreation);
                        SELECT CAST(SCOPE_IDENTITY() as int)";

                    var id = await connection.QuerySingleAsync<int>(query, new
                    {
                        document.LitigeId,
                        document.Nom,
                        document.Description,
                        document.CheminFichier,
                        document.TypeFichier,
                        document.TailleFichier,
                        DateAjout = DateTime.Now,
                        document.UtilisateurId,
                        CreePar = creePar,
                        DateCreation = DateTime.Now
                    });

                    document.Id = id;
                    document.EstActif = true;
                    document.CreePar = creePar;
                    document.DateCreation = DateTime.Now;
                    return document;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ajout d'un document");
                throw;
            }
        }

        /// <summary>
        /// Met à jour un document
        /// </summary>
        /// <param name="document">Document à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui modifie le document</param>
        /// <returns>True si le document a été mis à jour</returns>
        public async Task<bool> UpdateAsync(DocumentLitige document, int modifiePar)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        UPDATE DocumentsLitige
                        SET Description = @Description,
                            ModifiePar = @ModifiePar,
                            DateModification = @DateModification
                        WHERE Id = @Id AND EstActif = 1";

                    var rowsAffected = await connection.ExecuteAsync(query, new
                    {
                        document.Id,
                        document.Description,
                        ModifiePar = modifiePar,
                        DateModification = DateTime.Now
                    });

                    return rowsAffected > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour du document {Id}", document.Id);
                throw;
            }
        }

        /// <summary>
        /// Supprime un document
        /// </summary>
        /// <param name="id">Identifiant du document</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui supprime le document</param>
        /// <returns>True si le document a été supprimé</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        UPDATE DocumentsLitige
                        SET EstActif = 0,
                            ModifiePar = @ModifiePar,
                            DateModification = @DateModification
                        WHERE Id = @Id AND EstActif = 1";

                    var rowsAffected = await connection.ExecuteAsync(query, new
                    {
                        Id = id,
                        ModifiePar = supprimePar,
                        DateModification = DateTime.Now
                    });

                    return rowsAffected > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression du document {Id}", id);
                throw;
            }
        }
    }
}
