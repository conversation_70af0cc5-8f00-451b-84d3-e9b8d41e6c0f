using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des relances
    /// </summary>
    public class RelanceService : IRelanceService
    {
        private readonly IRelanceRepository _relanceRepository;
        private readonly IFactureRepository _factureRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="relanceRepository">Repository des relances</param>
        /// <param name="factureRepository">Repository des factures</param>
        /// <param name="clientRepository">Repository des clients</param>
        /// <param name="utilisateurRepository">Repository des utilisateurs</param>
        /// <param name="journalAuditRepository">Repository du journal d'audit</param>
        public RelanceService(
            IRelanceRepository relanceRepository,
            IFactureRepository factureRepository,
            IClientRepository clientRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _relanceRepository = relanceRepository ?? throw new ArgumentNullException(nameof(relanceRepository));
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère toutes les relances
        /// </summary>
        /// <returns>Liste des relances</returns>
        public async Task<IEnumerable<Relance>> GetAllAsync()
        {
            return await _relanceRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère une relance par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la relance</param>
        /// <returns>Relance trouvée ou null</returns>
        public async Task<Relance> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _relanceRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère toutes les relances d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des relances de la facture</returns>
        public async Task<IEnumerable<Relance>> GetByFactureIdAsync(int factureId)
        {
            if (factureId <= 0)
                return new List<Relance>();

            return await _relanceRepository.GetByFactureIdAsync(factureId);
        }

        /// <summary>
        /// Récupère toutes les relances effectuées par un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des relances effectuées par l'utilisateur</returns>
        public async Task<IEnumerable<Relance>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            if (utilisateurId <= 0)
                return new List<Relance>();

            return await _relanceRepository.GetByUtilisateurIdAsync(utilisateurId);
        }

        /// <summary>
        /// Récupère les relances par type
        /// </summary>
        /// <param name="type">Type de relance</param>
        /// <returns>Liste des relances du type spécifié</returns>
        public async Task<IEnumerable<Relance>> GetByTypeAsync(string type)
        {
            if (string.IsNullOrEmpty(type))
                return new List<Relance>();

            return await _relanceRepository.GetByTypeAsync(type);
        }

        /// <summary>
        /// Récupère les relances par statut
        /// </summary>
        /// <param name="statut">Statut de relance</param>
        /// <returns>Liste des relances ayant le statut spécifié</returns>
        public async Task<IEnumerable<Relance>> GetByStatutAsync(string statut)
        {
            if (string.IsNullOrEmpty(statut))
                return new List<Relance>();

            return await _relanceRepository.GetByStatutAsync(statut);
        }

        /// <summary>
        /// Récupère les relances par niveau
        /// </summary>
        /// <param name="niveau">Niveau de relance</param>
        /// <returns>Liste des relances du niveau spécifié</returns>
        public async Task<IEnumerable<Relance>> GetByNiveauAsync(int niveau)
        {
            if (niveau <= 0)
                return new List<Relance>();

            return await _relanceRepository.GetByNiveauAsync(niveau);
        }

        /// <summary>
        /// Récupère les relances par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des relances effectuées dans la période spécifiée</returns>
        public async Task<IEnumerable<Relance>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            return await _relanceRepository.GetByPeriodAsync(dateDebut, dateFin);
        }

        /// <summary>
        /// Récupère les relances planifiées pour une date donnée
        /// </summary>
        /// <param name="date">Date des relances planifiées</param>
        /// <returns>Liste des relances planifiées pour la date spécifiée</returns>
        public async Task<IEnumerable<Relance>> GetPlannedForDateAsync(DateTime date)
        {
            return await _relanceRepository.GetPlannedForDateAsync(date);
        }

        /// <summary>
        /// Crée une nouvelle relance
        /// </summary>
        /// <param name="relance">Relance à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Relance créée avec son identifiant généré</returns>
        public async Task<Relance> CreateAsync(Relance relance, int creePar)
        {
            if (relance == null || creePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la création d'une relance");

            // Vérifier si la facture existe
            var facture = await _factureRepository.GetByIdAsync(relance.FactureId);
            if (facture == null)
                throw new InvalidOperationException($"La facture avec l'ID {relance.FactureId} n'existe pas");

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(facture.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {facture.ClientId} n'existe pas");

            // Vérifier si l'utilisateur existe
            if (relance.UtilisateurId.HasValue && relance.UtilisateurId.Value > 0)
            {
                var utilisateur = await _utilisateurRepository.GetByIdAsync(relance.UtilisateurId.Value);
                if (utilisateur == null)
                    throw new InvalidOperationException($"L'utilisateur avec l'ID {relance.UtilisateurId.Value} n'existe pas");
            }

            // Vérifier si le type est valide
            if (string.IsNullOrEmpty(relance.Type))
                throw new ArgumentException("Le type de relance ne peut pas être vide");

            // Vérifier si le statut est valide
            if (string.IsNullOrEmpty(relance.Statut))
            {
                relance.Statut = StatutRelance.Planifiee;
            }

            // Déterminer le niveau de relance si non spécifié
            if (relance.Niveau <= 0)
            {
                // Récupérer les relances existantes pour cette facture
                var relancesExistantes = await _relanceRepository.GetByFactureIdAsync(relance.FactureId);
                relance.Niveau = relancesExistantes.Any() ? relancesExistantes.Max(r => r.Niveau) + 1 : 1;
            }

            // Créer la relance
            var createdRelance = await _relanceRepository.AddAsync(relance, creePar);

            // Journaliser la création
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = creePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(creePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Creation,
                TypeEntite = "Relance",
                EntiteId = createdRelance.Id,
                Description = $"Création d'une relance de niveau {createdRelance.Niveau} pour la facture {facture.Numero} du client {client.RaisonSociale}",
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdRelance.Id,
                    createdRelance.FactureId,
                    FactureNumero = facture.Numero,
                    ClientNom = client.RaisonSociale,
                    createdRelance.Type,
                    createdRelance.DateRelance,
                    createdRelance.Niveau,
                    createdRelance.Statut,
                    createdRelance.UtilisateurId,
                    UtilisateurNom = relance.UtilisateurId.HasValue ? (await _utilisateurRepository.GetByIdAsync(relance.UtilisateurId.Value))?.NomUtilisateur : null,
                    createdRelance.DateProchaineRelance
                })
            });

            return createdRelance;
        }

        /// <summary>
        /// Met à jour une relance existante
        /// </summary>
        /// <param name="relance">Relance à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Relance mise à jour</returns>
        public async Task<Relance> UpdateAsync(Relance relance, int modifiePar)
        {
            if (relance == null || relance.Id <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la mise à jour d'une relance");

            // Récupérer la relance existante
            var existingRelance = await _relanceRepository.GetByIdAsync(relance.Id);
            if (existingRelance == null)
                throw new InvalidOperationException($"La relance avec l'ID {relance.Id} n'existe pas");

            // Vérifier si la facture existe
            var facture = await _factureRepository.GetByIdAsync(relance.FactureId);
            if (facture == null)
                throw new InvalidOperationException($"La facture avec l'ID {relance.FactureId} n'existe pas");

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(facture.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {facture.ClientId} n'existe pas");

            // Vérifier si l'utilisateur existe
            if (relance.UtilisateurId.HasValue && relance.UtilisateurId.Value > 0)
            {
                var utilisateur = await _utilisateurRepository.GetByIdAsync(relance.UtilisateurId.Value);
                if (utilisateur == null)
                    throw new InvalidOperationException($"L'utilisateur avec l'ID {relance.UtilisateurId.Value} n'existe pas");
            }

            // Vérifier si le type est valide
            if (string.IsNullOrEmpty(relance.Type))
                throw new ArgumentException("Le type de relance ne peut pas être vide");

            // Vérifier si le statut est valide
            if (string.IsNullOrEmpty(relance.Statut))
                throw new ArgumentException("Le statut de relance ne peut pas être vide");

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Relance",
                EntiteId = relance.Id,
                Description = $"Modification de la relance de niveau {relance.Niveau} pour la facture {facture.Numero} du client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingRelance.Id,
                    existingRelance.FactureId,
                    existingRelance.Type,
                    existingRelance.DateRelance,
                    existingRelance.Niveau,
                    existingRelance.Statut,
                    existingRelance.UtilisateurId,
                    existingRelance.Contenu,
                    existingRelance.ReponseClient,
                    existingRelance.DateProchaineRelance
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    relance.Id,
                    relance.FactureId,
                    FactureNumero = facture.Numero,
                    ClientNom = client.RaisonSociale,
                    relance.Type,
                    relance.DateRelance,
                    relance.Niveau,
                    relance.Statut,
                    relance.UtilisateurId,
                    UtilisateurNom = relance.UtilisateurId.HasValue ? (await _utilisateurRepository.GetByIdAsync(relance.UtilisateurId.Value))?.NomUtilisateur : null,
                    relance.Contenu,
                    relance.ReponseClient,
                    relance.DateProchaineRelance
                })
            });

            // Mettre à jour la relance
            return await _relanceRepository.UpdateAsync(relance, modifiePar);
        }

        /// <summary>
        /// Supprime une relance
        /// </summary>
        /// <param name="id">Identifiant de la relance à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0 || supprimePar <= 0)
                return false;

            // Récupérer la relance à supprimer
            var relance = await _relanceRepository.GetByIdAsync(id);
            if (relance == null)
                return false;

            // Récupérer la facture
            var facture = await _factureRepository.GetByIdAsync(relance.FactureId);
            if (facture == null)
                return false;

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(facture.ClientId);
            if (client == null)
                return false;

            // Journaliser la suppression
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = supprimePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(supprimePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Suppression,
                TypeEntite = "Relance",
                EntiteId = id,
                Description = $"Suppression de la relance de niveau {relance.Niveau} pour la facture {facture.Numero} du client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    relance.Id,
                    relance.FactureId,
                    FactureNumero = facture.Numero,
                    ClientNom = client.RaisonSociale,
                    relance.Type,
                    relance.DateRelance,
                    relance.Niveau,
                    relance.Statut,
                    relance.UtilisateurId,
                    UtilisateurNom = relance.UtilisateurId.HasValue ? (await _utilisateurRepository.GetByIdAsync(relance.UtilisateurId.Value))?.NomUtilisateur : null,
                    relance.Contenu,
                    relance.ReponseClient,
                    relance.DateProchaineRelance
                })
            });

            // Supprimer la relance
            return await _relanceRepository.DeleteAsync(id, supprimePar);
        }

        /// <summary>
        /// Met à jour le statut d'une relance
        /// </summary>
        /// <param name="relanceId">Identifiant de la relance</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateStatutAsync(int relanceId, string statut, int modifiePar)
        {
            if (relanceId <= 0 || string.IsNullOrEmpty(statut) || modifiePar <= 0)
                return false;

            // Récupérer la relance
            var relance = await _relanceRepository.GetByIdAsync(relanceId);
            if (relance == null)
                return false;

            // Vérifier si le statut est valide
            if (statut != StatutRelance.Planifiee &&
                statut != StatutRelance.Effectuee &&
                statut != StatutRelance.EnAttente &&
                statut != StatutRelance.Annulee &&
                statut != StatutRelance.SansReponse)
            {
                throw new ArgumentException($"Le statut '{statut}' n'est pas valide");
            }

            // Récupérer la facture
            var facture = await _factureRepository.GetByIdAsync(relance.FactureId);
            if (facture == null)
                return false;

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Relance",
                EntiteId = relanceId,
                Description = $"Modification du statut de la relance de niveau {relance.Niveau} pour la facture {facture.Numero} de '{relance.Statut}' à '{statut}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new { relance.Statut }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new { Statut = statut })
            });

            // Mettre à jour le statut
            return await _relanceRepository.UpdateStatutAsync(relanceId, statut, modifiePar);
        }

        /// <summary>
        /// Planifie une relance pour une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="type">Type de relance</param>
        /// <param name="datePrevue">Date prévue pour la relance</param>
        /// <param name="niveau">Niveau de la relance</param>
        /// <param name="contenu">Contenu de la relance</param>
        /// <param name="planifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Relance planifiée avec son identifiant généré</returns>
        public async Task<Relance> PlanifierRelanceAsync(int factureId, string type, DateTime datePrevue, int niveau, string contenu, int planifiePar)
        {
            if (factureId <= 0 || string.IsNullOrEmpty(type) || datePrevue <= DateTime.Now || niveau <= 0 || planifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la planification d'une relance");

            // Vérifier si la facture existe
            var facture = await _factureRepository.GetByIdAsync(factureId);
            if (facture == null)
                throw new InvalidOperationException($"La facture avec l'ID {factureId} n'existe pas");

            // Créer la relance planifiée
            var relance = new Relance
            {
                FactureId = factureId,
                Type = type,
                DateRelance = datePrevue,
                Niveau = niveau,
                Statut = StatutRelance.Planifiee,
                UtilisateurId = planifiePar,
                Contenu = contenu
            };

            return await CreateAsync(relance, planifiePar);
        }

        /// <summary>
        /// Génère des relances automatiques pour les factures en retard
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre de relances générées</returns>
        public async Task<int> GenererRelancesAutomatiquesAsync(int utilisateurId)
        {
            if (utilisateurId <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Récupérer les factures en retard
            var facturesEnRetard = await _factureRepository.GetOverdueInvoicesAsync();
            int count = 0;

            foreach (var facture in facturesEnRetard)
            {
                // Récupérer les relances existantes pour cette facture
                var relancesExistantes = await _relanceRepository.GetByFactureIdAsync(facture.Id);

                // Déterminer le niveau de la prochaine relance
                int prochainNiveau = relancesExistantes.Any() ? relancesExistantes.Max(r => r.Niveau) + 1 : 1;

                // Vérifier si une relance a déjà été effectuée récemment (moins de 7 jours)
                var derniereRelance = relancesExistantes.OrderByDescending(r => r.DateRelance).FirstOrDefault();
                if (derniereRelance != null && (DateTime.Now - derniereRelance.DateRelance).TotalDays < 7)
                    continue;

                // Déterminer le type de relance en fonction du niveau
                string typeRelance;
                if (prochainNiveau == 1)
                    typeRelance = TypeRelance.Email;
                else if (prochainNiveau == 2)
                    typeRelance = TypeRelance.Telephonique;
                else
                    typeRelance = TypeRelance.Courrier;

                // Générer le contenu de la relance
                string contenu = $"Relance de niveau {prochainNiveau} pour la facture {facture.Numero} d'un montant de {facture.MontantRestant} €, en retard de {(DateTime.Now - facture.DateEcheance).TotalDays:F0} jours.";

                // Créer la relance
                var relance = new Relance
                {
                    FactureId = facture.Id,
                    Type = typeRelance,
                    DateRelance = DateTime.Now,
                    Niveau = prochainNiveau,
                    Statut = StatutRelance.Planifiee,
                    UtilisateurId = utilisateurId,
                    Contenu = contenu,
                    DateProchaineRelance = DateTime.Now.AddDays(7) // Planifier la prochaine relance dans 7 jours
                };

                await CreateAsync(relance, utilisateurId);
                count++;
            }

            return count;
        }
    }
}
