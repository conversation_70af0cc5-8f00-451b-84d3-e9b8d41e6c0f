using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des alertes
    /// </summary>
    public interface IAlerteService
    {
        /// <summary>
        /// R<PERSON>cup<PERSON> toutes les alertes
        /// </summary>
        /// <returns>Liste des alertes</returns>
        Task<IEnumerable<Alerte>> GetAllAsync();

        /// <summary>
        /// Récupère une alerte par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'alerte</param>
        /// <returns>Alerte trouvée ou null</returns>
        Task<Alerte> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les alertes d'un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des alertes de l'utilisateur</returns>
        Task<IEnumerable<Alerte>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Crée une nouvelle alerte
        /// </summary>
        /// <param name="alerte">Alerte à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Alerte créée avec son identifiant généré</returns>
        Task<Alerte> CreateAsync(Alerte alerte, int creePar);

        /// <summary>
        /// Met à jour une alerte existante
        /// </summary>
        /// <param name="alerte">Alerte à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Alerte mise à jour</returns>
        Task<Alerte> UpdateAsync(Alerte alerte, int modifiePar);

        /// <summary>
        /// Supprime une alerte
        /// </summary>
        /// <param name="id">Identifiant de l'alerte à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Active ou désactive une alerte
        /// </summary>
        /// <param name="id">Identifiant de l'alerte</param>
        /// <param name="estActive">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> SetActiveStatusAsync(int id, bool estActive, int modifiePar);

        /// <summary>
        /// Vérifie les alertes et retourne celles qui sont déclenchées
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des alertes déclenchées</returns>
        Task<IEnumerable<Alerte>> CheckAlertesAsync(int utilisateurId);

        /// <summary>
        /// Enregistre une notification pour une alerte
        /// </summary>
        /// <param name="alerteId">Identifiant de l'alerte</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>True si l'enregistrement a réussi, sinon False</returns>
        Task<bool> RecordNotificationAsync(int alerteId, int utilisateurId);
    }
}
