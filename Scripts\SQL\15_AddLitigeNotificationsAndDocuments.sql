-- Script pour ajouter les tables de notifications et documents pour les litiges
USE RecouvreX;
GO

-- Vérifier si la table NotificationsLitige existe déjà
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[app].[NotificationsLitige]') AND type in (N'U'))
BEGIN
    -- Créer la table NotificationsLitige
    CREATE TABLE [app].[NotificationsLitige] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [LitigeId] INT NOT NULL,
        [UtilisateurId] INT NOT NULL,
        [Type] NVARCHAR(50) NOT NULL, -- 'Escalade', 'EtapeChangee', 'EcheanceProche', 'EcheanceDepassee'
        [Message] NVARCHAR(MAX) NOT NULL,
        [DateNotification] DATETIME NOT NULL DEFAULT GETDATE(),
        [EstLue] BIT NOT NULL DEFAULT 0,
        [DateLecture] DATETIME NULL,
        [EstActif] BIT NOT NULL DEFAULT 1,
        CONSTRAINT [FK_NotificationsLitige_Litiges] FOREIGN KEY ([LitigeId]) REFERENCES [app].[Litiges] ([Id]),
        CONSTRAINT [FK_NotificationsLitige_Utilisateurs] FOREIGN KEY ([UtilisateurId]) REFERENCES [app].[Utilisateurs] ([Id])
    );

    PRINT 'Table NotificationsLitige créée avec succès.';
END
ELSE
BEGIN
    PRINT 'La table NotificationsLitige existe déjà.';
END
GO

-- Vérifier si la table DocumentsLitige existe déjà
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[app].[DocumentsLitige]') AND type in (N'U'))
BEGIN
    -- Créer la table DocumentsLitige
    CREATE TABLE [app].[DocumentsLitige] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [LitigeId] INT NOT NULL,
        [Nom] NVARCHAR(255) NOT NULL,
        [Description] NVARCHAR(MAX) NULL,
        [CheminFichier] NVARCHAR(MAX) NOT NULL,
        [TypeFichier] NVARCHAR(50) NOT NULL,
        [TailleFichier] BIGINT NOT NULL,
        [DateAjout] DATETIME NOT NULL DEFAULT GETDATE(),
        [UtilisateurId] INT NOT NULL,
        [DateCreation] DATETIME NOT NULL DEFAULT GETDATE(),
        [CreePar] INT NOT NULL,
        [DateModification] DATETIME NULL,
        [ModifiePar] INT NULL,
        [EstActif] BIT NOT NULL DEFAULT 1,
        CONSTRAINT [FK_DocumentsLitige_Litiges] FOREIGN KEY ([LitigeId]) REFERENCES [app].[Litiges] ([Id]),
        CONSTRAINT [FK_DocumentsLitige_Utilisateurs] FOREIGN KEY ([UtilisateurId]) REFERENCES [app].[Utilisateurs] ([Id])
    );

    PRINT 'Table DocumentsLitige créée avec succès.';
END
ELSE
BEGIN
    PRINT 'La table DocumentsLitige existe déjà.';
END
GO

-- Ajouter un champ pour activer/désactiver l'escalade automatique dans la table EtapesLitige
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[app].[EtapesLitige]') AND name = 'EscaladeAutomatique')
BEGIN
    ALTER TABLE [app].[EtapesLitige] ADD [EscaladeAutomatique] BIT NOT NULL DEFAULT 0;
    PRINT 'Colonne EscaladeAutomatique ajoutée à la table EtapesLitige.';
END
ELSE
BEGIN
    PRINT 'La colonne EscaladeAutomatique existe déjà dans la table EtapesLitige.';
END
GO

-- Ajouter un champ pour l'étape d'escalade dans la table EtapesLitige
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[app].[EtapesLitige]') AND name = 'EtapeEscaladeId')
BEGIN
    ALTER TABLE [app].[EtapesLitige] ADD [EtapeEscaladeId] INT NULL;
    PRINT 'Colonne EtapeEscaladeId ajoutée à la table EtapesLitige.';
    
    -- Ajouter la contrainte de clé étrangère
    ALTER TABLE [app].[EtapesLitige] ADD CONSTRAINT [FK_EtapesLitige_EtapeEscalade] 
    FOREIGN KEY ([EtapeEscaladeId]) REFERENCES [app].[EtapesLitige] ([Id]);
    PRINT 'Contrainte FK_EtapesLitige_EtapeEscalade ajoutée.';
END
ELSE
BEGIN
    PRINT 'La colonne EtapeEscaladeId existe déjà dans la table EtapesLitige.';
END
GO

-- Créer les index pour améliorer les performances
CREATE INDEX [IX_NotificationsLitige_LitigeId] ON [app].[NotificationsLitige] ([LitigeId]);
CREATE INDEX [IX_NotificationsLitige_UtilisateurId] ON [app].[NotificationsLitige] ([UtilisateurId]);
CREATE INDEX [IX_NotificationsLitige_Type] ON [app].[NotificationsLitige] ([Type]);
CREATE INDEX [IX_NotificationsLitige_EstLue] ON [app].[NotificationsLitige] ([EstLue]);
CREATE INDEX [IX_NotificationsLitige_EstActif] ON [app].[NotificationsLitige] ([EstActif]);

CREATE INDEX [IX_DocumentsLitige_LitigeId] ON [app].[DocumentsLitige] ([LitigeId]);
CREATE INDEX [IX_DocumentsLitige_UtilisateurId] ON [app].[DocumentsLitige] ([UtilisateurId]);
CREATE INDEX [IX_DocumentsLitige_EstActif] ON [app].[DocumentsLitige] ([EstActif]);
GO

-- Ajouter des permissions pour la gestion des notifications et documents de litiges
INSERT INTO [app].[Permissions] ([Nom], [Description], [Code], [Module], [CreePar], [EstActif])
VALUES 
    ('Voir notifications litiges', 'Permet de voir les notifications de litiges', 'DISPUTES_NOTIFICATIONS_VIEW', 'Litiges', 1, 1),
    ('Marquer notification comme lue', 'Permet de marquer une notification de litige comme lue', 'DISPUTES_NOTIFICATIONS_MARK_READ', 'Litiges', 1, 1),
    ('Ajouter document litige', 'Permet d''ajouter un document à un litige', 'DISPUTES_DOCUMENTS_ADD', 'Litiges', 1, 1),
    ('Voir documents litige', 'Permet de voir les documents d''un litige', 'DISPUTES_DOCUMENTS_VIEW', 'Litiges', 1, 1),
    ('Supprimer document litige', 'Permet de supprimer un document d''un litige', 'DISPUTES_DOCUMENTS_DELETE', 'Litiges', 1, 1);
GO

-- Mettre à jour les étapes existantes pour configurer l'escalade automatique
UPDATE [app].[EtapesLitige]
SET [EscaladeAutomatique] = 1,
    [EtapeEscaladeId] = (SELECT Id FROM [app].[EtapesLitige] WHERE [Ordre] = 3)
WHERE [Ordre] = 2; -- Étape "Analyse" escalade vers "Vérification"

UPDATE [app].[EtapesLitige]
SET [EscaladeAutomatique] = 1,
    [EtapeEscaladeId] = (SELECT Id FROM [app].[EtapesLitige] WHERE [Ordre] = 4)
WHERE [Ordre] = 3; -- Étape "Vérification" escalade vers "Négociation"

UPDATE [app].[EtapesLitige]
SET [EscaladeAutomatique] = 1,
    [EtapeEscaladeId] = (SELECT Id FROM [app].[EtapesLitige] WHERE [Ordre] = 5)
WHERE [Ordre] = 4; -- Étape "Négociation" escalade vers "Proposition"
GO
