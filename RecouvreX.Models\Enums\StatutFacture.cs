namespace RecouvreX.Models.Enums
{
    /// <summary>
    /// Énumération des statuts possibles pour une facture
    /// </summary>
    public static class StatutFacture
    {
        public const string EnAttente = "En attente";
        public const string PayeePartiellement = "Payée partiellement";
        public const string Payee = "Payée";
        public const string EnRetard = "En retard";
        public const string Annulee = "Annulée";
        public const string Litige = "Litige";
    }
}
