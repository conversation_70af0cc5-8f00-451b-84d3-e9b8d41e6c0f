using RecouvreX.Models.Reporting;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des rapports personnalisés
    /// </summary>
    public interface IRapportPersonnaliseRepository : IRepository<RapportPersonnalise>
    {
        /// <summary>
        /// Récupère les rapports personnalisés d'un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des rapports personnalisés de l'utilisateur</returns>
        Task<IEnumerable<RapportPersonnalise>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les rapports personnalisés programmés à générer
        /// </summary>
        /// <returns>Liste des rapports personnalisés programmés</returns>
        Task<IEnumerable<RapportPersonnalise>> GetScheduledReportsAsync();

        /// <summary>
        /// Met à jour la date de dernière génération d'un rapport
        /// </summary>
        /// <param name="id">Identifiant du rapport</param>
        /// <param name="dateGeneration">Date de génération</param>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateLastGenerationDateAsync(int id, DateTime dateGeneration, int userId);

        /// <summary>
        /// Récupère les rapports programmés à générer à une date et heure spécifiques
        /// </summary>
        /// <param name="date">Date et heure</param>
        /// <returns>Liste des rapports à générer</returns>
        Task<IEnumerable<RapportPersonnalise>> GetReportsToGenerateAsync(DateTime date);
    }
}
