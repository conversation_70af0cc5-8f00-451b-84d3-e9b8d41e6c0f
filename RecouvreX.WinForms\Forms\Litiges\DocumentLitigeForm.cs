using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.Litiges
{
    /// <summary>
    /// Formulaire de gestion des documents liés à un litige
    /// </summary>
    public partial class DocumentLitigeForm : Form
    {
        private readonly ILitigeService _litigeService;
        private readonly int _currentUserId;
        private readonly int _litigeId;
        private readonly Litige _litige;
        private List<DocumentLitige> _documents = new List<DocumentLitige>();

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="litigeService">Service de gestion des litiges</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <param name="litige">Litige associé</param>
        public DocumentLitigeForm(ILitigeService litigeService, int currentUserId, int litigeId, Litige litige)
        {
            _litigeService = litigeService ?? throw new ArgumentNullException(nameof(litigeService));
            _currentUserId = currentUserId;
            _litigeId = litigeId;
            _litige = litige ?? throw new ArgumentNullException(nameof(litige));

            InitializeComponent();
        }

        // La méthode InitializeComponent() a été déplacée dans le fichier DocumentLitigeForm.Designer.cs

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void DocumentLitigeForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Définir le titre du formulaire
                this.Text = $"Documents du litige #{_litigeId}";

                // Charger l'icône du formulaire
                try
                {
                    this.Icon = new Icon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "document.ico"));
                }
                catch
                {
                    // Ignorer les erreurs d'icône
                }

                // Initialiser les informations du litige
                if (titleLabel != null)
                {
                    titleLabel.Text = $"Documents du litige #{_litigeId} - {_litige.Facture?.Numero}";
                }

                if (descriptionLabel != null)
                {
                    descriptionLabel.Text = $"Description : {_litige.Description}";
                }

                // Charger les documents
                await LoadDocumentsAsync();

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors du chargement du formulaire de documents de litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Charge les documents du litige
        /// </summary>
        private async Task LoadDocumentsAsync()
        {
            try
            {
                // Récupérer les documents
                _documents = (await _litigeService.GetDocumentsByLitigeIdAsync(_litigeId)).ToList();

                // Afficher les documents dans le DataGridView
                DisplayDocuments();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des documents");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Affiche les documents dans le DataGridView
        /// </summary>
        private void DisplayDocuments()
        {
            var dataGridView = this.Controls.Find("documentsDataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                // Créer une table de données
                var dataTable = new DataTable();
                dataTable.Columns.Add("Id", typeof(int));
                dataTable.Columns.Add("Nom", typeof(string));
                dataTable.Columns.Add("Description", typeof(string));
                dataTable.Columns.Add("Type", typeof(string));
                dataTable.Columns.Add("Taille", typeof(string));
                dataTable.Columns.Add("DateAjout", typeof(string));
                dataTable.Columns.Add("Utilisateur", typeof(string));

                // Remplir la table avec les données des documents
                foreach (var document in _documents)
                {
                    dataTable.Rows.Add(
                        document.Id,
                        document.Nom,
                        document.Description,
                        document.TypeFichier,
                        FormatFileSize(document.TailleFichier),
                        document.DateAjout.ToString("dd/MM/yyyy HH:mm"),
                        document.Utilisateur?.NomComplet ?? "N/A"
                    );
                }

                // Assigner la table au DataGridView
                dataGridView.DataSource = dataTable;

                // Masquer la colonne Id
                if (dataGridView.Columns["Id"] != null)
                {
                    dataGridView.Columns["Id"].Visible = false;
                }
            }
        }

        /// <summary>
        /// Formate la taille d'un fichier en Ko, Mo, etc.
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] suffixes = { "o", "Ko", "Mo", "Go", "To" };
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number = number / 1024;
                counter++;
            }
            return $"{number:n1} {suffixes[counter]}";
        }

        /// <summary>
        /// Événement de double-clic sur une cellule du DataGridView
        /// </summary>
        private void DocumentsDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                OpenSelectedDocument();
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Ajouter
        /// </summary>
        private void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Ouvrir une boîte de dialogue pour sélectionner un fichier
                using (var openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Title = "Sélectionner un document";
                    openFileDialog.Filter = "Tous les fichiers (*.*)|*.*";
                    openFileDialog.Multiselect = false;

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        // Demander une description pour le document
                        string description = string.Empty;
                        using (var inputDialog = new InputDialog("Description du document", "Veuillez entrer une description pour ce document :"))
                        {
                            if (inputDialog.ShowDialog() == DialogResult.OK)
                            {
                                description = inputDialog.InputText;
                            }
                            else
                            {
                                return; // Annulation
                            }
                        }

                        // Créer le répertoire de documents s'il n'existe pas
                        string documentsDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documents", "Litiges", _litigeId.ToString());
                        Directory.CreateDirectory(documentsDir);

                        // Copier le fichier dans le répertoire de documents
                        string fileName = Path.GetFileName(openFileDialog.FileName);
                        string destPath = Path.Combine(documentsDir, fileName);

                        // Si le fichier existe déjà, ajouter un suffixe
                        if (File.Exists(destPath))
                        {
                            string fileNameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
                            string fileExt = Path.GetExtension(fileName);
                            string newFileName = $"{fileNameWithoutExt}_{DateTime.Now.ToString("yyyyMMddHHmmss")}{fileExt}";
                            destPath = Path.Combine(documentsDir, newFileName);
                            fileName = newFileName;
                        }

                        File.Copy(openFileDialog.FileName, destPath);

                        // Créer le document
                        var document = new DocumentLitige
                        {
                            LitigeId = _litigeId,
                            Nom = fileName,
                            Description = description,
                            CheminFichier = destPath,
                            TypeFichier = Path.GetExtension(fileName).TrimStart('.').ToUpper(),
                            TailleFichier = new FileInfo(destPath).Length,
                            DateAjout = DateTime.Now,
                            UtilisateurId = _currentUserId
                        };

                        // Enregistrer le document
                        _ = AddDocumentAsync(document);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ajout d'un document");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Ajoute un document au litige
        /// </summary>
        private async Task AddDocumentAsync(DocumentLitige document)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Ajouter le document
                await _litigeService.AddDocumentAsync(document, _currentUserId);

                // Recharger les documents
                await LoadDocumentsAsync();

                // Restaurer le curseur
                this.Cursor = Cursors.Default;

                MessageBox.Show("Le document a été ajouté avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors de l'ajout d'un document");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Modifier
        /// </summary>
        private void EditButton_Click(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = this.Controls.Find("documentsDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int documentId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    var document = _documents.FirstOrDefault(d => d.Id == documentId);
                    if (document != null)
                    {
                        // Demander une nouvelle description pour le document
                        using (var inputDialog = new InputDialog("Modifier la description", "Veuillez entrer une nouvelle description :", document.Description))
                        {
                            if (inputDialog.ShowDialog() == DialogResult.OK)
                            {
                                document.Description = inputDialog.InputText;
                                _ = UpdateDocumentAsync(document);
                            }
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un document à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la modification d'un document");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Met à jour un document
        /// </summary>
        private async Task UpdateDocumentAsync(DocumentLitige document)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Mettre à jour le document
                await _litigeService.UpdateDocumentAsync(document, _currentUserId);

                // Recharger les documents
                await LoadDocumentsAsync();

                // Restaurer le curseur
                this.Cursor = Cursors.Default;

                MessageBox.Show("Le document a été mis à jour avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors de la mise à jour d'un document");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Supprimer
        /// </summary>
        private void DeleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                var dataGridView = this.Controls.Find("documentsDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int documentId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    var document = _documents.FirstOrDefault(d => d.Id == documentId);
                    if (document != null)
                    {
                        // Demander confirmation
                        if (MessageBox.Show($"Êtes-vous sûr de vouloir supprimer le document '{document.Nom}' ?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                        {
                            _ = DeleteDocumentAsync(documentId);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un document à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression d'un document");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Supprime un document
        /// </summary>
        private async Task DeleteDocumentAsync(int documentId)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Supprimer le document
                await _litigeService.DeleteDocumentAsync(documentId, _currentUserId);

                // Recharger les documents
                await LoadDocumentsAsync();

                // Restaurer le curseur
                this.Cursor = Cursors.Default;

                MessageBox.Show("Le document a été supprimé avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors de la suppression d'un document");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Ouvrir
        /// </summary>
        private void OpenButton_Click(object sender, EventArgs e)
        {
            OpenSelectedDocument();
        }

        /// <summary>
        /// Ouvre le document sélectionné
        /// </summary>
        private void OpenSelectedDocument()
        {
            try
            {
                var dataGridView = this.Controls.Find("documentsDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView != null && dataGridView.SelectedRows.Count > 0)
                {
                    int documentId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    var document = _documents.FirstOrDefault(d => d.Id == documentId);
                    if (document != null)
                    {
                        // Vérifier que le fichier existe
                        if (File.Exists(document.CheminFichier))
                        {
                            // Ouvrir le fichier avec l'application par défaut
                            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                            {
                                FileName = document.CheminFichier,
                                UseShellExecute = true
                            });
                        }
                        else
                        {
                            MessageBox.Show("Le fichier n'existe pas ou a été déplacé.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez sélectionner un document à ouvrir.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture d'un document");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Fermer
        /// </summary>
        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }

    /// <summary>
    /// Boîte de dialogue pour saisir du texte
    /// </summary>
    public class InputDialog : Form
    {
        private TextBox _textBox;
        private Button _okButton;
        private Button _cancelButton;

        /// <summary>
        /// Texte saisi
        /// </summary>
        public string InputText { get; private set; }

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="title">Titre de la boîte de dialogue</param>
        /// <param name="prompt">Message à afficher</param>
        /// <param name="defaultText">Texte par défaut</param>
        public InputDialog(string title, string prompt, string defaultText = "")
        {
            this.Text = title;
            this.Size = new Size(400, 200);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MinimizeBox = false;
            this.MaximizeBox = false;

            var label = new Label
            {
                Text = prompt,
                AutoSize = true,
                Location = new Point(20, 20)
            };
            this.Controls.Add(label);

            _textBox = new TextBox
            {
                Text = defaultText,
                Location = new Point(20, 50),
                Size = new Size(340, 25),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Height = 60
            };
            this.Controls.Add(_textBox);

            _okButton = new Button
            {
                Text = "OK",
                DialogResult = DialogResult.OK,
                Location = new Point(180, 120),
                Size = new Size(80, 30)
            };
            _okButton.Click += (s, e) => { InputText = _textBox.Text; this.Close(); };
            this.Controls.Add(_okButton);

            _cancelButton = new Button
            {
                Text = "Annuler",
                DialogResult = DialogResult.Cancel,
                Location = new Point(280, 120),
                Size = new Size(80, 30)
            };
            _cancelButton.Click += (s, e) => { this.Close(); };
            this.Controls.Add(_cancelButton);

            this.AcceptButton = _okButton;
            this.CancelButton = _cancelButton;
        }
    }
}
