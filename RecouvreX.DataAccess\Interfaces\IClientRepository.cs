using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des clients
    /// </summary>
    public interface IClientRepository : IRepository<Client>
    {
        /// <summary>
        /// Récupère un client par son code
        /// </summary>
        /// <param name="code">Code du client</param>
        /// <returns>Client trouvé ou null</returns>
        Task<Client> GetByCodeAsync(string code);

        /// <summary>
        /// Récupère un client avec ses contacts
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Client avec ses contacts</returns>
        Task<Client> GetWithContactsAsync(int clientId);

        /// <summary>
        /// Récupère un client avec ses factures
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Client avec ses factures</returns>
        Task<Client> GetWithFacturesAsync(int clientId);

        /// <summary>
        /// Récupère tous les clients d'un commercial
        /// </summary>
        /// <param name="commercialId">Identifiant du commercial</param>
        /// <returns>Liste des clients du commercial</returns>
        Task<IEnumerable<Client>> GetByCommercialIdAsync(int commercialId);

        /// <summary>
        /// Recherche des clients par nom ou raison sociale
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des clients correspondant au terme de recherche</returns>
        Task<IEnumerable<Client>> SearchByNameAsync(string searchTerm);

        /// <summary>
        /// Met à jour le solde d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="montant">Montant à ajouter (positif) ou soustraire (négatif) au solde</param>
        /// <returns>Nouveau solde du client</returns>
        Task<decimal> UpdateSoldeAsync(int clientId, decimal montant);

        /// <summary>
        /// Récupère les clients avec des factures en retard
        /// </summary>
        /// <returns>Liste des clients avec des factures en retard</returns>
        Task<IEnumerable<Client>> GetWithOverdueInvoicesAsync();

        /// <summary>
        /// Récupère les clients par segment
        /// </summary>
        /// <param name="segment">Segment de client</param>
        /// <returns>Liste des clients du segment spécifié</returns>
        Task<IEnumerable<Client>> GetBySegmentAsync(SegmentClient segment);

        /// <summary>
        /// Met à jour le segment d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="segment">Nouveau segment</param>
        /// <param name="commentaire">Commentaire sur la segmentation</param>
        /// <param name="segmentationManuelle">Indique si la segmentation a été faite manuellement</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Client mis à jour</returns>
        Task<Client> UpdateSegmentAsync(int clientId, SegmentClient segment, string commentaire, bool segmentationManuelle, int modifiePar);

        /// <summary>
        /// Récupère la répartition des clients par segment
        /// </summary>
        /// <returns>Dictionnaire avec le segment comme clé et le nombre de clients comme valeur</returns>
        Task<Dictionary<SegmentClient, int>> GetClientDistributionBySegmentAsync();
    }
}
