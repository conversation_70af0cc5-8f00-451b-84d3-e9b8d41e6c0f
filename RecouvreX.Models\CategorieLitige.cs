using System.Collections.Generic;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une catégorie de litige
    /// </summary>
    public class CategorieLitige : BaseEntity
    {
        /// <summary>
        /// Nom de la catégorie
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Description de la catégorie
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Couleur associée à la catégorie (au format hexadécimal)
        /// </summary>
        public string Couleur { get; set; }

        /// <summary>
        /// Délai de résolution recommandé (en jours)
        /// </summary>
        public int DelaiResolutionJours { get; set; }

        /// <summary>
        /// Liste des litiges associés à cette catégorie
        /// </summary>
        public List<Litige> Litiges { get; set; }
    }
}
