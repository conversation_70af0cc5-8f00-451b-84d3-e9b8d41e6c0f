using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour la gestion des segments client
    /// </summary>
    public class SegmentClientRepository : ISegmentClientRepository
    {
        private readonly DatabaseConnection _dbConnection;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public SegmentClientRepository(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection ?? throw new ArgumentNullException(nameof(dbConnection));
        }

        /// <summary>
        /// Récupère les clients par segment
        /// </summary>
        /// <param name="segment">Segment de client</param>
        /// <returns>Liste des clients du segment spécifié</returns>
        public async Task<IEnumerable<Client>> GetClientsBySegmentAsync(SegmentClient segment)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT *
                    FROM Clients
                    WHERE Segment = @Segment
                        AND EstActif = 1
                    ORDER BY RaisonSociale";

                return await connection.QueryAsync<Client>(query, new { Segment = (int)segment });
            }
        }

        /// <summary>
        /// Met à jour le segment d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="segment">Nouveau segment</param>
        /// <param name="commentaire">Commentaire sur la segmentation</param>
        /// <param name="segmentationManuelle">Indique si la segmentation a été faite manuellement</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Client mis à jour</returns>
        public async Task<Client> UpdateClientSegmentAsync(int clientId, SegmentClient segment, string commentaire, bool segmentationManuelle, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE Clients
                    SET Segment = @Segment,
                        CommentaireSegmentation = @Commentaire,
                        DateSegmentation = @DateSegmentation,
                        SegmentationManuelle = @SegmentationManuelle,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @ClientId AND EstActif = 1;

                    SELECT *
                    FROM Clients
                    WHERE Id = @ClientId AND EstActif = 1";

                var parameters = new
                {
                    ClientId = clientId,
                    Segment = (int)segment,
                    Commentaire = commentaire,
                    DateSegmentation = DateTime.Now,
                    SegmentationManuelle = segmentationManuelle,
                    DateModification = DateTime.Now,
                    ModifiePar = modifiePar
                };

                return await connection.QueryFirstOrDefaultAsync<Client>(query, parameters);
            }
        }

        /// <summary>
        /// Récupère la répartition des clients par segment
        /// </summary>
        /// <returns>Dictionnaire avec le segment comme clé et le nombre de clients comme valeur</returns>
        public async Task<Dictionary<SegmentClient, int>> GetClientDistributionBySegmentAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT Segment, COUNT(*) AS Count
                    FROM Clients
                    WHERE EstActif = 1
                    GROUP BY Segment
                    ORDER BY Segment";

                var results = await connection.QueryAsync<(int Segment, int Count)>(query);

                return results.ToDictionary(
                    r => (SegmentClient)r.Segment,
                    r => r.Count);
            }
        }
    }
}
