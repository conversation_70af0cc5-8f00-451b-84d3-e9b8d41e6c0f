using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des communications
    /// </summary>
    public interface ICommunicationRepository : IRepository<Communication>
    {
        /// <summary>
        /// Récupère les communications par facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des communications pour la facture spécifiée</returns>
        Task<IEnumerable<Communication>> GetByFactureIdAsync(int factureId);

        /// <summary>
        /// Récupère les communications par client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des communications pour le client spécifié</returns>
        Task<IEnumerable<Communication>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère les communications par utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des communications pour l'utilisateur spécifié</returns>
        Task<IEnumerable<Communication>> GetByUtilisateurIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les communications par type
        /// </summary>
        /// <param name="type">Type de communication</param>
        /// <returns>Liste des communications du type spécifié</returns>
        Task<IEnumerable<Communication>> GetByTypeAsync(string type);

        /// <summary>
        /// Récupère les communications par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des communications pour la période spécifiée</returns>
        Task<IEnumerable<Communication>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin);

        /// <summary>
        /// Récupère les communications nécessitant un suivi
        /// </summary>
        /// <returns>Liste des communications nécessitant un suivi</returns>
        Task<IEnumerable<Communication>> GetRequiringSuiviAsync();

        /// <summary>
        /// Récupère les communications avec leurs relations (facture, utilisateur, etc.)
        /// </summary>
        /// <returns>Liste des communications avec leurs relations</returns>
        Task<IEnumerable<Communication>> GetWithRelationsAsync();

        /// <summary>
        /// Marque une communication comme lue
        /// </summary>
        /// <param name="id">Identifiant de la communication</param>
        /// <param name="dateLecture">Date de lecture</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> MarkAsReadAsync(int id, DateTime dateLecture);

        /// <summary>
        /// Met à jour le statut de suivi d'une communication
        /// </summary>
        /// <param name="id">Identifiant de la communication</param>
        /// <param name="suiviNecessaire">Indique si un suivi est nécessaire</param>
        /// <param name="dateSuivi">Date de suivi prévue</param>
        /// <param name="notesSuivi">Notes de suivi</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdateSuiviAsync(int id, bool suiviNecessaire, DateTime? dateSuivi, string notesSuivi, int modifiePar);
    }
}
