using System.Collections.Generic;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une permission dans le système
    /// </summary>
    public class Permission : BaseEntity
    {
        /// <summary>
        /// Nom de la permission
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Description de la permission
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Code unique de la permission
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Module auquel appartient la permission
        /// </summary>
        public string Module { get; set; }

        /// <summary>
        /// Liste des associations rôle-permission (navigation property)
        /// </summary>
        public ICollection<RolePermission> RolePermissions { get; set; }
    }
}
