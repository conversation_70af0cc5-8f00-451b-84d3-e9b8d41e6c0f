using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;

namespace RecouvreX.WinForms.Forms.Actions
{
    partial class ActionPrioritaireListForm
    {
        /// <summary>
        /// Variable nécessaire au concepteur.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private Panel filterPanel;
        private ComboBox prioriteComboBox;
        private ComboBox typeActionComboBox;
        private ComboBox statutComboBox;
        private TextBox searchTextBox;
        private Button searchButton;
        private SplitContainer splitContainer;
        private DataGridView actionsDataGridView;
        private Chart distributionChart;
        private Panel buttonPanel;
        private Button generateButton;
        private Button createButton;
        private Button completeButton;
        private Button assignButton;
        private Button closeButton;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel countLabel;

        /// <summary>
        /// Nettoyage des ressources utilisées.
        /// </summary>
        /// <param name="disposing">true si les ressources managées doivent être supprimées ; sinon, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Code généré par le Concepteur Windows Form

        /// <summary>
        /// Méthode requise pour la prise en charge du concepteur - ne modifiez pas
        /// le contenu de cette méthode avec l'éditeur de code.
        /// </summary>
        private void InitializeComponent()
        {
            this.mainPanel = new Panel();
            this.filterPanel = new Panel();
            this.prioriteComboBox = new ComboBox();
            this.typeActionComboBox = new ComboBox();
            this.statutComboBox = new ComboBox();
            this.searchTextBox = new TextBox();
            this.searchButton = new Button();
            this.splitContainer = new SplitContainer();
            this.actionsDataGridView = new DataGridView();
            this.distributionChart = new Chart();
            this.buttonPanel = new Panel();
            this.generateButton = new Button();
            this.createButton = new Button();
            this.completeButton = new Button();
            this.assignButton = new Button();
            this.closeButton = new Button();
            this.statusStrip = new StatusStrip();
            this.countLabel = new ToolStripStatusLabel();

            this.mainPanel.SuspendLayout();
            this.filterPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).BeginInit();
            this.splitContainer.Panel1.SuspendLayout();
            this.splitContainer.Panel2.SuspendLayout();
            this.splitContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.actionsDataGridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.distributionChart)).BeginInit();
            this.buttonPanel.SuspendLayout();
            this.statusStrip.SuspendLayout();
            this.SuspendLayout();

            //
            // mainPanel
            //
            this.mainPanel.Dock = DockStyle.Fill;
            this.mainPanel.Padding = new Padding(10);
            this.mainPanel.Controls.Add(this.splitContainer);
            this.mainPanel.Controls.Add(this.filterPanel);
            this.mainPanel.Controls.Add(this.buttonPanel);

            //
            // filterPanel
            //
            this.filterPanel.Dock = DockStyle.Top;
            this.filterPanel.Height = 50;
            this.filterPanel.Padding = new Padding(5);
            this.filterPanel.Controls.Add(this.prioriteComboBox);
            this.filterPanel.Controls.Add(this.typeActionComboBox);
            this.filterPanel.Controls.Add(this.statutComboBox);
            this.filterPanel.Controls.Add(this.searchTextBox);
            this.filterPanel.Controls.Add(this.searchButton);

            // Ajouter des labels pour les filtres
            Label prioriteLabel = new Label();
            prioriteLabel.Text = "Priorité :";
            prioriteLabel.AutoSize = true;
            prioriteLabel.Location = new Point(10, 15);
            this.filterPanel.Controls.Add(prioriteLabel);

            Label typeActionLabel = new Label();
            typeActionLabel.Text = "Type d'action :";
            typeActionLabel.AutoSize = true;
            typeActionLabel.Location = new Point(240, 15);
            this.filterPanel.Controls.Add(typeActionLabel);

            Label statutLabel = new Label();
            statutLabel.Text = "Statut :";
            statutLabel.AutoSize = true;
            statutLabel.Location = new Point(530, 15);
            this.filterPanel.Controls.Add(statutLabel);

            Label searchLabel = new Label();
            searchLabel.Text = "Recherche :";
            searchLabel.AutoSize = true;
            searchLabel.Location = new Point(750, 15);
            this.filterPanel.Controls.Add(searchLabel);

            //
            // prioriteComboBox
            //
            this.prioriteComboBox.Name = "prioriteComboBox";
            this.prioriteComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.prioriteComboBox.Location = new Point(70, 12);
            this.prioriteComboBox.Width = 150;
            this.prioriteComboBox.Items.AddRange(new object[] { "Toutes", "Critique", "Haute", "Moyenne", "Basse" });
            this.prioriteComboBox.SelectedIndex = 0;
            this.prioriteComboBox.SelectedIndexChanged += new EventHandler(this.PrioriteComboBox_SelectedIndexChanged);

            //
            // typeActionComboBox
            //
            this.typeActionComboBox.Name = "typeActionComboBox";
            this.typeActionComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.typeActionComboBox.Location = new Point(330, 12);
            this.typeActionComboBox.Width = 180;
            this.typeActionComboBox.Items.AddRange(new object[] {
                "Toutes",
                "Relance téléphonique",
                "Relance par email",
                "Relance par courrier",
                "Négociation plan de paiement",
                "Escalade",
                "Procédure juridique",
                "Autre"
            });
            this.typeActionComboBox.SelectedIndex = 0;
            this.typeActionComboBox.SelectedIndexChanged += new EventHandler(this.TypeActionComboBox_SelectedIndexChanged);

            //
            // statutComboBox
            //
            this.statutComboBox.Name = "statutComboBox";
            this.statutComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.statutComboBox.Location = new Point(580, 12);
            this.statutComboBox.Width = 150;
            this.statutComboBox.Items.AddRange(new object[] { "Toutes", "À faire", "Complétées", "En retard" });
            this.statutComboBox.SelectedIndex = 0;
            this.statutComboBox.SelectedIndexChanged += new EventHandler(this.StatutComboBox_SelectedIndexChanged);

            //
            // searchTextBox
            //
            this.searchTextBox.Name = "searchTextBox";
            this.searchTextBox.Location = new Point(820, 12);
            this.searchTextBox.Width = 200;
            this.searchTextBox.KeyDown += new KeyEventHandler(this.SearchTextBox_KeyDown);

            //
            // searchButton
            //
            this.searchButton.Text = "Rechercher";
            this.searchButton.Location = new Point(1030, 10);
            this.searchButton.Width = 100;
            this.searchButton.Height = 30;
            this.searchButton.Click += new EventHandler(this.SearchButton_Click);

            //
            // splitContainer
            //
            this.splitContainer.Dock = DockStyle.Fill;
            this.splitContainer.Orientation = Orientation.Vertical;
            this.splitContainer.SplitterDistance = 400;

            // Panneau supérieur (tableau des actions)
            Panel gridPanel = new Panel();
            gridPanel.Dock = DockStyle.Fill;
            gridPanel.Padding = new Padding(0, 10, 0, 10);
            gridPanel.Controls.Add(this.actionsDataGridView);
            this.splitContainer.Panel1.Controls.Add(gridPanel);

            //
            // actionsDataGridView
            //
            this.actionsDataGridView.Name = "actionsDataGridView";
            this.actionsDataGridView.Dock = DockStyle.Fill;
            this.actionsDataGridView.AllowUserToAddRows = false;
            this.actionsDataGridView.AllowUserToDeleteRows = false;
            this.actionsDataGridView.AllowUserToResizeRows = false;
            this.actionsDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.actionsDataGridView.ReadOnly = true;
            this.actionsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.actionsDataGridView.RowHeadersVisible = false;
            this.actionsDataGridView.MultiSelect = false;
            this.actionsDataGridView.CellFormatting += new DataGridViewCellFormattingEventHandler(this.ActionsDataGridView_CellFormatting);
            this.actionsDataGridView.CellDoubleClick += new DataGridViewCellEventHandler(this.ActionsDataGridView_CellDoubleClick);

            // Panneau inférieur (graphique de distribution)
            Panel chartPanel = new Panel();
            chartPanel.Dock = DockStyle.Fill;
            chartPanel.Padding = new Padding(0, 10, 0, 0);
            chartPanel.Controls.Add(this.distributionChart);
            this.splitContainer.Panel2.Controls.Add(chartPanel);

            //
            // distributionChart
            //
            this.distributionChart.Name = "distributionChart";
            this.distributionChart.Dock = DockStyle.Fill;
            this.distributionChart.Palette = ChartColorPalette.BrightPastel;
            this.distributionChart.Titles.Add("Distribution des actions par niveau de priorité");
            this.distributionChart.ChartAreas.Add(new ChartArea("Main"));
            this.distributionChart.Series.Add(new Series("Distribution"));
            this.distributionChart.Series["Distribution"].ChartType = SeriesChartType.Pie;
            this.distributionChart.Series["Distribution"].IsValueShownAsLabel = true;
            this.distributionChart.Series["Distribution"].LabelFormat = "{0} ({1:P0})";

            //
            // buttonPanel
            //
            this.buttonPanel.Dock = DockStyle.Bottom;
            this.buttonPanel.Height = 50;
            this.buttonPanel.Padding = new Padding(5);
            this.buttonPanel.Controls.Add(this.generateButton);
            this.buttonPanel.Controls.Add(this.createButton);
            this.buttonPanel.Controls.Add(this.completeButton);
            this.buttonPanel.Controls.Add(this.assignButton);
            this.buttonPanel.Controls.Add(this.closeButton);

            //
            // generateButton
            //
            this.generateButton.Name = "generateButton";
            this.generateButton.Text = "Générer des actions prioritaires";
            this.generateButton.Location = new Point(10, 10);
            this.generateButton.Width = 200;
            this.generateButton.Height = 30;
            this.generateButton.Click += new EventHandler(this.GenerateButton_Click);

            //
            // createButton
            //
            this.createButton.Name = "createButton";
            this.createButton.Text = "Créer une action";
            this.createButton.Location = new Point(220, 10);
            this.createButton.Width = 120;
            this.createButton.Height = 30;
            this.createButton.Click += new EventHandler(this.CreateButton_Click);

            //
            // completeButton
            //
            this.completeButton.Name = "completeButton";
            this.completeButton.Text = "Marquer comme complétée";
            this.completeButton.Location = new Point(350, 10);
            this.completeButton.Width = 180;
            this.completeButton.Height = 30;
            this.completeButton.Click += new EventHandler(this.CompleteButton_Click);

            //
            // assignButton
            //
            this.assignButton.Name = "assignButton";
            this.assignButton.Text = "Assigner";
            this.assignButton.Location = new Point(540, 10);
            this.assignButton.Width = 100;
            this.assignButton.Height = 30;
            this.assignButton.Click += new EventHandler(this.AssignButton_Click);

            //
            // closeButton
            //
            this.closeButton.Name = "closeButton";
            this.closeButton.Text = "Fermer";
            this.closeButton.Location = new Point(1080, 10);
            this.closeButton.Width = 100;
            this.closeButton.Height = 30;
            this.closeButton.Click += new EventHandler(this.CloseButton_Click);

            //
            // statusStrip
            //
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Items.Add(this.countLabel);

            //
            // countLabel
            //
            this.countLabel.Name = "countLabel";
            this.countLabel.Text = "Nombre d'actions : 0";

            //
            // ActionPrioritaireListForm
            //
            this.ClientSize = new System.Drawing.Size(1200, 700);
            this.Controls.Add(this.mainPanel);
            this.Controls.Add(this.statusStrip);
            this.Name = "ActionPrioritaireListForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Actions prioritaires";
            this.Load += new System.EventHandler(this.ActionPrioritaireListForm_Load);

            this.mainPanel.ResumeLayout(false);
            this.filterPanel.ResumeLayout(false);
            this.filterPanel.PerformLayout();
            this.splitContainer.Panel1.ResumeLayout(false);
            this.splitContainer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).EndInit();
            this.splitContainer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.actionsDataGridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.distributionChart)).EndInit();
            this.buttonPanel.ResumeLayout(false);
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }



        #endregion
    }
}
