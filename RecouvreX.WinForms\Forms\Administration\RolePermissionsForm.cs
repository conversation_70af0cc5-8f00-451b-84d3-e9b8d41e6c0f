using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Administration
{
    public partial class RolePermissionsForm : Form
    {
        private readonly IRoleService _roleService;
        private readonly int _currentUserId;
        private readonly int _roleId;
        private readonly string _roleName;
        private List<Permission> _allPermissions = new List<Permission>();
        private List<Permission> _rolePermissions = new List<Permission>();
        private Dictionary<string, List<Permission>> _permissionsByModule = new Dictionary<string, List<Permission>>();

        public RolePermissionsForm(IRoleService roleService, int currentUserId, int roleId, string roleName)
        {
            _roleService = roleService ?? throw new ArgumentNullException(nameof(roleService));
            _currentUserId = currentUserId;
            _roleId = roleId;
            _roleName = roleName;

            InitializeComponent();

            // Mettre à jour le titre du formulaire après l'initialisation des composants
            this.Text = $"Permissions du rôle : {_roleName}";
        }

        private async void RolePermissionsForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Charger toutes les permissions
                _allPermissions = (await _roleService.GetAllPermissionsAsync()).ToList();

                // Charger les permissions du rôle
                _rolePermissions = (await _roleService.GetPermissionsByRoleIdAsync(_roleId)).ToList();

                // Organiser les permissions par module
                OrganizePermissionsByModule();

                // Remplir la ComboBox des modules
                FillModuleComboBox();

                // Remplir le TreeView des permissions
                FillPermissionsTreeView();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des permissions");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des permissions : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void OrganizePermissionsByModule()
        {
            _permissionsByModule.Clear();

            foreach (var permission in _allPermissions)
            {
                string module = permission.Module;
                if (!_permissionsByModule.ContainsKey(module))
                {
                    _permissionsByModule[module] = new List<Permission>();
                }
                _permissionsByModule[module].Add(permission);
            }
        }

        private void FillModuleComboBox()
        {
            var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
            var moduleComboBox = toolStrip?.Items.Find("moduleComboBox", false).FirstOrDefault() as ToolStripComboBox;
            if (moduleComboBox != null)
            {
                moduleComboBox.Items.Clear();
                moduleComboBox.Items.Add("Tous les modules");
                foreach (var module in _permissionsByModule.Keys.OrderBy(m => m))
                {
                    moduleComboBox.Items.Add(module);
                }
                moduleComboBox.SelectedIndex = 0;
            }
        }

        private void FillPermissionsTreeView()
        {
            var permissionsTreeView = this.Controls.Find("permissionsTreeView", true).FirstOrDefault() as TreeView;
            if (permissionsTreeView != null)
            {
                permissionsTreeView.BeginUpdate();
                permissionsTreeView.Nodes.Clear();

                var toolStrip = this.Controls.Find("toolStrip", true).FirstOrDefault() as ToolStrip;
                var moduleComboBox = toolStrip?.Items.Find("moduleComboBox", false).FirstOrDefault() as ToolStripComboBox;
                string selectedModule = moduleComboBox?.SelectedItem.ToString() ?? "Tous les modules";

                if (selectedModule == "Tous les modules")
                {
                    // Afficher toutes les permissions regroupées par module
                    foreach (var module in _permissionsByModule.Keys.OrderBy(m => m))
                    {
                        TreeNode moduleNode = new TreeNode(module);
                        moduleNode.Tag = null; // Pas de permission associée au nœud de module

                        // Ajouter les permissions du module
                        foreach (var permission in _permissionsByModule[module].OrderBy(p => p.Description))
                        {
                            TreeNode permissionNode = new TreeNode(permission.Description);
                            permissionNode.Tag = permission;
                            permissionNode.Checked = _rolePermissions.Any(p => p.Id == permission.Id);
                            moduleNode.Nodes.Add(permissionNode);
                        }

                        // Vérifier si toutes les permissions du module sont sélectionnées
                        moduleNode.Checked = moduleNode.Nodes.Cast<TreeNode>().All(n => n.Checked);
                        permissionsTreeView.Nodes.Add(moduleNode);
                    }
                }
                else
                {
                    // Afficher uniquement les permissions du module sélectionné
                    if (_permissionsByModule.ContainsKey(selectedModule))
                    {
                        foreach (var permission in _permissionsByModule[selectedModule].OrderBy(p => p.Description))
                        {
                            TreeNode permissionNode = new TreeNode(permission.Description);
                            permissionNode.Tag = permission;
                            permissionNode.Checked = _rolePermissions.Any(p => p.Id == permission.Id);
                            permissionsTreeView.Nodes.Add(permissionNode);
                        }
                    }
                }

                permissionsTreeView.ExpandAll();
                permissionsTreeView.EndUpdate();
            }
        }

        private void ModuleComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillPermissionsTreeView();
        }

        private void PermissionsTreeView_AfterCheck(object sender, TreeViewEventArgs e)
        {
            var treeView = sender as TreeView;
            if (treeView != null)
            {
                // Désactiver temporairement l'événement AfterCheck pour éviter les boucles infinies
                treeView.AfterCheck -= PermissionsTreeView_AfterCheck;

                try
                {
                    // Si c'est un nœud parent (module), cocher/décocher tous les enfants
                    if (e.Node.Tag == null && e.Node.Nodes.Count > 0)
                    {
                        foreach (TreeNode childNode in e.Node.Nodes)
                        {
                            childNode.Checked = e.Node.Checked;
                        }
                    }
                    // Si c'est un nœud enfant (permission), vérifier si tous les enfants sont cochés pour mettre à jour le parent
                    else if (e.Node.Parent != null)
                    {
                        bool allChecked = true;
                        foreach (TreeNode siblingNode in e.Node.Parent.Nodes)
                        {
                            if (!siblingNode.Checked)
                            {
                                allChecked = false;
                                break;
                            }
                        }
                        e.Node.Parent.Checked = allChecked;
                    }
                }
                finally
                {
                    // Réactiver l'événement AfterCheck
                    treeView.AfterCheck += PermissionsTreeView_AfterCheck;
                }
            }
        }

        private void SelectAllButton_Click(object sender, EventArgs e)
        {
            var permissionsTreeView = this.Controls.Find("permissionsTreeView", true).FirstOrDefault() as TreeView;
            if (permissionsTreeView != null)
            {
                // Désactiver temporairement l'événement AfterCheck pour éviter les boucles infinies
                permissionsTreeView.AfterCheck -= PermissionsTreeView_AfterCheck;

                try
                {
                    // Cocher tous les nœuds
                    foreach (TreeNode node in permissionsTreeView.Nodes)
                    {
                        node.Checked = true;
                        foreach (TreeNode childNode in node.Nodes)
                        {
                            childNode.Checked = true;
                        }
                    }
                }
                finally
                {
                    // Réactiver l'événement AfterCheck
                    permissionsTreeView.AfterCheck += PermissionsTreeView_AfterCheck;
                }
            }
        }

        private void DeselectAllButton_Click(object sender, EventArgs e)
        {
            var permissionsTreeView = this.Controls.Find("permissionsTreeView", true).FirstOrDefault() as TreeView;
            if (permissionsTreeView != null)
            {
                // Désactiver temporairement l'événement AfterCheck pour éviter les boucles infinies
                permissionsTreeView.AfterCheck -= PermissionsTreeView_AfterCheck;

                try
                {
                    // Décocher tous les nœuds
                    foreach (TreeNode node in permissionsTreeView.Nodes)
                    {
                        node.Checked = false;
                        foreach (TreeNode childNode in node.Nodes)
                        {
                            childNode.Checked = false;
                        }
                    }
                }
                finally
                {
                    // Réactiver l'événement AfterCheck
                    permissionsTreeView.AfterCheck += PermissionsTreeView_AfterCheck;
                }
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer les permissions sélectionnées
                var selectedPermissions = new List<Permission>();
                var permissionsTreeView = this.Controls.Find("permissionsTreeView", true).FirstOrDefault() as TreeView;
                if (permissionsTreeView != null)
                {
                    foreach (TreeNode moduleNode in permissionsTreeView.Nodes)
                    {
                        // Si c'est un nœud de module avec des enfants
                        if (moduleNode.Tag == null && moduleNode.Nodes.Count > 0)
                        {
                            foreach (TreeNode permissionNode in moduleNode.Nodes)
                            {
                                if (permissionNode.Checked && permissionNode.Tag is Permission permission)
                                {
                                    selectedPermissions.Add(permission);
                                }
                            }
                        }
                        // Si c'est un nœud de permission (dans le cas d'un filtre par module)
                        else if (moduleNode.Checked && moduleNode.Tag is Permission permission)
                        {
                            selectedPermissions.Add(permission);
                        }
                    }
                }

                // Enregistrer les permissions du rôle
                bool success = await _roleService.UpdatePermissionsAsync(_roleId, selectedPermissions.Select(p => p.Id).ToList(), _currentUserId);
                if (success)
                {
                    MessageBox.Show("Les permissions ont été mises à jour avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Une erreur s'est produite lors de la mise à jour des permissions.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement des permissions");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement des permissions : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }
    }
}
