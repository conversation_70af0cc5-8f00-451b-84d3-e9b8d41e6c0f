using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des plans de paiement
    /// </summary>
    public class PlanPaiementService : IPlanPaiementService
    {
        private readonly IPlanPaiementRepository _planPaiementRepository;
        private readonly IEcheancePaiementRepository _echeancePaiementRepository;
        private readonly IFactureRepository _factureRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="planPaiementRepository">Repository des plans de paiement</param>
        /// <param name="echeancePaiementRepository">Repository des échéances de paiement</param>
        /// <param name="factureRepository">Repository des factures</param>
        /// <param name="clientRepository">Repository des clients</param>
        /// <param name="utilisateurRepository">Repository des utilisateurs</param>
        /// <param name="journalAuditRepository">Repository du journal d'audit</param>
        public PlanPaiementService(
            IPlanPaiementRepository planPaiementRepository,
            IEcheancePaiementRepository echeancePaiementRepository,
            IFactureRepository factureRepository,
            IClientRepository clientRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _planPaiementRepository = planPaiementRepository ?? throw new ArgumentNullException(nameof(planPaiementRepository));
            _echeancePaiementRepository = echeancePaiementRepository ?? throw new ArgumentNullException(nameof(echeancePaiementRepository));
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        #region Plans de paiement

        /// <summary>
        /// Récupère tous les plans de paiement
        /// </summary>
        /// <returns>Liste des plans de paiement</returns>
        public async Task<IEnumerable<PlanPaiement>> GetAllPlansAsync()
        {
            return await _planPaiementRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère un plan de paiement par son identifiant
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement trouvé ou null</returns>
        public async Task<PlanPaiement> GetPlanByIdAsync(int planPaiementId)
        {
            return await _planPaiementRepository.GetByIdAsync(planPaiementId);
        }

        /// <summary>
        /// Récupère un plan de paiement par sa référence
        /// </summary>
        /// <param name="reference">Référence du plan de paiement</param>
        /// <returns>Plan de paiement trouvé ou null</returns>
        public async Task<PlanPaiement> GetPlanByReferenceAsync(string reference)
        {
            return await _planPaiementRepository.GetByReferenceAsync(reference);
        }

        /// <summary>
        /// Récupère un plan de paiement avec ses échéances
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement avec ses échéances</returns>
        public async Task<PlanPaiement> GetPlanWithEcheancesAsync(int planPaiementId)
        {
            return await _planPaiementRepository.GetWithEcheancesAsync(planPaiementId);
        }

        /// <summary>
        /// Récupère un plan de paiement avec ses factures associées
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement avec ses factures</returns>
        public async Task<PlanPaiement> GetPlanWithFacturesAsync(int planPaiementId)
        {
            return await _planPaiementRepository.GetWithFacturesAsync(planPaiementId);
        }

        /// <summary>
        /// Récupère un plan de paiement avec ses documents associés
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement avec ses documents</returns>
        public async Task<PlanPaiement> GetPlanWithDocumentsAsync(int planPaiementId)
        {
            return await _planPaiementRepository.GetWithDocumentsAsync(planPaiementId);
        }

        /// <summary>
        /// Récupère un plan de paiement complet avec toutes ses relations
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Plan de paiement complet</returns>
        public async Task<PlanPaiement> GetPlanCompleteAsync(int planPaiementId)
        {
            return await _planPaiementRepository.GetCompleteAsync(planPaiementId);
        }

        /// <summary>
        /// Récupère tous les plans de paiement d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des plans de paiement du client</returns>
        public async Task<IEnumerable<PlanPaiement>> GetPlansByClientIdAsync(int clientId)
        {
            return await _planPaiementRepository.GetByClientIdAsync(clientId);
        }

        /// <summary>
        /// Récupère tous les plans de paiement associés à une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des plans de paiement associés à la facture</returns>
        public async Task<IEnumerable<PlanPaiement>> GetPlansByFactureIdAsync(int factureId)
        {
            return await _planPaiementRepository.GetByFactureIdAsync(factureId);
        }

        /// <summary>
        /// Récupère les plans de paiement avec des échéances à venir
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des plans de paiement avec des échéances dans la période spécifiée</returns>
        public async Task<IEnumerable<PlanPaiement>> GetPlansWithUpcomingEcheancesAsync(DateTime dateDebut, DateTime dateFin)
        {
            return await _planPaiementRepository.GetWithUpcomingEcheancesAsync(dateDebut, dateFin);
        }

        /// <summary>
        /// Récupère les plans de paiement avec des échéances en retard
        /// </summary>
        /// <returns>Liste des plans de paiement avec des échéances en retard</returns>
        public async Task<IEnumerable<PlanPaiement>> GetPlansWithLateEcheancesAsync()
        {
            return await _planPaiementRepository.GetWithLateEcheancesAsync();
        }

        /// <summary>
        /// Crée un nouveau plan de paiement
        /// </summary>
        /// <param name="planPaiement">Plan de paiement à créer</param>
        /// <param name="echeances">Liste des échéances du plan</param>
        /// <param name="factureIds">Liste des identifiants des factures à associer</param>
        /// <param name="montantsCouvert">Liste des montants couverts pour chaque facture</param>
        /// <param name="userId">Identifiant de l'utilisateur qui crée le plan</param>
        /// <returns>Plan de paiement créé</returns>
        public async Task<PlanPaiement> CreatePlanAsync(PlanPaiement planPaiement, List<EcheancePaiement> echeances, List<int> factureIds, List<decimal> montantsCouvert, int userId)
        {
            // Vérifier les paramètres
            if (planPaiement == null)
                throw new ArgumentNullException(nameof(planPaiement));
            if (echeances == null || !echeances.Any())
                throw new ArgumentException("La liste des échéances ne peut pas être vide", nameof(echeances));
            if (factureIds == null || !factureIds.Any())
                throw new ArgumentException("La liste des factures ne peut pas être vide", nameof(factureIds));
            if (montantsCouvert == null || montantsCouvert.Count != factureIds.Count)
                throw new ArgumentException("La liste des montants couverts doit correspondre à la liste des factures", nameof(montantsCouvert));

            // Vérifier que le client existe
            var client = await _clientRepository.GetByIdAsync(planPaiement.ClientId);
            if (client == null)
                throw new ArgumentException($"Le client avec l'ID {planPaiement.ClientId} n'existe pas");

            // Vérifier que le responsable existe
            var responsable = await _utilisateurRepository.GetByIdAsync(planPaiement.ResponsableId);
            if (responsable == null)
                throw new ArgumentException($"L'utilisateur responsable avec l'ID {planPaiement.ResponsableId} n'existe pas");

            // Générer une référence unique si non fournie
            if (string.IsNullOrEmpty(planPaiement.Reference))
            {
                planPaiement.Reference = await GenerateReferencePlanPaiementAsync();
            }
            else
            {
                // Vérifier si la référence existe déjà
                var existingPlan = await _planPaiementRepository.GetByReferenceAsync(planPaiement.Reference);
                if (existingPlan != null)
                    throw new InvalidOperationException($"La référence de plan de paiement '{planPaiement.Reference}' existe déjà");
            }

            // Créer le plan de paiement
            var createdPlan = await _planPaiementRepository.AddAsync(planPaiement, userId);
            if (createdPlan == null)
                throw new InvalidOperationException("Erreur lors de la création du plan de paiement");

            // Ajouter les échéances
            foreach (var echeance in echeances)
            {
                echeance.PlanPaiementId = createdPlan.Id;
                await _echeancePaiementRepository.AddAsync(echeance, userId);
            }

            // Associer les factures
            for (int i = 0; i < factureIds.Count; i++)
            {
                await _planPaiementRepository.AssociateToFactureAsync(createdPlan.Id, factureIds[i], montantsCouvert[i], userId);
            }

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "PlanPaiement",
                EntiteId = createdPlan.Id,
                TypeAction = "Création",
                Description = $"Création d'un plan de paiement pour le client {client.RaisonSociale} avec {echeances.Count} échéances et {factureIds.Count} factures associées",
                UtilisateurId = userId,
                DateAction = DateTime.Now,
                NomUtilisateur = "Système" // À remplacer par le nom réel si disponible
            });

            return await GetPlanCompleteAsync(createdPlan.Id);
        }

        /// <summary>
        /// Met à jour un plan de paiement existant
        /// </summary>
        /// <param name="planPaiement">Plan de paiement à mettre à jour</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue la mise à jour</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdatePlanAsync(PlanPaiement planPaiement, int userId)
        {
            if (planPaiement == null)
                throw new ArgumentNullException(nameof(planPaiement));

            // Vérifier que le plan existe
            var existingPlan = await _planPaiementRepository.GetByIdAsync(planPaiement.Id);
            if (existingPlan == null)
                throw new ArgumentException($"Le plan de paiement avec l'ID {planPaiement.Id} n'existe pas");

            // Mettre à jour le plan
            var result = await _planPaiementRepository.UpdateAsync(planPaiement, userId) != null;

            if (result)
            {
                // Journaliser l'action
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    TypeEntite = "PlanPaiement",
                    EntiteId = planPaiement.Id,
                    TypeAction = "Mise à jour",
                    Description = $"Mise à jour du plan de paiement {planPaiement.Reference}",
                    UtilisateurId = userId,
                    DateAction = DateTime.Now,
                    NomUtilisateur = "Système" // À remplacer par le nom réel si disponible
                });
            }

            return result;
        }

        /// <summary>
        /// Supprime un plan de paiement (désactivation logique)
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue la suppression</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeletePlanAsync(int planPaiementId, int userId)
        {
            // Vérifier que le plan existe
            var existingPlan = await _planPaiementRepository.GetByIdAsync(planPaiementId);
            if (existingPlan == null)
                return false;

            // Supprimer le plan (désactivation logique)
            var result = await _planPaiementRepository.DeleteAsync(planPaiementId, userId);

            if (result)
            {
                // Journaliser l'action
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    TypeEntite = "PlanPaiement",
                    EntiteId = planPaiementId,
                    TypeAction = "Suppression",
                    Description = $"Suppression du plan de paiement {existingPlan.Reference}",
                    UtilisateurId = userId,
                    DateAction = DateTime.Now,
                    NomUtilisateur = "Système" // À remplacer par le nom réel si disponible
                });
            }

            return result;
        }

        /// <summary>
        /// Génère une référence de plan de paiement unique
        /// </summary>
        /// <returns>Référence de plan de paiement unique</returns>
        private async Task<string> GenerateReferencePlanPaiementAsync()
        {
            // Format: PLAN-YYYYMMDD-XXXX où XXXX est un numéro séquentiel
            string datePrefix = DateTime.Now.ToString("yyyyMMdd");
            string prefix = $"PLAN-{datePrefix}-";

            // Récupérer tous les plans dont la référence commence par le préfixe
            var plans = await _planPaiementRepository.FindAsync("Reference LIKE @Prefix", new { Prefix = $"{prefix}%" });

            int sequence = 1;
            if (plans.Any())
            {
                // Trouver le numéro séquentiel le plus élevé
                foreach (var plan in plans)
                {
                    string[] parts = plan.Reference.Split('-');
                    if (parts.Length == 3 && int.TryParse(parts[2], out int seq))
                    {
                        sequence = Math.Max(sequence, seq + 1);
                    }
                }
            }

            return $"{prefix}{sequence:D4}";
        }

        /// <summary>
        /// Génère un plan de paiement automatique
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="factureIds">Liste des identifiants des factures à inclure</param>
        /// <param name="montantTotal">Montant total du plan (si différent de la somme des factures)</param>
        /// <param name="dateDebut">Date de début du plan</param>
        /// <param name="nombreEcheances">Nombre d'échéances</param>
        /// <param name="frequence">Fréquence des échéances (en jours)</param>
        /// <param name="tauxInteret">Taux d'intérêt annuel (en pourcentage)</param>
        /// <param name="frais">Frais additionnels</param>
        /// <param name="responsableId">Identifiant du responsable du plan</param>
        /// <param name="userId">Identifiant de l'utilisateur qui crée le plan</param>
        /// <returns>Plan de paiement généré</returns>
        public async Task<PlanPaiement> GeneratePlanAutomatiqueAsync(int clientId, List<int> factureIds, decimal? montantTotal, DateTime dateDebut, int nombreEcheances, int frequence, decimal tauxInteret, decimal frais, int responsableId, int userId)
        {
            // Vérifier les paramètres
            if (factureIds == null || !factureIds.Any())
                throw new ArgumentException("La liste des factures ne peut pas être vide", nameof(factureIds));
            if (nombreEcheances <= 0)
                throw new ArgumentException("Le nombre d'échéances doit être supérieur à zéro", nameof(nombreEcheances));
            if (frequence <= 0)
                throw new ArgumentException("La fréquence des échéances doit être supérieure à zéro", nameof(frequence));
            if (tauxInteret < 0)
                throw new ArgumentException("Le taux d'intérêt ne peut pas être négatif", nameof(tauxInteret));
            if (frais < 0)
                throw new ArgumentException("Les frais ne peuvent pas être négatifs", nameof(frais));

            // Récupérer les factures
            List<Facture> factures = new List<Facture>();
            List<decimal> montantsCouvert = new List<decimal>();
            decimal totalFactures = 0;

            foreach (var factureId in factureIds)
            {
                var facture = await _factureRepository.GetByIdAsync(factureId);
                if (facture == null)
                    throw new ArgumentException($"La facture avec l'ID {factureId} n'existe pas");
                if (facture.ClientId != clientId)
                    throw new ArgumentException($"La facture avec l'ID {factureId} n'appartient pas au client avec l'ID {clientId}");

                factures.Add(facture);
                decimal montantCouvert = facture.MontantRestant;
                montantsCouvert.Add(montantCouvert);
                totalFactures += montantCouvert;
            }

            // Calculer le montant total avec intérêts et frais
            decimal montantPrincipal = montantTotal ?? totalFactures;
            decimal montantInterets = montantPrincipal * (tauxInteret / 100) * (nombreEcheances * frequence / 365.0m);
            decimal montantTotalAvecInterets = montantPrincipal + montantInterets + frais;

            // Créer le plan de paiement
            var planPaiement = new PlanPaiement
            {
                ClientId = clientId,
                DateCreationPlan = DateTime.Now,
                DateDebut = dateDebut,
                DateFinPrevue = dateDebut.AddDays(nombreEcheances * frequence),
                MontantTotal = montantTotalAvecInterets,
                MontantInterets = montantInterets,
                MontantFrais = frais,
                Statut = "En cours",
                ResponsableId = responsableId,
                AccordSigne = false,
                EstActif = true
            };

            // Générer les échéances
            List<EcheancePaiement> echeances = new List<EcheancePaiement>();
            decimal montantParEcheance = Math.Round(montantTotalAvecInterets / nombreEcheances, 2);
            decimal montantRestant = montantTotalAvecInterets;

            for (int i = 0; i < nombreEcheances; i++)
            {
                decimal montantEcheance;
                if (i == nombreEcheances - 1)
                {
                    // Dernière échéance : ajuster pour éviter les problèmes d'arrondi
                    montantEcheance = montantRestant;
                }
                else
                {
                    montantEcheance = montantParEcheance;
                    montantRestant -= montantEcheance;
                }

                echeances.Add(new EcheancePaiement
                {
                    NumeroOrdre = i + 1,
                    DateEcheance = dateDebut.AddDays(i * frequence),
                    MontantPrevu = montantEcheance,
                    EstPayee = false,
                    EstActif = true
                });
            }

            // Créer le plan avec ses échéances et factures associées
            return await CreatePlanAsync(planPaiement, echeances, factureIds, montantsCouvert, userId);
        }

        #endregion

        #region Échéances

        /// <summary>
        /// Récupère toutes les échéances d'un plan de paiement
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Liste des échéances du plan de paiement</returns>
        public async Task<IEnumerable<EcheancePaiement>> GetEcheancesByPlanIdAsync(int planPaiementId)
        {
            return await _echeancePaiementRepository.GetByPlanPaiementIdAsync(planPaiementId);
        }

        /// <summary>
        /// Récupère une échéance par son identifiant
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <returns>Échéance trouvée ou null</returns>
        public async Task<EcheancePaiement> GetEcheanceByIdAsync(int echeanceId)
        {
            return await _echeancePaiementRepository.GetByIdAsync(echeanceId);
        }

        /// <summary>
        /// Récupère les échéances à venir pour une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des échéances à venir dans la période spécifiée</returns>
        public async Task<IEnumerable<EcheancePaiement>> GetUpcomingEcheancesAsync(DateTime dateDebut, DateTime dateFin)
        {
            return await _echeancePaiementRepository.GetUpcomingAsync(dateDebut, dateFin);
        }

        /// <summary>
        /// Récupère les échéances en retard
        /// </summary>
        /// <returns>Liste des échéances en retard</returns>
        public async Task<IEnumerable<EcheancePaiement>> GetLateEcheancesAsync()
        {
            return await _echeancePaiementRepository.GetLateAsync();
        }

        /// <summary>
        /// Marque une échéance comme payée
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="paiementId">Identifiant du paiement associé</param>
        /// <param name="montantPaye">Montant effectivement payé</param>
        /// <param name="datePaiement">Date du paiement</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> MarkEcheanceAsPaidAsync(int echeanceId, int paiementId, decimal montantPaye, DateTime datePaiement, int userId)
        {
            // Vérifier que l'échéance existe
            var echeance = await _echeancePaiementRepository.GetByIdAsync(echeanceId);
            if (echeance == null)
                return false;

            // Marquer l'échéance comme payée
            var result = await _echeancePaiementRepository.MarkAsPaidAsync(echeanceId, paiementId, montantPaye, datePaiement, userId);

            if (result)
            {
                // Journaliser l'action
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    TypeEntite = "EcheancePaiement",
                    EntiteId = echeanceId,
                    TypeAction = "Paiement",
                    Description = $"Échéance {echeance.NumeroOrdre} du plan {echeance.PlanPaiementId} marquée comme payée",
                    UtilisateurId = userId,
                    DateAction = DateTime.Now,
                    NomUtilisateur = "Système" // À remplacer par le nom réel si disponible
                });
            }

            return result;
        }

        /// <summary>
        /// Marque une échéance comme non payée (annulation d'un paiement)
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> MarkEcheanceAsUnpaidAsync(int echeanceId, int userId)
        {
            // Vérifier que l'échéance existe
            var echeance = await _echeancePaiementRepository.GetByIdAsync(echeanceId);
            if (echeance == null)
                return false;

            // Marquer l'échéance comme non payée
            var result = await _echeancePaiementRepository.MarkAsUnpaidAsync(echeanceId, userId);

            if (result)
            {
                // Journaliser l'action
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    TypeEntite = "EcheancePaiement",
                    EntiteId = echeanceId,
                    TypeAction = "Annulation paiement",
                    Description = $"Paiement de l'échéance {echeance.NumeroOrdre} du plan {echeance.PlanPaiementId} annulé",
                    UtilisateurId = userId,
                    DateAction = DateTime.Now,
                    NomUtilisateur = "Système" // À remplacer par le nom réel si disponible
                });
            }

            return result;
        }

        /// <summary>
        /// Met à jour une échéance
        /// </summary>
        /// <param name="echeance">Échéance à mettre à jour</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue la mise à jour</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateEcheanceAsync(EcheancePaiement echeance, int userId)
        {
            if (echeance == null)
                throw new ArgumentNullException(nameof(echeance));

            // Vérifier que l'échéance existe
            var existingEcheance = await _echeancePaiementRepository.GetByIdAsync(echeance.Id);
            if (existingEcheance == null)
                throw new ArgumentException($"L'échéance avec l'ID {echeance.Id} n'existe pas");

            // Mettre à jour l'échéance
            var result = await _echeancePaiementRepository.UpdateAsync(echeance, userId) != null;

            if (result)
            {
                // Journaliser l'action
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    TypeEntite = "EcheancePaiement",
                    EntiteId = echeance.Id,
                    TypeAction = "Mise à jour",
                    Description = $"Mise à jour de l'échéance {echeance.NumeroOrdre} du plan {echeance.PlanPaiementId}",
                    UtilisateurId = userId,
                    DateAction = DateTime.Now,
                    NomUtilisateur = "Système" // À remplacer par le nom réel si disponible
                });
            }

            return result;
        }

        #endregion

        /// <summary>
        /// Récupère les échéances en retard pour un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des échéances en retard pour le client</returns>
        public async Task<IEnumerable<EcheancePaiement>> GetLateEcheancesByClientIdAsync(int clientId)
        {
            // Récupérer tous les plans du client
            var plans = await _planPaiementRepository.GetByClientIdAsync(clientId);
            if (!plans.Any())
                return new List<EcheancePaiement>();

            // Récupérer toutes les échéances en retard des plans
            List<EcheancePaiement> lateEcheances = new List<EcheancePaiement>();
            foreach (var plan in plans)
            {
                var planWithEcheances = await _planPaiementRepository.GetWithEcheancesAsync(plan.Id);
                if (planWithEcheances?.Echeances != null)
                {
                    lateEcheances.AddRange(planWithEcheances.Echeances.Where(e => e.EstEnRetard));
                }
            }

            return lateEcheances;
        }

        /// <summary>
        /// Ajoute une échéance à un plan de paiement
        /// </summary>
        /// <param name="echeance">Échéance à ajouter</param>
        /// <param name="userId">Identifiant de l'utilisateur qui ajoute l'échéance</param>
        /// <returns>Échéance ajoutée</returns>
        public async Task<EcheancePaiement> AddEcheanceAsync(EcheancePaiement echeance, int userId)
        {
            if (echeance == null)
                throw new ArgumentNullException(nameof(echeance));

            // Vérifier que le plan existe
            var plan = await _planPaiementRepository.GetByIdAsync(echeance.PlanPaiementId);
            if (plan == null)
                throw new ArgumentException($"Le plan de paiement avec l'ID {echeance.PlanPaiementId} n'existe pas");

            // Ajouter l'échéance
            var addedEcheance = await _echeancePaiementRepository.AddAsync(echeance, userId);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "EcheancePaiement",
                EntiteId = addedEcheance.Id,
                TypeAction = "Création",
                Description = $"Ajout d'une échéance au plan de paiement {plan.Reference}",
                UtilisateurId = userId,
                DateAction = DateTime.Now,
                NomUtilisateur = "Système" // À remplacer par le nom réel si disponible
            });

            return addedEcheance;
        }

        /// <summary>
        /// Supprime une échéance
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="userId">Identifiant de l'utilisateur qui supprime l'échéance</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteEcheanceAsync(int echeanceId, int userId)
        {
            // Vérifier que l'échéance existe
            var echeance = await _echeancePaiementRepository.GetByIdAsync(echeanceId);
            if (echeance == null)
                return false;

            // Supprimer l'échéance
            var result = await _echeancePaiementRepository.DeleteAsync(echeanceId, userId);

            if (result)
            {
                // Journaliser l'action
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    TypeEntite = "EcheancePaiement",
                    EntiteId = echeanceId,
                    TypeAction = "Suppression",
                    Description = $"Suppression de l'échéance {echeance.NumeroOrdre} du plan {echeance.PlanPaiementId}",
                    UtilisateurId = userId,
                    DateAction = DateTime.Now,
                    NomUtilisateur = "Système" // À remplacer par le nom réel si disponible
                });
            }

            return result;
        }

        /// <summary>
        /// Envoie une notification pour une échéance
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="userId">Identifiant de l'utilisateur qui envoie la notification</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        public async Task<bool> SendEcheanceNotificationAsync(int echeanceId, int userId)
        {
            // Vérifier que l'échéance existe
            var echeance = await _echeancePaiementRepository.GetByIdAsync(echeanceId);
            if (echeance == null)
                return false;

            // Récupérer le plan de paiement
            var plan = await _planPaiementRepository.GetByIdAsync(echeance.PlanPaiementId);
            if (plan == null)
                return false;

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(plan.ClientId);
            if (client == null)
                return false;

            // TODO: Implémenter l'envoi de notification (email, SMS, etc.)
            // Cette fonctionnalité nécessite l'intégration avec un service de communication

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "EcheancePaiement",
                EntiteId = echeanceId,
                TypeAction = "Notification",
                Description = $"Envoi d'une notification pour l'échéance {echeance.NumeroOrdre} du plan {plan.Reference}",
                UtilisateurId = userId,
                DateAction = DateTime.Now,
                NomUtilisateur = "Système" // À remplacer par le nom réel si disponible
            });

            // Pour l'instant, on simule un succès
            return true;
        }

        /// <summary>
        /// Met à jour le statut d'un plan de paiement
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdatePlanStatutAsync(int planPaiementId, string statut, int userId)
        {
            // Vérifier que le plan existe
            var plan = await _planPaiementRepository.GetByIdAsync(planPaiementId);
            if (plan == null)
                return false;

            // Mettre à jour le statut
            var result = await _planPaiementRepository.UpdateStatutAsync(planPaiementId, statut, userId);

            if (result)
            {
                // Journaliser l'action
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    TypeEntite = "PlanPaiement",
                    EntiteId = planPaiementId,
                    TypeAction = "Mise à jour statut",
                    Description = $"Mise à jour du statut du plan {plan.Reference} : {statut}",
                    UtilisateurId = userId,
                    DateAction = DateTime.Now,
                    NomUtilisateur = "Système" // À remplacer par le nom réel si disponible
                });
            }

            return result;
        }

        /// <summary>
        /// Associe un plan de paiement à une facture
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="montantCouvert">Montant de la facture couvert par le plan</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'association a réussi, sinon False</returns>
        public async Task<bool> AssociatePlanToFactureAsync(int planPaiementId, int factureId, decimal montantCouvert, int userId)
        {
            // Vérifier que le plan existe
            var plan = await _planPaiementRepository.GetByIdAsync(planPaiementId);
            if (plan == null)
                return false;

            // Vérifier que la facture existe
            var facture = await _factureRepository.GetByIdAsync(factureId);
            if (facture == null)
                return false;

            // Associer le plan à la facture
            var result = await _planPaiementRepository.AssociateToFactureAsync(planPaiementId, factureId, montantCouvert, userId);

            if (result)
            {
                // Journaliser l'action
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    TypeEntite = "PlanPaiement",
                    EntiteId = planPaiementId,
                    TypeAction = "Association facture",
                    Description = $"Association du plan {plan.Reference} à la facture {facture.Numero} pour un montant de {montantCouvert:C2}",
                    UtilisateurId = userId,
                    DateAction = DateTime.Now,
                    NomUtilisateur = "Système" // À remplacer par le nom réel si disponible
                });
            }

            return result;
        }

        /// <summary>
        /// Dissocie un plan de paiement d'une facture
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la dissociation a réussi, sinon False</returns>
        public async Task<bool> DissociatePlanFromFactureAsync(int planPaiementId, int factureId, int userId)
        {
            // Vérifier que le plan existe
            var plan = await _planPaiementRepository.GetByIdAsync(planPaiementId);
            if (plan == null)
                return false;

            // Vérifier que la facture existe
            var facture = await _factureRepository.GetByIdAsync(factureId);
            if (facture == null)
                return false;

            // Dissocier le plan de la facture
            var result = await _planPaiementRepository.DissociateFromFactureAsync(planPaiementId, factureId, userId);

            if (result)
            {
                // Journaliser l'action
                await _journalAuditRepository.AddAsync(new JournalAudit
                {
                    TypeEntite = "PlanPaiement",
                    EntiteId = planPaiementId,
                    TypeAction = "Dissociation facture",
                    Description = $"Dissociation du plan {plan.Reference} de la facture {facture.Numero}",
                    UtilisateurId = userId,
                    DateAction = DateTime.Now,
                    NomUtilisateur = "Système" // À remplacer par le nom réel si disponible
                });
            }

            return result;
        }

        /// <summary>
        /// Récupère les plans de paiement par statut
        /// </summary>
        /// <param name="statut">Statut des plans de paiement</param>
        /// <returns>Liste des plans de paiement ayant le statut spécifié</returns>
        public async Task<IEnumerable<PlanPaiement>> GetPlansByStatutAsync(string statut)
        {
            return await _planPaiementRepository.GetByStatutAsync(statut);
        }

        /// <summary>
        /// Récupère les statistiques des plans de paiement pour une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Statistiques pour la période</returns>
        public async Task<(int NombrePlans, decimal MontantTotal, decimal MontantPaye, int NombreEcheances, int EcheancesPaye)> GetStatisticsByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            // Récupérer tous les plans créés dans la période
            var plans = await _planPaiementRepository.GetByPeriodAsync(dateDebut, dateFin);
            if (!plans.Any())
                return (0, 0, 0, 0, 0);

            // Récupérer toutes les échéances des plans
            List<EcheancePaiement> allEcheances = new List<EcheancePaiement>();
            foreach (var plan in plans)
            {
                var planWithEcheances = await _planPaiementRepository.GetWithEcheancesAsync(plan.Id);
                if (planWithEcheances?.Echeances != null)
                {
                    allEcheances.AddRange(planWithEcheances.Echeances);
                }
            }

            // Calculer les statistiques
            int nombrePlans = plans.Count();
            decimal montantTotal = plans.Sum(p => p.MontantTotal);
            decimal montantPaye = plans.Sum(p => p.MontantPaye);
            int nombreEcheances = allEcheances.Count;
            int echeancesPaye = allEcheances.Count(e => e.EstPayee);

            return (nombrePlans, montantTotal, montantPaye, nombreEcheances, echeancesPaye);
        }

        #region Rapports et statistiques

        /// <summary>
        /// Récupère les statistiques globales des plans de paiement
        /// </summary>
        /// <returns>Statistiques globales</returns>
        public async Task<(int NombrePlans, decimal MontantTotal, decimal MontantPaye, decimal MontantRestant, int NombreEcheances, int EcheancesPaye, int EcheancesRetard)> GetGlobalStatisticsAsync()
        {
            // Récupérer tous les plans actifs
            var plans = await _planPaiementRepository.FindAsync("EstActif = 1", null);
            if (!plans.Any())
                return (0, 0, 0, 0, 0, 0, 0);

            // Récupérer toutes les échéances des plans actifs
            List<EcheancePaiement> allEcheances = new List<EcheancePaiement>();
            foreach (var plan in plans)
            {
                var planWithEcheances = await _planPaiementRepository.GetWithEcheancesAsync(plan.Id);
                if (planWithEcheances?.Echeances != null)
                {
                    allEcheances.AddRange(planWithEcheances.Echeances);
                }
            }

            // Calculer les statistiques
            int nombrePlans = plans.Count();
            decimal montantTotal = plans.Sum(p => p.MontantTotal);
            decimal montantPaye = plans.Sum(p => p.MontantPaye);
            decimal montantRestant = montantTotal - montantPaye;
            int nombreEcheances = allEcheances.Count;
            int echeancesPaye = allEcheances.Count(e => e.EstPayee);
            int echeancesRetard = allEcheances.Count(e => e.EstEnRetard);

            return (nombrePlans, montantTotal, montantPaye, montantRestant, nombreEcheances, echeancesPaye, echeancesRetard);
        }

        /// <summary>
        /// Récupère les statistiques de respect des échéances pour un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Statistiques de respect des échéances</returns>
        public async Task<(int Total, int Payees, int EnRetard, decimal TauxRespect)> GetClientEcheanceStatisticsAsync(int clientId)
        {
            // Récupérer tous les plans du client
            var plans = await _planPaiementRepository.GetByClientIdAsync(clientId);
            if (!plans.Any())
                return (0, 0, 0, 0);

            // Récupérer toutes les échéances des plans
            List<EcheancePaiement> allEcheances = new List<EcheancePaiement>();
            foreach (var plan in plans)
            {
                var planWithEcheances = await _planPaiementRepository.GetWithEcheancesAsync(plan.Id);
                if (planWithEcheances?.Echeances != null)
                {
                    allEcheances.AddRange(planWithEcheances.Echeances);
                }
            }

            // Calculer les statistiques
            int total = allEcheances.Count;
            int payees = allEcheances.Count(e => e.EstPayee);
            int enRetard = allEcheances.Count(e => e.EstEnRetard);
            decimal tauxRespect = total > 0 ? (decimal)payees / total * 100 : 0;

            return (total, payees, enRetard, tauxRespect);
        }

        #endregion
    }
}
