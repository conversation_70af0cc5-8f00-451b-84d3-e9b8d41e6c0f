using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.Configuration;

namespace RecouvreX.WinForms.Forms.Communications
{
    /// <summary>
    /// Formulaire pour l'envoi d'un email
    /// </summary>
    public partial class EmailForm : Form
    {
        private readonly ICommunicationService _communicationService;
        private readonly IContactClientService _contactClientService;
        private readonly IConfiguration _configuration;
        private readonly int _currentUserId;
        private readonly int _factureId;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();
        private List<string> _piecesJointes = new List<string>();

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="communicationService">Service de gestion des communications</param>
        /// <param name="contactClientService">Service de gestion des contacts client</param>
        /// <param name="configuration">Configuration de l'application</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        /// <param name="factureId">Identifiant de la facture</param>
        public EmailForm(ICommunicationService communicationService, IContactClientService contactClientService, IConfiguration configuration, int currentUserId, int factureId)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _contactClientService = contactClientService ?? throw new ArgumentNullException(nameof(contactClientService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _currentUserId = currentUserId;
            _factureId = factureId;

            InitializeComponent();

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;
        }



        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void EmailForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Charger les contacts du client
                await LoadContactsAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'email");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Chargement des contacts du client
        /// </summary>
        private async Task LoadContactsAsync()
        {
            try
            {
                // Récupérer d'abord la facture pour obtenir le clientId
                var facture = await _communicationService.GetByIdAsync(_factureId);
                if (facture == null || facture.FactureId <= 0)
                {
                    MessageBox.Show("Impossible de récupérer les informations de la facture.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Récupérer les contacts du client
                var contacts = await _contactClientService.GetByClientIdAsync(facture.FactureId);

                // Remplir la liste déroulante des destinataires
                this.destinataireComboBox.Items.Clear();
                this.destinataireComboBox.DisplayMember = "NomComplet";
                this.destinataireComboBox.ValueMember = "Id";

                foreach (var contact in contacts)
                {
                    if (!string.IsNullOrEmpty(contact.Email))
                    {
                        this.destinataireComboBox.Items.Add(contact);
                    }
                }

                // Sélectionner le contact principal s'il existe
                var contactPrincipal = contacts.FirstOrDefault(c => c.EstPrincipal);
                if (contactPrincipal != null && !string.IsNullOrEmpty(contactPrincipal.Email))
                {
                    this.destinataireComboBox.SelectedItem = contactPrincipal;
                }
                else if (this.destinataireComboBox.Items.Count > 0)
                {
                    this.destinataireComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des contacts");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des contacts : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Validation du destinataire
        /// </summary>
        private void DestinataireComboBox_Validating(object sender, CancelEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                if (comboBox.SelectedItem == null)
                {
                    _errorProvider.SetError(comboBox, "Veuillez sélectionner un destinataire.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(comboBox, "");
                }
            }
        }

        /// <summary>
        /// Validation de l'objet
        /// </summary>
        private void ObjetTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "L'objet est obligatoire.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        /// <summary>
        /// Validation du contenu
        /// </summary>
        private void ContenuTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "Le contenu est obligatoire.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        /// <summary>
        /// Gestion du changement d'état de la case à cocher "Suivi nécessaire"
        /// </summary>
        private void SuiviNecessaireCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            // TODO: Ajouter un sélecteur de date pour le suivi si la case est cochée
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Ajouter..." pour les pièces jointes
        /// </summary>
        private void PiecesJointesButton_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Tous les fichiers (*.*)|*.*";
                openFileDialog.Multiselect = true;
                openFileDialog.Title = "Sélectionner des pièces jointes";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    foreach (string fileName in openFileDialog.FileNames)
                    {
                        _piecesJointes.Add(fileName);
                    }

                    // Mettre à jour le texte du bouton pour indiquer le nombre de pièces jointes
                    this.piecesJointesButton.Text = $"Ajouter... ({_piecesJointes.Count} fichier(s))";
                }
            }
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Envoyer"
        /// </summary>
        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant de continuer.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer le contact sélectionné
                var contact = this.destinataireComboBox.SelectedItem as ContactClient;
                if (contact == null)
                {
                    MessageBox.Show("Veuillez sélectionner un destinataire.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Envoyer et enregistrer l'email
                var result = await _communicationService.EnvoyerEtEnregistrerEmailAsync(
                    _factureId,
                    contact.Id,
                    contact.Email,
                    this.objetTextBox.Text,
                    this.contenuTextBox.Text,
                    _piecesJointes,
                    this.suiviNecessaireCheckBox.Checked,
                    null, // Date de suivi
                    _currentUserId,
                    _configuration);

                // Restaurer le curseur
                Cursor.Current = Cursors.Default;

                // Afficher un message de confirmation ou d'erreur
                if (result.EmailEnvoye)
                {
                    MessageBox.Show("L'email a été envoyé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Fermer le formulaire
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("L'email a été enregistré mais n'a pas pu être envoyé. Vérifiez la configuration SMTP.",
                        "Avertissement", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                    // Fermer le formulaire quand même car l'email est enregistré
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'envoi de l'email");
                MessageBox.Show($"Une erreur s'est produite lors de l'envoi de l'email : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Restaurer le curseur en cas d'erreur
                Cursor.Current = Cursors.Default;
            }
        }

        /// <summary>
        /// Gestion du clic sur le bouton "Annuler"
        /// </summary>
        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
