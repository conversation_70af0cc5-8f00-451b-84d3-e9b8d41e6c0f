using Moq;
using RecouvreX.Business.Interfaces;
using RecouvreX.Business.Services;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Xunit;

namespace RecouvreX.Tests.Business
{
    public class UtilisateurServiceTests
    {
        private readonly Mock<IUtilisateurRepository> _mockUtilisateurRepository;
        private readonly Mock<IAuditService> _mockAuditService;
        private readonly IUtilisateurService _utilisateurService;

        public UtilisateurServiceTests()
        {
            _mockUtilisateurRepository = new Mock<IUtilisateurRepository>();
            _mockAuditService = new Mock<IAuditService>();
            _utilisateurService = new UtilisateurService(_mockUtilisateurRepository.Object, _mockAuditService.Object);
        }

        [Fact]
        public async Task GetAllAsync_ShouldReturnAllUtilisateurs()
        {
            // Arrange
            var expectedUtilisateurs = new List<Utilisateur>
            {
                new Utilisateur { Id = 1, NomUtilisateur = "user1", Nom = "Nom1", Prenom = "Prenom1" },
                new Utilisateur { Id = 2, NomUtilisateur = "user2", Nom = "Nom2", Prenom = "Prenom2" },
                new Utilisateur { Id = 3, NomUtilisateur = "user3", Nom = "Nom3", Prenom = "Prenom3" }
            };

            _mockUtilisateurRepository.Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(expectedUtilisateurs);

            // Act
            var result = await _utilisateurService.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedUtilisateurs.Count, result.Count());
            Assert.Equal(expectedUtilisateurs, result);
            _mockUtilisateurRepository.Verify(repo => repo.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithValidId_ShouldReturnUtilisateur()
        {
            // Arrange
            int utilisateurId = 1;
            var expectedUtilisateur = new Utilisateur { Id = utilisateurId, NomUtilisateur = "user1", Nom = "Nom1", Prenom = "Prenom1" };

            _mockUtilisateurRepository.Setup(repo => repo.GetByIdAsync(utilisateurId))
                .ReturnsAsync(expectedUtilisateur);

            // Act
            var result = await _utilisateurService.GetByIdAsync(utilisateurId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedUtilisateur, result);
            _mockUtilisateurRepository.Verify(repo => repo.GetByIdAsync(utilisateurId), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            int utilisateurId = 999;

            _mockUtilisateurRepository.Setup(repo => repo.GetByIdAsync(utilisateurId))
                .ReturnsAsync((Utilisateur)null);

            // Act
            var result = await _utilisateurService.GetByIdAsync(utilisateurId);

            // Assert
            Assert.Null(result);
            _mockUtilisateurRepository.Verify(repo => repo.GetByIdAsync(utilisateurId), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_ShouldCreateUtilisateurAndReturnId()
        {
            // Arrange
            int userId = 1;
            int expectedUtilisateurId = 2;
            string password = "Password123!";
            var utilisateur = new Utilisateur
            {
                NomUtilisateur = "newuser",
                Nom = "Nouveau",
                Prenom = "Utilisateur",
                Email = "<EMAIL>",
                RoleId = 2,
                EstActif = true
            };

            _mockUtilisateurRepository.Setup(repo => repo.GetByUsernameAsync(utilisateur.NomUtilisateur))
                .ReturnsAsync((Utilisateur)null);

            _mockUtilisateurRepository.Setup(repo => repo.CreateAsync(It.IsAny<Utilisateur>()))
                .ReturnsAsync(expectedUtilisateurId);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _utilisateurService.CreateAsync(utilisateur, password, userId);

            // Assert
            Assert.Equal(expectedUtilisateurId, result);
            Assert.Equal(expectedUtilisateurId, utilisateur.Id);
            Assert.NotNull(utilisateur.MotDePasse);
            Assert.True(BCrypt.Net.BCrypt.Verify(password, utilisateur.MotDePasse));
            _mockUtilisateurRepository.Verify(repo => repo.GetByUsernameAsync(utilisateur.NomUtilisateur), Times.Once);
            _mockUtilisateurRepository.Verify(repo => repo.CreateAsync(utilisateur), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Utilisateur",
                It.IsAny<string>(),
                expectedUtilisateurId,
                userId), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_WithExistingUsername_ShouldThrowException()
        {
            // Arrange
            int userId = 1;
            string password = "Password123!";
            var utilisateur = new Utilisateur
            {
                NomUtilisateur = "existinguser",
                Nom = "Nouveau",
                Prenom = "Utilisateur",
                Email = "<EMAIL>",
                RoleId = 2,
                EstActif = true
            };

            var existingUtilisateur = new Utilisateur
            {
                Id = 3,
                NomUtilisateur = "existinguser",
                Nom = "Existant",
                Prenom = "Utilisateur",
                Email = "<EMAIL>"
            };

            _mockUtilisateurRepository.Setup(repo => repo.GetByUsernameAsync(utilisateur.NomUtilisateur))
                .ReturnsAsync(existingUtilisateur);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _utilisateurService.CreateAsync(utilisateur, password, userId));
            Assert.Contains("Ce nom d'utilisateur existe déjà", exception.Message);
            
            _mockUtilisateurRepository.Verify(repo => repo.GetByUsernameAsync(utilisateur.NomUtilisateur), Times.Once);
            _mockUtilisateurRepository.Verify(repo => repo.CreateAsync(It.IsAny<Utilisateur>()), Times.Never);
        }

        [Fact]
        public async Task UpdateAsync_ShouldUpdateUtilisateurAndReturnTrue()
        {
            // Arrange
            int userId = 1;
            var utilisateur = new Utilisateur
            {
                Id = 2,
                NomUtilisateur = "updateduser",
                Nom = "Mis à jour",
                Prenom = "Utilisateur",
                Email = "<EMAIL>",
                RoleId = 2,
                EstActif = true
            };

            var existingUtilisateur = new Utilisateur
            {
                Id = 2,
                NomUtilisateur = "user2",
                Nom = "Nom2",
                Prenom = "Prenom2",
                Email = "<EMAIL>",
                RoleId = 3,
                EstActif = true
            };

            _mockUtilisateurRepository.Setup(repo => repo.GetByIdAsync(utilisateur.Id))
                .ReturnsAsync(existingUtilisateur);

            _mockUtilisateurRepository.Setup(repo => repo.GetByUsernameAsync(utilisateur.NomUtilisateur))
                .ReturnsAsync((Utilisateur)null);

            _mockUtilisateurRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Utilisateur>()))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _utilisateurService.UpdateAsync(utilisateur, userId);

            // Assert
            Assert.True(result);
            _mockUtilisateurRepository.Verify(repo => repo.GetByIdAsync(utilisateur.Id), Times.Once);
            _mockUtilisateurRepository.Verify(repo => repo.GetByUsernameAsync(utilisateur.NomUtilisateur), Times.Once);
            _mockUtilisateurRepository.Verify(repo => repo.UpdateAsync(utilisateur), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Utilisateur",
                It.IsAny<string>(),
                utilisateur.Id,
                userId), Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_WithExistingUsername_ShouldThrowException()
        {
            // Arrange
            int userId = 1;
            var utilisateur = new Utilisateur
            {
                Id = 2,
                NomUtilisateur = "user3", // Nom d'utilisateur d'un autre utilisateur
                Nom = "Mis à jour",
                Prenom = "Utilisateur",
                Email = "<EMAIL>",
                RoleId = 2,
                EstActif = true
            };

            var existingUtilisateur = new Utilisateur
            {
                Id = 2,
                NomUtilisateur = "user2",
                Nom = "Nom2",
                Prenom = "Prenom2",
                Email = "<EMAIL>",
                RoleId = 3,
                EstActif = true
            };

            var otherUtilisateur = new Utilisateur
            {
                Id = 3,
                NomUtilisateur = "user3",
                Nom = "Nom3",
                Prenom = "Prenom3",
                Email = "<EMAIL>",
                RoleId = 2,
                EstActif = true
            };

            _mockUtilisateurRepository.Setup(repo => repo.GetByIdAsync(utilisateur.Id))
                .ReturnsAsync(existingUtilisateur);

            _mockUtilisateurRepository.Setup(repo => repo.GetByUsernameAsync(utilisateur.NomUtilisateur))
                .ReturnsAsync(otherUtilisateur);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _utilisateurService.UpdateAsync(utilisateur, userId));
            Assert.Contains("Ce nom d'utilisateur est déjà utilisé par un autre utilisateur", exception.Message);
            
            _mockUtilisateurRepository.Verify(repo => repo.GetByIdAsync(utilisateur.Id), Times.Once);
            _mockUtilisateurRepository.Verify(repo => repo.GetByUsernameAsync(utilisateur.NomUtilisateur), Times.Once);
            _mockUtilisateurRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Utilisateur>()), Times.Never);
        }

        [Fact]
        public async Task DeleteAsync_ShouldDeleteUtilisateurAndReturnTrue()
        {
            // Arrange
            int utilisateurId = 2;
            int userId = 1;

            _mockUtilisateurRepository.Setup(repo => repo.DeleteAsync(utilisateurId))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _utilisateurService.DeleteAsync(utilisateurId, userId);

            // Assert
            Assert.True(result);
            _mockUtilisateurRepository.Verify(repo => repo.DeleteAsync(utilisateurId), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Utilisateur",
                It.IsAny<string>(),
                utilisateurId,
                userId), Times.Once);
        }

        [Fact]
        public async Task ResetPasswordAsync_ShouldResetPasswordAndReturnTrue()
        {
            // Arrange
            int utilisateurId = 2;
            int userId = 1;
            string newPassword = "NewPassword123!";

            _mockUtilisateurRepository.Setup(repo => repo.UpdatePasswordAsync(
                It.IsAny<int>(), It.IsAny<string>()))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _utilisateurService.ResetPasswordAsync(utilisateurId, newPassword, userId);

            // Assert
            Assert.True(result);
            _mockUtilisateurRepository.Verify(repo => repo.UpdatePasswordAsync(
                utilisateurId, 
                It.Is<string>(pwd => BCrypt.Net.BCrypt.Verify(newPassword, pwd))), 
                Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Utilisateur",
                It.IsAny<string>(),
                utilisateurId,
                userId), Times.Once);
        }

        [Fact]
        public async Task SetActiveStatusAsync_ShouldUpdateStatusAndReturnTrue()
        {
            // Arrange
            int utilisateurId = 2;
            int userId = 1;
            bool isActive = false;

            _mockUtilisateurRepository.Setup(repo => repo.SetActiveStatusAsync(utilisateurId, isActive))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _utilisateurService.SetActiveStatusAsync(utilisateurId, isActive, userId);

            // Assert
            Assert.True(result);
            _mockUtilisateurRepository.Verify(repo => repo.SetActiveStatusAsync(utilisateurId, isActive), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Utilisateur",
                It.IsAny<string>(),
                utilisateurId,
                userId), Times.Once);
        }

        [Fact]
        public async Task SearchAsync_ShouldReturnMatchingUtilisateurs()
        {
            // Arrange
            string searchTerm = "user";
            var expectedUtilisateurs = new List<Utilisateur>
            {
                new Utilisateur { Id = 1, NomUtilisateur = "user1", Nom = "Nom1", Prenom = "Prenom1" },
                new Utilisateur { Id = 2, NomUtilisateur = "user2", Nom = "Nom2", Prenom = "Prenom2" }
            };

            _mockUtilisateurRepository.Setup(repo => repo.SearchAsync(searchTerm))
                .ReturnsAsync(expectedUtilisateurs);

            // Act
            var result = await _utilisateurService.SearchAsync(searchTerm);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedUtilisateurs.Count, result.Count());
            Assert.Equal(expectedUtilisateurs, result);
            _mockUtilisateurRepository.Verify(repo => repo.SearchAsync(searchTerm), Times.Once);
        }
    }
}
