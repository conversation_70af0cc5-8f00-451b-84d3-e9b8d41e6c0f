using Moq;
using RecouvreX.Business.Interfaces;
using RecouvreX.Business.Services;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Xunit;

namespace RecouvreX.Tests.Business
{
    public class FactureServiceTests
    {
        private readonly Mock<IFactureRepository> _mockFactureRepository;
        private readonly Mock<IAuditService> _mockAuditService;
        private readonly IFactureService _factureService;

        public FactureServiceTests()
        {
            _mockFactureRepository = new Mock<IFactureRepository>();
            _mockAuditService = new Mock<IAuditService>();
            _factureService = new FactureService(_mockFactureRepository.Object, _mockAuditService.Object);
        }

        [Fact]
        public async Task GetAllAsync_ShouldReturnAllFactures()
        {
            // Arrange
            var expectedFactures = new List<Facture>
            {
                new Facture { Id = 1, Numero = "FAC001", ClientId = 1, MontantTTC = 1000 },
                new Facture { Id = 2, Numero = "FAC002", ClientId = 2, MontantTTC = 2000 },
                new Facture { Id = 3, Numero = "FAC003", ClientId = 1, MontantTTC = 3000 }
            };

            _mockFactureRepository.Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(expectedFactures);

            // Act
            var result = await _factureService.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedFactures.Count, result.Count());
            Assert.Equal(expectedFactures, result);
            _mockFactureRepository.Verify(repo => repo.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithValidId_ShouldReturnFacture()
        {
            // Arrange
            int factureId = 1;
            var expectedFacture = new Facture { Id = factureId, Numero = "FAC001", ClientId = 1, MontantTTC = 1000 };

            _mockFactureRepository.Setup(repo => repo.GetByIdAsync(factureId))
                .ReturnsAsync(expectedFacture);

            // Act
            var result = await _factureService.GetByIdAsync(factureId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedFacture, result);
            _mockFactureRepository.Verify(repo => repo.GetByIdAsync(factureId), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            int factureId = 999;

            _mockFactureRepository.Setup(repo => repo.GetByIdAsync(factureId))
                .ReturnsAsync((Facture)null);

            // Act
            var result = await _factureService.GetByIdAsync(factureId);

            // Assert
            Assert.Null(result);
            _mockFactureRepository.Verify(repo => repo.GetByIdAsync(factureId), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_ShouldCreateFactureAndReturnId()
        {
            // Arrange
            int userId = 1;
            int expectedFactureId = 1;
            var facture = new Facture
            {
                ClientId = 1,
                DateEmission = DateTime.Today,
                DateEcheance = DateTime.Today.AddDays(30),
                MontantHT = 1000,
                MontantTVA = 200,
                MontantTTC = 1200
            };

            _mockFactureRepository.Setup(repo => repo.CreateAsync(It.IsAny<Facture>()))
                .ReturnsAsync(expectedFactureId);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _factureService.CreateAsync(facture, userId);

            // Assert
            Assert.Equal(expectedFactureId, result);
            Assert.Equal(expectedFactureId, facture.Id);
            Assert.NotNull(facture.Numero);
            Assert.True(!string.IsNullOrEmpty(facture.Numero));
            Assert.Equal("En attente", facture.Statut);
            Assert.Equal(facture.MontantTTC, facture.MontantRestant);
            Assert.Equal(0, facture.MontantPaye);
            _mockFactureRepository.Verify(repo => repo.CreateAsync(facture), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Facture",
                It.IsAny<string>(),
                expectedFactureId,
                userId), Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_ShouldUpdateFactureAndReturnTrue()
        {
            // Arrange
            int userId = 1;
            var facture = new Facture
            {
                Id = 1,
                Numero = "FAC001",
                ClientId = 1,
                DateEmission = DateTime.Today,
                DateEcheance = DateTime.Today.AddDays(30),
                MontantHT = 1000,
                MontantTVA = 200,
                MontantTTC = 1200,
                MontantPaye = 500,
                MontantRestant = 700,
                Statut = "Payée partiellement"
            };

            _mockFactureRepository.Setup(repo => repo.UpdateAsync(facture))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _factureService.UpdateAsync(facture, userId);

            // Assert
            Assert.True(result);
            _mockFactureRepository.Verify(repo => repo.UpdateAsync(facture), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Facture",
                It.IsAny<string>(),
                facture.Id,
                userId), Times.Once);
        }

        [Fact]
        public async Task DeleteAsync_ShouldDeleteFactureAndReturnTrue()
        {
            // Arrange
            int factureId = 1;
            int userId = 1;

            _mockFactureRepository.Setup(repo => repo.DeleteAsync(factureId))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _factureService.DeleteAsync(factureId, userId);

            // Assert
            Assert.True(result);
            _mockFactureRepository.Verify(repo => repo.DeleteAsync(factureId), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Facture",
                It.IsAny<string>(),
                factureId,
                userId), Times.Once);
        }

        [Fact]
        public async Task GetByClientIdAsync_ShouldReturnClientFactures()
        {
            // Arrange
            int clientId = 1;
            var expectedFactures = new List<Facture>
            {
                new Facture { Id = 1, Numero = "FAC001", ClientId = clientId, MontantTTC = 1000 },
                new Facture { Id = 3, Numero = "FAC003", ClientId = clientId, MontantTTC = 3000 }
            };

            _mockFactureRepository.Setup(repo => repo.GetByClientIdAsync(clientId))
                .ReturnsAsync(expectedFactures);

            // Act
            var result = await _factureService.GetByClientIdAsync(clientId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedFactures.Count, result.Count());
            Assert.Equal(expectedFactures, result);
            _mockFactureRepository.Verify(repo => repo.GetByClientIdAsync(clientId), Times.Once);
        }

        [Fact]
        public async Task GetByNumeroAsync_ShouldReturnFacture()
        {
            // Arrange
            string numero = "FAC001";
            var expectedFacture = new Facture { Id = 1, Numero = numero, ClientId = 1, MontantTTC = 1000 };

            _mockFactureRepository.Setup(repo => repo.GetByNumeroAsync(numero))
                .ReturnsAsync(expectedFacture);

            // Act
            var result = await _factureService.GetByNumeroAsync(numero);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedFacture, result);
            _mockFactureRepository.Verify(repo => repo.GetByNumeroAsync(numero), Times.Once);
        }

        [Fact]
        public async Task GetByStatutAsync_ShouldReturnFacturesWithSpecifiedStatut()
        {
            // Arrange
            string statut = "En retard";
            var expectedFactures = new List<Facture>
            {
                new Facture { Id = 1, Numero = "FAC001", ClientId = 1, MontantTTC = 1000, Statut = statut },
                new Facture { Id = 3, Numero = "FAC003", ClientId = 2, MontantTTC = 3000, Statut = statut }
            };

            _mockFactureRepository.Setup(repo => repo.GetByStatutAsync(statut))
                .ReturnsAsync(expectedFactures);

            // Act
            var result = await _factureService.GetByStatutAsync(statut);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedFactures.Count, result.Count());
            Assert.Equal(expectedFactures, result);
            _mockFactureRepository.Verify(repo => repo.GetByStatutAsync(statut), Times.Once);
        }

        [Fact]
        public async Task GetOverdueInvoicesAsync_ShouldReturnOverdueFactures()
        {
            // Arrange
            var expectedFactures = new List<Facture>
            {
                new Facture { Id = 1, Numero = "FAC001", ClientId = 1, MontantTTC = 1000, DateEcheance = DateTime.Today.AddDays(-10), Statut = "En retard" },
                new Facture { Id = 3, Numero = "FAC003", ClientId = 2, MontantTTC = 3000, DateEcheance = DateTime.Today.AddDays(-5), Statut = "En retard" }
            };

            _mockFactureRepository.Setup(repo => repo.GetOverdueInvoicesAsync())
                .ReturnsAsync(expectedFactures);

            // Act
            var result = await _factureService.GetOverdueInvoicesAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedFactures.Count, result.Count());
            Assert.Equal(expectedFactures, result);
            _mockFactureRepository.Verify(repo => repo.GetOverdueInvoicesAsync(), Times.Once);
        }

        [Fact]
        public async Task UpdateStatutAsync_ShouldUpdateFactureStatutAndReturnTrue()
        {
            // Arrange
            int factureId = 1;
            string nouveauStatut = "Payée";
            int userId = 1;

            _mockFactureRepository.Setup(repo => repo.UpdateStatutAsync(factureId, nouveauStatut))
                .ReturnsAsync(true);

            _mockAuditService.Setup(service => service.LogActivityAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _factureService.UpdateStatutAsync(factureId, nouveauStatut, userId);

            // Assert
            Assert.True(result);
            _mockFactureRepository.Verify(repo => repo.UpdateStatutAsync(factureId, nouveauStatut), Times.Once);
            _mockAuditService.Verify(service => service.LogActivityAsync(
                "Facture",
                It.IsAny<string>(),
                factureId,
                userId), Times.Once);
        }
    }
}
