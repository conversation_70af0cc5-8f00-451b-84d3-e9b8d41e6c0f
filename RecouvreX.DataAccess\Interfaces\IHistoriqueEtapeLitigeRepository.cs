using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des historiques d'étapes de litiges
    /// </summary>
    public interface IHistoriqueEtapeLitigeRepository
    {
        /// <summary>
        /// Récupère tous les historiques d'étapes
        /// </summary>
        /// <returns>Liste des historiques d'étapes</returns>
        Task<IEnumerable<HistoriqueEtapeLitige>> GetAllAsync();

        /// <summary>
        /// Récupère un historique d'étape par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de l'historique d'étape</param>
        /// <returns>Historique d'étape trouvé ou null</returns>
        Task<HistoriqueEtapeLitige> GetByIdAsync(int id);

        /// <summary>
        /// Récupère les historiques d'étapes pour un litige
        /// </summary>
        /// <param name="litigeId">Identifiant du litige</param>
        /// <returns>Liste des historiques d'étapes pour le litige</returns>
        Task<IEnumerable<HistoriqueEtapeLitige>> GetByLitigeIdAsync(int litigeId);

        /// <summary>
        /// Ajoute un historique d'étape
        /// </summary>
        /// <param name="historiqueEtape">Historique d'étape à ajouter</param>
        /// <returns>Historique d'étape ajouté</returns>
        Task<HistoriqueEtapeLitige> AddAsync(HistoriqueEtapeLitige historiqueEtape);

        /// <summary>
        /// Met à jour un historique d'étape
        /// </summary>
        /// <param name="historiqueEtape">Historique d'étape à mettre à jour</param>
        /// <returns>True si l'historique d'étape a été mis à jour</returns>
        Task<bool> UpdateAsync(HistoriqueEtapeLitige historiqueEtape);
    }
}
