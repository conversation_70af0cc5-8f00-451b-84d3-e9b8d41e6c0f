using System;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente un contact chez un client
    /// </summary>
    public class ContactClient : BaseEntity
    {
        /// <summary>
        /// Identifiant du client
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// Client (navigation property)
        /// </summary>
        public Client Client { get; set; }

        /// <summary>
        /// Nom du contact
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Prénom du contact
        /// </summary>
        public string Prenom { get; set; }

        /// <summary>
        /// Fonction du contact
        /// </summary>
        public string Fonction { get; set; }

        /// <summary>
        /// Service du contact
        /// </summary>
        public string Service { get; set; }

        /// <summary>
        /// Email du contact
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Téléphone du contact
        /// </summary>
        public string Telephone { get; set; }

        /// <summary>
        /// Téléphone mobile du contact
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// Indique si le contact est le contact principal
        /// </summary>
        public bool EstPrincipal { get; set; }

        /// <summary>
        /// Indique si le contact est responsable des paiements
        /// </summary>
        public bool EstResponsablePaiements { get; set; }

        /// <summary>
        /// Notes sur le contact
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Nom complet du contact (Prénom + Nom)
        /// </summary>
        public string NomComplet => $"{Prenom} {Nom}";
    }
}
