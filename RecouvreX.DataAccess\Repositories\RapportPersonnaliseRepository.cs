using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models.Reporting;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les rapports personnalisés
    /// </summary>
    public class RapportPersonnaliseRepository : BaseRepository<RapportPersonnalise>, IRapportPersonnaliseRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public RapportPersonnaliseRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "RapportPersonnalise")
        {
        }

        /// <summary>
        /// Récupère les rapports personnalisés d'un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des rapports personnalisés de l'utilisateur</returns>
        public async Task<IEnumerable<RapportPersonnalise>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT * FROM RapportPersonnalise
                    WHERE UtilisateurId = @UtilisateurId AND EstActif = 1
                    ORDER BY Nom";

                return await connection.QueryAsync<RapportPersonnalise>(query, new { UtilisateurId = utilisateurId });
            }
        }

        /// <summary>
        /// Récupère les rapports personnalisés programmés à générer
        /// </summary>
        /// <returns>Liste des rapports personnalisés programmés</returns>
        public async Task<IEnumerable<RapportPersonnalise>> GetScheduledReportsAsync()
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT * FROM RapportPersonnalise
                    WHERE EstProgramme = 1 AND EstActif = 1
                    ORDER BY Nom";

                return await connection.QueryAsync<RapportPersonnalise>(query);
            }
        }

        /// <summary>
        /// Met à jour la date de dernière génération d'un rapport
        /// </summary>
        /// <param name="id">Identifiant du rapport</param>
        /// <param name="dateGeneration">Date de génération</param>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateLastGenerationDateAsync(int id, DateTime dateGeneration, int userId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE RapportPersonnalise
                    SET DerniereGeneration = @DateGeneration,
                        DateModification = GETDATE(),
                        ModifiePar = @UserId
                    WHERE Id = @Id";

                var result = await connection.ExecuteAsync(query, new { Id = id, DateGeneration = dateGeneration, UserId = userId });
                return result > 0;
            }
        }

        /// <summary>
        /// Récupère les rapports programmés à générer à une date et heure spécifiques
        /// </summary>
        /// <param name="date">Date et heure</param>
        /// <returns>Liste des rapports à générer</returns>
        public async Task<IEnumerable<RapportPersonnalise>> GetReportsToGenerateAsync(DateTime date)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Récupérer les rapports quotidiens dont l'heure de génération correspond
                var dailyReports = await GetDailyReportsToGenerateAsync(connection, date);

                // Récupérer les rapports hebdomadaires dont le jour et l'heure correspondent
                var weeklyReports = await GetWeeklyReportsToGenerateAsync(connection, date);

                // Récupérer les rapports mensuels dont le jour et l'heure correspondent
                var monthlyReports = await GetMonthlyReportsToGenerateAsync(connection, date);

                // Récupérer les rapports trimestriels dont le jour et l'heure correspondent
                var quarterlyReports = await GetQuarterlyReportsToGenerateAsync(connection, date);

                // Combiner tous les rapports
                return dailyReports.Concat(weeklyReports).Concat(monthlyReports).Concat(quarterlyReports);
            }
        }

        private async Task<IEnumerable<RapportPersonnalise>> GetDailyReportsToGenerateAsync(IDbConnection connection, DateTime date)
        {
            var query = @"
                SELECT * FROM RapportPersonnalise
                WHERE EstProgramme = 1
                AND EstActif = 1
                AND FrequenceGeneration = 'Quotidien'
                AND DATEPART(HOUR, HeureGeneration) = @Hour
                AND DATEPART(MINUTE, HeureGeneration) = @Minute";

            return await connection.QueryAsync<RapportPersonnalise>(query, new { Hour = date.Hour, Minute = date.Minute });
        }

        private async Task<IEnumerable<RapportPersonnalise>> GetWeeklyReportsToGenerateAsync(IDbConnection connection, DateTime date)
        {
            // Jour de la semaine (1 = Lundi, 7 = Dimanche)
            int dayOfWeek = ((int)date.DayOfWeek + 6) % 7 + 1;

            var query = @"
                SELECT * FROM RapportPersonnalise
                WHERE EstProgramme = 1
                AND EstActif = 1
                AND FrequenceGeneration = 'Hebdomadaire'
                AND JourGeneration = @DayOfWeek
                AND DATEPART(HOUR, HeureGeneration) = @Hour
                AND DATEPART(MINUTE, HeureGeneration) = @Minute";

            return await connection.QueryAsync<RapportPersonnalise>(query, new { DayOfWeek = dayOfWeek, Hour = date.Hour, Minute = date.Minute });
        }

        private async Task<IEnumerable<RapportPersonnalise>> GetMonthlyReportsToGenerateAsync(IDbConnection connection, DateTime date)
        {
            var query = @"
                SELECT * FROM RapportPersonnalise
                WHERE EstProgramme = 1
                AND EstActif = 1
                AND FrequenceGeneration = 'Mensuel'
                AND JourGeneration = @DayOfMonth
                AND DATEPART(HOUR, HeureGeneration) = @Hour
                AND DATEPART(MINUTE, HeureGeneration) = @Minute";

            return await connection.QueryAsync<RapportPersonnalise>(query, new { DayOfMonth = date.Day, Hour = date.Hour, Minute = date.Minute });
        }

        private async Task<IEnumerable<RapportPersonnalise>> GetQuarterlyReportsToGenerateAsync(IDbConnection connection, DateTime date)
        {
            // Vérifier si c'est le premier jour du trimestre
            bool isFirstDayOfQuarter = date.Month % 3 == 1 && date.Day == 1;

            if (!isFirstDayOfQuarter)
                return Enumerable.Empty<RapportPersonnalise>();

            var query = @"
                SELECT * FROM RapportPersonnalise
                WHERE EstProgramme = 1
                AND EstActif = 1
                AND FrequenceGeneration = 'Trimestriel'
                AND DATEPART(HOUR, HeureGeneration) = @Hour
                AND DATEPART(MINUTE, HeureGeneration) = @Minute";

            return await connection.QueryAsync<RapportPersonnalise>(query, new { Hour = date.Hour, Minute = date.Minute });
        }
    }
}
