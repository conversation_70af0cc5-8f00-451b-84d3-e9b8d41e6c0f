-- Script de création des déclencheurs (triggers) pour la base de données RecouvreX
-- Ce script crée les déclencheurs pour maintenir l'intégrité des données et automatiser certaines tâches

USE RecouvreX;
GO

-- Déclencheur pour mettre à jour le solde client après modification d'une facture
CREATE OR ALTER TRIGGER app.trg_Factures_UpdateClientBalance
ON app.Factures
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Table temporaire pour stocker les IDs des clients à mettre à jour
    DECLARE @ClientsToUpdate TABLE (ClientId INT);
    
    -- Récupérer les IDs des clients des factures insérées ou mises à jour
    INSERT INTO @ClientsToUpdate (ClientId)
    SELECT DISTINCT ClientId FROM inserted
    UNION
    SELECT DISTINCT ClientId FROM deleted;
    
    -- Mettre à jour le solde de chaque client
    DECLARE @ClientId INT;
    DECLARE client_cursor CURSOR FOR 
        SELECT ClientId FROM @ClientsToUpdate;
    
    OPEN client_cursor;
    FETCH NEXT FROM client_cursor INTO @ClientId;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        EXEC app.CalculateClientBalance @ClientId;
        FETCH NEXT FROM client_cursor INTO @ClientId;
    END
    
    CLOSE client_cursor;
    DEALLOCATE client_cursor;
END;
GO

-- Déclencheur pour mettre à jour le montant restant d'une facture après modification
CREATE OR ALTER TRIGGER app.trg_Factures_UpdateRemainingAmount
ON app.Factures
AFTER INSERT, UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Mettre à jour le montant restant pour les factures insérées ou mises à jour
    UPDATE f
    SET MontantRestant = MontantTTC - MontantPaye,
        Statut = CASE 
                    WHEN MontantTTC <= MontantPaye THEN 'Payée'
                    WHEN MontantPaye > 0 THEN 'Payée partiellement'
                    WHEN DateEcheance < GETDATE() THEN 'En retard'
                    ELSE 'En attente'
                 END
    FROM app.Factures f
    INNER JOIN inserted i ON f.Id = i.Id
    WHERE f.Id = i.Id;
END;
GO

-- Déclencheur pour mettre à jour le montant payé d'une facture après modification d'un paiement
CREATE OR ALTER TRIGGER app.trg_FacturePaiements_UpdateInvoicePaidAmount
ON app.FacturePaiements
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Table temporaire pour stocker les IDs des factures à mettre à jour
    DECLARE @FacturesToUpdate TABLE (FactureId INT);
    
    -- Récupérer les IDs des factures des paiements insérés, mis à jour ou supprimés
    INSERT INTO @FacturesToUpdate (FactureId)
    SELECT DISTINCT FactureId FROM inserted
    UNION
    SELECT DISTINCT FactureId FROM deleted;
    
    -- Mettre à jour le montant payé de chaque facture
    UPDATE f
    SET MontantPaye = COALESCE(
                        (SELECT SUM(fp.MontantAffecte)
                         FROM app.FacturePaiements fp
                         WHERE fp.FactureId = f.Id
                           AND fp.EstActif = 1),
                        0),
        DateModification = GETDATE(),
        ModifiePar = 1 -- Utilisateur système
    FROM app.Factures f
    INNER JOIN @FacturesToUpdate ft ON f.Id = ft.FactureId;
    
    -- Le déclencheur trg_Factures_UpdateRemainingAmount mettra à jour le montant restant et le statut
END;
GO

-- Déclencheur pour journaliser les modifications des utilisateurs
CREATE OR ALTER TRIGGER app.trg_Utilisateurs_Audit
ON app.Utilisateurs
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @TypeAction NVARCHAR(50);
    DECLARE @UtilisateurId INT = 1; -- Utilisateur système par défaut
    DECLARE @NomUtilisateur NVARCHAR(50) = 'Système';
    
    -- Déterminer le type d'action
    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
        SET @TypeAction = 'Modification';
    ELSE IF EXISTS (SELECT * FROM inserted)
        SET @TypeAction = 'Création';
    ELSE
        SET @TypeAction = 'Suppression';
    
    -- Récupérer l'ID de l'utilisateur qui effectue l'action si disponible
    IF @TypeAction = 'Modification' AND EXISTS (SELECT * FROM inserted WHERE ModifiePar IS NOT NULL)
    BEGIN
        SELECT TOP 1 @UtilisateurId = ModifiePar, @NomUtilisateur = (SELECT NomUtilisateur FROM app.Utilisateurs WHERE Id = ModifiePar)
        FROM inserted
        WHERE ModifiePar IS NOT NULL;
    END
    ELSE IF @TypeAction IN ('Création', 'Modification') AND EXISTS (SELECT * FROM inserted WHERE CreePar IS NOT NULL)
    BEGIN
        SELECT TOP 1 @UtilisateurId = CreePar, @NomUtilisateur = (SELECT NomUtilisateur FROM app.Utilisateurs WHERE Id = CreePar)
        FROM inserted
        WHERE CreePar IS NOT NULL;
    END
    
    -- Insérer les entrées d'audit
    IF @TypeAction = 'Création'
    BEGIN
        INSERT INTO app.JournalAudit (
            DateAction, UtilisateurId, NomUtilisateur, TypeAction,
            TypeEntite, EntiteId, Description, DonneesAvant, DonneesApres
        )
        SELECT 
            GETDATE(), @UtilisateurId, @NomUtilisateur, @TypeAction,
            'Utilisateur', Id, 'Création d''un utilisateur: ' + NomUtilisateur,
            NULL,
            (SELECT * FROM inserted i WHERE i.Id = ins.Id FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
        FROM inserted ins;
    END
    ELSE IF @TypeAction = 'Modification'
    BEGIN
        INSERT INTO app.JournalAudit (
            DateAction, UtilisateurId, NomUtilisateur, TypeAction,
            TypeEntite, EntiteId, Description, DonneesAvant, DonneesApres
        )
        SELECT 
            GETDATE(), @UtilisateurId, @NomUtilisateur, @TypeAction,
            'Utilisateur', ins.Id, 'Modification d''un utilisateur: ' + ins.NomUtilisateur,
            (SELECT * FROM deleted d WHERE d.Id = ins.Id FOR JSON PATH, WITHOUT_ARRAY_WRAPPER),
            (SELECT * FROM inserted i WHERE i.Id = ins.Id FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
        FROM inserted ins
        INNER JOIN deleted del ON ins.Id = del.Id;
    END
    ELSE -- Suppression
    BEGIN
        INSERT INTO app.JournalAudit (
            DateAction, UtilisateurId, NomUtilisateur, TypeAction,
            TypeEntite, EntiteId, Description, DonneesAvant, DonneesApres
        )
        SELECT 
            GETDATE(), @UtilisateurId, @NomUtilisateur, @TypeAction,
            'Utilisateur', Id, 'Suppression d''un utilisateur: ' + NomUtilisateur,
            (SELECT * FROM deleted d WHERE d.Id = del.Id FOR JSON PATH, WITHOUT_ARRAY_WRAPPER),
            NULL
        FROM deleted del;
    END
END;
GO

-- Déclencheur pour journaliser les modifications des clients
CREATE OR ALTER TRIGGER app.trg_Clients_Audit
ON app.Clients
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @TypeAction NVARCHAR(50);
    DECLARE @UtilisateurId INT = 1; -- Utilisateur système par défaut
    DECLARE @NomUtilisateur NVARCHAR(50) = 'Système';
    
    -- Déterminer le type d'action
    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
        SET @TypeAction = 'Modification';
    ELSE IF EXISTS (SELECT * FROM inserted)
        SET @TypeAction = 'Création';
    ELSE
        SET @TypeAction = 'Suppression';
    
    -- Récupérer l'ID de l'utilisateur qui effectue l'action si disponible
    IF @TypeAction = 'Modification' AND EXISTS (SELECT * FROM inserted WHERE ModifiePar IS NOT NULL)
    BEGIN
        SELECT TOP 1 @UtilisateurId = ModifiePar, @NomUtilisateur = (SELECT NomUtilisateur FROM app.Utilisateurs WHERE Id = ModifiePar)
        FROM inserted
        WHERE ModifiePar IS NOT NULL;
    END
    ELSE IF @TypeAction IN ('Création', 'Modification') AND EXISTS (SELECT * FROM inserted WHERE CreePar IS NOT NULL)
    BEGIN
        SELECT TOP 1 @UtilisateurId = CreePar, @NomUtilisateur = (SELECT NomUtilisateur FROM app.Utilisateurs WHERE Id = CreePar)
        FROM inserted
        WHERE CreePar IS NOT NULL;
    END
    
    -- Insérer les entrées d'audit
    IF @TypeAction = 'Création'
    BEGIN
        INSERT INTO app.JournalAudit (
            DateAction, UtilisateurId, NomUtilisateur, TypeAction,
            TypeEntite, EntiteId, Description, DonneesAvant, DonneesApres
        )
        SELECT 
            GETDATE(), @UtilisateurId, @NomUtilisateur, @TypeAction,
            'Client', Id, 'Création d''un client: ' + RaisonSociale,
            NULL,
            (SELECT * FROM inserted i WHERE i.Id = ins.Id FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
        FROM inserted ins;
    END
    ELSE IF @TypeAction = 'Modification'
    BEGIN
        INSERT INTO app.JournalAudit (
            DateAction, UtilisateurId, NomUtilisateur, TypeAction,
            TypeEntite, EntiteId, Description, DonneesAvant, DonneesApres
        )
        SELECT 
            GETDATE(), @UtilisateurId, @NomUtilisateur, @TypeAction,
            'Client', ins.Id, 'Modification d''un client: ' + ins.RaisonSociale,
            (SELECT * FROM deleted d WHERE d.Id = ins.Id FOR JSON PATH, WITHOUT_ARRAY_WRAPPER),
            (SELECT * FROM inserted i WHERE i.Id = ins.Id FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
        FROM inserted ins
        INNER JOIN deleted del ON ins.Id = del.Id;
    END
    ELSE -- Suppression
    BEGIN
        INSERT INTO app.JournalAudit (
            DateAction, UtilisateurId, NomUtilisateur, TypeAction,
            TypeEntite, EntiteId, Description, DonneesAvant, DonneesApres
        )
        SELECT 
            GETDATE(), @UtilisateurId, @NomUtilisateur, @TypeAction,
            'Client', Id, 'Suppression d''un client: ' + RaisonSociale,
            (SELECT * FROM deleted d WHERE d.Id = del.Id FOR JSON PATH, WITHOUT_ARRAY_WRAPPER),
            NULL
        FROM deleted del;
    END
END;
GO

PRINT 'Déclencheurs créés avec succès.';
GO
