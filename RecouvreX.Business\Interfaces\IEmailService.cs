using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service d'envoi d'emails
    /// </summary>
    public interface IEmailService
    {
        /// <summary>
        /// Envoie un email simple
        /// </summary>
        /// <param name="destinataires">Liste des adresses email des destinataires</param>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="corps">Corps de l'email</param>
        /// <param name="estHtml">Indique si le corps est au format HTML</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        Task<bool> EnvoyerEmailAsync(List<string> destinataires, string objet, string corps, bool estHtml = false);

        /// <summary>
        /// Envoie un email avec une pièce jointe
        /// </summary>
        /// <param name="destinataires">Liste des adresses email des destinataires</param>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="corps">Corps de l'email</param>
        /// <param name="cheminPieceJointe">Chemin vers la pièce jointe</param>
        /// <param name="nomPieceJointe">Nom de la pièce jointe</param>
        /// <param name="estHtml">Indique si le corps est au format HTML</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        Task<bool> EnvoyerEmailAvecPieceJointeAsync(List<string> destinataires, string objet, string corps, string cheminPieceJointe, string nomPieceJointe, bool estHtml = false);

        /// <summary>
        /// Envoie un email avec plusieurs pièces jointes
        /// </summary>
        /// <param name="destinataires">Liste des adresses email des destinataires</param>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="corps">Corps de l'email</param>
        /// <param name="piecesJointes">Dictionnaire des pièces jointes (clé = chemin, valeur = nom)</param>
        /// <param name="estHtml">Indique si le corps est au format HTML</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        Task<bool> EnvoyerEmailAvecPiecesJointesAsync(List<string> destinataires, string objet, string corps, Dictionary<string, string> piecesJointes, bool estHtml = false);
    }
}
