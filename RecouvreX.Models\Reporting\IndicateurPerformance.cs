using System;

namespace RecouvreX.Models.Reporting
{
    /// <summary>
    /// Représente un indicateur de performance pour le tableau de bord
    /// </summary>
    public class IndicateurPerformance
    {
        /// <summary>
        /// Nom de l'indicateur
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Valeur actuelle de l'indicateur
        /// </summary>
        public decimal Valeur { get; set; }

        /// <summary>
        /// Valeur cible de l'indicateur
        /// </summary>
        public decimal Cible { get; set; }

        /// <summary>
        /// Valeur précédente de l'indicateur (période précédente)
        /// </summary>
        public decimal ValeurPrecedente { get; set; }

        /// <summary>
        /// Unité de mesure (€, %, jours, etc.)
        /// </summary>
        public string Unite { get; set; }

        /// <summary>
        /// Tendance (hausse, baisse, stable)
        /// </summary>
        public string Tendance { get; set; }

        /// <summary>
        /// Couleur associée à l'indicateur (selon performance)
        /// </summary>
        public string Couleur { get; set; }

        /// <summary>
        /// Description de l'indicateur
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Catégorie de l'indicateur
        /// </summary>
        public string Categorie { get; set; }

        /// <summary>
        /// Date de mise à jour
        /// </summary>
        public DateTime DateMiseAJour { get; set; }

        /// <summary>
        /// Pourcentage d'atteinte de l'objectif
        /// </summary>
        public decimal PourcentageObjectif => Cible != 0 ? Math.Min(100, Math.Max(0, (Valeur / Cible) * 100)) : 0;

        /// <summary>
        /// Variation par rapport à la période précédente
        /// </summary>
        public decimal Variation => ValeurPrecedente != 0 ? ((Valeur - ValeurPrecedente) / ValeurPrecedente) * 100 : 0;
    }
}
