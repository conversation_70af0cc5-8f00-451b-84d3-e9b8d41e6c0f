using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Communications
{
    partial class EmailForm
    {
        /// <summary>
        /// Variable nécessaire au concepteur.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private TableLayoutPanel tableLayoutPanel;
        private Label destinataireLabel;
        private ComboBox destinataireComboBox;
        private Label objetLabel;
        private TextBox objetTextBox;
        private Label piecesJointesLabel;
        private Button piecesJointesButton;
        private Label suiviLabel;
        private CheckBox suiviNecessaireCheckBox;
        private Label contenuLabel;
        private TextBox contenuTextBox;
        private FlowLayoutPanel buttonsPanel;
        private Button cancelButton;
        private Button saveButton;
        private Label noteLabel;

        /// <summary>
        /// Nettoyage des ressources utilisées.
        /// </summary>
        /// <param name="disposing">true si les ressources managées doivent être supprimées ; sinon, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Code généré par le Concepteur Windows Form

        /// <summary>
        /// Méthode requise pour la prise en charge du concepteur - ne modifiez pas
        /// le contenu de cette méthode avec l'éditeur de code.
        /// </summary>
        private void InitializeComponent()
        {
            mainPanel = new Panel();
            tableLayoutPanel = new TableLayoutPanel();
            destinataireLabel = new Label();
            destinataireComboBox = new ComboBox();
            objetLabel = new Label();
            objetTextBox = new TextBox();
            piecesJointesLabel = new Label();
            piecesJointesButton = new Button();
            suiviLabel = new Label();
            suiviNecessaireCheckBox = new CheckBox();
            contenuLabel = new Label();
            contenuTextBox = new TextBox();
            buttonsPanel = new FlowLayoutPanel();
            cancelButton = new Button();
            saveButton = new Button();
            noteLabel = new Label();
            mainPanel.SuspendLayout();
            tableLayoutPanel.SuspendLayout();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.Controls.Add(tableLayoutPanel);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Location = new Point(0, 0);
            mainPanel.Name = "mainPanel";
            mainPanel.Padding = new Padding(10);
            mainPanel.Size = new Size(600, 500);
            mainPanel.TabIndex = 0;
            // 
            // tableLayoutPanel
            // 
            tableLayoutPanel.ColumnCount = 2;
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            tableLayoutPanel.Controls.Add(destinataireLabel, 0, 0);
            tableLayoutPanel.Controls.Add(destinataireComboBox, 1, 0);
            tableLayoutPanel.Controls.Add(objetLabel, 0, 1);
            tableLayoutPanel.Controls.Add(objetTextBox, 1, 1);
            tableLayoutPanel.Controls.Add(piecesJointesLabel, 0, 2);
            tableLayoutPanel.Controls.Add(piecesJointesButton, 1, 2);
            tableLayoutPanel.Controls.Add(suiviLabel, 0, 3);
            tableLayoutPanel.Controls.Add(suiviNecessaireCheckBox, 1, 3);
            tableLayoutPanel.Controls.Add(contenuLabel, 0, 4);
            tableLayoutPanel.Controls.Add(contenuTextBox, 1, 4);
            tableLayoutPanel.Controls.Add(buttonsPanel, 1, 5);
            tableLayoutPanel.Dock = DockStyle.Fill;
            tableLayoutPanel.Location = new Point(10, 10);
            tableLayoutPanel.Name = "tableLayoutPanel";
            tableLayoutPanel.RowCount = 6;
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            tableLayoutPanel.Size = new Size(580, 480);
            tableLayoutPanel.TabIndex = 0;
            // 
            // destinataireLabel
            // 
            destinataireLabel.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            destinataireLabel.Location = new Point(3, 3);
            destinataireLabel.Name = "destinataireLabel";
            destinataireLabel.Size = new Size(168, 23);
            destinataireLabel.TabIndex = 0;
            destinataireLabel.Text = "Destinataire *:";
            // 
            // destinataireComboBox
            // 
            destinataireComboBox.Dock = DockStyle.Fill;
            destinataireComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            destinataireComboBox.Location = new Point(177, 3);
            destinataireComboBox.Name = "destinataireComboBox";
            destinataireComboBox.Size = new Size(400, 23);
            destinataireComboBox.TabIndex = 1;
            destinataireComboBox.Validating += DestinataireComboBox_Validating;
            // 
            // objetLabel
            // 
            objetLabel.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            objetLabel.Location = new Point(3, 33);
            objetLabel.Name = "objetLabel";
            objetLabel.Size = new Size(168, 23);
            objetLabel.TabIndex = 2;
            objetLabel.Text = "Objet *:";
            // 
            // objetTextBox
            // 
            objetTextBox.Dock = DockStyle.Fill;
            objetTextBox.Location = new Point(177, 33);
            objetTextBox.Name = "objetTextBox";
            objetTextBox.Size = new Size(400, 23);
            objetTextBox.TabIndex = 3;
            objetTextBox.Validating += ObjetTextBox_Validating;
            // 
            // piecesJointesLabel
            // 
            piecesJointesLabel.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            piecesJointesLabel.Location = new Point(3, 63);
            piecesJointesLabel.Name = "piecesJointesLabel";
            piecesJointesLabel.Size = new Size(168, 23);
            piecesJointesLabel.TabIndex = 4;
            piecesJointesLabel.Text = "Pièces jointes:";
            // 
            // piecesJointesButton
            // 
            piecesJointesButton.Location = new Point(177, 63);
            piecesJointesButton.Name = "piecesJointesButton";
            piecesJointesButton.Size = new Size(75, 23);
            piecesJointesButton.TabIndex = 5;
            piecesJointesButton.Text = "Ajouter...";
            piecesJointesButton.Click += PiecesJointesButton_Click;
            // 
            // suiviLabel
            // 
            suiviLabel.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            suiviLabel.Location = new Point(3, 93);
            suiviLabel.Name = "suiviLabel";
            suiviLabel.Size = new Size(168, 23);
            suiviLabel.TabIndex = 6;
            suiviLabel.Text = "Suivi nécessaire:";
            // 
            // suiviNecessaireCheckBox
            // 
            suiviNecessaireCheckBox.Location = new Point(177, 93);
            suiviNecessaireCheckBox.Name = "suiviNecessaireCheckBox";
            suiviNecessaireCheckBox.Size = new Size(104, 24);
            suiviNecessaireCheckBox.TabIndex = 7;
            suiviNecessaireCheckBox.Text = "Nécessite un suivi";
            suiviNecessaireCheckBox.CheckedChanged += SuiviNecessaireCheckBox_CheckedChanged;
            // 
            // contenuLabel
            // 
            contenuLabel.Anchor = AnchorStyles.Left;
            contenuLabel.Location = new Point(3, 268);
            contenuLabel.Name = "contenuLabel";
            contenuLabel.Size = new Size(100, 23);
            contenuLabel.TabIndex = 8;
            contenuLabel.Text = "Contenu *:";
            // 
            // contenuTextBox
            // 
            contenuTextBox.Dock = DockStyle.Fill;
            contenuTextBox.Location = new Point(177, 123);
            contenuTextBox.Multiline = true;
            contenuTextBox.Name = "contenuTextBox";
            contenuTextBox.ScrollBars = ScrollBars.Vertical;
            contenuTextBox.Size = new Size(400, 314);
            contenuTextBox.TabIndex = 9;
            contenuTextBox.Validating += ContenuTextBox_Validating;
            // 
            // buttonsPanel
            // 
            buttonsPanel.Controls.Add(cancelButton);
            buttonsPanel.Controls.Add(saveButton);
            buttonsPanel.Dock = DockStyle.Fill;
            buttonsPanel.FlowDirection = FlowDirection.RightToLeft;
            buttonsPanel.Location = new Point(177, 443);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.Size = new Size(400, 34);
            buttonsPanel.TabIndex = 10;
            buttonsPanel.WrapContents = false;
            // 
            // cancelButton
            // 
            cancelButton.Location = new Point(297, 3);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(100, 30);
            cancelButton.TabIndex = 0;
            cancelButton.Text = "Annuler";
            cancelButton.Click += CancelButton_Click;
            // 
            // saveButton
            // 
            saveButton.Location = new Point(191, 3);
            saveButton.Name = "saveButton";
            saveButton.Size = new Size(100, 30);
            saveButton.TabIndex = 1;
            saveButton.Text = "Envoyer";
            saveButton.Click += SaveButton_Click;
            // 
            // noteLabel
            // 
            noteLabel.AutoSize = true;
            noteLabel.ForeColor = Color.Red;
            noteLabel.Location = new Point(10, 261);
            noteLabel.Name = "noteLabel";
            noteLabel.Size = new Size(124, 15);
            noteLabel.TabIndex = 1;
            noteLabel.Text = "* Champs obligatoires";
            // 
            // EmailForm
            // 
            ClientSize = new Size(600, 500);
            Controls.Add(mainPanel);
            Controls.Add(noteLabel);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "EmailForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Envoyer un email";
            Load += EmailForm_Load;
            mainPanel.ResumeLayout(false);
            tableLayoutPanel.ResumeLayout(false);
            tableLayoutPanel.PerformLayout();
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion
    }
}
