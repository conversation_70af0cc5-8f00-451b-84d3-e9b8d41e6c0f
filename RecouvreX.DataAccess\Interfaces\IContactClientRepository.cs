using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des contacts client
    /// </summary>
    public interface IContactClientRepository : IRepository<ContactClient>
    {
        /// <summary>
        /// Récupère les contacts par client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des contacts pour le client spécifié</returns>
        Task<IEnumerable<ContactClient>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère le contact principal d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Contact principal du client ou null</returns>
        Task<ContactClient> GetPrincipalContactAsync(int clientId);

        /// <summary>
        /// Récupère le contact responsable des paiements d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Contact responsable des paiements du client ou null</returns>
        Task<ContactClient> GetPaymentContactAsync(int clientId);

        /// <summary>
        /// Définit un contact comme contact principal
        /// </summary>
        /// <param name="contactId">Identifiant du contact</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> SetAsPrincipalAsync(int contactId, int modifiePar);

        /// <summary>
        /// Définit un contact comme responsable des paiements
        /// </summary>
        /// <param name="contactId">Identifiant du contact</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue la modification</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> SetAsPaymentResponsibleAsync(int contactId, int modifiePar);
    }
}
