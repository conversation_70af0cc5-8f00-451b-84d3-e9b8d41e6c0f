-- Script de création des tables pour les plans de paiement et échéanciers
USE [RecouvreX]
GO

-- Table des plans de paiement
CREATE TABLE app.PlansPaiement (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Reference NVARCHAR(50) NOT NULL UNIQUE,
    ClientId INT NOT NULL,
    DateCreationPlan DATETIME NOT NULL,
    DateDebut DATETIME NOT NULL,
    DateFinPrevue DATETIME NOT NULL,
    MontantTotal DECIMAL(18, 2) NOT NULL,
    MontantInterets DECIMAL(18, 2) NOT NULL DEFAULT 0,
    Mont<PERSON><PERSON><PERSON><PERSON> DECIMAL(18, 2) NOT NULL DEFAULT 0,
    Statut NVARCHAR(50) NOT NULL,
    ResponsableId INT NOT NULL,
    Notes NVARCHAR(MAX) NULL,
    AccordSigne BIT NOT NULL DEFAULT 0,
    DateSignatureAccord DATETIME NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_PlansPaiement_Clients FOREIGN KEY (ClientId) REFERENCES app.Clients(Id),
    CONSTRAINT FK_PlansPaiement_Responsables FOREIGN KEY (ResponsableId) REFERENCES app.Utilisateurs(Id)
);
GO

-- Table des échéances de paiement
CREATE TABLE app.EcheancesPaiement (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    PlanPaiementId INT NOT NULL,
    NumeroOrdre INT NOT NULL,
    DateEcheance DATETIME NOT NULL,
    MontantPrevu DECIMAL(18, 2) NOT NULL,
    EstPayee BIT NOT NULL DEFAULT 0,
    DatePaiement DATETIME NULL,
    PaiementId INT NULL,
    MontantPaye DECIMAL(18, 2) NULL,
    Notes NVARCHAR(MAX) NULL,
    NotificationEnvoyee BIT NOT NULL DEFAULT 0,
    DateNotification DATETIME NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_EcheancesPaiement_PlansPaiement FOREIGN KEY (PlanPaiementId) REFERENCES app.PlansPaiement(Id),
    CONSTRAINT FK_EcheancesPaiement_Paiements FOREIGN KEY (PaiementId) REFERENCES app.Paiements(Id)
);
GO

-- Table d'association entre plans de paiement et factures (relation N-N)
CREATE TABLE app.PlanPaiementFactures (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    PlanPaiementId INT NOT NULL,
    FactureId INT NOT NULL,
    MontantCouvert DECIMAL(18, 2) NOT NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_PlanPaiementFactures_PlansPaiement FOREIGN KEY (PlanPaiementId) REFERENCES app.PlansPaiement(Id),
    CONSTRAINT FK_PlanPaiementFactures_Factures FOREIGN KEY (FactureId) REFERENCES app.Factures(Id),
    CONSTRAINT UQ_PlanPaiementFactures UNIQUE (PlanPaiementId, FactureId)
);
GO

-- Table d'association entre plans de paiement et documents (relation N-N)
CREATE TABLE app.PlanPaiementDocuments (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    PlanPaiementId INT NOT NULL,
    DocumentId INT NOT NULL,
    TypeDocument NVARCHAR(50) NOT NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_PlanPaiementDocuments_PlansPaiement FOREIGN KEY (PlanPaiementId) REFERENCES app.PlansPaiement(Id),
    CONSTRAINT FK_PlanPaiementDocuments_Documents FOREIGN KEY (DocumentId) REFERENCES app.Documents(Id),
    CONSTRAINT UQ_PlanPaiementDocuments UNIQUE (PlanPaiementId, DocumentId)
);
GO

-- Ajout d'un index sur la date d'échéance pour les performances
CREATE INDEX IX_EcheancesPaiement_DateEcheance ON app.EcheancesPaiement(DateEcheance);
GO

-- Ajout d'un index sur le statut des plans de paiement pour les performances
CREATE INDEX IX_PlansPaiement_Statut ON app.PlansPaiement(Statut);
GO

-- Ajout d'un index sur le client pour les performances
CREATE INDEX IX_PlansPaiement_ClientId ON app.PlansPaiement(ClientId);
GO
