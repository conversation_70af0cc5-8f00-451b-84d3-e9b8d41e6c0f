using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;

namespace RecouvreX.WinForms.Forms.Actions
{
    /// <summary>
    /// Formulaire de liste des actions prioritaires
    /// </summary>
    public partial class ActionPrioritaireListForm : Form
    {
        private readonly IActionPrioritaireService _actionPrioritaireService;
        private readonly IClientService _clientService;
        private readonly IUtilisateurService _utilisateurService;
        private readonly int _currentUserId;
        private List<ActionPrioritaire> _actions = new List<ActionPrioritaire>();
        private DataTable _actionsDataTable = new DataTable();
        private Dictionary<NiveauPriorite, int> _distribution = new Dictionary<NiveauPriorite, int>();

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="actionPrioritaireService">Service de gestion des actions prioritaires</param>
        /// <param name="clientService">Service de gestion des clients</param>
        /// <param name="utilisateurService">Service de gestion des utilisateurs</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        public ActionPrioritaireListForm(
            IActionPrioritaireService actionPrioritaireService,
            IClientService clientService,
            IUtilisateurService utilisateurService,
            int currentUserId)
        {
            _actionPrioritaireService = actionPrioritaireService ?? throw new ArgumentNullException(nameof(actionPrioritaireService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _currentUserId = currentUserId;

            InitializeComponent();
        }



        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void ActionPrioritaireListForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Initialiser la table de données
                InitializeDataTable();

                // Charger les actions prioritaires
                await LoadActionsAsync();

                // Charger la distribution des niveaux de priorité
                await LoadDistributionAsync();

                // Mettre à jour le graphique
                UpdateChart();

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des actions prioritaires");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des actions prioritaires : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Gère le changement de sélection dans la liste déroulante des priorités
        /// </summary>
        private void PrioriteComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// Gère le changement de sélection dans la liste déroulante des types d'action
        /// </summary>
        private void TypeActionComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// Gère le changement de sélection dans la liste déroulante des statuts
        /// </summary>
        private void StatutComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// Gère l'appui sur une touche dans la zone de recherche
        /// </summary>
        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                ApplyFilters();
                e.Handled = true;
                e.SuppressKeyPress = true;
            }
        }

        /// <summary>
        /// Gère le clic sur le bouton de recherche
        /// </summary>
        private void SearchButton_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// Gère le formatage des cellules du DataGridView
        /// </summary>
        private void ActionsDataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            var dataGridView = sender as DataGridView;
            if (dataGridView == null)
                return;

            // Colorer les lignes en fonction de la priorité
            if (dataGridView.Columns[e.ColumnIndex].Name == "Priorite")
            {
                string priorite = e.Value?.ToString() ?? string.Empty;
                switch (priorite)
                {
                    case "Critique":
                        e.CellStyle.ForeColor = Color.White;
                        e.CellStyle.BackColor = Color.DarkRed;
                        break;
                    case "Haute":
                        e.CellStyle.ForeColor = Color.White;
                        e.CellStyle.BackColor = Color.Red;
                        break;
                    case "Moyenne":
                        e.CellStyle.ForeColor = Color.Black;
                        e.CellStyle.BackColor = Color.Orange;
                        break;
                    case "Basse":
                        e.CellStyle.ForeColor = Color.Black;
                        e.CellStyle.BackColor = Color.Yellow;
                        break;
                }
            }

            // Colorer les lignes en fonction du statut
            if (dataGridView.Columns[e.ColumnIndex].Name == "Statut")
            {
                string statut = e.Value?.ToString() ?? string.Empty;
                switch (statut)
                {
                    case "À faire":
                        e.CellStyle.ForeColor = Color.Black;
                        e.CellStyle.BackColor = Color.LightBlue;
                        break;
                    case "Complétée":
                        e.CellStyle.ForeColor = Color.White;
                        e.CellStyle.BackColor = Color.Green;
                        break;
                    case "En retard":
                        e.CellStyle.ForeColor = Color.White;
                        e.CellStyle.BackColor = Color.DarkRed;
                        break;
                }
            }
        }

        /// <summary>
        /// Gère le double-clic sur une cellule du DataGridView
        /// </summary>
        private void ActionsDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0)
                return;

            var dataGridView = sender as DataGridView;
            if (dataGridView == null)
                return;

            // Récupérer l'identifiant de l'action sélectionnée
            int actionId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);

            // Trouver l'action correspondante
            var action = _actions.FirstOrDefault(a => a.Id == actionId);
            if (action == null)
                return;

            // Ouvrir le formulaire de détail de l'action
            // TODO: Implémenter le formulaire de détail
            MessageBox.Show($"Détails de l'action {action.Id} : {action.Description}", "Détails de l'action", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// Gère le clic sur le bouton de génération d'actions prioritaires
        /// </summary>
        private async void GenerateButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Demander confirmation
                var result = MessageBox.Show(
                    "Voulez-vous générer de nouvelles actions prioritaires ? Cette opération peut prendre quelques instants.",
                    "Confirmation",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Générer les actions prioritaires
                    int count = await _actionPrioritaireService.GenerateActionsAsync(_currentUserId);

                    // Recharger les actions
                    await LoadActionsAsync();
                    await LoadDistributionAsync();
                    UpdateChart();

                    // Afficher un message de confirmation
                    MessageBox.Show($"{count} actions prioritaires ont été générées avec succès.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la génération des actions prioritaires");
                MessageBox.Show($"Une erreur s'est produite lors de la génération des actions prioritaires : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Gère le clic sur le bouton de création d'action
        /// </summary>
        private void CreateButton_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter le formulaire de création d'action
            MessageBox.Show("Fonctionnalité non implémentée", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// Gère le clic sur le bouton pour marquer une action comme complétée
        /// </summary>
        private async void CompleteButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer le DataGridView
                var dataGridView = this.Controls.Find("actionsDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView == null || dataGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("Veuillez sélectionner une action à marquer comme complétée.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Récupérer l'identifiant de l'action sélectionnée
                int actionId = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);

                // Trouver l'action correspondante
                var action = _actions.FirstOrDefault(a => a.Id == actionId);
                if (action == null)
                    return;

                // Vérifier si l'action est déjà complétée
                if (action.EstCompletee)
                {
                    MessageBox.Show("Cette action est déjà marquée comme complétée.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Demander confirmation
                var result = MessageBox.Show(
                    $"Voulez-vous marquer l'action '{action.Description}' comme complétée ?",
                    "Confirmation",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Marquer l'action comme complétée
                    await _actionPrioritaireService.CompleteActionAsync(actionId, _currentUserId);

                    // Recharger les actions
                    await LoadActionsAsync();
                    await LoadDistributionAsync();
                    UpdateChart();

                    // Afficher un message de confirmation
                    MessageBox.Show("L'action a été marquée comme complétée avec succès.",
                        "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du marquage de l'action comme complétée");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gère le clic sur le bouton d'assignation d'action
        /// </summary>
        private async void AssignButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Récupérer l'action sélectionnée
                var dataGridView = this.Controls.Find("actionsDataGridView", true).FirstOrDefault() as DataGridView;
                if (dataGridView == null || dataGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("Veuillez sélectionner une action à assigner.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Récupérer l'ID de l'action sélectionnée
                int actionId = (int)dataGridView.SelectedRows[0].Cells["Id"].Value;

                // Récupérer l'action complète
                var action = await _actionPrioritaireService.GetByIdAsync(actionId);
                if (action == null)
                {
                    MessageBox.Show("L'action sélectionnée n'existe plus.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Vérifier si l'action est déjà complétée
                if (action.EstCompletee)
                {
                    MessageBox.Show("Impossible d'assigner une action déjà complétée.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Ouvrir le formulaire d'assignation
                using (var form = new AssignerActionForm(_actionPrioritaireService, _utilisateurService, _currentUserId, action))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les données
                        await LoadActionsAsync();
                        ApplyFilters();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'assignation d'une action prioritaire");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gère le clic sur le bouton de fermeture
        /// </summary>
        private void CloseButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// Initialise la table de données pour le DataGridView
        /// </summary>
        private void InitializeDataTable()
        {
            _actionsDataTable = new DataTable();
            _actionsDataTable.Columns.Add("Id", typeof(int));
            _actionsDataTable.Columns.Add("Client", typeof(string));
            _actionsDataTable.Columns.Add("Description", typeof(string));
            _actionsDataTable.Columns.Add("TypeAction", typeof(string));
            _actionsDataTable.Columns.Add("Priorite", typeof(string));
            _actionsDataTable.Columns.Add("DateCreation", typeof(DateTime));
            _actionsDataTable.Columns.Add("DateEcheance", typeof(DateTime));
            _actionsDataTable.Columns.Add("AssigneA", typeof(string));
            _actionsDataTable.Columns.Add("Statut", typeof(string));

            // Configurer le DataGridView
            var dataGridView = this.Controls.Find("actionsDataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                dataGridView.DataSource = _actionsDataTable;

                // Configurer les colonnes
                dataGridView.Columns["Id"].Visible = false;
                dataGridView.Columns["Client"].HeaderText = "Client";
                dataGridView.Columns["Description"].HeaderText = "Description";
                dataGridView.Columns["TypeAction"].HeaderText = "Type d'action";
                dataGridView.Columns["Priorite"].HeaderText = "Priorité";
                dataGridView.Columns["DateCreation"].HeaderText = "Date de création";
                dataGridView.Columns["DateCreation"].DefaultCellStyle.Format = "dd/MM/yyyy";
                dataGridView.Columns["DateEcheance"].HeaderText = "Date d'échéance";
                dataGridView.Columns["DateEcheance"].DefaultCellStyle.Format = "dd/MM/yyyy";
                dataGridView.Columns["AssigneA"].HeaderText = "Assigné à";
                dataGridView.Columns["Statut"].HeaderText = "Statut";
            }
        }

        /// <summary>
        /// Charge les actions prioritaires depuis le service
        /// </summary>
        private async Task LoadActionsAsync()
        {
            try
            {
                // Récupérer toutes les actions prioritaires
                _actions = (await _actionPrioritaireService.GetAllAsync()).ToList();

                // Appliquer les filtres
                ApplyFilters();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des actions prioritaires");
                throw;
            }
        }

        /// <summary>
        /// Charge la distribution des niveaux de priorité
        /// </summary>
        private async Task LoadDistributionAsync()
        {
            try
            {
                // Récupérer la distribution des niveaux de priorité
                _distribution = await _actionPrioritaireService.GetDistributionByPrioriteAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement de la distribution des niveaux de priorité");
                throw;
            }
        }

        /// <summary>
        /// Met à jour le graphique de distribution des niveaux de priorité
        /// </summary>
        private void UpdateChart()
        {
            try
            {
                // Récupérer le graphique
                var chart = this.Controls.Find("distributionChart", true).FirstOrDefault() as Chart;
                if (chart == null)
                    return;

                // Effacer les données existantes
                chart.Series["Distribution"].Points.Clear();

                // Ajouter les nouvelles données
                foreach (var kvp in _distribution)
                {
                    int pointIndex = chart.Series["Distribution"].Points.AddY(kvp.Value);
                    var point = chart.Series["Distribution"].Points[pointIndex];
                    point.AxisLabel = kvp.Key.ToString();
                    point.Label = $"{kvp.Value}";

                    // Définir la couleur en fonction du niveau de priorité
                    switch (kvp.Key)
                    {
                        case NiveauPriorite.Critique:
                            point.Color = System.Drawing.Color.DarkRed;
                            break;
                        case NiveauPriorite.Haute:
                            point.Color = System.Drawing.Color.Red;
                            break;
                        case NiveauPriorite.Moyenne:
                            point.Color = System.Drawing.Color.Orange;
                            break;
                        case NiveauPriorite.Basse:
                            point.Color = System.Drawing.Color.Yellow;
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la mise à jour du graphique");
                throw;
            }
        }

        /// <summary>
        /// Applique les filtres sélectionnés
        /// </summary>
        private void ApplyFilters()
        {
            try
            {
                // Récupérer les contrôles de filtre
                var prioriteComboBox = this.Controls.Find("prioriteComboBox", true).FirstOrDefault() as ComboBox;
                var typeActionComboBox = this.Controls.Find("typeActionComboBox", true).FirstOrDefault() as ComboBox;
                var statutComboBox = this.Controls.Find("statutComboBox", true).FirstOrDefault() as ComboBox;
                var searchTextBox = this.Controls.Find("searchTextBox", true).FirstOrDefault() as TextBox;

                if (prioriteComboBox == null || typeActionComboBox == null || statutComboBox == null || searchTextBox == null)
                    return;

                // Récupérer les valeurs des filtres
                string selectedPriorite = prioriteComboBox.SelectedItem?.ToString() ?? "Toutes";
                string selectedTypeAction = typeActionComboBox.SelectedItem?.ToString() ?? "Toutes";
                string selectedStatut = statutComboBox.SelectedItem?.ToString() ?? "Toutes";
                string searchText = searchTextBox.Text.Trim().ToLower();

                // Filtrer les actions
                var filteredActions = _actions;

                // Filtre par priorité
                if (selectedPriorite != "Toutes")
                {
                    filteredActions = filteredActions.Where(a => a.NiveauPriorite.ToString() == selectedPriorite).ToList();
                }

                // Filtre par type d'action
                if (selectedTypeAction != "Toutes")
                {
                    filteredActions = filteredActions.Where(a => a.TypeAction.ToString() == selectedTypeAction).ToList();
                }

                // Filtre par statut
                if (selectedStatut != "Toutes")
                {
                    if (selectedStatut == "À faire")
                    {
                        filteredActions = filteredActions.Where(a => !a.EstCompletee && a.DateEcheance >= DateTime.Now).ToList();
                    }
                    else if (selectedStatut == "Complétées")
                    {
                        filteredActions = filteredActions.Where(a => a.EstCompletee).ToList();
                    }
                    else if (selectedStatut == "En retard")
                    {
                        filteredActions = filteredActions.Where(a => !a.EstCompletee && a.DateEcheance < DateTime.Now).ToList();
                    }
                }

                // Filtre par texte de recherche
                if (!string.IsNullOrEmpty(searchText))
                {
                    filteredActions = filteredActions.Where(a =>
                        (a.Client?.RaisonSociale?.ToLower().Contains(searchText) ?? false) ||
                        (a.Description?.ToLower().Contains(searchText) ?? false)).ToList();
                }

                // Mettre à jour la table de données
                UpdateDataTable(filteredActions);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'application des filtres");
                MessageBox.Show($"Une erreur s'est produite lors de l'application des filtres : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Met à jour la table de données avec les actions filtrées
        /// </summary>
        /// <param name="actions">Liste des actions filtrées</param>
        private void UpdateDataTable(List<ActionPrioritaire> actions)
        {
            // Effacer les données existantes
            _actionsDataTable.Rows.Clear();

            // Ajouter les nouvelles données
            foreach (var action in actions)
            {
                string statut = action.EstCompletee ? "Complétée" : (action.DateEcheance < DateTime.Now ? "En retard" : "À faire");
                string assigneA = action.UtilisateurAssigne?.NomComplet ?? "Non assignée";

                _actionsDataTable.Rows.Add(
                    action.Id,
                    action.Client?.RaisonSociale,
                    action.Description,
                    action.TypeAction.ToString(),
                    action.NiveauPriorite.ToString(),
                    action.DateCreation,
                    action.DateEcheance,
                    assigneA,
                    statut
                );
            }

            // Mettre à jour le compteur
            var statusStrip = this.Controls.Find("statusStrip", true).FirstOrDefault() as StatusStrip;
            var countLabel = statusStrip?.Items.Find("countLabel", false).FirstOrDefault() as ToolStripStatusLabel;
            if (countLabel != null)
            {
                countLabel.Text = $"Nombre d'actions : {actions.Count}";
            }
        }
    }
}
