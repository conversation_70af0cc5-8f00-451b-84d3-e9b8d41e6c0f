-- Script de création des tables pour la base de données RecouvreX
-- Ce script crée toutes les tables nécessaires pour l'application de gestion de recouvrement des créances clients

USE RecouvreX;
GO

-- Table des rôles
CREATE TABLE app.Roles (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Nom NVARCHAR(50) NOT NULL,
    Description NVARCHAR(255) NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1
);
GO

-- Table des permissions
CREATE TABLE app.Permissions (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Nom NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255) NULL,
    Code NVARCHAR(50) NOT NULL UNIQUE,
    Module NVARCHAR(50) NOT NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1
);
GO

-- Table des utilisateurs
CREATE TABLE app.Utilisateurs (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    NomUtilisateur NVARCHAR(50) NOT NULL UNIQUE,
    MotDePasse NVARCHAR(255) NOT NULL,
    NomComplet NVARCHAR(100) NOT NULL,
    Email NVARCHAR(100) NULL,
    Telephone NVARCHAR(20) NULL,
    RoleId INT NOT NULL,
    DerniereConnexion DATETIME NULL,
    EstVerrouille BIT NOT NULL DEFAULT 0,
    TentativesConnexionEchouees INT NOT NULL DEFAULT 0,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_Utilisateurs_Roles FOREIGN KEY (RoleId) REFERENCES app.Roles(Id)
);
GO

-- Table d'association entre rôles et permissions
CREATE TABLE app.RolePermissions (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    RoleId INT NOT NULL,
    PermissionId INT NOT NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_RolePermissions_Roles FOREIGN KEY (RoleId) REFERENCES app.Roles(Id),
    CONSTRAINT FK_RolePermissions_Permissions FOREIGN KEY (PermissionId) REFERENCES app.Permissions(Id),
    CONSTRAINT UQ_RolePermissions UNIQUE (RoleId, PermissionId)
);
GO

-- Table des clients
CREATE TABLE app.Clients (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Code NVARCHAR(20) NOT NULL UNIQUE,
    RaisonSociale NVARCHAR(100) NOT NULL,
    Adresse NVARCHAR(255) NULL,
    Ville NVARCHAR(100) NULL,
    CodePostal NVARCHAR(20) NULL,
    Pays NVARCHAR(50) NULL DEFAULT 'France',
    Telephone NVARCHAR(20) NULL,
    Email NVARCHAR(100) NULL,
    NumeroFiscal NVARCHAR(50) NULL,
    CommercialId INT NULL,
    LimiteCredit DECIMAL(18, 2) NOT NULL DEFAULT 0,
    SoldeActuel DECIMAL(18, 2) NOT NULL DEFAULT 0,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_Clients_Commerciaux FOREIGN KEY (CommercialId) REFERENCES app.Utilisateurs(Id)
);
GO

-- Table des contacts clients
CREATE TABLE app.Contacts (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    ClientId INT NOT NULL,
    Nom NVARCHAR(50) NOT NULL,
    Prenom NVARCHAR(50) NULL,
    Fonction NVARCHAR(100) NULL,
    Telephone NVARCHAR(20) NULL,
    Email NVARCHAR(100) NULL,
    EstPrincipal BIT NOT NULL DEFAULT 0,
    Notes NVARCHAR(MAX) NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_Contacts_Clients FOREIGN KEY (ClientId) REFERENCES app.Clients(Id)
);
GO

-- Table des factures
CREATE TABLE app.Factures (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Numero NVARCHAR(20) NOT NULL UNIQUE,
    ClientId INT NOT NULL,
    DateEmission DATETIME NOT NULL,
    DateEcheance DATETIME NOT NULL,
    MontantHT DECIMAL(18, 2) NOT NULL,
    MontantTVA DECIMAL(18, 2) NOT NULL,
    MontantTTC DECIMAL(18, 2) NOT NULL,
    MontantPaye DECIMAL(18, 2) NOT NULL DEFAULT 0,
    MontantRestant DECIMAL(18, 2) NOT NULL,
    Statut NVARCHAR(50) NOT NULL,
    CommercialId INT NULL,
    LivreurId INT NULL,
    Notes NVARCHAR(MAX) NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_Factures_Clients FOREIGN KEY (ClientId) REFERENCES app.Clients(Id),
    CONSTRAINT FK_Factures_Commerciaux FOREIGN KEY (CommercialId) REFERENCES app.Utilisateurs(Id),
    CONSTRAINT FK_Factures_Livreurs FOREIGN KEY (LivreurId) REFERENCES app.Utilisateurs(Id)
);
GO

-- Table des paiements
CREATE TABLE app.Paiements (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Reference NVARCHAR(50) NOT NULL UNIQUE,
    ClientId INT NOT NULL,
    DatePaiement DATETIME NOT NULL,
    Montant DECIMAL(18, 2) NOT NULL,
    ModePaiement NVARCHAR(50) NOT NULL,
    ReferenceBancaire NVARCHAR(100) NULL,
    Notes NVARCHAR(MAX) NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_Paiements_Clients FOREIGN KEY (ClientId) REFERENCES app.Clients(Id)
);
GO

-- Table d'association entre factures et paiements (relation N-N)
CREATE TABLE app.FacturePaiements (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    FactureId INT NOT NULL,
    PaiementId INT NOT NULL,
    MontantAffecte DECIMAL(18, 2) NOT NULL,
    Notes NVARCHAR(MAX) NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_FacturePaiements_Factures FOREIGN KEY (FactureId) REFERENCES app.Factures(Id),
    CONSTRAINT FK_FacturePaiements_Paiements FOREIGN KEY (PaiementId) REFERENCES app.Paiements(Id),
    CONSTRAINT UQ_FacturePaiements UNIQUE (FactureId, PaiementId)
);
GO

-- Table des relances
CREATE TABLE app.Relances (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    FactureId INT NOT NULL,
    Type NVARCHAR(50) NOT NULL,
    DateRelance DATETIME NOT NULL,
    Niveau INT NOT NULL,
    Statut NVARCHAR(50) NOT NULL,
    UtilisateurId INT NULL,
    Contenu NVARCHAR(MAX) NULL,
    ReponseClient NVARCHAR(MAX) NULL,
    DateProchaineRelance DATETIME NULL,
    Notes NVARCHAR(MAX) NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_Relances_Factures FOREIGN KEY (FactureId) REFERENCES app.Factures(Id),
    CONSTRAINT FK_Relances_Utilisateurs FOREIGN KEY (UtilisateurId) REFERENCES app.Utilisateurs(Id)
);
GO

-- Table des documents
CREATE TABLE app.Documents (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Nom NVARCHAR(255) NOT NULL,
    Type NVARCHAR(50) NOT NULL,
    MimeType NVARCHAR(100) NOT NULL,
    Taille BIGINT NOT NULL,
    Contenu VARBINARY(MAX) NOT NULL,
    Description NVARCHAR(MAX) NULL,
    TypeEntite NVARCHAR(50) NOT NULL,
    EntiteId INT NOT NULL,
    DateCreation DATETIME NOT NULL DEFAULT GETDATE(),
    CreePar INT NOT NULL,
    DateModification DATETIME NULL,
    ModifiePar INT NULL,
    EstActif BIT NOT NULL DEFAULT 1
);
GO

-- Table du journal d'audit
CREATE TABLE app.JournalAudit (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    DateAction DATETIME NOT NULL,
    UtilisateurId INT NOT NULL,
    NomUtilisateur NVARCHAR(50) NOT NULL,
    TypeAction NVARCHAR(50) NOT NULL,
    TypeEntite NVARCHAR(50) NULL,
    EntiteId INT NULL,
    Description NVARCHAR(MAX) NULL,
    DonneesAvant NVARCHAR(MAX) NULL,
    DonneesApres NVARCHAR(MAX) NULL,
    AdresseIP NVARCHAR(50) NULL
);
GO

PRINT 'Tables créées avec succès.';
GO
