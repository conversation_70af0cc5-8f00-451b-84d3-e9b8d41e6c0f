using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using RecouvreX.DataAccess.Repositories;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des clients
    /// </summary>
    public class ClientService : IClientService
    {
        private readonly IClientRepository _clientRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="clientRepository">Repository des clients</param>
        /// <param name="utilisateurRepository">Repository des utilisateurs</param>
        /// <param name="journalAuditRepository">Repository du journal d'audit</param>
        public ClientService(
            IClientRepository clientRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère tous les clients
        /// </summary>
        /// <returns>Liste des clients</returns>
        public async Task<IEnumerable<Client>> GetAllAsync()
        {
            return await _clientRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère un client par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du client</param>
        /// <returns>Client trouvé ou null</returns>
        public async Task<Client> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _clientRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère un client par son code
        /// </summary>
        /// <param name="code">Code du client</param>
        /// <returns>Client trouvé ou null</returns>
        public async Task<Client> GetByCodeAsync(string code)
        {
            if (string.IsNullOrEmpty(code))
                return null;

            return await _clientRepository.GetByCodeAsync(code);
        }

        /// <summary>
        /// Récupère un client avec ses contacts
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Client avec ses contacts</returns>
        public async Task<Client> GetWithContactsAsync(int clientId)
        {
            if (clientId <= 0)
                return null;

            return await _clientRepository.GetWithContactsAsync(clientId);
        }

        /// <summary>
        /// Récupère un client avec ses factures
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Client avec ses factures</returns>
        public async Task<Client> GetWithFacturesAsync(int clientId)
        {
            if (clientId <= 0)
                return null;

            return await _clientRepository.GetWithFacturesAsync(clientId);
        }

        /// <summary>
        /// Récupère tous les clients d'un commercial
        /// </summary>
        /// <param name="commercialId">Identifiant du commercial</param>
        /// <returns>Liste des clients du commercial</returns>
        public async Task<IEnumerable<Client>> GetByCommercialIdAsync(int commercialId)
        {
            if (commercialId <= 0)
                return new List<Client>();

            return await _clientRepository.GetByCommercialIdAsync(commercialId);
        }

        /// <summary>
        /// Recherche des clients par nom ou raison sociale
        /// </summary>
        /// <param name="searchTerm">Terme de recherche</param>
        /// <returns>Liste des clients correspondant au terme de recherche</returns>
        public async Task<IEnumerable<Client>> SearchByNameAsync(string searchTerm)
        {
            if (string.IsNullOrEmpty(searchTerm))
                return new List<Client>();

            return await _clientRepository.SearchByNameAsync(searchTerm);
        }

        /// <summary>
        /// Crée un nouveau client
        /// </summary>
        /// <param name="client">Client à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Client créé avec son identifiant généré</returns>
        public async Task<Client> CreateAsync(Client client, int creePar)
        {
            if (client == null || creePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la création d'un client");

            // Vérifier si le code du client existe déjà
            if (!string.IsNullOrEmpty(client.Code))
            {
                var existingClient = await _clientRepository.GetByCodeAsync(client.Code);
                if (existingClient != null)
                    throw new InvalidOperationException($"Le code client '{client.Code}' existe déjà");
            }
            else
            {
                // Générer un code client unique si non fourni
                client.Code = await GenerateUniqueClientCodeAsync(client.RaisonSociale);
            }

            // Vérifier si le commercial existe
            if (client.CommercialId.HasValue && client.CommercialId.Value > 0)
            {
                var commercial = await _utilisateurRepository.GetByIdAsync(client.CommercialId.Value);
                if (commercial == null)
                    throw new InvalidOperationException($"Le commercial avec l'ID {client.CommercialId.Value} n'existe pas");
            }

            // Créer le client
            var createdClient = await _clientRepository.AddAsync(client, creePar);

            // Journaliser la création
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = creePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(creePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Creation,
                TypeEntite = "Client",
                EntiteId = createdClient.Id,
                Description = $"Création du client {createdClient.RaisonSociale}",
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdClient.Id,
                    createdClient.Code,
                    createdClient.RaisonSociale,
                    createdClient.Adresse,
                    createdClient.Ville,
                    createdClient.CodePostal,
                    createdClient.Pays,
                    createdClient.Telephone,
                    createdClient.Email,
                    createdClient.CommercialId,
                    createdClient.LimiteCredit,
                    createdClient.SoldeActuel
                })
            });

            return createdClient;
        }

        /// <summary>
        /// Met à jour un client existant
        /// </summary>
        /// <param name="client">Client à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Client mis à jour</returns>
        public async Task<Client> UpdateAsync(Client client, int modifiePar)
        {
            if (client == null || client.Id <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la mise à jour d'un client");

            // Récupérer le client existant
            var existingClient = await _clientRepository.GetByIdAsync(client.Id);
            if (existingClient == null)
                throw new InvalidOperationException($"Le client avec l'ID {client.Id} n'existe pas");

            // Vérifier si le code du client existe déjà pour un autre client
            if (client.Code != existingClient.Code && !string.IsNullOrEmpty(client.Code))
            {
                var clientWithSameCode = await _clientRepository.GetByCodeAsync(client.Code);
                if (clientWithSameCode != null && clientWithSameCode.Id != client.Id)
                    throw new InvalidOperationException($"Le code client '{client.Code}' existe déjà");
            }

            // Vérifier si le commercial existe
            if (client.CommercialId.HasValue && client.CommercialId.Value > 0)
            {
                var commercial = await _utilisateurRepository.GetByIdAsync(client.CommercialId.Value);
                if (commercial == null)
                    throw new InvalidOperationException($"Le commercial avec l'ID {client.CommercialId.Value} n'existe pas");
            }

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Client",
                EntiteId = client.Id,
                Description = $"Modification du client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingClient.Id,
                    existingClient.Code,
                    existingClient.RaisonSociale,
                    existingClient.Adresse,
                    existingClient.Ville,
                    existingClient.CodePostal,
                    existingClient.Pays,
                    existingClient.Telephone,
                    existingClient.Email,
                    existingClient.CommercialId,
                    existingClient.LimiteCredit,
                    existingClient.SoldeActuel
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    client.Id,
                    client.Code,
                    client.RaisonSociale,
                    client.Adresse,
                    client.Ville,
                    client.CodePostal,
                    client.Pays,
                    client.Telephone,
                    client.Email,
                    client.CommercialId,
                    client.LimiteCredit,
                    client.SoldeActuel
                })
            });

            // Mettre à jour le client
            return await _clientRepository.UpdateAsync(client, modifiePar);
        }

        /// <summary>
        /// Supprime un client
        /// </summary>
        /// <param name="id">Identifiant du client à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0 || supprimePar <= 0)
                return false;

            // Récupérer le client à supprimer
            var client = await _clientRepository.GetByIdAsync(id);
            if (client == null)
                return false;

            // Journaliser la suppression
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = supprimePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(supprimePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Suppression,
                TypeEntite = "Client",
                EntiteId = id,
                Description = $"Suppression du client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    client.Id,
                    client.Code,
                    client.RaisonSociale,
                    client.Adresse,
                    client.Ville,
                    client.CodePostal,
                    client.Pays,
                    client.Telephone,
                    client.Email,
                    client.CommercialId,
                    client.LimiteCredit,
                    client.SoldeActuel
                })
            });

            // Supprimer le client
            return await _clientRepository.DeleteAsync(id, supprimePar);
        }

        /// <summary>
        /// Ajoute un contact à un client
        /// </summary>
        /// <param name="contact">Contact à ajouter</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact ajouté avec son identifiant généré</returns>
        public async Task<Contact> AddContactAsync(Contact contact, int creePar)
        {
            if (contact == null || contact.ClientId <= 0 || creePar <= 0)
                throw new ArgumentException("Paramètres invalides pour l'ajout d'un contact");

            // Vérifier si le client existe
            var client = await _clientRepository.GetByIdAsync(contact.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {contact.ClientId} n'existe pas");

            // Ajouter le contact
            var contactRepository = new ContactRepository(_clientRepository.GetDatabaseConnection());
            var contactId = await contactRepository.AddAsync(contact, creePar);

            // Récupérer le contact créé pour avoir toutes ses propriétés
            var createdContact = await contactRepository.GetByIdAsync(contactId.Id);

            // Journaliser l'ajout du contact
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = creePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(creePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Creation,
                TypeEntite = "Contact",
                EntiteId = createdContact.Id,
                Description = $"Ajout du contact {createdContact.Nom} {createdContact.Prenom} pour le client {client.RaisonSociale}",
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdContact.Id,
                    createdContact.ClientId,
                    createdContact.Nom,
                    createdContact.Prenom,
                    createdContact.Fonction,
                    createdContact.Telephone,
                    createdContact.Email,
                    createdContact.EstPrincipal
                })
            });

            return createdContact;
        }

        /// <summary>
        /// Met à jour un contact
        /// </summary>
        /// <param name="contact">Contact à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Contact mis à jour</returns>
        public async Task<Contact> UpdateContactAsync(Contact contact, int modifiePar)
        {
            if (contact == null || contact.Id <= 0 || contact.ClientId <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la mise à jour d'un contact");

            // Vérifier si le client existe
            var client = await _clientRepository.GetByIdAsync(contact.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {contact.ClientId} n'existe pas");

            // Récupérer le contact existant
            var contactRepository = new ContactRepository(_clientRepository.GetDatabaseConnection());
            var existingContact = await contactRepository.GetByIdAsync(contact.Id);
            if (existingContact == null)
                throw new InvalidOperationException($"Le contact avec l'ID {contact.Id} n'existe pas");

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Contact",
                EntiteId = contact.Id,
                Description = $"Modification du contact {contact.Nom} {contact.Prenom} pour le client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingContact.Id,
                    existingContact.ClientId,
                    existingContact.Nom,
                    existingContact.Prenom,
                    existingContact.Fonction,
                    existingContact.Telephone,
                    existingContact.Email,
                    existingContact.EstPrincipal
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    contact.Id,
                    contact.ClientId,
                    contact.Nom,
                    contact.Prenom,
                    contact.Fonction,
                    contact.Telephone,
                    contact.Email,
                    contact.EstPrincipal
                })
            });

            // Mettre à jour le contact
            var updatedContact = await contactRepository.UpdateAsync(contact, modifiePar);
            return updatedContact;
        }

        /// <summary>
        /// Supprime un contact
        /// </summary>
        /// <param name="contactId">Identifiant du contact à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteContactAsync(int contactId, int supprimePar)
        {
            if (contactId <= 0 || supprimePar <= 0)
                return false;

            // Récupérer le contact à supprimer
            var contactRepository = new ContactRepository(_clientRepository.GetDatabaseConnection());
            var contact = await contactRepository.GetByIdAsync(contactId);
            if (contact == null)
                return false;

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(contact.ClientId);
            if (client == null)
                return false;

            // Journaliser la suppression
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = supprimePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(supprimePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Suppression,
                TypeEntite = "Contact",
                EntiteId = contactId,
                Description = $"Suppression du contact {contact.Nom} {contact.Prenom} pour le client {client.RaisonSociale}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    contact.Id,
                    contact.ClientId,
                    contact.Nom,
                    contact.Prenom,
                    contact.Fonction,
                    contact.Telephone,
                    contact.Email,
                    contact.EstPrincipal
                })
            });

            // Supprimer le contact
            return await contactRepository.DeleteAsync(contactId, supprimePar);
        }

        /// <summary>
        /// Récupère les clients avec des factures en retard
        /// </summary>
        /// <returns>Liste des clients avec des factures en retard</returns>
        public async Task<IEnumerable<Client>> GetWithOverdueInvoicesAsync()
        {
            return await _clientRepository.GetWithOverdueInvoicesAsync();
        }

        /// <summary>
        /// Calcule le solde d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Solde actuel du client</returns>
        public async Task<decimal> CalculateSoldeAsync(int clientId)
        {
            if (clientId <= 0)
                return 0;

            return await _clientRepository.UpdateSoldeAsync(clientId, 0);
        }

        /// <summary>
        /// Récupère les clients par segment
        /// </summary>
        /// <param name="segment">Segment de client</param>
        /// <returns>Liste des clients du segment spécifié</returns>
        public async Task<IEnumerable<Client>> GetBySegmentAsync(SegmentClient segment)
        {
            return await _clientRepository.GetBySegmentAsync(segment);
        }

        /// <summary>
        /// Met à jour le segment d'un client manuellement
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="segment">Nouveau segment</param>
        /// <param name="commentaire">Commentaire sur la segmentation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Client mis à jour</returns>
        public async Task<Client> UpdateSegmentAsync(int clientId, SegmentClient segment, string commentaire, int modifiePar)
        {
            if (clientId <= 0 || modifiePar <= 0)
                throw new ArgumentException("Paramètres invalides pour la mise à jour du segment d'un client");

            // Récupérer le client existant
            var existingClient = await _clientRepository.GetByIdAsync(clientId);
            if (existingClient == null)
                throw new InvalidOperationException($"Le client avec l'ID {clientId} n'existe pas");

            // Journaliser la modification avant de mettre à jour
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                DateAction = DateTime.Now,
                UtilisateurId = modifiePar,
                NomUtilisateur = (await _utilisateurRepository.GetByIdAsync(modifiePar))?.NomUtilisateur ?? "Système",
                TypeAction = TypeAudit.Modification,
                TypeEntite = "Client",
                EntiteId = clientId,
                Description = $"Modification du segment du client {existingClient.RaisonSociale} : {existingClient.Segment} -> {segment}",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingClient.Id,
                    existingClient.RaisonSociale,
                    Segment = existingClient.Segment,
                    existingClient.CommentaireSegmentation,
                    existingClient.DateSegmentation,
                    existingClient.SegmentationManuelle
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingClient.Id,
                    existingClient.RaisonSociale,
                    Segment = segment,
                    CommentaireSegmentation = commentaire,
                    DateSegmentation = DateTime.Now,
                    SegmentationManuelle = true
                })
            });

            // Mettre à jour le segment du client
            return await _clientRepository.UpdateSegmentAsync(clientId, segment, commentaire, true, modifiePar);
        }

        /// <summary>
        /// Segmente automatiquement tous les clients
        /// </summary>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre de clients segmentés</returns>
        public async Task<int> SegmenterClientsAutomatiquementAsync(int modifiePar)
        {
            if (modifiePar <= 0)
                throw new ArgumentException("Paramètre utilisateurId invalide");

            // Récupérer tous les clients
            var clients = await _clientRepository.GetAllAsync();
            int count = 0;

            foreach (var client in clients)
            {
                try
                {
                    // Ne pas modifier les clients qui ont été segmentés manuellement
                    if (client.SegmentationManuelle)
                        continue;

                    // Calculer le segment du client
                    var segment = await CalculerSegmentClientAsync(client.Id);

                    // Mettre à jour le segment du client
                    string commentaire = $"Segmentation automatique basée sur les critères du {DateTime.Now:dd/MM/yyyy}";
                    await _clientRepository.UpdateSegmentAsync(client.Id, segment, commentaire, false, modifiePar);

                    count++;
                }
                catch (Exception ex)
                {
                    // Journaliser l'erreur mais continuer avec les autres clients
                    Serilog.Log.Error(ex, "Erreur lors de la segmentation automatique du client {ClientId}", client.Id);
                }
            }

            return count;
        }

        /// <summary>
        /// Récupère la répartition des clients par segment
        /// </summary>
        /// <returns>Dictionnaire avec le segment comme clé et le nombre de clients comme valeur</returns>
        public async Task<Dictionary<SegmentClient, int>> GetClientDistributionBySegmentAsync()
        {
            return await _clientRepository.GetClientDistributionBySegmentAsync();
        }

        /// <summary>
        /// Calcule le segment d'un client en fonction de critères prédéfinis
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Segment calculé</returns>
        private async Task<SegmentClient> CalculerSegmentClientAsync(int clientId)
        {
            // Récupérer le client avec ses factures
            var client = await _clientRepository.GetWithFacturesAsync(clientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {clientId} n'existe pas");

            // Critères de segmentation
            int score = 0;

            // Critère 1: Montant total des factures (poids: 40%)
            decimal montantTotal = 0;
            if (client.Factures != null && client.Factures.Any())
            {
                montantTotal = client.Factures.Sum(f => f.MontantTotal);
            }

            // Segmentation par montant total
            if (montantTotal > 50000) // Clients à haute valeur
            {
                score += 40;
            }
            else if (montantTotal > 10000) // Clients à valeur moyenne
            {
                score += 20;
            }

            // Critère 2: Historique de paiement (poids: 30%)
            int facturesEnRetard = 0;
            int facturesTotal = 0;
            if (client.Factures != null && client.Factures.Any())
            {
                facturesTotal = client.Factures.Count();
                facturesEnRetard = client.Factures.Count(f => f.Statut == StatutFacture.EnRetard);
            }

            double pourcentageRetard = facturesTotal > 0 ? (double)facturesEnRetard / facturesTotal * 100 : 0;

            // Segmentation par historique de paiement
            if (pourcentageRetard < 10) // Excellent historique de paiement
            {
                score += 30;
            }
            else if (pourcentageRetard < 30) // Bon historique de paiement
            {
                score += 15;
            }

            // Critère 3: Ancienneté de la relation (poids: 20%)
            if (client.DateCreation != DateTime.MinValue)
            {
                var anciennete = (DateTime.Now - client.DateCreation).TotalDays / 365; // En années

                // Segmentation par ancienneté
                if (anciennete > 3) // Client fidèle
                {
                    score += 20;
                }
                else if (anciennete > 1) // Client établi
                {
                    score += 10;
                }
            }

            // Critère 4: Limite de crédit (poids: 10%)
            if (client.LimiteCredit > 10000) // Limite de crédit élevée
            {
                score += 10;
            }
            else if (client.LimiteCredit > 5000) // Limite de crédit moyenne
            {
                score += 5;
            }

            // Déterminer le segment en fonction du score
            if (score >= 70) // Clients stratégiques
            {
                return SegmentClient.A;
            }
            else if (score >= 40) // Clients importants
            {
                return SegmentClient.B;
            }
            else // Clients standards
            {
                return SegmentClient.C;
            }
        }

        /// <summary>
        /// Génère un code client unique
        /// </summary>
        /// <param name="raisonSociale">Raison sociale du client</param>
        /// <returns>Code client unique</returns>
        private async Task<string> GenerateUniqueClientCodeAsync(string raisonSociale)
        {
            if (string.IsNullOrEmpty(raisonSociale))
                throw new ArgumentException("La raison sociale ne peut pas être vide pour générer un code client");

            // Extraire les 3 premières lettres de la raison sociale (ou moins si la raison sociale est plus courte)
            string prefix = raisonSociale.Length >= 3 ? raisonSociale.Substring(0, 3).ToUpper() : raisonSociale.ToUpper();

            // Récupérer tous les codes clients existants qui commencent par ce préfixe
            var existingCodes = await _clientRepository.FindAsync("Code LIKE @Prefix", new { Prefix = $"{prefix}%" });

            // Compter le nombre de codes existants et ajouter 1 pour le nouveau code
            int count = 1;
            foreach (var client in existingCodes)
            {
                if (client.Code.StartsWith(prefix) && client.Code.Length > prefix.Length)
                {
                    if (int.TryParse(client.Code.Substring(prefix.Length), out int num) && num >= count)
                    {
                        count = num + 1;
                    }
                }
            }

            // Générer le nouveau code
            return $"{prefix}{count:D4}";
        }
    }
}
