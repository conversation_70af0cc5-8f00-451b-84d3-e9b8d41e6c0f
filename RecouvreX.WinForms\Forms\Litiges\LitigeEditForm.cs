using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using RecouvreX.WinForms.Helpers;
using Serilog;
using System.Data;

namespace RecouvreX.WinForms.Forms.Litiges
{
    /// <summary>
    /// Formulaire d'édition d'un litige
    /// </summary>
    public partial class LitigeEditForm : Form
    {
        private readonly ILitigeService _litigeService;
        private readonly IClientService _clientService;
        private readonly IFactureService _factureService;
        private readonly IUtilisateurService _utilisateurService;
        private readonly int _currentUserId;
        private Litige? _litige;
        private int? _clientId;
        private int? _factureId;
        private List<CategorieLitige> _categories = new List<CategorieLitige>();
        private List<EtapeLitige> _etapes = new List<EtapeLitige>();
        private List<Utilisateur> _utilisateurs = new List<Utilisateur>();
        private List<Facture> _factures = new List<Facture>();
        private List<CommentaireLitige> _commentaires = new List<CommentaireLitige>();
        private List<HistoriqueEtapeLitige> _historiqueEtapes = new List<HistoriqueEtapeLitige>();
        private bool _isNewLitige = true;
        private bool _isLoading = true;

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="litigeService">Service de gestion des litiges</param>
        /// <param name="clientService">Service de gestion des clients</param>
        /// <param name="factureService">Service de gestion des factures</param>
        /// <param name="utilisateurService">Service de gestion des utilisateurs</param>
        /// <param name="currentUserId">Identifiant de l'utilisateur courant</param>
        /// <param name="litige">Litige à éditer (null pour un nouveau litige)</param>
        /// <param name="clientId">Identifiant du client (optionnel)</param>
        /// <param name="factureId">Identifiant de la facture (optionnel)</param>
        public LitigeEditForm(
            ILitigeService litigeService,
            IClientService clientService,
            IFactureService factureService,
            IUtilisateurService utilisateurService,
            int currentUserId,
            Litige? litige = null,
            int? clientId = null,
            int? factureId = null)
        {
            _litigeService = litigeService ?? throw new ArgumentNullException(nameof(litigeService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _utilisateurService = utilisateurService ?? throw new ArgumentNullException(nameof(utilisateurService));
            _currentUserId = currentUserId;
            _litige = litige;
            _clientId = clientId;
            _factureId = factureId;
            _isNewLitige = litige == null;

            InitializeComponent();
        }

        // La méthode InitializeComponent() a été déplacée dans le fichier LitigeEditForm.Designer.cs

        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private async void LitigeEditForm_Load(object sender, EventArgs e)
        {
            try
            {
                _isLoading = true;

                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Définir le titre du formulaire
                this.Text = _isNewLitige ? "Nouveau litige" : "Modifier un litige";

                // Charger l'icône du formulaire
                try
                {
                    this.Icon = new Icon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "dispute.ico"));
                }
                catch
                {
                    // Ignorer les erreurs d'icône
                }

                // Mettre à jour le titre
                var titleLabelControl = this.Controls.Find("titleLabel", true).FirstOrDefault() as Label;
                if (titleLabelControl != null)
                {
                    titleLabelControl.Text = _isNewLitige ? "Nouveau litige" : "Modifier un litige";
                }

                // Configurer le ComboBox de facture
                var factureComboBoxControl = this.Controls.Find("factureComboBox", true).FirstOrDefault() as ComboBox;
                if (factureComboBoxControl != null)
                {
                    factureComboBoxControl.Enabled = _factureId == null && _isNewLitige;
                }

                // Charger les catégories de litiges
                _categories = (await _litigeService.GetAllCategoriesAsync()).ToList();
                var categorieComboBox = this.Controls.Find("categorieComboBox", true).FirstOrDefault() as ComboBox;
                if (categorieComboBox != null)
                {
                    categorieComboBox.Items.Clear();
                    foreach (var categorie in _categories)
                    {
                        categorieComboBox.Items.Add(categorie.Nom);
                    }
                }

                // Charger les étapes de litiges
                _etapes = (await _litigeService.GetAllEtapesAsync()).ToList();
                ComboBox etapeComboBox = this.Controls.Find("etapeComboBox", true).FirstOrDefault() as ComboBox;
                if (etapeComboBox != null)
                {
                    etapeComboBox.Items.Clear();
                    foreach (var etape in _etapes)
                    {
                        etapeComboBox.Items.Add(etape.Nom);
                    }
                }

                // Charger les utilisateurs
                _utilisateurs = (await _utilisateurService.GetAllAsync()).ToList();
                ComboBox responsableComboBox = this.Controls.Find("responsableComboBox", true).FirstOrDefault() as ComboBox;
                if (responsableComboBox != null)
                {
                    responsableComboBox.Items.Clear();
                    foreach (var utilisateur in _utilisateurs)
                    {
                        responsableComboBox.Items.Add(utilisateur.NomComplet);
                    }
                }

                // Charger les factures
                if (_clientId.HasValue)
                {
                    // Si un client est spécifié, charger ses factures
                    _factures = (await _factureService.GetByClientIdAsync(_clientId.Value)).ToList();
                }
                else
                {
                    // Sinon, charger toutes les factures
                    _factures = (await _factureService.GetAllAsync()).ToList();
                }

                var factureComboBox = this.Controls.Find("factureComboBox", true).FirstOrDefault() as ComboBox;
                if (factureComboBox != null)
                {
                    factureComboBox.Items.Clear();
                    foreach (var facture in _factures)
                    {
                        string clientNom = facture.Client?.RaisonSociale ?? "Client inconnu";
                        factureComboBox.Items.Add($"{facture.Numero} - {clientNom} - {facture.MontantTotal:C}");
                    }
                }

                // Si c'est une modification, charger les données du litige
                if (!_isNewLitige && _litige != null)
                {
                    // Charger le litige avec tous ses détails
                    _litige = await _litigeService.GetWithDetailsAsync(_litige.Id);

                    // Remplir les champs avec les données du litige
                    if (_litige != null)
                    {
                        // Facture
                        if (factureComboBox != null && _litige.Facture != null)
                        {
                            for (int i = 0; i < _factures.Count; i++)
                            {
                                if (_factures[i].Id == _litige.FactureId)
                                {
                                    factureComboBox.SelectedIndex = i;
                                    break;
                                }
                            }
                        }

                        // Catégorie
                        if (categorieComboBox != null && _litige.CategorieLitige != null)
                        {
                            for (int i = 0; i < _categories.Count; i++)
                            {
                                if (_categories[i].Id == _litige.CategorieLitigeId)
                                {
                                    categorieComboBox.SelectedIndex = i;
                                    break;
                                }
                            }
                        }

                        // Montant contesté
                        var montantTextBox = this.Controls.Find("montantTextBox", true).FirstOrDefault() as TextBox;
                        if (montantTextBox != null)
                        {
                            montantTextBox.Text = _litige.MontantConteste.ToString();
                        }

                        // Description
                        var descriptionTextBox = this.Controls.Find("descriptionTextBox", true).FirstOrDefault() as TextBox;
                        if (descriptionTextBox != null)
                        {
                            descriptionTextBox.Text = _litige.Description;
                        }

                        // Date d'ouverture
                        var dateOuverturePicker = this.Controls.Find("dateOuverturePicker", true).FirstOrDefault() as DateTimePicker;
                        if (dateOuverturePicker != null)
                        {
                            dateOuverturePicker.Value = _litige.DateOuverture;
                        }

                        // Date d'échéance
                        var dateEcheancePicker = this.Controls.Find("dateEcheancePicker", true).FirstOrDefault() as DateTimePicker;
                        if (dateEcheancePicker != null)
                        {
                            dateEcheancePicker.Value = _litige.DateEcheance;
                        }

                        // Responsable
                        if (responsableComboBox != null && _litige.Responsable != null)
                        {
                            for (int i = 0; i < _utilisateurs.Count; i++)
                            {
                                if (_utilisateurs[i].Id == _litige.ResponsableId)
                                {
                                    responsableComboBox.SelectedIndex = i;
                                    break;
                                }
                            }
                        }

                        // Priorité
                        var prioriteComboBox = this.Controls.Find("prioriteComboBox", true).FirstOrDefault() as ComboBox;
                        if (prioriteComboBox != null)
                        {
                            prioriteComboBox.SelectedIndex = _litige.Priorite - 1; // 1-based to 0-based
                        }

                        // Statut
                        var statutComboBox = this.Controls.Find("statutComboBox", true).FirstOrDefault() as ComboBox;
                        if (statutComboBox != null)
                        {
                            switch (_litige.Statut)
                            {
                                case "Ouvert":
                                    statutComboBox.SelectedIndex = 0;
                                    break;
                                case "En cours":
                                    statutComboBox.SelectedIndex = 1;
                                    break;
                                case "Résolu":
                                    statutComboBox.SelectedIndex = 2;
                                    break;
                            }
                        }

                        // Étape
                        if (etapeComboBox != null && _litige.EtapeLitige != null)
                        {
                            for (int i = 0; i < _etapes.Count; i++)
                            {
                                if (_etapes[i].Id == _litige.EtapeLitigeId)
                                {
                                    etapeComboBox.SelectedIndex = i;
                                    break;
                                }
                            }
                        }

                        // Solution
                        var solutionTextBox = this.Controls.Find("solutionTextBox", true).FirstOrDefault() as TextBox;
                        if (solutionTextBox != null)
                        {
                            solutionTextBox.Text = _litige.Solution;
                        }

                        // Commentaires
                        _commentaires = (_litige.Commentaires ?? new List<CommentaireLitige>()).ToList();
                        DisplayCommentaires();

                        // Historique des étapes
                        _historiqueEtapes = (_litige.HistoriqueEtapes ?? new List<HistoriqueEtapeLitige>()).ToList();
                        DisplayHistoriqueEtapes();
                    }
                }
                else
                {
                    // Si c'est un nouveau litige, initialiser les valeurs par défaut
                    var dateOuverturePicker = this.Controls.Find("dateOuverturePicker", true).FirstOrDefault() as DateTimePicker;
                    if (dateOuverturePicker != null)
                    {
                        dateOuverturePicker.Value = DateTime.Today;
                    }

                    var dateEcheancePicker = this.Controls.Find("dateEcheancePicker", true).FirstOrDefault() as DateTimePicker;
                    if (dateEcheancePicker != null)
                    {
                        dateEcheancePicker.Value = DateTime.Today.AddDays(30);
                    }

                    var prioriteComboBox = this.Controls.Find("prioriteComboBox", true).FirstOrDefault() as ComboBox;
                    if (prioriteComboBox != null)
                    {
                        prioriteComboBox.SelectedIndex = 1; // Moyenne
                    }

                    var statutComboBox = this.Controls.Find("statutComboBox", true).FirstOrDefault() as ComboBox;
                    if (statutComboBox != null)
                    {
                        statutComboBox.SelectedIndex = 0; // Ouvert
                    }

                    ComboBox etapeComboBoxDefault = this.Controls.Find("etapeComboBox", true).FirstOrDefault() as ComboBox;
                    if (etapeComboBoxDefault != null && etapeComboBoxDefault.Items.Count > 0)
                    {
                        etapeComboBoxDefault.SelectedIndex = 0; // Première étape
                    }

                    ComboBox responsableComboBoxDefault = this.Controls.Find("responsableComboBox", true).FirstOrDefault() as ComboBox;
                    if (responsableComboBoxDefault != null)
                    {
                        // Sélectionner l'utilisateur courant comme responsable
                        for (int i = 0; i < _utilisateurs.Count; i++)
                        {
                            if (_utilisateurs[i].Id == _currentUserId)
                            {
                                responsableComboBoxDefault.SelectedIndex = i;
                                break;
                            }
                        }
                    }

                    // Si une facture est spécifiée, la sélectionner
                    if (_factureId.HasValue && factureComboBox != null)
                    {
                        for (int i = 0; i < _factures.Count; i++)
                        {
                            if (_factures[i].Id == _factureId.Value)
                            {
                                factureComboBox.SelectedIndex = i;
                                break;
                            }
                        }
                    }
                }

                // Mettre à jour l'affichage du champ Solution en fonction du statut
                UpdateSolutionVisibility();

                _isLoading = false;

                // Restaurer le curseur
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                _isLoading = false;
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors du chargement du formulaire d'édition de litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Affiche les commentaires dans le DataGridView
        /// </summary>
        private void DisplayCommentaires()
        {
            var dataGridView = this.Controls.Find("commentairesDataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                // Créer une table de données
                var dataTable = new DataTable();
                dataTable.Columns.Add("Id", typeof(int));
                dataTable.Columns.Add("DateCommentaire", typeof(string));
                dataTable.Columns.Add("Utilisateur", typeof(string));
                dataTable.Columns.Add("Contenu", typeof(string));
                dataTable.Columns.Add("EstInterne", typeof(bool));

                // Remplir la table avec les données des commentaires
                foreach (var commentaire in _commentaires.OrderByDescending(c => c.DateCommentaire))
                {
                    dataTable.Rows.Add(
                        commentaire.Id,
                        commentaire.DateCommentaire.ToString("dd/MM/yyyy HH:mm"),
                        commentaire.Utilisateur?.NomComplet ?? "Utilisateur inconnu",
                        commentaire.Contenu,
                        commentaire.EstInterne
                    );
                }

                // Assigner la table au DataGridView
                dataGridView.DataSource = dataTable;
            }
        }

        /// <summary>
        /// Affiche l'historique des étapes dans le DataGridView
        /// </summary>
        private void DisplayHistoriqueEtapes()
        {
            var dataGridView = this.Controls.Find("historiqueDataGridView", true).FirstOrDefault() as DataGridView;
            if (dataGridView != null)
            {
                // Créer une table de données
                var dataTable = new DataTable();
                dataTable.Columns.Add("Id", typeof(int));
                dataTable.Columns.Add("DateDebut", typeof(string));
                dataTable.Columns.Add("DateFin", typeof(string));
                dataTable.Columns.Add("Etape", typeof(string));
                dataTable.Columns.Add("Utilisateur", typeof(string));
                dataTable.Columns.Add("Commentaire", typeof(string));

                // Remplir la table avec les données de l'historique
                foreach (var historique in _historiqueEtapes.OrderByDescending(h => h.DateDebut))
                {
                    dataTable.Rows.Add(
                        historique.Id,
                        historique.DateDebut.ToString("dd/MM/yyyy HH:mm"),
                        historique.DateFin?.ToString("dd/MM/yyyy HH:mm") ?? "-",
                        historique.EtapeLitige?.Nom ?? "Étape inconnue",
                        historique.Utilisateur?.NomComplet ?? "Utilisateur inconnu",
                        historique.Commentaire
                    );
                }

                // Assigner la table au DataGridView
                dataGridView.DataSource = dataTable;
            }
        }

        /// <summary>
        /// Met à jour la visibilité du champ Solution en fonction du statut
        /// </summary>
        private void UpdateSolutionVisibility()
        {
            var statutComboBox = this.Controls.Find("statutComboBox", true).FirstOrDefault() as ComboBox;
            var solutionLabel = this.Controls.Find("solutionLabel", true).FirstOrDefault() as Label;
            var solutionTextBox = this.Controls.Find("solutionTextBox", true).FirstOrDefault() as TextBox;

            if (statutComboBox != null && solutionLabel != null && solutionTextBox != null)
            {
                bool isResolved = statutComboBox.SelectedItem?.ToString() == "Résolu";
                solutionLabel.Visible = isResolved;
                solutionTextBox.Visible = isResolved;
            }
        }

        /// <summary>
        /// Événement de changement de sélection dans la liste déroulante des statuts
        /// </summary>
        private void StatutComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!_isLoading)
            {
                UpdateSolutionVisibility();
            }
        }

        /// <summary>
        /// Événement de changement de sélection dans la liste déroulante des catégories
        /// </summary>
        private void CategorieComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!_isLoading)
            {
                // Mettre à jour la date d'échéance en fonction de la catégorie sélectionnée
                var categorieComboBox = sender as ComboBox;
                var dateEcheancePicker = this.Controls.Find("dateEcheancePicker", true).FirstOrDefault() as DateTimePicker;

                if (categorieComboBox != null && dateEcheancePicker != null && categorieComboBox.SelectedIndex >= 0)
                {
                    var categorie = _categories[categorieComboBox.SelectedIndex];
                    var dateOuverturePicker = this.Controls.Find("dateOuverturePicker", true).FirstOrDefault() as DateTimePicker;
                    if (dateOuverturePicker != null)
                    {
                        dateEcheancePicker.Value = dateOuverturePicker.Value.AddDays(categorie.DelaiResolutionJours);
                    }
                }
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Ajouter un commentaire
        /// </summary>
        private async void AjouterCommentaireButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Vérifier que le litige existe
                if (_isNewLitige)
                {
                    MessageBox.Show("Veuillez d'abord enregistrer le litige avant d'ajouter un commentaire.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Récupérer le contenu du commentaire
                var nouveauCommentaireTextBox = this.Controls.Find("nouveauCommentaireTextBox", true).FirstOrDefault() as TextBox;
                var commentaireInterneCheckBox = this.Controls.Find("commentaireInterneCheckBox", true).FirstOrDefault() as CheckBox;

                if (nouveauCommentaireTextBox != null && !string.IsNullOrWhiteSpace(nouveauCommentaireTextBox.Text))
                {
                    // Créer le commentaire
                    var commentaire = new CommentaireLitige
                    {
                        LitigeId = _litige.Id,
                        Contenu = nouveauCommentaireTextBox.Text,
                        DateCommentaire = DateTime.Now,
                        UtilisateurId = _currentUserId,
                        EstInterne = commentaireInterneCheckBox?.Checked ?? false
                    };

                    // Ajouter le commentaire
                    var nouveauCommentaire = await _litigeService.AddCommentaireAsync(commentaire);

                    // Ajouter le commentaire à la liste
                    _commentaires.Add(nouveauCommentaire);

                    // Mettre à jour l'affichage
                    DisplayCommentaires();

                    // Effacer le champ de saisie
                    nouveauCommentaireTextBox.Text = "";
                    if (commentaireInterneCheckBox != null)
                    {
                        commentaireInterneCheckBox.Checked = false;
                    }
                }
                else
                {
                    MessageBox.Show("Veuillez saisir un commentaire.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ajout d'un commentaire");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Enregistrer
        /// </summary>
        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Afficher un indicateur de chargement
                this.Cursor = Cursors.WaitCursor;

                // Récupérer les valeurs des champs
                var factureComboBox = this.Controls.Find("factureComboBox", true).FirstOrDefault() as ComboBox;
                var categorieComboBox = this.Controls.Find("categorieComboBox", true).FirstOrDefault() as ComboBox;
                var montantTextBox = this.Controls.Find("montantTextBox", true).FirstOrDefault() as TextBox;
                var descriptionTextBox = this.Controls.Find("descriptionTextBox", true).FirstOrDefault() as TextBox;
                var dateOuverturePicker = this.Controls.Find("dateOuverturePicker", true).FirstOrDefault() as DateTimePicker;
                var dateEcheancePicker = this.Controls.Find("dateEcheancePicker", true).FirstOrDefault() as DateTimePicker;
                var responsableComboBox = this.Controls.Find("responsableComboBox", true).FirstOrDefault() as ComboBox;
                var prioriteComboBox = this.Controls.Find("prioriteComboBox", true).FirstOrDefault() as ComboBox;
                var statutComboBox = this.Controls.Find("statutComboBox", true).FirstOrDefault() as ComboBox;
                var etapeComboBox = this.Controls.Find("etapeComboBox", true).FirstOrDefault() as ComboBox;
                var solutionTextBox = this.Controls.Find("solutionTextBox", true).FirstOrDefault() as TextBox;

                // Valider les champs obligatoires
                if (factureComboBox?.SelectedIndex < 0 ||
                    categorieComboBox?.SelectedIndex < 0 ||
                    string.IsNullOrWhiteSpace(montantTextBox?.Text) ||
                    string.IsNullOrWhiteSpace(descriptionTextBox?.Text) ||
                    responsableComboBox?.SelectedIndex < 0 ||
                    prioriteComboBox?.SelectedIndex < 0 ||
                    statutComboBox?.SelectedIndex < 0 ||
                    etapeComboBox?.SelectedIndex < 0)
                {
                    MessageBox.Show("Veuillez remplir tous les champs obligatoires.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Cursor = Cursors.Default;
                    return;
                }

                // Valider le montant contesté
                if (!decimal.TryParse(montantTextBox.Text, out decimal montantConteste) || montantConteste <= 0)
                {
                    MessageBox.Show("Le montant contesté doit être un nombre positif.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Cursor = Cursors.Default;
                    return;
                }

                // Valider la solution si le statut est "Résolu"
                if (statutComboBox.SelectedItem.ToString() == "Résolu" && string.IsNullOrWhiteSpace(solutionTextBox?.Text))
                {
                    MessageBox.Show("Veuillez saisir une solution pour résoudre le litige.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Cursor = Cursors.Default;
                    return;
                }

                // Créer ou mettre à jour le litige
                if (_isNewLitige)
                {
                    // Créer un nouveau litige
                    var litige = new Litige
                    {
                        FactureId = _factures[factureComboBox.SelectedIndex].Id,
                        CategorieLitigeId = _categories[categorieComboBox.SelectedIndex].Id,
                        MontantConteste = montantConteste,
                        Description = descriptionTextBox.Text,
                        DateOuverture = dateOuverturePicker.Value,
                        DateEcheance = dateEcheancePicker.Value,
                        ResponsableId = _utilisateurs[responsableComboBox.SelectedIndex].Id,
                        Priorite = prioriteComboBox.SelectedIndex + 1, // 0-based to 1-based
                        Statut = statutComboBox.SelectedItem.ToString(),
                        EtapeLitigeId = _etapes[etapeComboBox.SelectedIndex].Id,
                        Solution = statutComboBox.SelectedItem.ToString() == "Résolu" ? solutionTextBox.Text : null,
                        DateResolution = statutComboBox.SelectedItem.ToString() == "Résolu" ? DateTime.Now : (DateTime?)null
                    };

                    // Créer le litige
                    _litige = await _litigeService.CreateAsync(litige, _currentUserId);
                }
                else
                {
                    // Mettre à jour le litige existant
                    _litige.FactureId = _factures[factureComboBox.SelectedIndex].Id;
                    _litige.CategorieLitigeId = _categories[categorieComboBox.SelectedIndex].Id;
                    _litige.MontantConteste = montantConteste;
                    _litige.Description = descriptionTextBox.Text;
                    _litige.DateOuverture = dateOuverturePicker.Value;
                    _litige.DateEcheance = dateEcheancePicker.Value;
                    _litige.ResponsableId = _utilisateurs[responsableComboBox.SelectedIndex].Id;
                    _litige.Priorite = prioriteComboBox.SelectedIndex + 1; // 0-based to 1-based
                    _litige.Statut = statutComboBox.SelectedItem.ToString();
                    _litige.EtapeLitigeId = _etapes[etapeComboBox.SelectedIndex].Id;
                    _litige.Solution = statutComboBox.SelectedItem.ToString() == "Résolu" ? solutionTextBox.Text : null;
                    _litige.DateResolution = statutComboBox.SelectedItem.ToString() == "Résolu" ? DateTime.Now : (DateTime?)null;

                    // Mettre à jour le litige
                    _litige = await _litigeService.UpdateAsync(_litige, _currentUserId);
                }

                // Restaurer le curseur
                this.Cursor = Cursors.Default;

                // Fermer le formulaire avec succès
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                Log.Error(ex, "Erreur lors de l'enregistrement du litige");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Annuler
        /// </summary>
        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// Événement de clic sur le bouton Gérer les documents
        /// </summary>
        private void DocumentsButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Vérifier que le litige est enregistré
                if (_litige == null || _litige.Id == 0)
                {
                    MessageBox.Show("Veuillez d'abord enregistrer le litige avant de gérer les documents.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Ouvrir le formulaire de gestion des documents
                using (var form = new DocumentLitigeForm(_litigeService, _currentUserId, _litige.Id, _litige))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de gestion des documents");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
