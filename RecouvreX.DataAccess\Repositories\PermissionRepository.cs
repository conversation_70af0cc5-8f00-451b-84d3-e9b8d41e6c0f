using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les permissions
    /// </summary>
    public class PermissionRepository : BaseRepository<Permission>, IPermissionRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public PermissionRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "Permissions")
        {
        }

        /// <summary>
        /// Récupère une permission par son code
        /// </summary>
        /// <param name="code">Code de la permission</param>
        /// <returns>Permission trouvée ou null</returns>
        public async Task<Permission> GetByCodeAsync(string code)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Permissions WHERE Code = @Code AND EstActif = 1";
                return await connection.QueryFirstOrDefaultAsync<Permission>(query, new { Code = code });
            }
        }

        /// <summary>
        /// Récupère toutes les permissions par module
        /// </summary>
        /// <param name="module">Nom du module</param>
        /// <returns>Liste des permissions du module</returns>
        public async Task<IEnumerable<Permission>> GetByModuleAsync(string module)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Permissions WHERE Module = @Module AND EstActif = 1";
                return await connection.QueryAsync<Permission>(query, new { Module = module });
            }
        }

        /// <summary>
        /// Récupère toutes les permissions d'un utilisateur
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des permissions de l'utilisateur</returns>
        public async Task<IEnumerable<Permission>> GetByUserIdAsync(int userId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT p.*
                    FROM Permissions p
                    INNER JOIN RolePermissions rp ON p.Id = rp.PermissionId
                    INNER JOIN Utilisateurs u ON rp.RoleId = u.RoleId
                    WHERE u.Id = @UserId AND p.EstActif = 1 AND rp.EstActif = 1 AND u.EstActif = 1";

                return await connection.QueryAsync<Permission>(query, new { UserId = userId });
            }
        }

        /// <summary>
        /// Vérifie si un utilisateur possède une permission spécifique
        /// </summary>
        /// <param name="userId">Identifiant de l'utilisateur</param>
        /// <param name="permissionCode">Code de la permission</param>
        /// <returns>True si l'utilisateur possède la permission, sinon False</returns>
        public async Task<bool> UserHasPermissionAsync(int userId, string permissionCode)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT COUNT(*)
                    FROM Permissions p
                    INNER JOIN RolePermissions rp ON p.Id = rp.PermissionId
                    INNER JOIN Utilisateurs u ON rp.RoleId = u.RoleId
                    WHERE u.Id = @UserId AND p.Code = @PermissionCode
                        AND p.EstActif = 1 AND rp.EstActif = 1 AND u.EstActif = 1";

                var count = await connection.ExecuteScalarAsync<int>(query, new { UserId = userId, PermissionCode = permissionCode });
                return count > 0;
            }
        }

        /// <summary>
        /// Récupère les permissions associées à un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Liste des permissions du rôle</returns>
        public async Task<IEnumerable<Permission>> GetByRoleIdAsync(int roleId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT p.*
                    FROM Permissions p
                    INNER JOIN RolePermissions rp ON p.Id = rp.PermissionId
                    WHERE rp.RoleId = @RoleId AND p.EstActif = 1 AND rp.EstActif = 1
                    ORDER BY p.Module, p.Nom";

                return await connection.QueryAsync<Permission>(query, new { RoleId = roleId });
            }
        }
    }
}
