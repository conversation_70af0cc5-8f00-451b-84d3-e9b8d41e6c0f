using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.Business.Interfaces
{
    /// <summary>
    /// Interface pour le service de gestion des rôles
    /// </summary>
    public interface IRoleService
    {
        /// <summary>
        /// Récupère tous les rôles
        /// </summary>
        /// <returns>Liste des rôles</returns>
        Task<IEnumerable<Role>> GetAllAsync();

        /// <summary>
        /// Récupère un rôle par son identifiant
        /// </summary>
        /// <param name="id">Identifiant du rôle</param>
        /// <returns>Rôle trouvé ou null</returns>
        Task<Role> GetByIdAsync(int id);

        /// <summary>
        /// Récupère un rôle par son nom
        /// </summary>
        /// <param name="nom">Nom du rôle</param>
        /// <returns>Rôle trouvé ou null</returns>
        Task<Role> GetByNomAsync(string nom);

        /// <summary>
        /// Récupère un rôle avec ses permissions
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Rôle avec ses permissions</returns>
        Task<Role> GetWithPermissionsAsync(int roleId);

        /// <summary>
        /// Récupère tous les rôles avec leurs permissions
        /// </summary>
        /// <returns>Liste des rôles avec leurs permissions</returns>
        Task<IEnumerable<Role>> GetAllWithPermissionsAsync();

        /// <summary>
        /// Récupère tous les rôles avec des statistiques (nombre d'utilisateurs et de permissions)
        /// </summary>
        /// <returns>Liste des rôles avec leurs statistiques</returns>
        Task<IEnumerable<RoleWithStats>> GetAllWithStatsAsync();

        /// <summary>
        /// Crée un nouveau rôle
        /// </summary>
        /// <param name="role">Rôle à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Rôle créé avec son identifiant généré</returns>
        Task<Role> CreateAsync(Role role, int creePar);

        /// <summary>
        /// Met à jour un rôle existant
        /// </summary>
        /// <param name="role">Rôle à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Rôle mis à jour</returns>
        Task<Role> UpdateAsync(Role role, int modifiePar);

        /// <summary>
        /// Supprime un rôle
        /// </summary>
        /// <param name="id">Identifiant du rôle à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> DeleteAsync(int id, int supprimePar);

        /// <summary>
        /// Ajoute une permission à un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <param name="permissionId">Identifiant de la permission</param>
        /// <param name="ajoutePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si l'ajout a réussi, sinon False</returns>
        Task<bool> AddPermissionAsync(int roleId, int permissionId, int ajoutePar);

        /// <summary>
        /// Supprime une permission d'un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <param name="permissionId">Identifiant de la permission</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        Task<bool> RemovePermissionAsync(int roleId, int permissionId, int supprimePar);

        /// <summary>
        /// Récupère toutes les permissions disponibles
        /// </summary>
        /// <returns>Liste de toutes les permissions</returns>
        Task<IEnumerable<Permission>> GetAllPermissionsAsync();

        /// <summary>
        /// Récupère les permissions associées à un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <returns>Liste des permissions du rôle</returns>
        Task<IEnumerable<Permission>> GetPermissionsByRoleIdAsync(int roleId);

        /// <summary>
        /// Met à jour les permissions d'un rôle
        /// </summary>
        /// <param name="roleId">Identifiant du rôle</param>
        /// <param name="permissionIds">Liste des identifiants des permissions à associer au rôle</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        Task<bool> UpdatePermissionsAsync(int roleId, List<int> permissionIds, int modifiePar);
    }
}
