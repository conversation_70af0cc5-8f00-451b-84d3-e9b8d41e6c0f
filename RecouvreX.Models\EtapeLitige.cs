using System.Collections.Generic;

namespace RecouvreX.Models
{
    /// <summary>
    /// Représente une étape dans le workflow de résolution d'un litige
    /// </summary>
    public class EtapeLitige : BaseEntity
    {
        /// <summary>
        /// Nom de l'étape
        /// </summary>
        public string Nom { get; set; }

        /// <summary>
        /// Description de l'étape
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Ordre de l'étape dans le workflow
        /// </summary>
        public int Ordre { get; set; }

        /// <summary>
        /// Délai recommandé pour cette étape (en jours)
        /// </summary>
        public int DelaiJours { get; set; }

        /// <summary>
        /// Indique si cette étape est une étape finale (résolution)
        /// </summary>
        public bool EstEtapeFinale { get; set; }

        /// <summary>
        /// Indique si cette étape nécessite une validation
        /// </summary>
        public bool NecessiteValidation { get; set; }

        /// <summary>
        /// Indique si l'escalade automatique est activée pour cette étape
        /// </summary>
        public bool EscaladeAutomatique { get; set; }

        /// <summary>
        /// Identifiant de l'étape vers laquelle escalader
        /// </summary>
        public int? EtapeEscaladeId { get; set; }

        /// <summary>
        /// Étape vers laquelle escalader (navigation property)
        /// </summary>
        public EtapeLitige EtapeEscalade { get; set; }

        /// <summary>
        /// Liste des étapes qui escaladent vers cette étape
        /// </summary>
        public List<EtapeLitige> EtapesSource { get; set; }

        /// <summary>
        /// Liste des litiges actuellement à cette étape
        /// </summary>
        public List<Litige> Litiges { get; set; }

        /// <summary>
        /// Liste des historiques d'étapes associés à cette étape
        /// </summary>
        public List<HistoriqueEtapeLitige> HistoriqueEtapes { get; set; }
    }
}
