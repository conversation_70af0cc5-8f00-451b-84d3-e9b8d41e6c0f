using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.IO;
using System.Data;

namespace RecouvreX.WinForms.Forms.Litiges
{
    partial class EtapeLitigeListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private Label titleLabel;
        private DataGridView etapesDataGridView;
        private Panel buttonsPanel;
        private Button newButton;
        private Button editButton;
        private Button deleteButton;
        private Button refreshButton;
        private Button closeButton;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            
            // Définir les propriétés du formulaire
            this.Text = "Gestion des étapes de litiges";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimizeBox = false;
            this.MaximizeBox = true;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            
            // Créer les contrôles
            this.mainPanel = new Panel();
            this.mainPanel.Dock = DockStyle.Fill;
            this.Controls.Add(this.mainPanel);

            // Titre
            this.titleLabel = new Label();
            this.titleLabel.Text = "Gestion des étapes de litiges";
            this.titleLabel.Font = new Font(this.Font.FontFamily, 16, FontStyle.Bold);
            this.titleLabel.AutoSize = true;
            this.titleLabel.Location = new Point(20, 20);
            this.mainPanel.Controls.Add(this.titleLabel);

            // Liste des étapes
            this.etapesDataGridView = new DataGridView();
            this.etapesDataGridView.Name = "etapesDataGridView";
            this.etapesDataGridView.Location = new Point(20, 60);
            this.etapesDataGridView.Size = new Size(850, 450);
            this.etapesDataGridView.AllowUserToAddRows = false;
            this.etapesDataGridView.AllowUserToDeleteRows = false;
            this.etapesDataGridView.AllowUserToResizeRows = false;
            this.etapesDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.etapesDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.etapesDataGridView.MultiSelect = false;
            this.etapesDataGridView.ReadOnly = true;
            this.etapesDataGridView.RowHeadersVisible = false;
            this.etapesDataGridView.CellDoubleClick += new DataGridViewCellEventHandler(this.EtapesDataGridView_CellDoubleClick);
            this.mainPanel.Controls.Add(this.etapesDataGridView);

            // Configurer les colonnes du DataGridView
            ConfigureDataGridView(this.etapesDataGridView);

            // Boutons d'action
            this.buttonsPanel = new Panel();
            this.buttonsPanel.Size = new Size(850, 50);
            this.buttonsPanel.Location = new Point(20, 520);
            this.mainPanel.Controls.Add(this.buttonsPanel);

            this.newButton = new Button();
            this.newButton.Text = "Nouvelle étape";
            this.newButton.Size = new Size(150, 30);
            this.newButton.Location = new Point(0, 10);
            this.newButton.Click += new EventHandler(this.NewButton_Click);
            this.buttonsPanel.Controls.Add(this.newButton);

            this.editButton = new Button();
            this.editButton.Text = "Modifier";
            this.editButton.Size = new Size(100, 30);
            this.editButton.Location = new Point(160, 10);
            this.editButton.Click += new EventHandler(this.EditButton_Click);
            this.buttonsPanel.Controls.Add(this.editButton);

            this.deleteButton = new Button();
            this.deleteButton.Text = "Supprimer";
            this.deleteButton.Size = new Size(100, 30);
            this.deleteButton.Location = new Point(270, 10);
            this.deleteButton.Click += new EventHandler(this.DeleteButton_Click);
            this.buttonsPanel.Controls.Add(this.deleteButton);

            this.refreshButton = new Button();
            this.refreshButton.Text = "Rafraîchir";
            this.refreshButton.Size = new Size(100, 30);
            this.refreshButton.Location = new Point(380, 10);
            this.refreshButton.Click += new EventHandler(this.RefreshButton_Click);
            this.buttonsPanel.Controls.Add(this.refreshButton);

            this.closeButton = new Button();
            this.closeButton.Text = "Fermer";
            this.closeButton.Size = new Size(100, 30);
            this.closeButton.Location = new Point(750, 10);
            this.closeButton.Click += new EventHandler(this.CloseButton_Click);
            this.buttonsPanel.Controls.Add(this.closeButton);

            // Charger le formulaire
            this.Load += new EventHandler(this.EtapeLitigeListForm_Load);
        }

        /// <summary>
        /// Configure les colonnes du DataGridView
        /// </summary>
        private void ConfigureDataGridView(DataGridView dataGridView)
        {
            // Ajouter les colonnes
            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "ID",
                DataPropertyName = "Id",
                Visible = false
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Ordre",
                HeaderText = "Ordre",
                DataPropertyName = "Ordre",
                Width = 60
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Nom",
                HeaderText = "Nom",
                DataPropertyName = "Nom",
                Width = 200
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "Description",
                DataPropertyName = "Description",
                Width = 250
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DelaiJours",
                HeaderText = "Délai (jours)",
                DataPropertyName = "DelaiJours",
                Width = 80
            });

            dataGridView.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "EstEtapeFinale",
                HeaderText = "Étape finale",
                DataPropertyName = "EstEtapeFinale",
                Width = 80
            });

            dataGridView.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "NecessiteValidation",
                HeaderText = "Nécessite validation",
                DataPropertyName = "NecessiteValidation",
                Width = 80
            });

            dataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "NombreLitiges",
                HeaderText = "Nombre de litiges",
                DataPropertyName = "NombreLitiges",
                Width = 100
            });
        }

        #endregion
    }
}
