using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les échéances de paiement
    /// </summary>
    public class EcheancePaiementRepository : BaseRepository<EcheancePaiement>, IEcheancePaiementRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public EcheancePaiementRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "EcheancesPaiement")
        {
        }

        /// <summary>
        /// Récupère toutes les échéances d'un plan de paiement
        /// </summary>
        /// <param name="planPaiementId">Identifiant du plan de paiement</param>
        /// <returns>Liste des échéances du plan de paiement</returns>
        public async Task<IEnumerable<EcheancePaiement>> GetByPlanPaiementIdAsync(int planPaiementId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = $"SELECT * FROM {_tableName} WHERE PlanPaiementId = @PlanPaiementId AND EstActif = 1 ORDER BY NumeroOrdre";
                    return await connection.QueryAsync<EcheancePaiement>(query, new { PlanPaiementId = planPaiementId });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des échéances du plan de paiement {PlanPaiementId}", planPaiementId);
                throw;
            }
        }

        /// <summary>
        /// Récupère les échéances à venir pour une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des échéances à venir dans la période spécifiée</returns>
        public async Task<IEnumerable<EcheancePaiement>> GetUpcomingAsync(DateTime dateDebut, DateTime dateFin)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        SELECT e.*, p.*, c.*
                        FROM EcheancesPaiement e
                        INNER JOIN PlansPaiement p ON e.PlanPaiementId = p.Id
                        INNER JOIN Clients c ON p.ClientId = c.Id
                        WHERE e.DateEcheance BETWEEN @DateDebut AND @DateFin
                        AND e.EstPayee = 0
                        AND e.EstActif = 1
                        AND p.EstActif = 1
                        ORDER BY e.DateEcheance";

                    var echeances = new Dictionary<int, EcheancePaiement>();

                    await connection.QueryAsync<EcheancePaiement, PlanPaiement, Client, EcheancePaiement>(
                        query,
                        (echeance, plan, client) =>
                        {
                            if (!echeances.TryGetValue(echeance.Id, out var echeanceEntry))
                            {
                                echeanceEntry = echeance;
                                echeances.Add(echeance.Id, echeanceEntry);
                            }

                            if (plan != null)
                            {
                                echeanceEntry.PlanPaiement = plan;
                                if (client != null)
                                {
                                    plan.Client = client;
                                }
                            }

                            return echeanceEntry;
                        },
                        new { DateDebut = dateDebut, DateFin = dateFin },
                        splitOn: "Id,Id");

                    return echeances.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des échéances à venir");
                throw;
            }
        }

        /// <summary>
        /// Récupère les échéances en retard
        /// </summary>
        /// <returns>Liste des échéances en retard</returns>
        public async Task<IEnumerable<EcheancePaiement>> GetLateAsync()
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        SELECT e.*, p.*, c.*
                        FROM EcheancesPaiement e
                        INNER JOIN PlansPaiement p ON e.PlanPaiementId = p.Id
                        INNER JOIN Clients c ON p.ClientId = c.Id
                        WHERE e.DateEcheance < GETDATE()
                        AND e.EstPayee = 0
                        AND e.EstActif = 1
                        AND p.EstActif = 1
                        ORDER BY e.DateEcheance";

                    var echeances = new Dictionary<int, EcheancePaiement>();

                    await connection.QueryAsync<EcheancePaiement, PlanPaiement, Client, EcheancePaiement>(
                        query,
                        (echeance, plan, client) =>
                        {
                            if (!echeances.TryGetValue(echeance.Id, out var echeanceEntry))
                            {
                                echeanceEntry = echeance;
                                echeances.Add(echeance.Id, echeanceEntry);
                            }

                            if (plan != null)
                            {
                                echeanceEntry.PlanPaiement = plan;
                                if (client != null)
                                {
                                    plan.Client = client;
                                }
                            }

                            return echeanceEntry;
                        },
                        splitOn: "Id,Id");

                    return echeances.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des échéances en retard");
                throw;
            }
        }

        /// <summary>
        /// Récupère les échéances en retard pour un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des échéances en retard pour le client</returns>
        public async Task<IEnumerable<EcheancePaiement>> GetLateByClientIdAsync(int clientId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        SELECT e.*, p.*
                        FROM EcheancesPaiement e
                        INNER JOIN PlansPaiement p ON e.PlanPaiementId = p.Id
                        WHERE p.ClientId = @ClientId
                        AND e.DateEcheance < GETDATE()
                        AND e.EstPayee = 0
                        AND e.EstActif = 1
                        AND p.EstActif = 1
                        ORDER BY e.DateEcheance";

                    var echeances = new Dictionary<int, EcheancePaiement>();

                    await connection.QueryAsync<EcheancePaiement, PlanPaiement, EcheancePaiement>(
                        query,
                        (echeance, plan) =>
                        {
                            if (!echeances.TryGetValue(echeance.Id, out var echeanceEntry))
                            {
                                echeanceEntry = echeance;
                                echeances.Add(echeance.Id, echeanceEntry);
                            }

                            if (plan != null)
                            {
                                echeanceEntry.PlanPaiement = plan;
                            }

                            return echeanceEntry;
                        },
                        new { ClientId = clientId },
                        splitOn: "Id");

                    return echeances.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des échéances en retard pour le client {ClientId}", clientId);
                throw;
            }
        }

        /// <summary>
        /// Récupère les échéances payées pour une période donnée
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des échéances payées dans la période spécifiée</returns>
        public async Task<IEnumerable<EcheancePaiement>> GetPaidAsync(DateTime dateDebut, DateTime dateFin)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        SELECT e.*, p.*, c.*
                        FROM EcheancesPaiement e
                        INNER JOIN PlansPaiement p ON e.PlanPaiementId = p.Id
                        INNER JOIN Clients c ON p.ClientId = c.Id
                        WHERE e.DatePaiement BETWEEN @DateDebut AND @DateFin
                        AND e.EstPayee = 1
                        AND e.EstActif = 1
                        AND p.EstActif = 1
                        ORDER BY e.DatePaiement";

                    var echeances = new Dictionary<int, EcheancePaiement>();

                    await connection.QueryAsync<EcheancePaiement, PlanPaiement, Client, EcheancePaiement>(
                        query,
                        (echeance, plan, client) =>
                        {
                            if (!echeances.TryGetValue(echeance.Id, out var echeanceEntry))
                            {
                                echeanceEntry = echeance;
                                echeances.Add(echeance.Id, echeanceEntry);
                            }

                            if (plan != null)
                            {
                                echeanceEntry.PlanPaiement = plan;
                                if (client != null)
                                {
                                    plan.Client = client;
                                }
                            }

                            return echeanceEntry;
                        },
                        new { DateDebut = dateDebut, DateFin = dateFin },
                        splitOn: "Id,Id");

                    return echeances.Values;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des échéances payées");
                throw;
            }
        }

        /// <summary>
        /// Marque une échéance comme payée
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="paiementId">Identifiant du paiement associé</param>
        /// <param name="montantPaye">Montant effectivement payé</param>
        /// <param name="datePaiement">Date du paiement</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> MarkAsPaidAsync(int echeanceId, int paiementId, decimal montantPaye, DateTime datePaiement, int userId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        UPDATE EcheancesPaiement
                        SET EstPayee = 1,
                            DatePaiement = @DatePaiement,
                            PaiementId = @PaiementId,
                            MontantPaye = @MontantPaye,
                            DateModification = GETDATE(),
                            ModifiePar = @UserId
                        WHERE Id = @EcheanceId";

                    var result = await connection.ExecuteAsync(query, new
                    {
                        EcheanceId = echeanceId,
                        PaiementId = paiementId,
                        MontantPaye = montantPaye,
                        DatePaiement = datePaiement,
                        UserId = userId
                    });

                    return result > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du marquage de l'échéance {EcheanceId} comme payée", echeanceId);
                throw;
            }
        }

        /// <summary>
        /// Marque une échéance comme non payée (annulation d'un paiement)
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> MarkAsUnpaidAsync(int echeanceId, int userId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        UPDATE EcheancesPaiement
                        SET EstPayee = 0,
                            DatePaiement = NULL,
                            PaiementId = NULL,
                            MontantPaye = NULL,
                            DateModification = GETDATE(),
                            ModifiePar = @UserId
                        WHERE Id = @EcheanceId";

                    var result = await connection.ExecuteAsync(query, new
                    {
                        EcheanceId = echeanceId,
                        UserId = userId
                    });

                    return result > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du marquage de l'échéance {EcheanceId} comme non payée", echeanceId);
                throw;
            }
        }

        /// <summary>
        /// Marque une échéance comme ayant reçu une notification
        /// </summary>
        /// <param name="echeanceId">Identifiant de l'échéance</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> MarkNotificationSentAsync(int echeanceId, int userId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        UPDATE EcheancesPaiement
                        SET NotificationEnvoyee = 1,
                            DateNotification = GETDATE(),
                            DateModification = GETDATE(),
                            ModifiePar = @UserId
                        WHERE Id = @EcheanceId";

                    var result = await connection.ExecuteAsync(query, new
                    {
                        EcheanceId = echeanceId,
                        UserId = userId
                    });

                    return result > 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du marquage de l'échéance {EcheanceId} comme notifiée", echeanceId);
                throw;
            }
        }

        /// <summary>
        /// Récupère les statistiques de respect des échéances pour un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Statistiques de respect des échéances</returns>
        public async Task<(int Total, int Payees, int EnRetard, decimal TauxRespect)> GetClientStatisticsAsync(int clientId)
        {
            try
            {
                using (var connection = await _dbConnection.CreateConnectionAsync())
                {
                    var query = @"
                        SELECT 
                            COUNT(*) AS Total,
                            SUM(CASE WHEN e.EstPayee = 1 THEN 1 ELSE 0 END) AS Payees,
                            SUM(CASE WHEN e.EstPayee = 0 AND e.DateEcheance < GETDATE() THEN 1 ELSE 0 END) AS EnRetard
                        FROM EcheancesPaiement e
                        INNER JOIN PlansPaiement p ON e.PlanPaiementId = p.Id
                        WHERE p.ClientId = @ClientId
                        AND e.EstActif = 1
                        AND p.EstActif = 1";

                    var result = await connection.QueryFirstOrDefaultAsync<(int Total, int Payees, int EnRetard)>(query, new { ClientId = clientId });

                    decimal tauxRespect = 0;
                    if (result.Total > 0)
                    {
                        tauxRespect = Math.Round(((decimal)result.Payees / result.Total) * 100, 2);
                    }

                    return (result.Total, result.Payees, result.EnRetard, tauxRespect);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la récupération des statistiques de respect des échéances pour le client {ClientId}", clientId);
                throw;
            }
        }
    }
}
