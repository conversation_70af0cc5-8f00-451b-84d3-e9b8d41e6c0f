using RecouvreX.Models.Enums;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Clients
{
    /// <summary>
    /// Formulaire d'édition du segment d'un client
    /// </summary>
    public partial class ClientSegmentEditForm : Form
    {
        private readonly int _clientId;
        private readonly string _raisonSociale;
        private readonly SegmentClient _currentSegment;

        /// <summary>
        /// Segment sélectionné
        /// </summary>
        public SegmentClient SelectedSegment { get; private set; }

        /// <summary>
        /// Commentaire sur la segmentation
        /// </summary>
        public string Commentaire { get; private set; }

        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="raisonSociale">Raison sociale du client</param>
        /// <param name="currentSegment">Segment actuel du client</param>
        public ClientSegmentEditForm(int clientId, string raisonSociale, SegmentClient currentSegment)
        {
            _clientId = clientId;
            _raisonSociale = raisonSociale;
            _currentSegment = currentSegment;
            SelectedSegment = currentSegment;

            InitializeComponent();
        }



        /// <summary>
        /// Chargement du formulaire
        /// </summary>
        private void ClientSegmentEditForm_Load(object sender, EventArgs e)
        {
            // Mettre à jour le titre avec la raison sociale du client
            titleLabel.Text = $"Modifier le segment du client : {_raisonSociale}";

            // Mettre à jour le texte du segment actuel
            currentSegmentValueLabel.Text = GetSegmentText(_currentSegment);

            // Sélectionner le segment actuel dans la liste déroulante
            switch (_currentSegment)
            {
                case SegmentClient.A:
                    segmentComboBox.SelectedIndex = 0;
                    break;
                case SegmentClient.B:
                    segmentComboBox.SelectedIndex = 1;
                    break;
                case SegmentClient.C:
                    segmentComboBox.SelectedIndex = 2;
                    break;
                case SegmentClient.NonSegmente:
                    segmentComboBox.SelectedIndex = 3;
                    break;
            }
        }

        /// <summary>
        /// Événement de clic sur le bouton Enregistrer
        /// </summary>
        private void SaveButton_Click(object sender, EventArgs e)
        {
            // Récupérer le segment sélectionné
            switch (segmentComboBox.SelectedIndex)
            {
                case 0:
                    SelectedSegment = SegmentClient.A;
                    break;
                case 1:
                    SelectedSegment = SegmentClient.B;
                    break;
                case 2:
                    SelectedSegment = SegmentClient.C;
                    break;
                case 3:
                    SelectedSegment = SegmentClient.NonSegmente;
                    break;
            }

            // Récupérer le commentaire
            Commentaire = commentaireTextBox.Text ?? "";
        }

        /// <summary>
        /// Obtient le texte correspondant à un segment
        /// </summary>
        private string GetSegmentText(SegmentClient segment)
        {
            switch (segment)
            {
                case SegmentClient.A:
                    return "A - Stratégique";
                case SegmentClient.B:
                    return "B - Important";
                case SegmentClient.C:
                    return "C - Standard";
                case SegmentClient.NonSegmente:
                    return "Non segmenté";
                default:
                    return segment.ToString();
            }
        }
    }
}
