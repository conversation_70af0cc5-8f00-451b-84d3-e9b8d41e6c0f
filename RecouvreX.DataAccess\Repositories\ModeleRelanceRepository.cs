using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour la gestion des modèles de relance
    /// </summary>
    public class ModeleRelanceRepository : BaseRepository<ModeleRelance>, IModeleRelanceRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public ModeleRelanceRepository(DatabaseConnection dbConnection) : base(dbConnection, "ModelesRelance")
        {
        }

        /// <summary>
        /// Récupère les modèles de relance par type
        /// </summary>
        /// <param name="type">Type de modèle de relance</param>
        /// <returns>Liste des modèles de relance du type spécifié</returns>
        public async Task<IEnumerable<ModeleRelance>> GetByTypeAsync(string type)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE Type = @Type AND EstActif = 1";
                return await connection.QueryAsync<ModeleRelance>(query, new { Type = type });
            }
        }

        /// <summary>
        /// Récupère les modèles de relance par niveau de fermeté
        /// </summary>
        /// <param name="niveauFermete">Niveau de fermeté</param>
        /// <returns>Liste des modèles de relance du niveau spécifié</returns>
        public async Task<IEnumerable<ModeleRelance>> GetByNiveauFermeteAsync(int niveauFermete)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE NiveauFermete = @NiveauFermete AND EstActif = 1";
                return await connection.QueryAsync<ModeleRelance>(query, new { NiveauFermete = niveauFermete });
            }
        }

        /// <summary>
        /// Récupère le modèle de relance par défaut pour un type et un niveau de fermeté
        /// </summary>
        /// <param name="type">Type de modèle de relance</param>
        /// <param name="niveauFermete">Niveau de fermeté</param>
        /// <returns>Modèle de relance par défaut ou null</returns>
        public async Task<ModeleRelance> GetDefaultAsync(string type, int niveauFermete)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = $"SELECT * FROM {_tableName} WHERE Type = @Type AND NiveauFermete = @NiveauFermete AND EstParDefaut = 1 AND EstActif = 1";
                return await connection.QueryFirstOrDefaultAsync<ModeleRelance>(query, new { Type = type, NiveauFermete = niveauFermete });
            }
        }

        /// <summary>
        /// Définit un modèle de relance comme modèle par défaut pour son type et niveau de fermeté
        /// </summary>
        /// <param name="id">Identifiant du modèle de relance</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> SetAsDefaultAsync(int id, int modifiePar)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                // Récupérer le modèle de relance
                var modele = await GetByIdAsync(id);
                if (modele == null)
                    return false;

                // Commencer une transaction
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Réinitialiser tous les modèles par défaut du même type et niveau
                        var resetQuery = $@"
                            UPDATE {_tableName}
                            SET EstParDefaut = 0,
                                DateModification = @DateModification,
                                ModifiePar = @ModifiePar
                            WHERE Type = @Type AND NiveauFermete = @NiveauFermete AND EstParDefaut = 1";

                        await connection.ExecuteAsync(resetQuery, new
                        {
                            Type = modele.Type,
                            NiveauFermete = modele.NiveauFermete,
                            DateModification = DateTime.Now,
                            ModifiePar = modifiePar
                        }, transaction);

                        // Définir le modèle sélectionné comme modèle par défaut
                        var setDefaultQuery = $@"
                            UPDATE {_tableName}
                            SET EstParDefaut = 1,
                                DateModification = @DateModification,
                                ModifiePar = @ModifiePar
                            WHERE Id = @Id";

                        await connection.ExecuteAsync(setDefaultQuery, new
                        {
                            Id = id,
                            DateModification = DateTime.Now,
                            ModifiePar = modifiePar
                        }, transaction);

                        // Valider la transaction
                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        // Annuler la transaction en cas d'erreur
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }
    }
}
