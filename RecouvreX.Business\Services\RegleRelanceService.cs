using RecouvreX.Business.Interfaces;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service de gestion des règles de relance
    /// </summary>
    public class RegleRelanceService : IRegleRelanceService
    {
        private readonly IRegleRelanceRepository _regleRelanceRepository;
        private readonly IPlanificationRelanceRepository _planificationRelanceRepository;
        private readonly IFactureRepository _factureRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IModeleRelanceRepository _modeleRelanceRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly IJournalAuditRepository _journalAuditRepository;

        /// <summary>
        /// Constructeur
        /// </summary>
        public RegleRelanceService(
            IRegleRelanceRepository regleRelanceRepository,
            IPlanificationRelanceRepository planificationRelanceRepository,
            IFactureRepository factureRepository,
            IClientRepository clientRepository,
            IModeleRelanceRepository modeleRelanceRepository,
            IUtilisateurRepository utilisateurRepository,
            IJournalAuditRepository journalAuditRepository)
        {
            _regleRelanceRepository = regleRelanceRepository ?? throw new ArgumentNullException(nameof(regleRelanceRepository));
            _planificationRelanceRepository = planificationRelanceRepository ?? throw new ArgumentNullException(nameof(planificationRelanceRepository));
            _factureRepository = factureRepository ?? throw new ArgumentNullException(nameof(factureRepository));
            _clientRepository = clientRepository ?? throw new ArgumentNullException(nameof(clientRepository));
            _modeleRelanceRepository = modeleRelanceRepository ?? throw new ArgumentNullException(nameof(modeleRelanceRepository));
            _utilisateurRepository = utilisateurRepository ?? throw new ArgumentNullException(nameof(utilisateurRepository));
            _journalAuditRepository = journalAuditRepository ?? throw new ArgumentNullException(nameof(journalAuditRepository));
        }

        /// <summary>
        /// Récupère toutes les règles de relance
        /// </summary>
        /// <returns>Liste des règles de relance</returns>
        public async Task<IEnumerable<RegleRelance>> GetAllAsync()
        {
            return await _regleRelanceRepository.GetAllAsync();
        }

        /// <summary>
        /// Récupère une règle de relance par son identifiant
        /// </summary>
        /// <param name="id">Identifiant de la règle de relance</param>
        /// <returns>Règle de relance trouvée ou null</returns>
        public async Task<RegleRelance> GetByIdAsync(int id)
        {
            if (id <= 0)
                return null;

            return await _regleRelanceRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Récupère les règles de relance actives
        /// </summary>
        /// <returns>Liste des règles de relance actives</returns>
        public async Task<IEnumerable<RegleRelance>> GetActiveAsync()
        {
            return await _regleRelanceRepository.GetActiveAsync();
        }

        /// <summary>
        /// Récupère les règles de relance par type de client
        /// </summary>
        /// <param name="typeClient">Type de client</param>
        /// <returns>Liste des règles de relance pour le type de client spécifié</returns>
        public async Task<IEnumerable<RegleRelance>> GetByTypeClientAsync(string typeClient)
        {
            if (string.IsNullOrEmpty(typeClient))
                return await _regleRelanceRepository.GetActiveAsync();

            return await _regleRelanceRepository.GetByTypeClientAsync(typeClient);
        }

        /// <summary>
        /// Crée une nouvelle règle de relance
        /// </summary>
        /// <param name="regleRelance">Règle de relance à créer</param>
        /// <param name="creePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Règle de relance créée avec son identifiant généré</returns>
        public async Task<RegleRelance> CreateAsync(RegleRelance regleRelance, int creePar)
        {
            if (regleRelance == null)
                throw new ArgumentNullException(nameof(regleRelance));

            if (creePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que l'utilisateur existe
            var utilisateur = await _utilisateurRepository.GetByIdAsync(regleRelance.UtilisateurId);
            if (utilisateur == null)
                throw new InvalidOperationException($"L'utilisateur avec l'ID {regleRelance.UtilisateurId} n'existe pas");

            // Vérifier que les modèles de relance existent
            if (regleRelance.ModeleRelanceNiveau1Id.HasValue)
            {
                var modele1 = await _modeleRelanceRepository.GetByIdAsync(regleRelance.ModeleRelanceNiveau1Id.Value);
                if (modele1 == null)
                    throw new InvalidOperationException($"Le modèle de relance niveau 1 avec l'ID {regleRelance.ModeleRelanceNiveau1Id.Value} n'existe pas");
            }

            if (regleRelance.ModeleRelanceNiveau2Id.HasValue)
            {
                var modele2 = await _modeleRelanceRepository.GetByIdAsync(regleRelance.ModeleRelanceNiveau2Id.Value);
                if (modele2 == null)
                    throw new InvalidOperationException($"Le modèle de relance niveau 2 avec l'ID {regleRelance.ModeleRelanceNiveau2Id.Value} n'existe pas");
            }

            if (regleRelance.ModeleRelanceNiveau3Id.HasValue)
            {
                var modele3 = await _modeleRelanceRepository.GetByIdAsync(regleRelance.ModeleRelanceNiveau3Id.Value);
                if (modele3 == null)
                    throw new InvalidOperationException($"Le modèle de relance niveau 3 avec l'ID {regleRelance.ModeleRelanceNiveau3Id.Value} n'existe pas");
            }

            // Créer la règle de relance
            var createdRegle = await _regleRelanceRepository.AddAsync(regleRelance, creePar);

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "RegleRelance",
                EntiteId = createdRegle.Id,
                TypeAction = "Création",
                UtilisateurId = creePar,
                DateAction = DateTime.Now,
                Description = $"Création de la règle de relance '{createdRegle.Nom:s}'",
                DonneesAvant = null,
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    createdRegle.Id,
                    createdRegle.Nom,
                    createdRegle.Description,
                    createdRegle.JoursApresPremiere,
                    createdRegle.JoursEntrePremiereDeuxieme,
                    createdRegle.JoursEntreDeuxiemeTroisieme,
                    createdRegle.ModeleRelanceNiveau1Id,
                    createdRegle.ModeleRelanceNiveau2Id,
                    createdRegle.ModeleRelanceNiveau3Id,
                    createdRegle.TypeClient,
                    createdRegle.MontantMinimum,
                    createdRegle.EstActive,
                    createdRegle.RequiertValidation,
                    createdRegle.Priorite,
                    createdRegle.UtilisateurId
                })
            });

            return createdRegle;
        }

        /// <summary>
        /// Met à jour une règle de relance existante
        /// </summary>
        /// <param name="regleRelance">Règle de relance à mettre à jour</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Règle de relance mise à jour</returns>
        public async Task<RegleRelance> UpdateAsync(RegleRelance regleRelance, int modifiePar)
        {
            if (regleRelance == null)
                throw new ArgumentNullException(nameof(regleRelance));

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que la règle de relance existe
            var existingRegle = await _regleRelanceRepository.GetByIdAsync(regleRelance.Id);
            if (existingRegle == null)
                throw new InvalidOperationException($"La règle de relance avec l'ID {regleRelance.Id} n'existe pas");

            // Vérifier que l'utilisateur existe
            var utilisateur = await _utilisateurRepository.GetByIdAsync(regleRelance.UtilisateurId);
            if (utilisateur == null)
                throw new InvalidOperationException($"L'utilisateur avec l'ID {regleRelance.UtilisateurId} n'existe pas");

            // Vérifier que les modèles de relance existent
            if (regleRelance.ModeleRelanceNiveau1Id.HasValue)
            {
                var modele1 = await _modeleRelanceRepository.GetByIdAsync(regleRelance.ModeleRelanceNiveau1Id.Value);
                if (modele1 == null)
                    throw new InvalidOperationException($"Le modèle de relance niveau 1 avec l'ID {regleRelance.ModeleRelanceNiveau1Id.Value} n'existe pas");
            }

            if (regleRelance.ModeleRelanceNiveau2Id.HasValue)
            {
                var modele2 = await _modeleRelanceRepository.GetByIdAsync(regleRelance.ModeleRelanceNiveau2Id.Value);
                if (modele2 == null)
                    throw new InvalidOperationException($"Le modèle de relance niveau 2 avec l'ID {regleRelance.ModeleRelanceNiveau2Id.Value} n'existe pas");
            }

            if (regleRelance.ModeleRelanceNiveau3Id.HasValue)
            {
                var modele3 = await _modeleRelanceRepository.GetByIdAsync(regleRelance.ModeleRelanceNiveau3Id.Value);
                if (modele3 == null)
                    throw new InvalidOperationException($"Le modèle de relance niveau 3 avec l'ID {regleRelance.ModeleRelanceNiveau3Id.Value} n'existe pas");
            }

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "RegleRelance",
                EntiteId = regleRelance.Id,
                TypeAction = "Modification",
                UtilisateurId = modifiePar,
                DateAction = DateTime.Now,
                Description = $"Modification de la règle de relance '{regleRelance.Nom:s}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    existingRegle.Id,
                    existingRegle.Nom,
                    existingRegle.Description,
                    existingRegle.JoursApresPremiere,
                    existingRegle.JoursEntrePremiereDeuxieme,
                    existingRegle.JoursEntreDeuxiemeTroisieme,
                    existingRegle.ModeleRelanceNiveau1Id,
                    existingRegle.ModeleRelanceNiveau2Id,
                    existingRegle.ModeleRelanceNiveau3Id,
                    existingRegle.TypeClient,
                    existingRegle.MontantMinimum,
                    existingRegle.EstActive,
                    existingRegle.RequiertValidation,
                    existingRegle.Priorite,
                    existingRegle.UtilisateurId
                }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    regleRelance.Id,
                    regleRelance.Nom,
                    regleRelance.Description,
                    regleRelance.JoursApresPremiere,
                    regleRelance.JoursEntrePremiereDeuxieme,
                    regleRelance.JoursEntreDeuxiemeTroisieme,
                    regleRelance.ModeleRelanceNiveau1Id,
                    regleRelance.ModeleRelanceNiveau2Id,
                    regleRelance.ModeleRelanceNiveau3Id,
                    regleRelance.TypeClient,
                    regleRelance.MontantMinimum,
                    regleRelance.EstActive,
                    regleRelance.RequiertValidation,
                    regleRelance.Priorite,
                    regleRelance.UtilisateurId
                })
            });

            // Mettre à jour la règle de relance
            return await _regleRelanceRepository.UpdateAsync(regleRelance, modifiePar);
        }

        /// <summary>
        /// Supprime une règle de relance
        /// </summary>
        /// <param name="id">Identifiant de la règle de relance à supprimer</param>
        /// <param name="supprimePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la suppression a réussi, sinon False</returns>
        public async Task<bool> DeleteAsync(int id, int supprimePar)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant de la règle de relance ne peut pas être négatif ou nul");

            if (supprimePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que la règle de relance existe
            var regle = await _regleRelanceRepository.GetByIdAsync(id);
            if (regle == null)
                return false;

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "RegleRelance",
                EntiteId = id,
                TypeAction = "Suppression",
                UtilisateurId = supprimePar,
                DateAction = DateTime.Now,
                Description = $"Suppression de la règle de relance '{regle.Nom:s}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    regle.Id,
                    regle.Nom,
                    regle.Description,
                    regle.JoursApresPremiere,
                    regle.JoursEntrePremiereDeuxieme,
                    regle.JoursEntreDeuxiemeTroisieme,
                    regle.ModeleRelanceNiveau1Id,
                    regle.ModeleRelanceNiveau2Id,
                    regle.ModeleRelanceNiveau3Id,
                    regle.TypeClient,
                    regle.MontantMinimum,
                    regle.EstActive,
                    regle.RequiertValidation,
                    regle.Priorite,
                    regle.UtilisateurId
                }),
                DonneesApres = null
            });

            // Supprimer la règle de relance
            return await _regleRelanceRepository.DeleteAsync(id, supprimePar);
        }

        /// <summary>
        /// Active ou désactive une règle de relance
        /// </summary>
        /// <param name="id">Identifiant de la règle de relance</param>
        /// <param name="estActive">Nouvel état d'activation</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> SetActiveStatusAsync(int id, bool estActive, int modifiePar)
        {
            if (id <= 0)
                throw new ArgumentException("L'identifiant de la règle de relance ne peut pas être négatif ou nul");

            if (modifiePar <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Vérifier que la règle de relance existe
            var regle = await _regleRelanceRepository.GetByIdAsync(id);
            if (regle == null)
                return false;

            // Mettre à jour l'état d'activation
            regle.EstActive = estActive;

            // Journaliser l'action
            await _journalAuditRepository.AddAsync(new JournalAudit
            {
                TypeEntite = "RegleRelance",
                EntiteId = id,
                TypeAction = estActive ? "Activation" : "Désactivation",
                UtilisateurId = modifiePar,
                DateAction = DateTime.Now,
                Description = $"{(estActive ? "Activation" : "Désactivation")} de la règle de relance '{regle.Nom:s}'",
                DonneesAvant = Newtonsoft.Json.JsonConvert.SerializeObject(new { EstActive = !estActive }),
                DonneesApres = Newtonsoft.Json.JsonConvert.SerializeObject(new { EstActive = estActive })
            });

            // Mettre à jour la règle de relance
            await _regleRelanceRepository.UpdateAsync(regle, modifiePar);
            return true;
        }

        /// <summary>
        /// Trouve la règle de relance applicable à une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Règle de relance applicable ou null</returns>
        public async Task<RegleRelance> FindApplicableRuleAsync(int factureId)
        {
            if (factureId <= 0)
                throw new ArgumentException("L'identifiant de la facture ne peut pas être négatif ou nul");

            // Récupérer la facture
            var facture = await _factureRepository.GetByIdAsync(factureId);
            if (facture == null)
                throw new InvalidOperationException($"La facture avec l'ID {factureId} n'existe pas");

            // Vérifier que la facture est en retard
            if (facture.Statut != StatutFacture.EnRetard)
                return null;

            // Récupérer le client
            var client = await _clientRepository.GetByIdAsync(facture.ClientId);
            if (client == null)
                throw new InvalidOperationException($"Le client avec l'ID {facture.ClientId} n'existe pas");

            // Récupérer les règles de relance actives
            var regles = await _regleRelanceRepository.GetActiveAsync();

            // Filtrer les règles par type de client
            var reglesApplicables = regles.Where(r =>
                (string.IsNullOrEmpty(r.TypeClient) || r.TypeClient == client.TypeClient) &&
                (!r.MontantMinimum.HasValue || facture.MontantRestant >= r.MontantMinimum.Value))
                .OrderByDescending(r => r.Priorite);

            // Retourner la première règle applicable
            return reglesApplicables.FirstOrDefault();
        }

        /// <summary>
        /// Planifie les relances pour une facture selon la règle applicable
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Liste des planifications de relance créées</returns>
        public async Task<IEnumerable<PlanificationRelance>> PlanifierRelancesAsync(int factureId, int utilisateurId)
        {
            if (factureId <= 0)
                throw new ArgumentException("L'identifiant de la facture ne peut pas être négatif ou nul");

            if (utilisateurId <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Récupérer la facture
            var facture = await _factureRepository.GetByIdAsync(factureId);
            if (facture == null)
                throw new InvalidOperationException($"La facture avec l'ID {factureId} n'existe pas");

            // Vérifier que la facture est en retard
            if (facture.Statut != StatutFacture.EnRetard)
                return Enumerable.Empty<PlanificationRelance>();

            // Trouver la règle applicable
            var regle = await FindApplicableRuleAsync(factureId);
            if (regle == null)
                return Enumerable.Empty<PlanificationRelance>();

            // Vérifier s'il existe déjà des planifications pour cette facture
            var existingPlanifications = await _planificationRelanceRepository.GetByFactureIdAsync(factureId);
            if (existingPlanifications.Any())
                return existingPlanifications;

            // Créer les planifications de relance
            var planifications = new List<PlanificationRelance>();

            // Première relance
            if (regle.ModeleRelanceNiveau1Id.HasValue)
            {
                var datePremiere = facture.DateEcheance.AddDays(regle.JoursApresPremiere);
                var planification1 = new PlanificationRelance
                {
                    FactureId = factureId,
                    RegleRelanceId = regle.Id,
                    NiveauRelance = 1,
                    DatePrevue = datePremiere,
                    Statut = regle.RequiertValidation ? StatutPlanificationRelance.EnAttenteValidation : StatutPlanificationRelance.Planifiee,
                    ModeleRelanceId = regle.ModeleRelanceNiveau1Id.Value,
                    Canal = TypeModeleRelance.Email // Par défaut, à adapter selon le modèle
                };

                planification1 = await _planificationRelanceRepository.AddAsync(planification1, utilisateurId);
                planifications.Add(planification1);

                // Deuxième relance
                if (regle.ModeleRelanceNiveau2Id.HasValue)
                {
                    var dateDeuxieme = datePremiere.AddDays(regle.JoursEntrePremiereDeuxieme);
                    var planification2 = new PlanificationRelance
                    {
                        FactureId = factureId,
                        RegleRelanceId = regle.Id,
                        NiveauRelance = 2,
                        DatePrevue = dateDeuxieme,
                        Statut = regle.RequiertValidation ? StatutPlanificationRelance.EnAttenteValidation : StatutPlanificationRelance.Planifiee,
                        ModeleRelanceId = regle.ModeleRelanceNiveau2Id.Value,
                        Canal = TypeModeleRelance.Email // Par défaut, à adapter selon le modèle
                    };

                    planification2 = await _planificationRelanceRepository.AddAsync(planification2, utilisateurId);
                    planifications.Add(planification2);

                    // Troisième relance
                    if (regle.ModeleRelanceNiveau3Id.HasValue)
                    {
                        var dateTroisieme = dateDeuxieme.AddDays(regle.JoursEntreDeuxiemeTroisieme);
                        var planification3 = new PlanificationRelance
                        {
                            FactureId = factureId,
                            RegleRelanceId = regle.Id,
                            NiveauRelance = 3,
                            DatePrevue = dateTroisieme,
                            Statut = regle.RequiertValidation ? StatutPlanificationRelance.EnAttenteValidation : StatutPlanificationRelance.Planifiee,
                            ModeleRelanceId = regle.ModeleRelanceNiveau3Id.Value,
                            Canal = TypeModeleRelance.Lettre // Par défaut, à adapter selon le modèle
                        };

                        planification3 = await _planificationRelanceRepository.AddAsync(planification3, utilisateurId);
                        planifications.Add(planification3);
                    }
                }
            }

            return planifications;
        }

        /// <summary>
        /// Planifie les relances pour toutes les factures en retard
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Nombre de planifications de relance créées</returns>
        public async Task<int> PlanifierRelancesAutomatiquesAsync(int utilisateurId)
        {
            if (utilisateurId <= 0)
                throw new ArgumentException("L'identifiant de l'utilisateur ne peut pas être négatif ou nul");

            // Récupérer toutes les factures en retard
            var facturesEnRetard = await _factureRepository.GetOverdueInvoicesAsync();

            int count = 0;
            foreach (var facture in facturesEnRetard)
            {
                var planifications = await PlanifierRelancesAsync(facture.Id, utilisateurId);
                count += planifications.Count();
            }

            return count;
        }
    }
}
