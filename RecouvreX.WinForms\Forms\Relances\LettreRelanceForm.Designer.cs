using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.IO;

namespace RecouvreX.WinForms.Forms.Relances
{
    partial class LettreRelanceForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private Label titleLabel;
        private TableLayoutPanel tableLayoutPanel;
        private Label factureLabel;
        private Label factureValueLabel;
        private Label clientLabel;
        private Label clientValueLabel;
        private Label niveauLabel;
        private ComboBox niveauComboBox;
        private Label contenuLabel;
        private RichTextBox contenuRichTextBox;
        private Label destinataireLabel;
        private ComboBox destinataireComboBox;
        private Panel optionsPanel;
        private CheckBox emailCheckBox;
        private CheckBox saveCheckBox;
        private Panel buttonsPanel;
        private Button previewButton;
        private Button generateButton;
        private Button cancelButton;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            mainPanel = new Panel();
            titleLabel = new Label();
            tableLayoutPanel = new TableLayoutPanel();
            factureLabel = new Label();
            factureValueLabel = new Label();
            clientLabel = new Label();
            clientValueLabel = new Label();
            niveauLabel = new Label();
            niveauComboBox = new ComboBox();
            contenuLabel = new Label();
            contenuRichTextBox = new RichTextBox();
            destinataireLabel = new Label();
            destinataireComboBox = new ComboBox();
            optionsPanel = new Panel();
            emailCheckBox = new CheckBox();
            saveCheckBox = new CheckBox();
            buttonsPanel = new Panel();
            previewButton = new Button();
            generateButton = new Button();
            cancelButton = new Button();
            mainPanel.SuspendLayout();
            tableLayoutPanel.SuspendLayout();
            optionsPanel.SuspendLayout();
            buttonsPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.Controls.Add(titleLabel);
            mainPanel.Controls.Add(tableLayoutPanel);
            mainPanel.Controls.Add(buttonsPanel);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Location = new Point(0, 0);
            mainPanel.Name = "mainPanel";
            mainPanel.Size = new Size(800, 600);
            mainPanel.TabIndex = 0;
            // 
            // titleLabel
            // 
            titleLabel.AutoSize = true;
            titleLabel.Font = new Font("Segoe UI", 14F, FontStyle.Bold, GraphicsUnit.Point);
            titleLabel.Location = new Point(20, 20);
            titleLabel.Name = "titleLabel";
            titleLabel.Size = new Size(287, 25);
            titleLabel.TabIndex = 0;
            titleLabel.Text = "Génération de lettre de relance";
            // 
            // tableLayoutPanel
            // 
            tableLayoutPanel.ColumnCount = 2;
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            tableLayoutPanel.Controls.Add(factureLabel, 0, 0);
            tableLayoutPanel.Controls.Add(factureValueLabel, 1, 0);
            tableLayoutPanel.Controls.Add(clientLabel, 0, 1);
            tableLayoutPanel.Controls.Add(clientValueLabel, 1, 1);
            tableLayoutPanel.Controls.Add(niveauLabel, 0, 2);
            tableLayoutPanel.Controls.Add(niveauComboBox, 1, 2);
            tableLayoutPanel.Controls.Add(contenuLabel, 0, 3);
            tableLayoutPanel.Controls.Add(contenuRichTextBox, 1, 3);
            tableLayoutPanel.Controls.Add(destinataireLabel, 0, 4);
            tableLayoutPanel.Controls.Add(destinataireComboBox, 1, 4);
            tableLayoutPanel.Controls.Add(optionsPanel, 1, 5);
            tableLayoutPanel.Location = new Point(20, 60);
            tableLayoutPanel.Name = "tableLayoutPanel";
            tableLayoutPanel.RowCount = 6;
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            tableLayoutPanel.Size = new Size(760, 400);
            tableLayoutPanel.TabIndex = 1;
            // 
            // factureLabel
            // 
            factureLabel.Dock = DockStyle.Fill;
            factureLabel.Location = new Point(3, 0);
            factureLabel.Name = "factureLabel";
            factureLabel.Size = new Size(222, 30);
            factureLabel.TabIndex = 0;
            factureLabel.Text = "Facture :";
            factureLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // factureValueLabel
            // 
            factureValueLabel.Dock = DockStyle.Fill;
            factureValueLabel.Location = new Point(231, 0);
            factureValueLabel.Name = "factureValueLabel";
            factureValueLabel.Size = new Size(526, 30);
            factureValueLabel.TabIndex = 1;
            factureValueLabel.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // clientLabel
            // 
            clientLabel.Dock = DockStyle.Fill;
            clientLabel.Location = new Point(3, 30);
            clientLabel.Name = "clientLabel";
            clientLabel.Size = new Size(222, 30);
            clientLabel.TabIndex = 2;
            clientLabel.Text = "Client :";
            clientLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // clientValueLabel
            // 
            clientValueLabel.Dock = DockStyle.Fill;
            clientValueLabel.Location = new Point(231, 30);
            clientValueLabel.Name = "clientValueLabel";
            clientValueLabel.Size = new Size(526, 30);
            clientValueLabel.TabIndex = 3;
            clientValueLabel.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // niveauLabel
            // 
            niveauLabel.Dock = DockStyle.Fill;
            niveauLabel.Location = new Point(3, 60);
            niveauLabel.Name = "niveauLabel";
            niveauLabel.Size = new Size(222, 30);
            niveauLabel.TabIndex = 4;
            niveauLabel.Text = "Niveau de relance :";
            niveauLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // niveauComboBox
            // 
            niveauComboBox.Dock = DockStyle.Fill;
            niveauComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            niveauComboBox.Items.AddRange(new object[] { "Niveau 1 - Rappel", "Niveau 2 - Deuxième rappel", "Niveau 3 - Mise en demeure" });
            niveauComboBox.Location = new Point(231, 63);
            niveauComboBox.Name = "niveauComboBox";
            niveauComboBox.Size = new Size(526, 23);
            niveauComboBox.TabIndex = 5;
            // 
            // contenuLabel
            // 
            contenuLabel.Dock = DockStyle.Fill;
            contenuLabel.Location = new Point(3, 90);
            contenuLabel.Name = "contenuLabel";
            contenuLabel.Size = new Size(222, 250);
            contenuLabel.TabIndex = 6;
            contenuLabel.Text = "Aperçu du contenu :";
            contenuLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // contenuRichTextBox
            // 
            contenuRichTextBox.Dock = DockStyle.Fill;
            contenuRichTextBox.Location = new Point(231, 93);
            contenuRichTextBox.Name = "contenuRichTextBox";
            contenuRichTextBox.ReadOnly = true;
            contenuRichTextBox.Size = new Size(526, 244);
            contenuRichTextBox.TabIndex = 7;
            contenuRichTextBox.Text = "";
            // 
            // destinataireLabel
            // 
            destinataireLabel.Dock = DockStyle.Fill;
            destinataireLabel.Location = new Point(3, 340);
            destinataireLabel.Name = "destinataireLabel";
            destinataireLabel.Size = new Size(222, 30);
            destinataireLabel.TabIndex = 8;
            destinataireLabel.Text = "Destinataire :";
            destinataireLabel.TextAlign = ContentAlignment.MiddleRight;
            // 
            // destinataireComboBox
            // 
            destinataireComboBox.Dock = DockStyle.Fill;
            destinataireComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            destinataireComboBox.Location = new Point(231, 343);
            destinataireComboBox.Name = "destinataireComboBox";
            destinataireComboBox.Size = new Size(526, 23);
            destinataireComboBox.TabIndex = 9;
            // 
            // optionsPanel
            // 
            optionsPanel.Controls.Add(emailCheckBox);
            optionsPanel.Controls.Add(saveCheckBox);
            optionsPanel.Dock = DockStyle.Fill;
            optionsPanel.Location = new Point(231, 373);
            optionsPanel.Name = "optionsPanel";
            optionsPanel.Size = new Size(526, 24);
            optionsPanel.TabIndex = 10;
            // 
            // emailCheckBox
            // 
            emailCheckBox.AutoSize = true;
            emailCheckBox.Location = new Point(0, 10);
            emailCheckBox.Name = "emailCheckBox";
            emailCheckBox.Size = new Size(120, 19);
            emailCheckBox.TabIndex = 0;
            emailCheckBox.Text = "Envoyer par email";
            // 
            // saveCheckBox
            // 
            saveCheckBox.AutoSize = true;
            saveCheckBox.Checked = true;
            saveCheckBox.CheckState = CheckState.Checked;
            saveCheckBox.Location = new Point(150, 10);
            saveCheckBox.Name = "saveCheckBox";
            saveCheckBox.Size = new Size(151, 19);
            saveCheckBox.TabIndex = 1;
            saveCheckBox.Text = "Enregistrer sur le disque";
            // 
            // buttonsPanel
            // 
            buttonsPanel.Controls.Add(previewButton);
            buttonsPanel.Controls.Add(generateButton);
            buttonsPanel.Controls.Add(cancelButton);
            buttonsPanel.Location = new Point(20, 480);
            buttonsPanel.Name = "buttonsPanel";
            buttonsPanel.Size = new Size(760, 50);
            buttonsPanel.TabIndex = 2;
            // 
            // previewButton
            // 
            previewButton.Location = new Point(450, 10);
            previewButton.Name = "previewButton";
            previewButton.Size = new Size(100, 30);
            previewButton.TabIndex = 0;
            previewButton.Text = "Aperçu";
            previewButton.Click += PreviewButton_Click;
            // 
            // generateButton
            // 
            generateButton.Location = new Point(560, 10);
            generateButton.Name = "generateButton";
            generateButton.Size = new Size(150, 30);
            generateButton.TabIndex = 1;
            generateButton.Text = "Générer et envoyer";
            generateButton.Click += GenerateButton_Click;
            // 
            // cancelButton
            // 
            cancelButton.Location = new Point(340, 10);
            cancelButton.Name = "cancelButton";
            cancelButton.Size = new Size(100, 30);
            cancelButton.TabIndex = 2;
            cancelButton.Text = "Annuler";
            cancelButton.Click += CancelButton_Click;
            // 
            // LettreRelanceForm
            // 
            ClientSize = new Size(800, 600);
            Controls.Add(mainPanel);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "LettreRelanceForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "Génération de lettre de relance";
            Load += LettreRelanceForm_Load;
            mainPanel.ResumeLayout(false);
            mainPanel.PerformLayout();
            tableLayoutPanel.ResumeLayout(false);
            optionsPanel.ResumeLayout(false);
            optionsPanel.PerformLayout();
            buttonsPanel.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
    }
}
