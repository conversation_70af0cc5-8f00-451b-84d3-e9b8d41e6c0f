using RecouvreX.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des variables dynamiques
    /// </summary>
    public interface IVariableDynamiqueRepository : IRepository<VariableDynamique>
    {
        /// <summary>
        /// Récupère les variables dynamiques par catégorie
        /// </summary>
        /// <param name="categorie">Catégorie de variable</param>
        /// <returns>Liste des variables dynamiques de la catégorie spécifiée</returns>
        Task<IEnumerable<VariableDynamique>> GetByCategorieAsync(string categorie);
    }
}
