using RecouvreX.Business.Interfaces;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Rapports
{
    public partial class SendReportByEmailForm : Form
    {
        private readonly IRapportPersonnaliseService _rapportPersonnaliseService;
        private readonly int _rapportId;
        private readonly int _currentUserId;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();

        public SendReportByEmailForm(IRapportPersonnaliseService rapportPersonnaliseService, int rapportId, int currentUserId)
        {
            _rapportPersonnaliseService = rapportPersonnaliseService ?? throw new ArgumentNullException(nameof(rapportPersonnaliseService));
            _rapportId = rapportId;
            _currentUserId = currentUserId;

            InitializeComponent();

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form
            this.Text = "Envoyer le rapport par email";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimizeBox = false;
            this.MaximizeBox = false;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;

            // Main panel
            TableLayoutPanel mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 1,
                RowCount = 2,
                RowStyles = {
                    new RowStyle(SizeType.Percent, 100),
                    new RowStyle(SizeType.Absolute, 40)
                }
            };
            this.Controls.Add(mainPanel);

            // Email panel
            TableLayoutPanel emailPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 5,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Absolute, 120),
                    new ColumnStyle(SizeType.Percent, 100)
                }
            };
            mainPanel.Controls.Add(emailPanel, 0, 0);

            // Destinataires
            emailPanel.Controls.Add(new Label { Text = "Destinataires :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 0);
            TextBox destinatairesTextBox = new TextBox
            {
                Name = "destinatairesTextBox",
                Dock = DockStyle.Fill,
                PlaceholderText = "Séparer les adresses par des points-virgules"
            };
            destinatairesTextBox.Validating += DestinatairesTextBox_Validating;
            emailPanel.Controls.Add(destinatairesTextBox, 1, 0);

            // Objet
            emailPanel.Controls.Add(new Label { Text = "Objet :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 1);
            TextBox objetTextBox = new TextBox
            {
                Name = "objetTextBox",
                Dock = DockStyle.Fill
            };
            objetTextBox.Validating += ObjetTextBox_Validating;
            emailPanel.Controls.Add(objetTextBox, 1, 1);

            // Format
            emailPanel.Controls.Add(new Label { Text = "Format :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 2);
            ComboBox formatComboBox = new ComboBox
            {
                Name = "formatComboBox",
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            formatComboBox.Items.Add("PDF");
            formatComboBox.Items.Add("Excel");
            formatComboBox.Items.Add("CSV");
            formatComboBox.SelectedIndex = 0;
            formatComboBox.Validating += FormatComboBox_Validating;
            emailPanel.Controls.Add(formatComboBox, 1, 2);

            // Message
            emailPanel.Controls.Add(new Label { Text = "Message :", Font = new Font("Segoe UI", 9, FontStyle.Bold), TextAlign = ContentAlignment.MiddleLeft }, 0, 3);
            TextBox messageTextBox = new TextBox
            {
                Name = "messageTextBox",
                Dock = DockStyle.Fill,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Height = 150
            };
            emailPanel.Controls.Add(messageTextBox, 1, 3);
            emailPanel.SetRowSpan(messageTextBox, 2);

            // Buttons panel
            TableLayoutPanel buttonsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                ColumnStyles = {
                    new ColumnStyle(SizeType.Percent, 100),
                    new ColumnStyle(SizeType.Absolute, 100),
                    new ColumnStyle(SizeType.Absolute, 100)
                }
            };
            mainPanel.Controls.Add(buttonsPanel, 0, 1);

            Button sendButton = new Button
            {
                Name = "sendButton",
                Text = "Envoyer",
                Dock = DockStyle.Fill,
                Margin = new Padding(3)
            };
            sendButton.Click += SendButton_Click;
            buttonsPanel.Controls.Add(sendButton, 1, 0);

            Button cancelButton = new Button
            {
                Name = "cancelButton",
                Text = "Annuler",
                Dock = DockStyle.Fill,
                Margin = new Padding(3),
                DialogResult = DialogResult.Cancel
            };
            cancelButton.Click += CancelButton_Click;
            buttonsPanel.Controls.Add(cancelButton, 2, 0);
            this.CancelButton = cancelButton;

            this.ResumeLayout(false);
        }

        private void DestinatairesTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "Veuillez saisir au moins un destinataire.");
                    e.Cancel = true;
                }
                else
                {
                    // Vérifier que les adresses email sont valides
                    var emails = textBox.Text.Split(';', StringSplitOptions.RemoveEmptyEntries);
                    bool allValid = true;
                    foreach (var email in emails)
                    {
                        if (!IsValidEmail(email.Trim()))
                        {
                            allValid = false;
                            break;
                        }
                    }

                    if (!allValid)
                    {
                        _errorProvider.SetError(textBox, "Une ou plusieurs adresses email ne sont pas valides.");
                        e.Cancel = true;
                    }
                    else
                    {
                        _errorProvider.SetError(textBox, "");
                    }
                }
            }
        }

        private void ObjetTextBox_Validating(object sender, CancelEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox != null)
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    _errorProvider.SetError(textBox, "Veuillez saisir un objet.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(textBox, "");
                }
            }
        }

        private void FormatComboBox_Validating(object sender, CancelEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                if (comboBox.SelectedIndex == -1)
                {
                    _errorProvider.SetError(comboBox, "Veuillez sélectionner un format.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(comboBox, "");
                }
            }
        }

        private async void SendButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant d'envoyer.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer les valeurs du formulaire
                var destinatairesTextBox = Controls.Find("destinatairesTextBox", true).FirstOrDefault() as TextBox;
                var objetTextBox = Controls.Find("objetTextBox", true).FirstOrDefault() as TextBox;
                var formatComboBox = Controls.Find("formatComboBox", true).FirstOrDefault() as ComboBox;
                var messageTextBox = Controls.Find("messageTextBox", true).FirstOrDefault() as TextBox;

                if (destinatairesTextBox == null || objetTextBox == null || formatComboBox == null || messageTextBox == null)
                {
                    throw new InvalidOperationException("Un ou plusieurs contrôles sont introuvables.");
                }

                // Préparer les données
                var destinataires = destinatairesTextBox.Text.Split(';', StringSplitOptions.RemoveEmptyEntries)
                    .Select(e => e.Trim())
                    .ToList();
                string objet = objetTextBox.Text;
                string format = formatComboBox.SelectedItem.ToString();
                string message = messageTextBox.Text;

                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Envoyer le rapport par email
                bool success = await _rapportPersonnaliseService.SendReportByEmailAsync(_rapportId, destinataires, objet, message, format, _currentUserId);

                if (success)
                {
                    MessageBox.Show("Le rapport a été envoyé avec succès.", "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    DialogResult = DialogResult.OK;
                    Close();
                }
                else
                {
                    MessageBox.Show("Une erreur s'est produite lors de l'envoi du rapport.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'envoi du rapport par email");
                MessageBox.Show($"Erreur : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
