using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace RecouvreX.WinForms.Forms.Clients
{
    public partial class ClientDetailsForm : Form
    {
        private readonly IClientService _clientService;
        private readonly IFactureService _factureService;
        private readonly IContactService _contactService;
        private readonly int _currentUserId;
        private readonly int _clientId;
        private Client? _client;
        private List<Contact> _contacts = new List<Contact>();
        private List<Facture> _factures = new List<Facture>();
        private DataTable _contactsDataTable = new DataTable();
        private DataTable _facturesDataTable = new DataTable();

        public ClientDetailsForm(IClientService clientService, IFactureService factureService, IContactService contactService, int currentUserId, int clientId)
        {
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _contactService = contactService ?? throw new ArgumentNullException(nameof(contactService));
            _currentUserId = currentUserId;
            _clientId = clientId;

            InitializeComponent();
        }



        private async void ClientDetailsForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialiser les tables de données
                InitializeContactsDataTable();
                InitializeFacturesDataTable();

                // Charger les données du client
                await LoadClientDataAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des détails du client");
                MessageBox.Show($"Une erreur s'est produite lors du chargement des détails du client : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeContactsDataTable()
        {
            _contactsDataTable = new DataTable();
            _contactsDataTable.Columns.Add("Id", typeof(int));
            _contactsDataTable.Columns.Add("Nom", typeof(string));
            _contactsDataTable.Columns.Add("Prénom", typeof(string));
            _contactsDataTable.Columns.Add("Fonction", typeof(string));
            _contactsDataTable.Columns.Add("Téléphone", typeof(string));
            _contactsDataTable.Columns.Add("Email", typeof(string));
            _contactsDataTable.Columns.Add("Principal", typeof(bool));

            var contactsDataGridView = this.Controls.Find("contactsDataGridView", true).FirstOrDefault() as DataGridView;
            if (contactsDataGridView != null)
            {
                contactsDataGridView.DataSource = _contactsDataTable;

                // Masquer la colonne Id
                if (contactsDataGridView.Columns["Id"] != null)
                    contactsDataGridView.Columns["Id"].Visible = false;
            }
        }

        private void InitializeFacturesDataTable()
        {
            _facturesDataTable = new DataTable();
            _facturesDataTable.Columns.Add("Id", typeof(int));
            _facturesDataTable.Columns.Add("Numéro", typeof(string));
            _facturesDataTable.Columns.Add("Date Émission", typeof(DateTime));
            _facturesDataTable.Columns.Add("Date Échéance", typeof(DateTime));
            _facturesDataTable.Columns.Add("Montant TTC", typeof(decimal));
            _facturesDataTable.Columns.Add("Montant Payé", typeof(decimal));
            _facturesDataTable.Columns.Add("Montant Restant", typeof(decimal));
            _facturesDataTable.Columns.Add("Statut", typeof(string));

            var facturesDataGridView = this.Controls.Find("facturesDataGridView", true).FirstOrDefault() as DataGridView;
            if (facturesDataGridView != null)
            {
                facturesDataGridView.DataSource = _facturesDataTable;

                // Masquer la colonne Id
                if (facturesDataGridView.Columns["Id"] != null)
                    facturesDataGridView.Columns["Id"].Visible = false;

                // Configurer les colonnes
                if (facturesDataGridView.Columns["Montant TTC"] != null)
                    facturesDataGridView.Columns["Montant TTC"].DefaultCellStyle.Format = "C2";
                if (facturesDataGridView.Columns["Montant Payé"] != null)
                    facturesDataGridView.Columns["Montant Payé"].DefaultCellStyle.Format = "C2";
                if (facturesDataGridView.Columns["Montant Restant"] != null)
                    facturesDataGridView.Columns["Montant Restant"].DefaultCellStyle.Format = "C2";
            }
        }

        private async Task LoadClientDataAsync()
        {
            try
            {
                // Afficher un indicateur de chargement
                Cursor.Current = Cursors.WaitCursor;

                // Récupérer le client avec ses contacts
                _client = await _clientService.GetWithContactsAsync(_clientId);
                if (_client == null)
                {
                    MessageBox.Show("Le client demandé n'existe pas.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // Mettre à jour le titre du formulaire
                this.Text = $"Détails du client - {_client.RaisonSociale}";

                // Remplir les informations du client
                FillClientInfo(_client);

                // Récupérer les contacts
                _contacts = _client.Contacts?.ToList() ?? new List<Contact>();
                UpdateContactsDataTable(_contacts);

                // Récupérer les factures
                _factures = (await _factureService.GetByClientIdAsync(_clientId)).ToList();
                UpdateFacturesDataTable(_factures);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des données du client");
                throw;
            }
            finally
            {
                // Restaurer le curseur
                Cursor.Current = Cursors.Default;
            }
        }

        private void FillClientInfo(Client client)
        {
            var codeValueLabel = this.Controls.Find("codeValueLabel", true).FirstOrDefault() as Label;
            var raisonSocialeValueLabel = this.Controls.Find("raisonSocialeValueLabel", true).FirstOrDefault() as Label;
            var adresseValueLabel = this.Controls.Find("adresseValueLabel", true).FirstOrDefault() as Label;
            var villeValueLabel = this.Controls.Find("villeValueLabel", true).FirstOrDefault() as Label;
            var telephoneValueLabel = this.Controls.Find("telephoneValueLabel", true).FirstOrDefault() as Label;
            var emailValueLabel = this.Controls.Find("emailValueLabel", true).FirstOrDefault() as Label;

            if (codeValueLabel != null) codeValueLabel.Text = client.Code;
            if (raisonSocialeValueLabel != null) raisonSocialeValueLabel.Text = client.RaisonSociale;
            if (adresseValueLabel != null) adresseValueLabel.Text = client.Adresse;
            if (villeValueLabel != null) villeValueLabel.Text = $"{client.CodePostal} {client.Ville}";
            if (telephoneValueLabel != null) telephoneValueLabel.Text = client.Telephone;
            if (emailValueLabel != null) emailValueLabel.Text = client.Email;
        }

        private void UpdateContactsDataTable(List<Contact> contacts)
        {
            _contactsDataTable.Clear();

            foreach (var contact in contacts)
            {
                _contactsDataTable.Rows.Add(
                    contact.Id,
                    contact.Nom,
                    contact.Prenom,
                    contact.Fonction,
                    contact.Telephone,
                    contact.Email,
                    contact.EstPrincipal
                );
            }
        }

        private void UpdateFacturesDataTable(List<Facture> factures)
        {
            _facturesDataTable.Clear();

            foreach (var facture in factures)
            {
                _facturesDataTable.Rows.Add(
                    facture.Id,
                    facture.Numero,
                    facture.DateEmission,
                    facture.DateEcheance,
                    facture.MontantTTC,
                    facture.MontantPaye,
                    facture.MontantRestant,
                    facture.Statut
                );
            }
        }

        private async void AddContactButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_client == null)
                {
                    MessageBox.Show("Veuillez d'abord charger un client.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Créer un nouveau contact
                var contact = new Contact
                {
                    ClientId = _client.Id,
                    EstActif = true
                };

                // Ouvrir le formulaire d'édition de contact
                using (var form = new ContactEditForm(_contactService, _currentUserId, contact))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les contacts
                        await LoadClientDataAsync();
                        MessageBox.Show("Le contact a été ajouté avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ajout d'un contact");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditContactButton_Click(object sender, EventArgs e)
        {
            var contactsDataGridView = this.Controls.Find("contactsDataGridView", true).FirstOrDefault() as DataGridView;
            if (contactsDataGridView != null && contactsDataGridView.SelectedRows.Count > 0)
            {
                int contactId = Convert.ToInt32(contactsDataGridView.SelectedRows[0].Cells["Id"].Value);
                EditContact(contactId);
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un contact à modifier.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ContactsDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int contactId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    EditContact(contactId);
                }
            }
        }

        private async void EditContact(int contactId)
        {
            try
            {
                // Récupérer le contact
                var contact = await _contactService.GetByIdAsync(contactId);
                if (contact == null)
                {
                    MessageBox.Show("Le contact demandé n'existe pas ou a été supprimé.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Ouvrir le formulaire d'édition de contact
                using (var form = new ContactEditForm(_contactService, _currentUserId, contact))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les contacts
                        await LoadClientDataAsync();
                        MessageBox.Show("Le contact a été modifié avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la modification d'un contact");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteContactButton_Click(object sender, EventArgs e)
        {
            var contactsDataGridView = this.Controls.Find("contactsDataGridView", true).FirstOrDefault() as DataGridView;
            if (contactsDataGridView != null && contactsDataGridView.SelectedRows.Count > 0)
            {
                int contactId = Convert.ToInt32(contactsDataGridView.SelectedRows[0].Cells["Id"].Value);
                string contactName = $"{contactsDataGridView.SelectedRows[0].Cells["Prénom"].Value} {contactsDataGridView.SelectedRows[0].Cells["Nom"].Value}";

                var result = MessageBox.Show($"Êtes-vous sûr de vouloir supprimer le contact '{contactName}' ?",
                    "Confirmation de suppression", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    DeleteContact(contactId);
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner un contact à supprimer.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async void DeleteContact(int contactId)
        {
            try
            {
                bool success = await _clientService.DeleteContactAsync(contactId, _currentUserId);
                if (success)
                {
                    MessageBox.Show("Le contact a été supprimé avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Recharger les contacts
                    await LoadClientDataAsync();
                }
                else
                {
                    MessageBox.Show("Impossible de supprimer le contact.", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de la suppression du contact");
                MessageBox.Show($"Une erreur s'est produite lors de la suppression du contact : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void AddFactureButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_client == null)
                {
                    MessageBox.Show("Veuillez d'abord charger un client.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Créer une nouvelle facture pour ce client
                var nouvelleFacture = new Facture
                {
                    ClientId = _client.Id,
                    DateEmission = DateTime.Today,
                    DateEcheance = DateTime.Today.AddDays(30),
                    Statut = "En attente",
                    EstActif = true
                };

                // Ouvrir le formulaire d'ajout de facture
                using (var form = new Factures.FactureEditForm(_factureService, _clientService, _currentUserId, nouvelleFacture))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        // Recharger les factures
                        await LoadClientDataAsync();
                        MessageBox.Show("La facture a été ajoutée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ajout d'une facture");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewFactureButton_Click(object sender, EventArgs e)
        {
            var facturesDataGridView = this.Controls.Find("facturesDataGridView", true).FirstOrDefault() as DataGridView;
            if (facturesDataGridView != null && facturesDataGridView.SelectedRows.Count > 0)
            {
                int factureId = Convert.ToInt32(facturesDataGridView.SelectedRows[0].Cells["Id"].Value);
                ViewFacture(factureId);
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une facture à consulter.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void FacturesDataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView != null)
                {
                    int factureId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                    ViewFacture(factureId);
                }
            }
        }

        private void ViewFacture(int factureId)
        {
            try
            {
                // Ouvrir le formulaire de détails de facture
                using (var form = new Factures.FactureDetailsForm(_factureService, _clientService, _currentUserId, factureId))
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'ouverture du formulaire de détails de facture");
                MessageBox.Show($"Une erreur s'est produite : {ex.Message}", "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
