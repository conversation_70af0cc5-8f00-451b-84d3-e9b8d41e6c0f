using Microsoft.Extensions.Configuration;
using RecouvreX.Business.Interfaces;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;

namespace RecouvreX.Business.Services
{
    /// <summary>
    /// Service d'envoi d'emails
    /// </summary>
    public class EmailService : IEmailService
    {
        private readonly IConfiguration _configuration;
        private readonly string _smtpServer;
        private readonly int _smtpPort;
        private readonly string _smtpUsername;
        private readonly string _smtpPassword;
        private readonly string _emailFrom;
        private readonly string _emailFromName;
        private readonly bool _enableSsl;

        public EmailService(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

            // Récupérer les paramètres SMTP depuis la configuration
            _smtpServer = _configuration["Email:SmtpServer"];
            _smtpPort = int.Parse(_configuration["Email:SmtpPort"]);
            _smtpUsername = _configuration["Email:Username"];
            _smtpPassword = _configuration["Email:Password"];
            _emailFrom = _configuration["Email:From"];
            _emailFromName = _configuration["Email:FromName"];
            _enableSsl = bool.Parse(_configuration["Email:EnableSsl"]);

            if (string.IsNullOrEmpty(_smtpServer) || string.IsNullOrEmpty(_smtpUsername) || 
                string.IsNullOrEmpty(_smtpPassword) || string.IsNullOrEmpty(_emailFrom))
            {
                throw new InvalidOperationException("La configuration SMTP est incomplète.");
            }
        }

        /// <summary>
        /// Envoie un email simple
        /// </summary>
        /// <param name="destinataires">Liste des adresses email des destinataires</param>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="corps">Corps de l'email</param>
        /// <param name="estHtml">Indique si le corps est au format HTML</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        public async Task<bool> EnvoyerEmailAsync(List<string> destinataires, string objet, string corps, bool estHtml = false)
        {
            try
            {
                using (var message = new MailMessage())
                {
                    // Configurer l'expéditeur
                    message.From = new MailAddress(_emailFrom, _emailFromName);

                    // Ajouter les destinataires
                    foreach (var destinataire in destinataires)
                    {
                        message.To.Add(destinataire);
                    }

                    // Configurer le message
                    message.Subject = objet;
                    message.Body = corps;
                    message.IsBodyHtml = estHtml;

                    // Envoyer l'email
                    using (var client = new SmtpClient(_smtpServer, _smtpPort))
                    {
                        client.EnableSsl = _enableSsl;
                        client.Credentials = new NetworkCredential(_smtpUsername, _smtpPassword);
                        await client.SendMailAsync(message);
                    }

                    Log.Information("Email envoyé avec succès à {Destinataires}", string.Join(", ", destinataires));
                    return true;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'envoi de l'email à {Destinataires}", string.Join(", ", destinataires));
                return false;
            }
        }

        /// <summary>
        /// Envoie un email avec une pièce jointe
        /// </summary>
        /// <param name="destinataires">Liste des adresses email des destinataires</param>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="corps">Corps de l'email</param>
        /// <param name="cheminPieceJointe">Chemin vers la pièce jointe</param>
        /// <param name="nomPieceJointe">Nom de la pièce jointe</param>
        /// <param name="estHtml">Indique si le corps est au format HTML</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        public async Task<bool> EnvoyerEmailAvecPieceJointeAsync(List<string> destinataires, string objet, string corps, string cheminPieceJointe, string nomPieceJointe, bool estHtml = false)
        {
            try
            {
                using (var message = new MailMessage())
                {
                    // Configurer l'expéditeur
                    message.From = new MailAddress(_emailFrom, _emailFromName);

                    // Ajouter les destinataires
                    foreach (var destinataire in destinataires)
                    {
                        message.To.Add(destinataire);
                    }

                    // Configurer le message
                    message.Subject = objet;
                    message.Body = corps;
                    message.IsBodyHtml = estHtml;

                    // Ajouter la pièce jointe
                    if (File.Exists(cheminPieceJointe))
                    {
                        var attachment = new Attachment(cheminPieceJointe);
                        if (!string.IsNullOrEmpty(nomPieceJointe))
                        {
                            attachment.Name = nomPieceJointe;
                        }
                        message.Attachments.Add(attachment);
                    }
                    else
                    {
                        Log.Warning("La pièce jointe {CheminPieceJointe} n'existe pas", cheminPieceJointe);
                        return false;
                    }

                    // Envoyer l'email
                    using (var client = new SmtpClient(_smtpServer, _smtpPort))
                    {
                        client.EnableSsl = _enableSsl;
                        client.Credentials = new NetworkCredential(_smtpUsername, _smtpPassword);
                        await client.SendMailAsync(message);
                    }

                    Log.Information("Email avec pièce jointe envoyé avec succès à {Destinataires}", string.Join(", ", destinataires));
                    return true;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'envoi de l'email avec pièce jointe à {Destinataires}", string.Join(", ", destinataires));
                return false;
            }
        }

        /// <summary>
        /// Envoie un email avec plusieurs pièces jointes
        /// </summary>
        /// <param name="destinataires">Liste des adresses email des destinataires</param>
        /// <param name="objet">Objet de l'email</param>
        /// <param name="corps">Corps de l'email</param>
        /// <param name="piecesJointes">Dictionnaire des pièces jointes (clé = chemin, valeur = nom)</param>
        /// <param name="estHtml">Indique si le corps est au format HTML</param>
        /// <returns>True si l'envoi a réussi, sinon False</returns>
        public async Task<bool> EnvoyerEmailAvecPiecesJointesAsync(List<string> destinataires, string objet, string corps, Dictionary<string, string> piecesJointes, bool estHtml = false)
        {
            try
            {
                using (var message = new MailMessage())
                {
                    // Configurer l'expéditeur
                    message.From = new MailAddress(_emailFrom, _emailFromName);

                    // Ajouter les destinataires
                    foreach (var destinataire in destinataires)
                    {
                        message.To.Add(destinataire);
                    }

                    // Configurer le message
                    message.Subject = objet;
                    message.Body = corps;
                    message.IsBodyHtml = estHtml;

                    // Ajouter les pièces jointes
                    foreach (var pieceJointe in piecesJointes)
                    {
                        if (File.Exists(pieceJointe.Key))
                        {
                            var attachment = new Attachment(pieceJointe.Key);
                            if (!string.IsNullOrEmpty(pieceJointe.Value))
                            {
                                attachment.Name = pieceJointe.Value;
                            }
                            message.Attachments.Add(attachment);
                        }
                        else
                        {
                            Log.Warning("La pièce jointe {CheminPieceJointe} n'existe pas", pieceJointe.Key);
                        }
                    }

                    // Envoyer l'email
                    using (var client = new SmtpClient(_smtpServer, _smtpPort))
                    {
                        client.EnableSsl = _enableSsl;
                        client.Credentials = new NetworkCredential(_smtpUsername, _smtpPassword);
                        await client.SendMailAsync(message);
                    }

                    Log.Information("Email avec pièces jointes envoyé avec succès à {Destinataires}", string.Join(", ", destinataires));
                    return true;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'envoi de l'email avec pièces jointes à {Destinataires}", string.Join(", ", destinataires));
                return false;
            }
        }
    }
}
