using Dapper;
using RecouvreX.DataAccess.Interfaces;
using RecouvreX.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Repositories
{
    /// <summary>
    /// Repository pour les relances
    /// </summary>
    public class RelanceRepository : BaseRepository<Relance>, IRelanceRepository
    {
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="dbConnection">Connexion à la base de données</param>
        public RelanceRepository(DatabaseConnection dbConnection)
            : base(dbConnection, "Relances")
        {
        }

        /// <summary>
        /// Récupère toutes les relances d'une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des relances de la facture</returns>
        public async Task<IEnumerable<Relance>> GetByFactureIdAsync(int factureId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT r.*, u.NomComplet as UtilisateurNom
                    FROM Relances r
                    LEFT JOIN Utilisateurs u ON r.UtilisateurId = u.Id
                    WHERE r.FactureId = @FactureId AND r.EstActif = 1
                    ORDER BY r.DateRelance DESC";

                var relances = new List<Relance>();

                var results = await connection.QueryAsync<Relance, string, Relance>(
                    query,
                    (relance, utilisateurNom) =>
                    {
                        if (relance.UtilisateurId.HasValue && !string.IsNullOrEmpty(utilisateurNom))
                        {
                            relance.Utilisateur = new Utilisateur { Id = relance.UtilisateurId.Value, NomComplet = utilisateurNom };
                        }
                        return relance;
                    },
                    new { FactureId = factureId },
                    splitOn: "UtilisateurNom");

                return results;
            }
        }

        /// <summary>
        /// Récupère toutes les relances effectuées par un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des relances effectuées par l'utilisateur</returns>
        public async Task<IEnumerable<Relance>> GetByUtilisateurIdAsync(int utilisateurId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT r.*, f.Numero as FactureNumero, c.RaisonSociale as ClientNom
                    FROM Relances r
                    INNER JOIN Factures f ON r.FactureId = f.Id
                    INNER JOIN Clients c ON f.ClientId = c.Id
                    WHERE r.UtilisateurId = @UtilisateurId AND r.EstActif = 1
                    ORDER BY r.DateRelance DESC";

                var relances = new List<Relance>();

                var results = await connection.QueryAsync<Relance, string, string, Relance>(
                    query,
                    (relance, factureNumero, clientNom) =>
                    {
                        relance.Facture = new Facture { Id = relance.FactureId, Numero = factureNumero };
                        relance.Facture.Client = new Client { RaisonSociale = clientNom };
                        return relance;
                    },
                    new { UtilisateurId = utilisateurId },
                    splitOn: "FactureNumero,ClientNom");

                return results;
            }
        }

        /// <summary>
        /// Récupère les relances par type
        /// </summary>
        /// <param name="type">Type de relance</param>
        /// <returns>Liste des relances du type spécifié</returns>
        public async Task<IEnumerable<Relance>> GetByTypeAsync(string type)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Relances WHERE Type = @Type AND EstActif = 1";
                return await connection.QueryAsync<Relance>(query, new { Type = type });
            }
        }

        /// <summary>
        /// Récupère les relances par statut
        /// </summary>
        /// <param name="statut">Statut de relance</param>
        /// <returns>Liste des relances ayant le statut spécifié</returns>
        public async Task<IEnumerable<Relance>> GetByStatutAsync(string statut)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Relances WHERE Statut = @Statut AND EstActif = 1";
                return await connection.QueryAsync<Relance>(query, new { Statut = statut });
            }
        }

        /// <summary>
        /// Récupère les relances par niveau
        /// </summary>
        /// <param name="niveau">Niveau de relance</param>
        /// <returns>Liste des relances du niveau spécifié</returns>
        public async Task<IEnumerable<Relance>> GetByNiveauAsync(int niveau)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = "SELECT * FROM Relances WHERE Niveau = @Niveau AND EstActif = 1";
                return await connection.QueryAsync<Relance>(query, new { Niveau = niveau });
            }
        }

        /// <summary>
        /// Récupère les relances par période
        /// </summary>
        /// <param name="dateDebut">Date de début</param>
        /// <param name="dateFin">Date de fin</param>
        /// <returns>Liste des relances effectuées dans la période spécifiée</returns>
        public async Task<IEnumerable<Relance>> GetByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT *
                    FROM Relances
                    WHERE DateRelance BETWEEN @DateDebut AND @DateFin
                        AND EstActif = 1";

                return await connection.QueryAsync<Relance>(query, new { DateDebut = dateDebut, DateFin = dateFin });
            }
        }

        /// <summary>
        /// Récupère les relances planifiées pour une date donnée
        /// </summary>
        /// <param name="date">Date des relances planifiées</param>
        /// <returns>Liste des relances planifiées pour la date spécifiée</returns>
        public async Task<IEnumerable<Relance>> GetPlannedForDateAsync(DateTime date)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT r.*, f.Numero as FactureNumero, c.RaisonSociale as ClientNom
                    FROM Relances r
                    INNER JOIN Factures f ON r.FactureId = f.Id
                    INNER JOIN Clients c ON f.ClientId = c.Id
                    WHERE CAST(r.DateProchaineRelance AS DATE) = CAST(@Date AS DATE)
                        AND r.EstActif = 1";

                var relances = new List<Relance>();

                var results = await connection.QueryAsync<Relance, string, string, Relance>(
                    query,
                    (relance, factureNumero, clientNom) =>
                    {
                        relance.Facture = new Facture { Id = relance.FactureId, Numero = factureNumero };
                        relance.Facture.Client = new Client { RaisonSociale = clientNom };
                        return relance;
                    },
                    new { Date = date },
                    splitOn: "FactureNumero,ClientNom");

                return results;
            }
        }

        /// <summary>
        /// Récupère toutes les relances associées à un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des relances du client</returns>
        public async Task<IEnumerable<Relance>> GetByClientIdAsync(int clientId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    SELECT r.*, f.Numero as FactureNumero
                    FROM Relances r
                    INNER JOIN Factures f ON r.FactureId = f.Id
                    WHERE f.ClientId = @ClientId AND r.EstActif = 1
                    ORDER BY r.DateRelance DESC";

                var relances = new List<Relance>();

                var results = await connection.QueryAsync<Relance, string, Relance>(
                    query,
                    (relance, factureNumero) =>
                    {
                        relance.Facture = new Facture { Id = relance.FactureId, Numero = factureNumero };
                        return relance;
                    },
                    new { ClientId = clientId },
                    splitOn: "FactureNumero");

                return results;
            }
        }

        /// <summary>
        /// Met à jour le statut d'une relance
        /// </summary>
        /// <param name="relanceId">Identifiant de la relance</param>
        /// <param name="statut">Nouveau statut</param>
        /// <param name="userId">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>True si la mise à jour a réussi, sinon False</returns>
        public async Task<bool> UpdateStatutAsync(int relanceId, string statut, int userId)
        {
            using (var connection = await _dbConnection.CreateConnectionAsync())
            {
                var query = @"
                    UPDATE Relances
                    SET Statut = @Statut,
                        DateModification = @DateModification,
                        ModifiePar = @ModifiePar
                    WHERE Id = @Id";

                var parameters = new
                {
                    Id = relanceId,
                    Statut = statut,
                    DateModification = DateTime.Now,
                    ModifiePar = userId
                };

                var result = await connection.ExecuteAsync(query, parameters);
                return result > 0;
            }
        }
    }
}
