using RecouvreX.Business.Interfaces;
using RecouvreX.Models;
using Serilog;
using System.ComponentModel;

namespace RecouvreX.WinForms.Forms.Factures
{
    public partial class FactureEditForm : Form
    {
        private readonly IFactureService _factureService;
        private readonly IClientService _clientService;
        private readonly int _currentUserId;
        private readonly Facture? _facture;
        private readonly bool _isEditMode;
        private readonly ErrorProvider _errorProvider = new ErrorProvider();
        private List<Client> _clients = new List<Client>();
        private TextBox numeroTextBox;
        private ComboBox statutComboBox;

        public FactureEditForm(IFactureService factureService, IClientService clientService, int currentUserId, Facture? facture = null)
        {
            _factureService = factureService ?? throw new ArgumentNullException(nameof(factureService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _currentUserId = currentUserId;
            _facture = facture;
            _isEditMode = facture != null;

            InitializeComponent();

            // Mettre à jour le titre du formulaire après l'initialisation des composants
            this.Text = _isEditMode ? "Modifier une facture" : "Ajouter une facture";

            // Configurer l'ErrorProvider
            _errorProvider.BlinkStyle = ErrorBlinkStyle.NeverBlink;

            // Activer/désactiver les champs en fonction du mode
            if (numeroTextBox != null)
            {
                numeroTextBox.Enabled = _isEditMode;
            }

            if (statutComboBox != null)
            {
                statutComboBox.Enabled = _isEditMode;
            }
        }

        private async void FactureEditForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Charger les clients
                await LoadClientsAsync();

                // Calculer les montants initiaux
                CalculateMontants();

                // Si en mode édition, remplir les champs avec les données de la facture
                if (_isEditMode && _facture != null)
                {
                    FillFormWithFactureData(_facture);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement du formulaire d'édition de facture");
                MessageBox.Show($"Une erreur s'est produite lors du chargement du formulaire : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadClientsAsync()
        {
            try
            {
                // Récupérer les clients
                _clients = (await _clientService.GetAllAsync()).ToList();

                // Remplir la ComboBox
                var clientComboBox = this.Controls.Find("clientComboBox", true).FirstOrDefault() as ComboBox;
                if (clientComboBox != null)
                {
                    clientComboBox.Items.Clear();
                    clientComboBox.DisplayMember = "Text";
                    clientComboBox.ValueMember = "Value";

                    // Ajouter un élément vide
                    clientComboBox.Items.Add(new { Text = "-- Sélectionner un client --", Value = 0 });

                    // Ajouter les clients
                    foreach (var client in _clients)
                    {
                        clientComboBox.Items.Add(new { Text = $"{client.Code} - {client.RaisonSociale}", Value = client.Id });
                    }

                    clientComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors du chargement des clients");
                throw;
            }
        }

        private void FillFormWithFactureData(Facture facture)
        {
            var numeroTextBox = this.Controls.Find("numeroTextBox", true).FirstOrDefault() as TextBox;
            var clientComboBox = this.Controls.Find("clientComboBox", true).FirstOrDefault() as ComboBox;
            var dateEmissionPicker = this.Controls.Find("dateEmissionPicker", true).FirstOrDefault() as DateTimePicker;
            var dateEcheancePicker = this.Controls.Find("dateEcheancePicker", true).FirstOrDefault() as DateTimePicker;
            var montantHTNumericUpDown = this.Controls.Find("montantHTNumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var tauxTVANumericUpDown = this.Controls.Find("tauxTVANumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var montantTVANumericUpDown = this.Controls.Find("montantTVANumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var montantTTCNumericUpDown = this.Controls.Find("montantTTCNumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var montantPayeNumericUpDown = this.Controls.Find("montantPayeNumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var montantRestantNumericUpDown = this.Controls.Find("montantRestantNumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var statutComboBox = this.Controls.Find("statutComboBox", true).FirstOrDefault() as ComboBox;

            if (numeroTextBox != null) numeroTextBox.Text = facture.Numero;

            if (clientComboBox != null)
            {
                // Sélectionner le client
                for (int i = 0; i < clientComboBox.Items.Count; i++)
                {
                    dynamic item = clientComboBox.Items[i];
                    if (item.Value == facture.ClientId)
                    {
                        clientComboBox.SelectedIndex = i;
                        break;
                    }
                }
            }

            if (dateEmissionPicker != null) dateEmissionPicker.Value = facture.DateEmission;
            if (dateEcheancePicker != null) dateEcheancePicker.Value = facture.DateEcheance;
            if (montantHTNumericUpDown != null) montantHTNumericUpDown.Value = facture.MontantHT;

            if (tauxTVANumericUpDown != null && montantHTNumericUpDown != null && montantTVANumericUpDown != null)
            {
                // Calculer le taux de TVA
                if (facture.MontantHT > 0)
                {
                    decimal tauxTVA = (facture.MontantTVA / facture.MontantHT) * 100;
                    tauxTVANumericUpDown.Value = tauxTVA;
                }
            }

            if (montantTVANumericUpDown != null) montantTVANumericUpDown.Value = facture.MontantTVA;
            if (montantTTCNumericUpDown != null) montantTTCNumericUpDown.Value = facture.MontantTTC;
            if (montantPayeNumericUpDown != null) montantPayeNumericUpDown.Value = facture.MontantPaye;
            if (montantRestantNumericUpDown != null) montantRestantNumericUpDown.Value = facture.MontantRestant;

            if (statutComboBox != null)
            {
                // Sélectionner le statut
                for (int i = 0; i < statutComboBox.Items.Count; i++)
                {
                    if (statutComboBox.Items[i].ToString() == facture.Statut)
                    {
                        statutComboBox.SelectedIndex = i;
                        break;
                    }
                }
            }
        }

        private void ClientComboBox_Validating(object sender, CancelEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox != null)
            {
                if (comboBox.SelectedIndex == 0) // L'élément vide
                {
                    _errorProvider.SetError(comboBox, "Veuillez sélectionner un client.");
                    e.Cancel = true;
                }
                else
                {
                    _errorProvider.SetError(comboBox, "");
                }
            }
        }

        private void MontantHTNumericUpDown_ValueChanged(object sender, EventArgs e)
        {
            CalculateMontants();
        }

        private void TauxTVANumericUpDown_ValueChanged(object sender, EventArgs e)
        {
            CalculateMontants();
        }

        private void MontantPayeNumericUpDown_ValueChanged(object sender, EventArgs e)
        {
            CalculateMontantRestant();
        }

        private void CalculateMontants()
        {
            var montantHTNumericUpDown = this.Controls.Find("montantHTNumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var tauxTVANumericUpDown = this.Controls.Find("tauxTVANumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var montantTVANumericUpDown = this.Controls.Find("montantTVANumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var montantTTCNumericUpDown = this.Controls.Find("montantTTCNumericUpDown", true).FirstOrDefault() as NumericUpDown;

            if (montantHTNumericUpDown != null && tauxTVANumericUpDown != null && montantTVANumericUpDown != null && montantTTCNumericUpDown != null)
            {
                decimal montantHT = montantHTNumericUpDown.Value;
                decimal tauxTVA = tauxTVANumericUpDown.Value;
                decimal montantTVA = Math.Round(montantHT * (tauxTVA / 100), 2);
                decimal montantTTC = montantHT + montantTVA;

                montantTVANumericUpDown.Value = montantTVA;
                montantTTCNumericUpDown.Value = montantTTC;

                CalculateMontantRestant();
            }
        }

        private void CalculateMontantRestant()
        {
            var montantTTCNumericUpDown = this.Controls.Find("montantTTCNumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var montantPayeNumericUpDown = this.Controls.Find("montantPayeNumericUpDown", true).FirstOrDefault() as NumericUpDown;
            var montantRestantNumericUpDown = this.Controls.Find("montantRestantNumericUpDown", true).FirstOrDefault() as NumericUpDown;

            if (montantTTCNumericUpDown != null && montantPayeNumericUpDown != null && montantRestantNumericUpDown != null)
            {
                decimal montantTTC = montantTTCNumericUpDown.Value;
                decimal montantPaye = montantPayeNumericUpDown.Value;
                decimal montantRestant = Math.Max(0, montantTTC - montantPaye);

                montantRestantNumericUpDown.Value = montantRestant;
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                // Valider le formulaire
                if (!ValidateChildren())
                {
                    MessageBox.Show("Veuillez corriger les erreurs avant d'enregistrer.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Récupérer les valeurs du formulaire
                var clientComboBox = this.Controls.Find("clientComboBox", true).FirstOrDefault() as ComboBox;
                var dateEmissionPicker = this.Controls.Find("dateEmissionPicker", true).FirstOrDefault() as DateTimePicker;
                var dateEcheancePicker = this.Controls.Find("dateEcheancePicker", true).FirstOrDefault() as DateTimePicker;
                var montantHTNumericUpDown = this.Controls.Find("montantHTNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                var montantTVANumericUpDown = this.Controls.Find("montantTVANumericUpDown", true).FirstOrDefault() as NumericUpDown;
                var montantTTCNumericUpDown = this.Controls.Find("montantTTCNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                var montantPayeNumericUpDown = this.Controls.Find("montantPayeNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                var montantRestantNumericUpDown = this.Controls.Find("montantRestantNumericUpDown", true).FirstOrDefault() as NumericUpDown;
                var statutComboBox = this.Controls.Find("statutComboBox", true).FirstOrDefault() as ComboBox;

                if (clientComboBox == null || dateEmissionPicker == null || dateEcheancePicker == null ||
                    montantHTNumericUpDown == null || montantTVANumericUpDown == null || montantTTCNumericUpDown == null ||
                    montantPayeNumericUpDown == null || montantRestantNumericUpDown == null || statutComboBox == null)
                {
                    throw new InvalidOperationException("Un ou plusieurs champs sont introuvables.");
                }

                // Créer ou mettre à jour la facture
                Facture facture;
                if (_isEditMode && _facture != null)
                {
                    // Mode édition
                    facture = _facture;
                }
                else
                {
                    // Mode ajout
                    facture = new Facture();
                }

                // Remplir les propriétés de la facture
                facture.ClientId = ((dynamic)clientComboBox.SelectedItem).Value;
                facture.DateEmission = dateEmissionPicker.Value;
                facture.DateEcheance = dateEcheancePicker.Value;
                facture.MontantHT = montantHTNumericUpDown.Value;
                facture.MontantTVA = montantTVANumericUpDown.Value;
                facture.MontantTTC = montantTTCNumericUpDown.Value;
                facture.MontantPaye = montantPayeNumericUpDown.Value;
                facture.MontantRestant = montantRestantNumericUpDown.Value;
                facture.Statut = statutComboBox.SelectedItem.ToString();

                // Enregistrer la facture
                if (_isEditMode)
                {
                    // Mettre à jour la facture existante
                    await _factureService.UpdateAsync(facture, _currentUserId);
                    MessageBox.Show("La facture a été mise à jour avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Créer une nouvelle facture
                    await _factureService.CreateAsync(facture, _currentUserId);
                    MessageBox.Show("La facture a été créée avec succès.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Erreur lors de l'enregistrement de la facture");
                MessageBox.Show($"Une erreur s'est produite lors de l'enregistrement de la facture : {ex.Message}",
                    "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
