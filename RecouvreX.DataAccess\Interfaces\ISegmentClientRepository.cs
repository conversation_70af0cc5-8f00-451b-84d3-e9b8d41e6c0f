using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository de gestion des segments client
    /// </summary>
    public interface ISegmentClientRepository
    {
        /// <summary>
        /// Récupère les clients par segment
        /// </summary>
        /// <param name="segment">Segment de client</param>
        /// <returns>Liste des clients du segment spécifié</returns>
        Task<IEnumerable<Client>> GetClientsBySegmentAsync(SegmentClient segment);

        /// <summary>
        /// Met à jour le segment d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <param name="segment">Nouveau segment</param>
        /// <param name="commentaire">Commentaire sur la segmentation</param>
        /// <param name="segmentationManuelle">Indique si la segmentation a été faite manuellement</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Client mis à jour</returns>
        Task<Client> UpdateClientSegmentAsync(int clientId, SegmentClient segment, string commentaire, bool segmentationManuelle, int modifiePar);

        /// <summary>
        /// Récupère la répartition des clients par segment
        /// </summary>
        /// <returns>Dictionnaire avec le segment comme clé et le nombre de clients comme valeur</returns>
        Task<Dictionary<SegmentClient, int>> GetClientDistributionBySegmentAsync();
    }
}
