namespace RecouvreX.WinForms.Forms.Clients
{
    partial class ClientEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // ClientEditForm
            // 
            this.ClientSize = new System.Drawing.Size(600, 500);
            this.Name = "ClientEditForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Client"; // Titre par défaut, sera mis à jour dans le constructeur
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Load += new System.EventHandler(this.ClientEditForm_Load);
            this.ResumeLayout(false);

            // Panneau principal
            System.Windows.Forms.TableLayoutPanel mainPanel = new System.Windows.Forms.TableLayoutPanel();
            mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            mainPanel.Padding = new System.Windows.Forms.Padding(10);
            mainPanel.ColumnCount = 2;
            mainPanel.RowCount = 12;
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 30F));
            mainPanel.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 70F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            mainPanel.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            this.Controls.Add(mainPanel);

            // Code client
            System.Windows.Forms.Label codeLabel = new System.Windows.Forms.Label();
            codeLabel.Text = "Code client :";
            codeLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(codeLabel, 0, 0);

            this.codeTextBox = new System.Windows.Forms.TextBox();
            this.codeTextBox.Name = "codeTextBox";
            this.codeTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            this.codeTextBox.Enabled = false; // Sera mis à jour dans le constructeur
            mainPanel.Controls.Add(this.codeTextBox, 1, 0);

            // Raison sociale
            System.Windows.Forms.Label raisonSocialeLabel = new System.Windows.Forms.Label();
            raisonSocialeLabel.Text = "Raison sociale* :";
            raisonSocialeLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(raisonSocialeLabel, 0, 1);

            System.Windows.Forms.TextBox raisonSocialeTextBox = new System.Windows.Forms.TextBox();
            raisonSocialeTextBox.Name = "raisonSocialeTextBox";
            raisonSocialeTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            raisonSocialeTextBox.Validating += new System.ComponentModel.CancelEventHandler(this.RaisonSocialeTextBox_Validating);
            mainPanel.Controls.Add(raisonSocialeTextBox, 1, 1);

            // Adresse
            System.Windows.Forms.Label adresseLabel = new System.Windows.Forms.Label();
            adresseLabel.Text = "Adresse :";
            adresseLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(adresseLabel, 0, 2);

            System.Windows.Forms.TextBox adresseTextBox = new System.Windows.Forms.TextBox();
            adresseTextBox.Name = "adresseTextBox";
            adresseTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(adresseTextBox, 1, 2);

            // Code postal
            System.Windows.Forms.Label codePostalLabel = new System.Windows.Forms.Label();
            codePostalLabel.Text = "Code postal :";
            codePostalLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(codePostalLabel, 0, 3);

            System.Windows.Forms.TextBox codePostalTextBox = new System.Windows.Forms.TextBox();
            codePostalTextBox.Name = "codePostalTextBox";
            codePostalTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(codePostalTextBox, 1, 3);

            // Ville
            System.Windows.Forms.Label villeLabel = new System.Windows.Forms.Label();
            villeLabel.Text = "Ville :";
            villeLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(villeLabel, 0, 4);

            System.Windows.Forms.TextBox villeTextBox = new System.Windows.Forms.TextBox();
            villeTextBox.Name = "villeTextBox";
            villeTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(villeTextBox, 1, 4);

            // Pays
            System.Windows.Forms.Label paysLabel = new System.Windows.Forms.Label();
            paysLabel.Text = "Pays :";
            paysLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(paysLabel, 0, 5);

            System.Windows.Forms.TextBox paysTextBox = new System.Windows.Forms.TextBox();
            paysTextBox.Name = "paysTextBox";
            paysTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            paysTextBox.Text = "France"; // Valeur par défaut
            mainPanel.Controls.Add(paysTextBox, 1, 5);

            // Téléphone
            System.Windows.Forms.Label telephoneLabel = new System.Windows.Forms.Label();
            telephoneLabel.Text = "Téléphone :";
            telephoneLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(telephoneLabel, 0, 6);

            System.Windows.Forms.TextBox telephoneTextBox = new System.Windows.Forms.TextBox();
            telephoneTextBox.Name = "telephoneTextBox";
            telephoneTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(telephoneTextBox, 1, 6);

            // Email
            System.Windows.Forms.Label emailLabel = new System.Windows.Forms.Label();
            emailLabel.Text = "Email :";
            emailLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(emailLabel, 0, 7);

            System.Windows.Forms.TextBox emailTextBox = new System.Windows.Forms.TextBox();
            emailTextBox.Name = "emailTextBox";
            emailTextBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            emailTextBox.Validating += new System.ComponentModel.CancelEventHandler(this.EmailTextBox_Validating);
            mainPanel.Controls.Add(emailTextBox, 1, 7);

            // Commercial
            System.Windows.Forms.Label commercialLabel = new System.Windows.Forms.Label();
            commercialLabel.Text = "Commercial :";
            commercialLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(commercialLabel, 0, 8);

            System.Windows.Forms.ComboBox commercialComboBox = new System.Windows.Forms.ComboBox();
            commercialComboBox.Name = "commercialComboBox";
            commercialComboBox.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            commercialComboBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            mainPanel.Controls.Add(commercialComboBox, 1, 8);

            // Limite de crédit
            System.Windows.Forms.Label limiteCreditLabel = new System.Windows.Forms.Label();
            limiteCreditLabel.Text = "Limite de crédit :";
            limiteCreditLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(limiteCreditLabel, 0, 9);

            System.Windows.Forms.NumericUpDown limiteCreditNumericUpDown = new System.Windows.Forms.NumericUpDown();
            limiteCreditNumericUpDown.Name = "limiteCreditNumericUpDown";
            limiteCreditNumericUpDown.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            limiteCreditNumericUpDown.DecimalPlaces = 2;
            limiteCreditNumericUpDown.Maximum = 1000000;
            limiteCreditNumericUpDown.ThousandsSeparator = true;
            mainPanel.Controls.Add(limiteCreditNumericUpDown, 1, 9);

            // Solde actuel (en lecture seule en mode édition)
            System.Windows.Forms.Label soldeActuelLabel = new System.Windows.Forms.Label();
            soldeActuelLabel.Text = "Solde actuel :";
            soldeActuelLabel.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(soldeActuelLabel, 0, 10);

            System.Windows.Forms.NumericUpDown soldeActuelNumericUpDown = new System.Windows.Forms.NumericUpDown();
            soldeActuelNumericUpDown.Name = "soldeActuelNumericUpDown";
            soldeActuelNumericUpDown.Anchor = System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
            soldeActuelNumericUpDown.DecimalPlaces = 2;
            soldeActuelNumericUpDown.Maximum = 1000000;
            soldeActuelNumericUpDown.Minimum = -1000000;
            soldeActuelNumericUpDown.ThousandsSeparator = true;
            soldeActuelNumericUpDown.Enabled = false; // Toujours en lecture seule
            mainPanel.Controls.Add(soldeActuelNumericUpDown, 1, 10);

            // Boutons
            System.Windows.Forms.FlowLayoutPanel buttonsPanel = new System.Windows.Forms.FlowLayoutPanel();
            buttonsPanel.FlowDirection = System.Windows.Forms.FlowDirection.RightToLeft;
            buttonsPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            buttonsPanel.Anchor = System.Windows.Forms.AnchorStyles.Right;
            mainPanel.Controls.Add(buttonsPanel, 1, 11);
            mainPanel.SetColumnSpan(buttonsPanel, 2);

            System.Windows.Forms.Button cancelButton = new System.Windows.Forms.Button();
            cancelButton.Name = "cancelButton";
            cancelButton.Text = "Annuler";
            cancelButton.Size = new System.Drawing.Size(100, 30);
            cancelButton.Click += new System.EventHandler(this.CancelButton_Click);
            buttonsPanel.Controls.Add(cancelButton);

            System.Windows.Forms.Button saveButton = new System.Windows.Forms.Button();
            saveButton.Name = "saveButton";
            saveButton.Text = "Enregistrer";
            saveButton.Size = new System.Drawing.Size(100, 30);
            saveButton.Click += new System.EventHandler(this.SaveButton_Click);
            buttonsPanel.Controls.Add(saveButton);

            // Note sur les champs obligatoires
            System.Windows.Forms.Label noteLabel = new System.Windows.Forms.Label();
            noteLabel.Text = "* Champs obligatoires";
            noteLabel.AutoSize = true;
            noteLabel.ForeColor = System.Drawing.Color.Red;
            noteLabel.Location = new System.Drawing.Point(10, this.ClientSize.Height - 30);
            this.Controls.Add(noteLabel);
        }

        #endregion
    }
}
