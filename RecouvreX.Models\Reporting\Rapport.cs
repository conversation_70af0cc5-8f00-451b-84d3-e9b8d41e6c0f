using System;
using System.Collections.Generic;

namespace RecouvreX.Models.Reporting
{
    /// <summary>
    /// Représente un rapport généré par le système
    /// </summary>
    public class Rapport : BaseEntity
    {

        /// <summary>
        /// Titre du rapport
        /// </summary>
        public string Titre { get; set; }

        /// <summary>
        /// Description du rapport
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Type de rapport
        /// </summary>
        public string Type { get; set; }



        /// <summary>
        /// Identifiant de l'utilisateur qui a créé le rapport
        /// </summary>
        public int UtilisateurId { get; set; }

        /// <summary>
        /// Utilisateur qui a créé le rapport (navigation property)
        /// </summary>
        public Utilisateur Utilisateur { get; set; }

        /// <summary>
        /// Date de début de la période couverte par le rapport
        /// </summary>
        public DateTime DateDebut { get; set; }

        /// <summary>
        /// Date de fin de la période couverte par le rapport
        /// </summary>
        public DateTime DateFin { get; set; }

        /// <summary>
        /// Format du rapport (PDF, Excel, etc.)
        /// </summary>
        public string Format { get; set; }

        /// <summary>
        /// Chemin vers le fichier du rapport
        /// </summary>
        public string CheminFichier { get; set; }

        /// <summary>
        /// Paramètres utilisés pour générer le rapport (au format JSON)
        /// </summary>
        public string Parametres { get; set; }

        /// <summary>
        /// Indique si le rapport est programmé pour être généré périodiquement
        /// </summary>
        public bool EstProgramme { get; set; }

        /// <summary>
        /// Fréquence de génération pour les rapports programmés (Quotidien, Hebdomadaire, Mensuel, etc.)
        /// </summary>
        public string Frequence { get; set; }

        /// <summary>
        /// Prochaine date de génération pour les rapports programmés
        /// </summary>
        public DateTime? DateProchainRapport { get; set; }

        /// <summary>
        /// Liste des destinataires du rapport (au format JSON)
        /// </summary>
        public string Destinataires { get; set; }


    }

    /// <summary>
    /// Types de rapports disponibles
    /// </summary>
    public static class TypeRapport
    {
        public const string RecouvrementGlobal = "RecouvrementGlobal";
        public const string PerformanceClient = "PerformanceClient";
        public const string DelaisPaiement = "DelaisPaiement";
        public const string EfficaciteRelances = "EfficaciteRelances";
        public const string FacturesImpayees = "FacturesImpayees";
        public const string AnalyseVieillissement = "AnalyseVieillissement";
    }

    /// <summary>
    /// Formats de rapports disponibles
    /// </summary>
    public static class FormatRapport
    {
        public const string PDF = "PDF";
        public const string Excel = "Excel";
        public const string CSV = "CSV";
        public const string HTML = "HTML";
    }

    /// <summary>
    /// Fréquences de génération de rapports disponibles
    /// </summary>
    public static class FrequenceRapport
    {
        public const string Quotidien = "Quotidien";
        public const string Hebdomadaire = "Hebdomadaire";
        public const string Mensuel = "Mensuel";
        public const string Trimestriel = "Trimestriel";
        public const string Semestriel = "Semestriel";
        public const string Annuel = "Annuel";
    }
}
