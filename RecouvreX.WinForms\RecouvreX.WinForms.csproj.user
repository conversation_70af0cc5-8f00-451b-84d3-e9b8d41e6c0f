﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup />
  <ItemGroup>
    <Compile Update="Controls\ActionsPrioritairesControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Controls\MesTachesControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Update="Forms\Actions\ActionPrioritaireListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Actions\AssignerActionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Administration\ResetPasswordForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Administration\RoleEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Administration\RoleListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Administration\RolePermissionsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Administration\UtilisateurEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Administration\UtilisateurListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Admin\DatabaseAdminForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Admin\DatabaseConnectionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Alertes\AlerteEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Alertes\AlerteListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Clients\ClientDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Clients\ClientEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Clients\ClientListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Clients\ClientSegmentationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Clients\ClientSegmentEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Clients\ContactEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Clients\ScoreRisqueClientListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Common\SelectionFactureForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Communications\AppelForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Communications\CommunicationListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Communications\EmailForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Communications\NoteForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Communications\WhatsAppForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Factures\FactureDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Factures\FactureEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Factures\FactureListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Litiges\CategorieLitigeEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Litiges\CategorieLitigeListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Litiges\DocumentLitigeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Litiges\EtapeLitigeEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Litiges\EtapeLitigeListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Litiges\LitigeEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Litiges\LitigeListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Litiges\NotificationsLitigeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Paiements\PaiementDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Paiements\PaiementEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Paiements\PaiementListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PlansPaiement\AccordPaiementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PlansPaiement\EcheanceEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PlansPaiement\EcheancePaiementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PlansPaiement\EcheancierDashboardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PlansPaiement\PlanPaiementDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PlansPaiement\PlanPaiementEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PlansPaiement\PlanPaiementListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Rapports\CustomReportBuilderForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Rapports\CustomReportListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Rapports\ExportFormatForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Rapports\ReportGeneratorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Rapports\ReportPreviewForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Rapports\SendReportByEmailForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Relances\CommentaireForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Relances\LettreRelanceForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Relances\ModeleRelanceEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Relances\ModeleRelanceListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Relances\PlanificationRelanceListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Relances\RegleRelanceEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Relances\RegleRelanceListForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Relances\RelanceDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Relances\RelanceEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\Relances\RelanceListForm.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
</Project>