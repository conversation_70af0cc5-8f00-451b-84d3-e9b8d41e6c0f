-- Script principal pour exécuter tous les scripts SQL dans l'ordre
-- Ce script crée la base de données RecouvreX complète

PRINT '=== Début de la création de la base de données RecouvreX ===';
PRINT '';

PRINT '1. Création de la base de données...';
:r 01_CreateDatabase.sql
PRINT 'Terminé.';
PRINT '';

PRINT '2. Création des tables...';
:r 02_CreateTables.sql
PRINT 'Terminé.';
PRINT '';

PRINT '3. Création des index...';
:r 03_CreateIndexes.sql
PRINT 'Terminé.';
PRINT '';

PRINT '4. Insertion des données initiales...';
:r 04_InsertInitialData.sql
PRINT 'Terminé.';
PRINT '';

PRINT '5. Création des procédures stockées...';
:r 05_CreateStoredProcedures.sql
PRINT 'Terminé.';
PRINT '';

PRINT '6. Création des déclencheurs...';
:r 06_CreateTriggers.sql
PRINT 'Terminé.';
PRINT '';

PRINT '=== Base de données RecouvreX créée avec succès ===';
PRINT '';
PRINT 'Informations de connexion par défaut:';
PRINT '- Nom d''utilisateur: admin';
PRINT '- Mot de passe: Admin123!';
PRINT '';
PRINT 'N''oubliez pas de changer le mot de passe par défaut après la première connexion.';
GO
