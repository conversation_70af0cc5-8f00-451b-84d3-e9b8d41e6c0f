using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.IO;

namespace RecouvreX.WinForms.Forms.Litiges
{
    partial class LitigeEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // Déclaration des contrôles principaux
        private Panel mainPanel;
        private Label titleLabel;
        private TabControl tabControl;
        private TabPage generalTab;
        private TabPage notesTab;
        private Label factureLabel;
        private ComboBox factureComboBox;
        private Label categorieLabel;
        private ComboBox categorieComboBox;
        private Label montantLabel;
        private TextBox montantTextBox;
        private Label descriptionLabel;
        private TextBox descriptionTextBox;
        private Label dateOuvertureLabel;
        private DateTimePicker dateOuverturePicker;
        private Label dateEcheanceLabel;
        private DateTimePicker dateEcheancePicker;
        private Label responsableLabel;
        private ComboBox responsableComboBox;
        private Label prioriteLabel;
        private ComboBox prioriteComboBox;
        private Label statutLabel;
        private ComboBox statutComboBox;
        private Label etapeLabel;
        private ComboBox etapeComboBox;
        private Label solutionLabel;
        private TextBox solutionTextBox;
        private Button documentsButton;
        private TextBox notesTextBox;
        private Panel buttonsPanel;
        private Button saveButton;
        private Button cancelButton;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();

            // Définir les propriétés du formulaire
            this.Size = new Size(800, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.MinimizeBox = false;
            this.MaximizeBox = false;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;

            // Créer les contrôles
            this.mainPanel = new Panel();
            this.mainPanel.Dock = DockStyle.Fill;
            this.Controls.Add(this.mainPanel);

            // Titre
            this.titleLabel = new Label();
            this.titleLabel.Font = new Font(this.Font.FontFamily, 16, FontStyle.Bold);
            this.titleLabel.AutoSize = true;
            this.titleLabel.Location = new Point(20, 20);
            this.mainPanel.Controls.Add(this.titleLabel);

            // TabControl pour organiser les informations
            this.tabControl = new TabControl();
            this.tabControl.Name = "tabControl";
            this.tabControl.Location = new Point(20, 60);
            this.tabControl.Size = new Size(750, 550);
            this.mainPanel.Controls.Add(this.tabControl);

            // Onglet Informations générales
            this.generalTab = new TabPage("Informations générales");
            this.tabControl.TabPages.Add(this.generalTab);

            // Facture
            this.factureLabel = new Label();
            this.factureLabel.Text = "Facture :";
            this.factureLabel.AutoSize = true;
            this.factureLabel.Location = new Point(20, 20);
            this.generalTab.Controls.Add(this.factureLabel);

            this.factureComboBox = new ComboBox();
            this.factureComboBox.Name = "factureComboBox";
            this.factureComboBox.Location = new Point(150, 17);
            this.factureComboBox.Size = new Size(300, 25);
            this.factureComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.generalTab.Controls.Add(this.factureComboBox);

            // Catégorie
            this.categorieLabel = new Label();
            this.categorieLabel.Text = "Catégorie :";
            this.categorieLabel.AutoSize = true;
            this.categorieLabel.Location = new Point(20, 60);
            this.generalTab.Controls.Add(this.categorieLabel);

            this.categorieComboBox = new ComboBox();
            this.categorieComboBox.Name = "categorieComboBox";
            this.categorieComboBox.Location = new Point(150, 57);
            this.categorieComboBox.Size = new Size(300, 25);
            this.categorieComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.generalTab.Controls.Add(this.categorieComboBox);

            // Montant contesté
            this.montantLabel = new Label();
            this.montantLabel.Text = "Montant contesté :";
            this.montantLabel.AutoSize = true;
            this.montantLabel.Location = new Point(20, 100);
            this.generalTab.Controls.Add(this.montantLabel);

            this.montantTextBox = new TextBox();
            this.montantTextBox.Name = "montantTextBox";
            this.montantTextBox.Location = new Point(150, 97);
            this.montantTextBox.Size = new Size(150, 25);
            this.generalTab.Controls.Add(this.montantTextBox);

            // Description
            this.descriptionLabel = new Label();
            this.descriptionLabel.Text = "Description :";
            this.descriptionLabel.AutoSize = true;
            this.descriptionLabel.Location = new Point(20, 140);
            this.generalTab.Controls.Add(this.descriptionLabel);

            this.descriptionTextBox = new TextBox();
            this.descriptionTextBox.Name = "descriptionTextBox";
            this.descriptionTextBox.Location = new Point(150, 137);
            this.descriptionTextBox.Size = new Size(550, 100);
            this.descriptionTextBox.Multiline = true;
            this.descriptionTextBox.ScrollBars = ScrollBars.Vertical;
            this.generalTab.Controls.Add(this.descriptionTextBox);

            // Date d'ouverture
            this.dateOuvertureLabel = new Label();
            this.dateOuvertureLabel.Text = "Date d'ouverture :";
            this.dateOuvertureLabel.AutoSize = true;
            this.dateOuvertureLabel.Location = new Point(20, 250);
            this.generalTab.Controls.Add(this.dateOuvertureLabel);

            this.dateOuverturePicker = new DateTimePicker();
            this.dateOuverturePicker.Name = "dateOuverturePicker";
            this.dateOuverturePicker.Location = new Point(150, 247);
            this.dateOuverturePicker.Size = new Size(200, 25);
            this.dateOuverturePicker.Format = DateTimePickerFormat.Short;
            this.generalTab.Controls.Add(this.dateOuverturePicker);

            // Date d'échéance
            this.dateEcheanceLabel = new Label();
            this.dateEcheanceLabel.Text = "Date d'échéance :";
            this.dateEcheanceLabel.AutoSize = true;
            this.dateEcheanceLabel.Location = new Point(20, 290);
            this.generalTab.Controls.Add(this.dateEcheanceLabel);

            this.dateEcheancePicker = new DateTimePicker();
            this.dateEcheancePicker.Name = "dateEcheancePicker";
            this.dateEcheancePicker.Location = new Point(150, 287);
            this.dateEcheancePicker.Size = new Size(200, 25);
            this.dateEcheancePicker.Format = DateTimePickerFormat.Short;
            this.generalTab.Controls.Add(this.dateEcheancePicker);

            // Responsable
            this.responsableLabel = new Label();
            this.responsableLabel.Text = "Responsable :";
            this.responsableLabel.AutoSize = true;
            this.responsableLabel.Location = new Point(20, 330);
            this.generalTab.Controls.Add(this.responsableLabel);

            this.responsableComboBox = new ComboBox();
            this.responsableComboBox.Name = "responsableComboBox";
            this.responsableComboBox.Location = new Point(150, 327);
            this.responsableComboBox.Size = new Size(300, 25);
            this.responsableComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.generalTab.Controls.Add(this.responsableComboBox);

            // Priorité
            this.prioriteLabel = new Label();
            this.prioriteLabel.Text = "Priorité :";
            this.prioriteLabel.AutoSize = true;
            this.prioriteLabel.Location = new Point(20, 370);
            this.generalTab.Controls.Add(this.prioriteLabel);

            this.prioriteComboBox = new ComboBox();
            this.prioriteComboBox.Name = "prioriteComboBox";
            this.prioriteComboBox.Location = new Point(150, 367);
            this.prioriteComboBox.Size = new Size(150, 25);
            this.prioriteComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.prioriteComboBox.Items.AddRange(new object[] { "Basse", "Moyenne", "Haute", "Critique" });
            this.generalTab.Controls.Add(this.prioriteComboBox);

            // Statut
            this.statutLabel = new Label();
            this.statutLabel.Text = "Statut :";
            this.statutLabel.AutoSize = true;
            this.statutLabel.Location = new Point(20, 410);
            this.generalTab.Controls.Add(this.statutLabel);

            this.statutComboBox = new ComboBox();
            this.statutComboBox.Name = "statutComboBox";
            this.statutComboBox.Location = new Point(150, 407);
            this.statutComboBox.Size = new Size(150, 25);
            this.statutComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.statutComboBox.Items.AddRange(new object[] { "Ouvert", "En cours", "En attente", "Résolu", "Annulé" });
            this.statutComboBox.SelectedIndexChanged += new EventHandler(this.StatutComboBox_SelectedIndexChanged);
            this.generalTab.Controls.Add(this.statutComboBox);

            // Étape
            this.etapeLabel = new Label();
            this.etapeLabel.Text = "Étape :";
            this.etapeLabel.AutoSize = true;
            this.etapeLabel.Location = new Point(20, 450);
            this.generalTab.Controls.Add(this.etapeLabel);

            this.etapeComboBox = new ComboBox();
            this.etapeComboBox.Name = "etapeComboBox";
            this.etapeComboBox.Location = new Point(150, 447);
            this.etapeComboBox.Size = new Size(300, 25);
            this.etapeComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            this.generalTab.Controls.Add(this.etapeComboBox);

            // Solution (visible uniquement si le statut est "Résolu")
            this.solutionLabel = new Label();
            this.solutionLabel.Text = "Solution :";
            this.solutionLabel.AutoSize = true;
            this.solutionLabel.Location = new Point(20, 490);
            this.solutionLabel.Visible = false;
            this.generalTab.Controls.Add(this.solutionLabel);

            this.solutionTextBox = new TextBox();
            this.solutionTextBox.Name = "solutionTextBox";
            this.solutionTextBox.Location = new Point(150, 487);
            this.solutionTextBox.Size = new Size(550, 50);
            this.solutionTextBox.Multiline = true;
            this.solutionTextBox.ScrollBars = ScrollBars.Vertical;
            this.solutionTextBox.Visible = false;
            this.generalTab.Controls.Add(this.solutionTextBox);

            // Bouton Documents
            this.documentsButton = new Button();
            this.documentsButton.Text = "Gérer les documents";
            this.documentsButton.Size = new Size(150, 30);
            this.documentsButton.Location = new Point(550, 17);
            this.documentsButton.Click += new EventHandler(this.DocumentsButton_Click);
            this.generalTab.Controls.Add(this.documentsButton);

            // Onglet Notes
            this.notesTab = new TabPage("Notes");
            this.tabControl.TabPages.Add(this.notesTab);

            this.notesTextBox = new TextBox();
            this.notesTextBox.Name = "notesTextBox";
            this.notesTextBox.Dock = DockStyle.Fill;
            this.notesTextBox.Multiline = true;
            this.notesTextBox.ScrollBars = ScrollBars.Vertical;
            this.notesTab.Controls.Add(this.notesTextBox);

            // Boutons d'action
            this.buttonsPanel = new Panel();
            this.buttonsPanel.Size = new Size(750, 50);
            this.buttonsPanel.Location = new Point(20, 620);
            this.mainPanel.Controls.Add(this.buttonsPanel);

            this.saveButton = new Button();
            this.saveButton.Text = "Enregistrer";
            this.saveButton.Size = new Size(100, 30);
            this.saveButton.Location = new Point(540, 10);
            this.saveButton.Click += new EventHandler(this.SaveButton_Click);
            this.buttonsPanel.Controls.Add(this.saveButton);

            this.cancelButton = new Button();
            this.cancelButton.Text = "Annuler";
            this.cancelButton.Size = new Size(100, 30);
            this.cancelButton.Location = new Point(650, 10);
            this.cancelButton.Click += new EventHandler(this.CancelButton_Click);
            this.buttonsPanel.Controls.Add(this.cancelButton);

            // Charger le formulaire
            this.Load += new EventHandler(this.LitigeEditForm_Load);
        }

        #endregion
    }
}
