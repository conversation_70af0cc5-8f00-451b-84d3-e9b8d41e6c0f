using RecouvreX.Models;
using RecouvreX.Models.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecouvreX.DataAccess.Interfaces
{
    /// <summary>
    /// Interface pour le repository des actions prioritaires
    /// </summary>
    public interface IActionPrioritaireRepository : IRepository<ActionPrioritaire>
    {
        /// <summary>
        /// Récupère les actions prioritaires d'un client
        /// </summary>
        /// <param name="clientId">Identifiant du client</param>
        /// <returns>Liste des actions prioritaires du client</returns>
        Task<IEnumerable<ActionPrioritaire>> GetByClientIdAsync(int clientId);

        /// <summary>
        /// Récupère les actions prioritaires liées à une facture
        /// </summary>
        /// <param name="factureId">Identifiant de la facture</param>
        /// <returns>Liste des actions prioritaires liées à la facture</returns>
        Task<IEnumerable<ActionPrioritaire>> GetByFactureIdAsync(int factureId);

        /// <summary>
        /// Récupère les actions prioritaires assignées à un utilisateur
        /// </summary>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <returns>Liste des actions prioritaires assignées à l'utilisateur</returns>
        Task<IEnumerable<ActionPrioritaire>> GetByUtilisateurAssigneIdAsync(int utilisateurId);

        /// <summary>
        /// Récupère les actions prioritaires par niveau de priorité
        /// </summary>
        /// <param name="niveauPriorite">Niveau de priorité</param>
        /// <returns>Liste des actions prioritaires du niveau spécifié</returns>
        Task<IEnumerable<ActionPrioritaire>> GetByNiveauPrioriteAsync(NiveauPriorite niveauPriorite);

        /// <summary>
        /// Récupère les actions prioritaires par type d'action
        /// </summary>
        /// <param name="typeAction">Type d'action</param>
        /// <returns>Liste des actions prioritaires du type spécifié</returns>
        Task<IEnumerable<ActionPrioritaire>> GetByTypeActionAsync(TypeActionPrioritaire typeAction);

        /// <summary>
        /// Récupère les actions prioritaires à échéance dans un nombre de jours spécifié
        /// </summary>
        /// <param name="jours">Nombre de jours</param>
        /// <returns>Liste des actions prioritaires à échéance dans le nombre de jours spécifié</returns>
        Task<IEnumerable<ActionPrioritaire>> GetByEcheanceAsync(int jours);

        /// <summary>
        /// Récupère les actions prioritaires en retard (échéance dépassée et non complétées)
        /// </summary>
        /// <returns>Liste des actions prioritaires en retard</returns>
        Task<IEnumerable<ActionPrioritaire>> GetEnRetardAsync();

        /// <summary>
        /// Marque une action prioritaire comme complétée
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire</param>
        /// <param name="commentaire">Commentaire sur la complétion</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire mise à jour</returns>
        Task<ActionPrioritaire> MarquerCompleteAsync(int id, string commentaire, int modifiePar);

        /// <summary>
        /// Assigne une action prioritaire à un utilisateur
        /// </summary>
        /// <param name="id">Identifiant de l'action prioritaire</param>
        /// <param name="utilisateurId">Identifiant de l'utilisateur</param>
        /// <param name="modifiePar">Identifiant de l'utilisateur qui effectue l'action</param>
        /// <returns>Action prioritaire mise à jour</returns>
        Task<ActionPrioritaire> AssignerAsync(int id, int utilisateurId, int modifiePar);

        /// <summary>
        /// Récupère le nombre d'actions prioritaires par niveau de priorité
        /// </summary>
        /// <returns>Dictionnaire avec le niveau de priorité comme clé et le nombre d'actions comme valeur</returns>
        Task<Dictionary<NiveauPriorite, int>> GetCountByNiveauPrioriteAsync();
    }
}
